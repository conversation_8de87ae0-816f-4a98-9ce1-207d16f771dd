import { Component, OnInit } from '@angular/core';
import Swal from 'sweetalert2';
import { BillService } from '../bill/bill.service';
import { BasReport } from './bas';
import { Entity } from '../../entity/entity';
import { forkJoin } from 'rxjs';
import { EntityService } from '../../entity/entity.service';
import * as moment from 'moment';
import { Router } from '@angular/router';

@Component({
  selector: 'app-bas',
  templateUrl: './bas.component.html',
  styleUrls: ['./bas.component.css']
})
export class BASComponent implements OnInit {

  fromDate: string = '';
  toDate: string = '';
  isLoading: boolean = false;
  basReport: BasReport = new BasReport();
  entity: Entity = new Entity();
  lastBasReport: BasReport | null = null;


  // BAS summary data model example
  basSummary = {
    G1: 0,
    G10:0,
    G11:0,
    G12:0,
    '1A': 0,
    '1B': 0,
    g1FreeIncome:0,
    g10FreeExpenses:0

  };

  // Transaction lists for Rate tab

  filteredTransactions: any[] = [];
  searchText: string = '';

  constructor(private billService: BillService, private entityService: EntityService, private router: Router) { }

 ngOnInit() {
  const entityId = +(localStorage.getItem('entityId') || '');

  forkJoin({
    entity: this.entityService.getBusinessEntityById(entityId),
    reports: this.billService.getBasReportsByEntityId(entityId),
  }).subscribe(({ entity, reports }) => {
    this.entity = entity;

    if (reports.length > 0) {
      reports.sort((a, b) => new Date(b.periodEnd).getTime() - new Date(a.periodEnd).getTime());
      this.lastBasReport = reports[0];
    }

    this.setInitialBasPeriod();
     if (this.fromDate && this.toDate) {
    this.loadBASReport();
  }
  });
}



  getNextBasPeriod(lastPeriodEnd: string, basPeriod: string): { nextStart: string, nextEnd: string } {
  const lastEnd = moment(lastPeriodEnd, 'YYYY-MM-DD');
  const nextStart = lastEnd.clone().add(1, 'day');
  let nextEnd: moment.Moment;

  if (basPeriod === 'Monthly') {
    nextEnd = nextStart.clone().add(1, 'month').subtract(1, 'day');
  } else if (basPeriod === 'Quarterly') {
    nextEnd = nextStart.clone().add(3, 'months').subtract(1, 'day');
  } else if (basPeriod === 'Annually') {
    nextEnd = nextStart.clone().add(1, 'year').subtract(1, 'day');
  } else {
    // Default fallback (e.g., treat unknowns as Monthly)
    nextEnd = nextStart.clone().add(1, 'month').subtract(1, 'day');
  }

  return {
    nextStart: nextStart.format('YYYY-MM-DD'),
    nextEnd: nextEnd.format('YYYY-MM-DD'),
  };
}

    setInitialBasPeriod() {
  if (this.entity && this.lastBasReport && this.entity.basPeriod) {
    const { nextStart, nextEnd } = this.getNextBasPeriod(this.lastBasReport.periodEnd, this.entity.basPeriod);
    this.fromDate = nextStart;
    this.toDate = nextEnd;
  }
}


  loadBASReport() {
    if (!this.fromDate || !this.toDate) {
      Swal.fire({
        title: 'Warning!',
        text: 'Please select Date Range',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f'
      });
      return;
    }
    this.isLoading = true;
    const entityId = +(localStorage.getItem('entityId') ?? '0');
    this.billService.getBalanceSheet(this.fromDate, this.toDate, entityId).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        if (data && data.response) {
          // Assuming response has BAS summary and transaction details
          this.mapBASData(data.response);
        } else {
          Swal.fire('No Data', 'No BAS data available for this period.', 'info');
        }
      },
      error: () => {
        this.isLoading = false;
        Swal.fire('Error', 'Error loading BAS report.', 'error');
      }
    });
  }

  private mapBASData(response: any) {
  console.log('Response in mapBASData:', response);

  const g1 = response.g1 ?? 0;
  const g10 = response.g10 ?? 0;
  const g11 = response.g11 ?? 0;
  const g12 = response.g12 ?? 0;
  const oneA = response['1A'] ?? 0;
  const oneB = response['1B'] ?? 0;
  const g1FreeIncome = response.g1FreeIncome ?? 0;
  const g10FreeExpenses = response.g10FreeExpenses ?? 0;

  this.basSummary = {
    ...this.basSummary,
    G1: g1,
    G10: g10,
    G11: g11,
    G12: g12,
    '1A': oneA,
    '1B': oneB,
  g1FreeIncome: g1FreeIncome,
 g10FreeExpenses: g10FreeExpenses
  };

  // Populate basReport here
  this.basReport.gstOnSales = parseFloat(g1.toFixed(2));
  this.basReport.gstOnPurchases = parseFloat(g10.toFixed(2));
  this.basReport.capitalPurchase = parseFloat(g11.toFixed(2));
  this.basReport.totalPurchase = parseFloat(g12.toFixed(2));
  this.basReport.gstCollected = parseFloat(oneA.toFixed(2));
  this.basReport.gstPaid = parseFloat(oneB.toFixed(2));
}


markAsLodged() {
      if (!this.fromDate || !this.toDate) {
      Swal.fire({
        title: 'Warning!',
        text: 'Please select Date Range',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f'
      });
      return;
    }
  const entityId = +(localStorage.getItem('entityId') ?? '0');
  const userId = +(localStorage.getItem('userid') ?? '0');
  const firstName = localStorage.getItem('firstName') ?? '';
  const lastName = localStorage.getItem('lastName') ?? '';

  this.basReport.entityId = entityId;
  this.basReport.userId = userId;
  this.basReport.periodStart = this.fromDate;
  this.basReport.periodEnd = this.toDate;
  this.basReport.basVersion = 'v1';
  this.basReport.status = 'Lodged';
  this.basReport.basLoadgeDate = new Date().toISOString().split('T')[0];
  this.basReport.userName = firstName + ' ' + lastName;
  console.log('Sending BAS Report:', this.basReport);

 this.billService.saveBasReportForLoadge(this.basReport).subscribe({
    next: () => {
      Swal.fire({
        title: 'Success',
        text: 'BAS Report has been lodged.',
        icon: 'success',
        confirmButtonText: 'OK',
        confirmButtonColor: '#4CAF50'
      }).then(() => {
        this.router.navigate(['/bas-period']);
      });
    },
    error: () => {
      Swal.fire('Error', 'Failed to lodge BAS report.', 'error');
    }
  });
}

}
