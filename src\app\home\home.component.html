<app-home-header></app-home-header>
<app-chat-assistant></app-chat-assistant>

<div class="container bg-transparent">
  <div class="row">
    <div class="col-12 home-text-centent-root">
      <div class="row">
        <div class="col-lg-8 col-sm-12 my-auto">
          <div class="col">
            <div class="col-12 home-text-content">
              <p>Invoicing | Payroll | BAS | Bank feeds | Reports</p>
              <div class="heading">
                Accounting software that works smarter—for a price that makes
                sense. Built for Australian small businesses.
              </div>
              <!-- <h1 class="my-4 bold-black-text" style="white-space: nowrap">
                Intelligent and user-friendly<br />
                web-based accounting<br />software for small businesses.
              </h1> -->
              <p>
                Monitor Expenditures, Calculate BAS, Pay Employees, Pay
                Superannuation, Personalise Invoices, Generate Reports, and much
                more, all conveniently accessible from a single location.
              </p>
            </div>
            <div class="col-12">
              <a class="try-now-button" (click)="scrollToPricing()">Try Now</a>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-sm-12 my-auto">
          <div class="image-1 text-center">
            <img
              src="../../assets/images/home-image-1.png"
              alt="home-image-1"
              class="homeBanner1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="container-fluid improve-business-root">
  <div class="container">
    <div class="row">
      <div
        class="col-10 col-xl-7 Banner"
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          width: 100%;
        "
      >
        <div class="improve-business">
          <div style="white-space: nowrap" class="textbnner">
            Are you ready to improve your business<br />
            using <span class="highlight">LEDGER CHIMP</span>?
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- pricing section -->
<div class="container pr-section px-4 px-md-0 py-5" id="pricing">
  <div class="planTxt">Choose Your Plan</div>

  <!-- Feature Comparison Table -->
  <div
    class="pricing-comparison-table w-100 g-0 bg-white"
    style="border-radius: 15px"
  >
    <div class="col-12">
      <div class="row g-0">
        <div class="col-7 col-xl-3 g-0">
          <div style="height: 303px"></div>
          <div class="w-100 mt-4">
            <p
              *ngFor="let feature of features.features; let i = index"
              class="px-2 px-md-4 feature-name text-start d-flex align-items-center"
              style="border-bottom: 1px solid #f0f0f0; height: 45px"
              [ngClass]="{ 'border-0': i === features.features.length - 1 }"
            >
              {{ feature }}
            </p>
          </div>
        </div>
        <div class="col-5 col-xl-9 g-0" style="overflow-x: auto">
          <div class="col-12 g-0 d-flex" style="gap: 0px">
            <div
              class="pricing-card p-0 w-100"
              (click)="selectPlan(plan)"
              [ngClass]="{ featured: isSelected(plan) }"
              *ngFor="let plan of subscriptionPlans; let i = index"
            >
              <div
                class="pricing-card-head m-0"
                [ngClass]="{
                  'content-middle':
                    getPlanName(plan) === 'Free' ||
                    getPlanName(plan) === 'Invoicer' ||
                    getPlanName(plan) === 'Payroll'
                }"
                style="min-width: 130px"
              >
                <div class="w-100">
                  <div>
                    <h3 class="plan-title">
                      {{ getDisplayPlanName(getPlanName(plan)) }}
                    </h3>

                    <div class="plan-price">
                      <span class="price">${{ plan.graceAmount }}</span>
                      <span class="period">
                        {{
                          getPlanName(plan) === "Free"
                            ? "/ Free"
                            : getPlanName(plan) === "Basic"
                            ? "/ Per One Year"
                            : "/ Months"
                        }}
                      </span>
                    </div>
                  </div>
                  <!-- Promo Section (Optional, based on plan name) -->
                  <div
                    class="plan-promo"
                    *ngIf="
                      getPlanName(plan) === 'Basic' ||
                      getPlanName(plan) === 'Premium'
                    "
                  >
                    <div>For 4 Months</div>
                    <div>
                      {{
                        getPlanName(plan) === "Basic" ? "Save $60" : "Save $160"
                      }}
                    </div>
                    <div class="promo-note">
                      {{
                        getPlanName(plan) === "Basic"
                          ? "$20/month after the offer period"
                          : "$45/month after offer period"
                      }}
                    </div>
                    <div class="employee-note">
                      <i>⚡️ $2/Month per additional employee</i>
                    </div>
                  </div>
                  <div
                    class="plan-promo"
                    *ngIf="getPlanName(plan) === 'Payroll'"
                  >
                    <div class="employee-note">
                      <i>⚡️ $2/Month per additional employee</i>
                    </div>
                  </div>

                  <button
                    class="btn-get-now mt-2 mb-0 position-absolute"
                    style="bottom: 10px; left: 10px; width: calc(100% - 20px)"
                    (click)="navigateUserRegistration(plan.subscriptionFeeId)"
                    [disabled]="isPurchasing === plan.subscriptionFeeId"
                  >
                    <span *ngIf="isPurchasing === plan.subscriptionFeeId">
                      <i class="fa fa-spinner fa-spin"></i> Processing...
                    </span>
                    <span *ngIf="isPurchasing !== plan.subscriptionFeeId">
                      {{ getPlanName(plan) === "Free" ? "Get Now" : "Buy Now" }}
                    </span>
                  </button>
                </div>
              </div>
              <div class="w-100 mt-4">
                <p
                  *ngFor="
                    let planDetail of getPlanDetails(plan);
                    let ind = index
                  "
                  class="feature-value text-center d-flex align-items-center justify-content-center"
                  style="border-bottom: 1px solid #f0f0f0; height: 45px"
                  [ngClass]="{
                    'border-0': ind === getPlanDetails(plan).length - 1
                  }"
                >
                  <span *ngIf="planDetail === '✔'">
                    <i class="fas fa-check-circle text-success fs-6"></i>
                  </span>
                  <span *ngIf="planDetail !== '✔'">
                    {{ planDetail }}
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- tools section -->
<div class="tools-section">
  <div class="tools-section-heading">
    <div class="toolTxt">Tools for everything your business requires.</div>
  </div>
  <div class="tools">
    <div class="tool-card">
      <h3>Create quotes & invoices</h3>
      <p>
        Simplify payments with “pay now” option and keep cash flow steady with
        automated reminders.
      </p>
    </div>
    <div class="tool-card">
      <h3>Connect bank accounts</h3>
      <p>
        Your transaction information will seamlessly transfer to your software,
        providing you with an up-to-date, picture of your finances.
      </p>
    </div>
    <div class="tool-card">
      <h3>Claim expenses</h3>
      <p>
        Capture images of your receipts, directly forward your bills from your
        email, and simplify expense tracking.
      </p>
    </div>
    <div class="tool-card">
      <h3>Auto-calculate GST</h3>
      <p>Let the GST be calculated automatically for you.</p>
    </div>
  </div>
  <div class="tools" style="padding-top: 20px">
    <div class="tool-card">
      <h3>Pay employees</h3>
      <p>Simple & user-friendly STP enabled payroll processing.</p>
    </div>
    <div class="tool-card">
      <h3>Pay Superannuation</h3>
      <p>Ensure timely super payments without the hassle.</p>
    </div>
    <div class="tool-card">
      <h3>Accountant Or Bookkeeper access</h3>
      <p>Seamless collaboration with your accountant or bookkeeper.</p>
    </div>
  </div>
</div>

<!-- contact section -->
<div class="contact-section">
  <div class="image-2">
    <img
      class="homeBanner2"
      src="../../assets/images/home-image-2.png"
      alt="home-image-2"
    />
  </div>
  <div class="info">
    <div class="info-text">
      If you have any questions about LEDGER CHIMP, Please don't<br />
      hesitate to get in touch with our team.
    </div>
    <a class="contact-now-button" (click)="navigateContactUs()">Contact Now</a>
  </div>
</div>

<app-home-footer></app-home-footer>
