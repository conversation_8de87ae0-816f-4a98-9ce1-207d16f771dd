import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-sales-reports',
  templateUrl: './sales-reports.component.html',
  styleUrls: ['./sales-reports.component.css']
})
export class SalesReportsComponent {
   userRole: string = '';
  constructor(
    private router: Router
  ) {}


   ngOnInit(): void {

   const userStr = localStorage.getItem('user');
    if (userStr) {
    const user = JSON.parse(userStr);
    this.userRole = user.roleName; // e.g., "Free", "Premium"
  }
  }

  navigateQuotationReport(): void {
    this.router.navigate(['/quotation-report']);
  }

  navigateInvoiceReport(): void {
    this.router.navigate(['/invoice-report']);
  }

  navigateCreditNoteReport(): void {
    this.router.navigate(['/credit-note-report']);
  }

  navigatePaymentReport(): void {
    this.router.navigate(['/payment-receipt-report']);
  }

  navigateInvoiceCashReport(): void {
    this.router.navigate(['/invoice-cash-report']);
  }

  navigateCustomerStatementReport(): void {
    this.router.navigate(['/customer-statement']);
  }

  navigateAccountsStatementReport(): void {
    this.router.navigate(['/account-statement']);
  }

}
