import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { CreditNote } from './credit-note';

@Injectable({
  providedIn: 'root',
})
export class CreditNoteService {
  private readonly baseURL = environment.salesApiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  saveCreditNote(creditNote: CreditNote): Observable<any> {
    return this.request('POST', `/saveCreditNote`, creditNote);
  }

  getCreditNoteList(): Observable<CreditNote[]> {
    return this.request('GET', `/creditNoteList`, {});
  }

  getAllSalesCreditNotesHeadList(entityId: number): Observable<CreditNote[]> {
    return this.request("GET", `/salescreditNotesHeadList/${entityId}`, {}, {});
  }

  updateCreditNote(id: number, creditNote: CreditNote): Observable<object> {
    return this.request('PUT', `/updateCreditNote/${id}`, creditNote);
  }

  getCreditNoteById(id: number): Observable<CreditNote> {
    return this.request('GET', `/getCreditNoteById/${id}`, {});
  }

 
  deleteCreditNote(creditNoteId: number): Observable<any> {
    return this.request('DELETE', `/deleteCreditNote/${creditNoteId}`, {});
  }

   getCreditNoteListReport(requestData: {
    fromDate: string,
    toDate: string,
    status: string,
    entityId: number,
    businessPartnerId: number | null,
    entityUUID: string
  }): Observable<any> {
     return this.request('POST',`/getCreditNoteListReport`, requestData);
  }

  getInvoiceDetailsByNumber(invoiceNumber: string): Observable<any> {
    return this.request('GET', `/getInvoiceByNumber/${invoiceNumber}`, {});
}

getCreditNoteReport(businessPartnerId: number, entityId: any): Observable<any> {
  return this.request('GET', `/getCreditNoteReport/${businessPartnerId}/${entityId}`, {});
}

}
