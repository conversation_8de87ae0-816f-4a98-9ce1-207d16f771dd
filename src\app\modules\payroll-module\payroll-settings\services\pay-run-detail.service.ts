import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpService } from 'src/app/http.service';
import { environment } from 'src/environments/environment';
import { Earning, PayItemType, PayRunDetail, PayRunDetailsSummary,  } from '../payroll-setting';
import { GlAccount } from 'src/app/modules/finance-module/gl-account/gl-account';
import { EmployeeEmployment } from '../empolyee/employee';

@Injectable({
  providedIn: 'root'
})
export class PayRunDetailService {

  private readonly baseURL = environment.payrollApiUrl;


  constructor(private http: HttpClient, private httpService: HttpService) { }

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  updatePayRunDetail(id: number, entityId: number, updatedPayRunDetail: PayRunDetail): Observable<PayRunDetail> {
    return this.request('PUT', `/payRunDetail/update/${id}/${entityId}`, updatedPayRunDetail);
  } 

  addPayRunDetail(payload: PayRunDetail): Observable<PayRunDetail> {
    return this.request('POST', '/payRunDetail/save', payload);
  }

  generatePaySlipReport(employeeId: number, entityId: number, calendarId: number, periodId: number ) : Observable<any>{
    return this.request('GET',`/payRunDetail/paySlipReport?employeeId=${employeeId}&entityId=${entityId}&calendarId=${calendarId}&periodId=${periodId}`, {} );
  }

  getPayrollSummaryReport(entityId: number, calendarId: number, periodId: number): Observable<any> {
    return this.request('GET', `/payProcess/getPayrollReport?entityId=${entityId}&calendarId=${calendarId}&periodId=${periodId}`, {});
  }

  updateEmployeeExclution(employeeId: number, payRunId: number, isExcluded: boolean) : Observable<PayRunDetailsSummary> {
    const params = { isExcluded };
    return this.request('PUT', `/payRunDetailsSummary/updateByEmployeeId/${employeeId}/${payRunId}`,null, params);
  }
  getBankPaymentsReport(payPeriodId: number, entityId: number): Observable<any> {
    return this.request('GET', `/payRunDetail/bankReport?payPeriodId=${payPeriodId}&entityId=${entityId}`, {});
  }
  getBankJournalReport(payPeriodId: number, entityId: number): Observable<any> {
    return this.request('GET', `/payRunDetail/bankJournal?payPeriodId=${payPeriodId}&entityId=${entityId}`, {});
  }
  
  
  getPayRunDetailsSummary(employeeId: number, payRunId: number): Observable<PayRunDetailsSummary> {
    return this.request('GET', `/payRunDetailsSummary/getSummaryByEmployeeAndPayRun/${employeeId}/${payRunId}`, {})
  }

  sendEmailToEmployee(employeeId: number, entityId: number, calendarId: number, periodId: number ) : Observable<any>{
    return this.request('GET',`/payRunDetail/paySlipReport?employeeId=${employeeId}&entityId=${entityId}&calendarId=${calendarId}&periodId=${periodId}`, {} );
  }

  addUnscheduledEmployee(selectedEmployee: EmployeeEmployment, payRunId: number): Observable<PayRunDetailsSummary> {
    return this.request('POST', `/payRunDetailsSummary/addUnscheduledEmployee/${payRunId}`, selectedEmployee)
  }


  
}