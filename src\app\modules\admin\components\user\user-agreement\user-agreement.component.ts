import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-user-agreement',
  templateUrl: './user-agreement.component.html',
  styleUrls: ['./user-agreement.component.css'],
})
export class UserAgreementComponent implements OnInit {
  inviteLogId: number = 0;
  isUserAgreementMarked = false;
  isTermsMarked = false;
  canProceed = false;
  isEntityInvitaion: boolean = false;
  subscriptionPlanId: number = 0;

  constructor(private router: Router, private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.inviteLogId = Number(params['inviteLogId']);
      this.isEntityInvitaion = params.hasOwnProperty('inviteLogId');
    });
  }

  isBothChecked() {
    this.canProceed = this.isUserAgreementMarked && this.isTermsMarked;
  }

  navigateUserRegistration() {
    if (this.canProceed) {
      const queryParams: any = {
        subscriptionPlanId: Number(this.route.snapshot.queryParams['subscriptionPlanId']),
        planName: this.route.snapshot.queryParams['planName']
      };

      if (this.isEntityInvitaion) {
        queryParams.inviteLogId = this.inviteLogId;
      }

      this.router.navigate([`/create-user`], { queryParams });
    } else {
      Swal.fire({
        title: 'Error!',
        text: 'Please accept both agreements to proceed.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      });
    }
  }
}
