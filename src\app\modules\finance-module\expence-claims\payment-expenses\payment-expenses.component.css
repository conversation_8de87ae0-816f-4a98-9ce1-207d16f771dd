body {
    font-family: Arial, sans-serif;
    background-color: transparent;
    margin: 0;
    padding: 0;
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: transparent;
  }
  /* Header styles */
  
  .header {
    display: flex;
    background-color: transparent;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    box-shadow: none;
    border: none;
    gap: 5px;
    /* Adds space between buttons */
  }
  
  .header h3 {
    flex: 1;
    margin-bottom: 0;
    font-family: Inter;
    font-size: 36px;
    font-weight: 700;
    text-align: left;
    color: #4262ff;
    top: 264px;
  }
  
  .bd {
    border: 2px solid #cec9c980; /* Sets the border width, style, and color */
    border-radius: 12px; /* Rounds the corners */
  }
  
  .form-section {
    display: flex;
    font-family: Arial, sans-serif;
    flex-direction: column;
    padding: 20px;
    background-color: #f7f7f7; /* If you want a solid background color */
  }
  
  .form-row {
    display: flex;
    gap: 32px;
    padding-right: 20px;
    margin-bottom: 5px;
  }
  
  .form-group {
    flex: 1;
  }
  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    width: 20%;
  }
  
  .form-group input {
    width: 40%;
    padding: 8px 10px;
    border: 2px solid #c7c7c7;
    border-radius: 8px;
    font-size: 14px;
  }
  
  .form-group select {
    width: 40%;
    padding: 8px 10px;
    border: 2px solid #c7c7c7;
    border-radius: 8px;
    font-size: 14px;
  }
  
  .form-section_2{
    display: flex;
    font-family: Arial, sans-serif;
    flex-direction: column;
    padding: 20px;
    background-color: #f7f7f7; /* If you want a solid background color */
  }
  
  .form-group_2 label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    width: 30%;
  }
  
  .form-group_2 input {
    width: 70%;
    padding: 8px 10px;
    border: 2px solid #c7c7c7;
    border-radius: 8px;
    font-size: 14px;
  }
  
  .form-group_2 textarea {
    width: 70%;
    padding: 8px 10px;
    border: 2px solid #c7c7c7;
    border-radius: 8px;
    font-size: 14px;
  }
  
  .form-control {
    border-radius: 5px;
    border: 1px solid #ced4da;
    padding: 10px;
    text-align: right;
  }
  
  .form-control1{
    border-radius: 5px;
    border: 1px solid #ced4da;
    padding: 10px;
  }
  
  .search-bar {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    padding-left: 20px;
    margin-bottom: 10px;
    margin-top: 10px;
    align-items: center;
    position: relative;
  }
  
  .btn-primary {
    background: linear-gradient(to right, #4262FF, #512CA2);
    color: white;
    font-weight: bold;
    padding: 5px 40px;
    cursor: pointer;
    border-radius: 25px;
    border: none;
    margin-left: 10px;
    font-size: 17px;
    margin-left: 20px;
  }
  
  .btn-primary:hover {
    background: linear-gradient(to right, #512CA2, #4262FF);
  }
  
  .btn-secondary {
    background: transparent;
    color: #4262FF;
    border: 2px solid #4262FF;
    padding: 5px 40px;
    margin-right: 10px;
    cursor: pointer;
    border-radius: 25px;
    font-weight: bold;
    font-size: 17px;
  }
  
  .btn-secondary:hover {
    background-color: #4262FF;
    color: white;
  }
  
  .exceeds-balance {
    color: red;
  }
  
  .table-section {
    background-color: transparent;
    overflow: hidden;
    margin-bottom: 20px;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: -20px;
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
  }
  
  th {
    background: linear-gradient(
      90deg,
      rgba(66, 98, 255, 0.06) 0%,
      rgba(63, 20, 153, 0.06) 100%
    );
    color: black;
    text-align: left;
    padding: 12px;
    font-weight: normal;
  }
  
  td {
    padding: 12px;
    border-bottom: 1px solid #ddd;
    background-color: white;
    vertical-align: middle;
  }
  