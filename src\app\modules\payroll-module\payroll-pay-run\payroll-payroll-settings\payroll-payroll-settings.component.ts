import { Component, OnInit } from '@angular/core';
import { GlAccountService } from '../../payroll-settings/services/gl-account.service';
import { GlAccount } from '../../../finance-module/gl-account/gl-account';
import { PayrollPayrollSettingsService } from './payroll-payroll-settings.service';
import { PayrollConfig } from '../../payroll-settings/payroll-setting';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-payroll-payroll-settings',
  templateUrl: './payroll-payroll-settings.component.html',
  styleUrls: ['./payroll-payroll-settings.component.css'],
})
export class PayrollPayrollSettingsComponent implements OnInit {
  glAccounts: GlAccount[] = [];
  filteredGlAccounts: any[] = [];
  payrollConfig: PayrollConfig = new PayrollConfig();
  payrollConfigs: PayrollConfig[] = [];
  isEditing: boolean = false;
  entityId: number = 0;

  constructor(
    private glAccountService: GlAccountService,
    private payrollpayrollSettingService: PayrollPayrollSettingsService
  ) {}

  ngOnInit(): void {
    this.getGlAccounts();
    this.entityId = +(localStorage.getItem('entityId') || '0');
    this.getPayrollConfig();
  }

  private getGlAccounts() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.glAccountService.getGlAccountList(entityId).subscribe((data) => {
      this.glAccounts = data;
      this.filteredGlAccounts = data; 
    });
  }

  onAddConfig(): void {
    if (this.payrollConfig) {
      const entityId = +(localStorage.getItem('entityId') + '');

      const payload = {
        ...this.payrollConfig,
        entityId,
      };

      this.payrollpayrollSettingService.savePayrollConfig(payload).subscribe(
        (_response: any) => {
          this.payrollConfig = _response;
          Swal.fire({
            title: 'Success!',
            text: 'Payroll settings added successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {
            this.isEditing = true;
            this.getPayrollConfig();
          });
        },
        (_error: any) => {
     Swal.fire({
            title: 'Error!',
            text:   'Unable to save Payroll settings. Please try again.', 
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
    } else {
      alert('Please fill in all required fields.');
    }
  }


    getPayrollConfig(): void {
    this.payrollpayrollSettingService.getPayrollConfigByEntity(this.entityId).subscribe({
      next: (data: PayrollConfig) => {
          this.payrollConfig = data;
          this.isEditing = true;
        
      },
      error: (err) => {
        console.error('Error fetching payRollConfig:', err);
      },
    });
  }



  updatePayrollConfig(): void {

    const payrollConfigId = this.payrollConfig.payrollConfigId; 
    const payload = {
      ...this.payrollConfig
    };
    this.payrollpayrollSettingService
        .updatePayrollConfig(payrollConfigId, payload)
        .subscribe(
            (_response: any) => {
                Swal.fire({
                    title: 'Success!',
                    text: 'Payroll Settings updated successfully!',
                    icon: 'success',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#28a745',
                }).then(() => {
                    this.isEditing = true;
                });
            },
            (_error: any) => {
                Swal.fire({
                    title: 'Error!',
                    text: 'Failed to update Payroll Settings. Please try again.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#be0032',
                });
            }
        );
}

onGlAccountChange() {
  const selectedAccount = this.glAccounts.find(account => account.ledgerAccountCode === this.payrollConfig.bankAccount);
  if (selectedAccount) {
    this.payrollConfig.bankAccountName = selectedAccount.ledgerAccountName;
  }
}

onGlAccountChangePaygLiability() {
  const selectedAccount = this.glAccounts.find(account => account.ledgerAccountCode === this.payrollConfig.paygLiabilityAccount);
  if (selectedAccount) {
    this.payrollConfig.paygLiabilityAccountName = selectedAccount.ledgerAccountName;
  }
}

onGlAccountChangeWagesExpens() {
  const selectedAccount = this.glAccounts.find(account => account.ledgerAccountCode === this.payrollConfig.wagesExpenseAccount);
  if (selectedAccount) {
    this.payrollConfig.wagesExpenseAccountName = selectedAccount.ledgerAccountName;
  }
}

onGlAccountChangeWagesPayable() {
  const selectedAccount = this.glAccounts.find(account => account.ledgerAccountCode === this.payrollConfig.wagesPayableAccount);
  if (selectedAccount) {
    this.payrollConfig.wagesPayableAccountName = selectedAccount.ledgerAccountName;
  }
}

onGlAccountChangeSuperannuationLiability() {
  const selectedAccount = this.glAccounts.find(account => account.ledgerAccountCode === this.payrollConfig.superannuationLiabilityAccount);
  if (selectedAccount) {
    this.payrollConfig.superannuationLiabilityAccountName = selectedAccount.ledgerAccountName;
  }
}

onGlAccountChangeSuperannuationExpense() {
  const selectedAccount = this.glAccounts.find(account => account.ledgerAccountCode === this.payrollConfig.superannuationExpenseAccount);
  if (selectedAccount) {
    this.payrollConfig.superannuationExpenseAccountName = selectedAccount.ledgerAccountName;
  }
}

filterGlAccounts(event: KeyboardEvent): void {
  const searchTerm = (event.key || '').toLowerCase();

  // Ensure input is a valid character
  if (searchTerm.length === 1 || event.key === 'Backspace') {
    this.filteredGlAccounts = this.glAccounts.filter((account) =>
      account.ledgerAccountName.toLowerCase().includes(searchTerm) ||
      account.ledgerAccountCode.toString().includes(searchTerm)
    );
  }
}


}
