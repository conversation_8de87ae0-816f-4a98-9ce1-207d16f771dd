import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { QuotationConstants, EmailType } from '../quotation.constants';
import { EmailData, EmailTemplateData, EmailTemplateVariables } from '../quotation.interfaces';
import { EmailTemplateService } from '../../settings/email-template.service';
import { EmailTemplate } from '../../settings/email-template';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { QuoteHead } from '../quotation';

@Injectable({
  providedIn: 'root'
})
export class QuotationEmailService {

  constructor(
    private emailTemplateService: EmailTemplateService,
    private businessPartnerService: BusinessPartnerService
  ) { }

  loadEmailTemplate(entityId: number): Observable<any[]> {
    return this.emailTemplateService.getEmailTemplateByEntityId(entityId);
  }

  findQuoteTemplate(templates: any[]): any | undefined {
    return templates.find(template => template.emailType === EmailType.QUOTE);
  }

  generateEmailContent(
    template: EmailTemplate, 
    selectedQuotes: QuoteHead[], 
    businessPartnerName?: string
  ): Promise<EmailTemplateData> {
    return new Promise((resolve, reject) => {
      const currentYear = new Date().getFullYear().toString();
      
      if (selectedQuotes.length === 1) {
        const selectedQuote = selectedQuotes[0];
        
        // Load business partner info if not provided
        if (!businessPartnerName) {
          this.businessPartnerService.getBusinessPartnerById(selectedQuote.businessPartnerId).subscribe(
            (bp) => {
              const bpName = bp?.bpName || QuotationConstants.DEFAULTS.CUSTOMER_NAME;
              const emailData = this.createSingleQuoteEmailData(template, selectedQuote, bpName, currentYear);
              resolve(emailData);
            },
            (error) => {
              reject(error);
            }
          );
        } else {
          const emailData = this.createSingleQuoteEmailData(template, selectedQuote, businessPartnerName, currentYear);
          resolve(emailData);
        }
      } else if (selectedQuotes.length > 1) {
        const emailData = this.createMultipleQuotesEmailData(template, currentYear);
        resolve(emailData);
      } else {
        resolve({
          subject: '',
          content: '',
          templateHtmlContent: '',
          recipientEmail: '',
          TempRecipientEmail: ''
        });
      }
    });
  }

  private createSingleQuoteEmailData(
    template: EmailTemplate, 
    quote: QuoteHead, 
    businessPartnerName: string, 
    currentYear: string
  ): EmailTemplateData {
    const subject = template.subject.replace(
      QuotationConstants.TEMPLATE_PLACEHOLDERS.QUOTE_NUMBER, 
      quote.quoteNumber
    );

    const templateHtmlContent = this.replaceTemplatePlaceholders(
      template.content,
      {
        businessPartnerName,
        quoteNumber: quote.quoteNumber || QuotationConstants.DEFAULTS.QUOTE_NUMBER,
        currentYear
      }
    );

    const parser = new DOMParser();
    const parsed = parser.parseFromString(templateHtmlContent, 'text/html');
    const content = parsed.body.textContent || "";

    return {
      subject,
      content,
      templateHtmlContent,
      recipientEmail: '',
      TempRecipientEmail: ''
    };
  }

  private createMultipleQuotesEmailData(template: EmailTemplate, currentYear: string): EmailTemplateData {
    const subject = QuotationConstants.DEFAULTS.MULTIPLE_QUOTES_SUBJECT;

    const templateHtmlContent = this.replaceTemplatePlaceholders(
      template.content,
      {
        businessPartnerName: QuotationConstants.DEFAULTS.CUSTOMER_NAME,
        quoteNumber: QuotationConstants.DEFAULTS.MULTIPLE_QUOTES_QUOTE_NUMBER,
        currentYear
      }
    );

    const parser = new DOMParser();
    const parsed = parser.parseFromString(templateHtmlContent, 'text/html');
    const content = parsed.body.textContent || "";

    return {
      subject,
      content,
      templateHtmlContent,
      recipientEmail: '',
      TempRecipientEmail: ''
    };
  }

  private replaceTemplatePlaceholders(content: string, variables: EmailTemplateVariables): string {
    return content
      .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.BUSINESS_PARTNER_NAME, variables.businessPartnerName)
      .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.QUOTE_NUMBER, variables.quoteNumber)
      .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.CURRENT_YEAR, variables.currentYear);
  }

  loadRecipientEmail(businessPartnerId: number): Observable<any> {
    return this.businessPartnerService.getBusinessPartnerById(businessPartnerId);
  }

  prepareEmailData(
    recipientEmail: string,
    subject: string,
    content: string,
    templateHtmlContent: string,
    quotes: QuoteHead[]
  ): EmailData {
    // Replace content in HTML template
    const finalContent = templateHtmlContent.replace(
      /<body[^>]*>.*<\/body>/is, 
      `<body><p>${content.replace(/\n/g, '</p><p>')}</p></body>`
    );

    return {
      TempRecipientEmail: recipientEmail,
      subject: subject,
      content: finalContent,
      quotes: quotes
    };
  }

  getEntityId(): number {
    const entityIdString = localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_ID);
    return entityIdString ? parseInt(entityIdString, 10) : 0;
  }

  getEntityUuid(): string | null {
    return localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_UUID);
  }
}
