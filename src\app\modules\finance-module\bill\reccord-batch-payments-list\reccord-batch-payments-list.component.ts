import { Component, ViewChild } from '@angular/core';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { PaymentVoucherHeader } from '../bill';
import { BillService } from '../bill.service';
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import Swal from 'sweetalert2';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { DateAdapter } from '@angular/material/core';
@Component({
  selector: 'app-reccord-batch-payments-list',
  templateUrl: './reccord-batch-payments-list.component.html',
  styleUrls: ['./reccord-batch-payments-list.component.css']
})
export class ReccordBatchPaymentsListComponent {

  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  
  [x: string]: any;
  paymentVoucherHeaders: PaymentVoucherHeader[] = [];
  searchTerm: string = ''; // Store the search term
  filteredPaymentVoucherHeaders: PaymentVoucherHeader[] = [];
  selectedPaymentVoucherHeaders: Set<PaymentVoucherHeader> = new Set<PaymentVoucherHeader>();
  paymentVoucherHeaderList: PaymentVoucherHeader[] = [];
  isAllSelected: boolean = false;
  creditNoteData: PaymentVoucherHeader = new PaymentVoucherHeader();
  activeTab = 'all';

  startDate: string | null = null; // Bind to the start date input
  endDate: string | null = null;   // Bind to the end date input



  constructor(
    private billService: BillService, 
    private router: Router,
    public sanitizer: DomSanitizer,
    private dateAdapter: DateAdapter<Date>,
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }

  ngOnInit(): void {
    this.fetchPaymentVoucherHeaders();
  }

   //dropdown
   isDropdownOpen = false;

   toggleDropdown(): void {
     this.isDropdownOpen = !this.isDropdownOpen;
   }
  
  toggleSelection(paymentVoucherHeader: PaymentVoucherHeader, event: any): void {
    if (event.target.checked) {
      this.selectedPaymentVoucherHeaders.add(paymentVoucherHeader);
    } else {
      this.selectedPaymentVoucherHeaders.delete(paymentVoucherHeader);
    }
  }

  fetchPaymentVoucherHeaders() {

    const entityId = +((localStorage.getItem('entityId')) + "");

    this.billService.getAllPaymentVoucherHeaderList(entityId).subscribe(
      (data: PaymentVoucherHeader[]) => {
        this.paymentVoucherHeaders = data;
        this.filteredPaymentVoucherHeaders = this.paymentVoucherHeaders;
      },
      (error) => {

      }
    );
  }

  onSearchChange() {
    this.filterPaymentVoucherHeaders(); // Call filter function on input change
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.filterPaymentVoucherHeaders(); // Filter invoices when tab changes
  }

 
  filterPaymentVoucherHeaders(): void {
    if (!this.searchTerm && !this.startDate && !this.endDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Search Criteria',
        text: 'Please provide at least one search criterion: Voucher Number, Payee Name, Start Date, or End Date.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    let filtered = this.paymentVoucherHeaders;

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(paymentVoucherHeader =>
        paymentVoucherHeader.voucherNumber.toString().toLowerCase().includes(searchTermLower) ||
        paymentVoucherHeader.payeeName.toLowerCase().includes(searchTermLower)
      );
    }

    // Filter by date range
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(paymentVoucherHeader => new Date(paymentVoucherHeader.date) >= startDate);
    }
  
    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(paymentVoucherHeader => new Date(paymentVoucherHeader.date) <= endDate);
    }
  
    this.filteredPaymentVoucherHeaders = filtered;
  }

  // Reset filters
  resetFilters() {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.filteredPaymentVoucherHeaders = this.paymentVoucherHeaders;
  }

deletePaymentVoucherHeaders(paymentVoucherHeaderId: number): void {
  Swal.fire({
    title: 'Are you sure?',
    text: 'Do you really want to delete this Paymenet?',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Yes, delete it!',
    cancelButtonText: 'No, keep it',
    confirmButtonColor: '#ff7e5f',
    cancelButtonColor: '#be0032',
  }).then((result) => {
    if (result.isConfirmed) {
  

      this.billService.deletePaymentVoucherHeaders(paymentVoucherHeaderId)
        .subscribe(
          () => {

            Swal.fire({
              title: 'Deleted!',
              text: 'Payment has been deleted.',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            }).then(() => {
              // Optionally update the UI or reload data
              this.fetchPaymentVoucherHeaders();
            });
          },
          (error) => {
            console.error('Failed to delete Payment :', error);

            Swal.fire({
              title: 'Error!',
              text: 'Failed to delete Payment.',
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Failed to delete Payment.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound() 
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
            });
          }
        );
    }
  });
}
  
createPaymentVoucherHeader() {
  Swal.fire({
    title: 'Please Select Bills',
    text: 'You need to select one or more bills before proceeding.',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'OK',
    cancelButtonText: 'Cancel'
  }).then((result) => {
    if (result.isConfirmed) {
      // Navigate to the payable bill list after confirmation
      this.router.navigate(['/payable-bill-list']);
    }
  });}

selectAll(event: any) {
  const isChecked = event.target.checked;
  this.paymentVoucherHeaders.forEach(paymentVoucherHeader => paymentVoucherHeader.selected = isChecked);
}
playLoadingSound() {
  let audio = new Audio();
  audio.src = "../assets/google_chat.mp3";
  audio.load();
  audio.play();
}

stopLoadingSound(): void {
  if (this.audio) {
    this.audio.pause();
    this.audio.currentTime = 0; 
  }
}
exportToExcel(): void {
  // Prepare data for export
  const exportData = this.filteredPaymentVoucherHeaders.map(payment => ({
    'Voucher Number': payment.voucherNumber,
    'Payee Name': payment.payeeName,
    'Date': new Date(payment.date).toLocaleDateString('en-GB'), // Format date as dd-MM-yyyy
    'Balance': payment.balance,
    'Net Amount': payment.netAmount
  }));

  // Generate Excel worksheet and workbook
  const worksheet = XLSX.utils.json_to_sheet(exportData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Payment Vouchers');

  // Create a buffer and save the file
  const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

  // Use a timestamp for a unique filename
  const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
  this.saveAsExcelFile(excelBuffer, `Payment_Vouchers_${timestamp}`);
}

private saveAsExcelFile(buffer: any, fileName: string): void {
  const data: Blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
  });
  const file = new File([data], `${fileName}.xlsx`, { type: data.type });
  saveAs(file);
}

cancelSelectedPaymentVouchers(): void {
  if (this.selectedPaymentVoucherHeaders.size > 0) {
    const ids = Array.from(this.selectedPaymentVoucherHeaders).map(
      (voucher) => voucher.paymentVoucherHeaderId
    );
    console.log('Selected Payment Voucher IDs:', ids); // Debug log

    this.billService.cancelPaymentVouchers(ids).subscribe(
      () => {
        Swal.fire({
          title: 'Success!',
          text: 'Payment vouchers canceled successfully.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.fetchPaymentVoucherHeaders();
          this.selectedPaymentVoucherHeaders.clear();
          this.isAllSelected = false;
        });
      },
      (error) => {
        console.error('Error canceling payment vouchers:', error); // Log error
        Swal.fire({
          title: 'Error!',
          text: 'Failed to cancel payment vouchers.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  } else {
    Swal.fire({
      title: 'Warning!',
      text: 'No payment vouchers selected for cancellation.',
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f',
    });
  }
}

}
