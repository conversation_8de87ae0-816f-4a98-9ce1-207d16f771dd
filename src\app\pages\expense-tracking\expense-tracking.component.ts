import { Component } from '@angular/core';

@Component({
  selector: 'app-expense-tracking',
  templateUrl: './expense-tracking.component.html',
  styleUrls: ['./expense-tracking.component.css'],
})
export class ExpenseTrackingComponent {
  features = [
    {
      icon: '✅',
      title: 'Automatic Receipt Scanning',
      description:
        'Simply upload or snap a photo of a receipt, and LEDGER CHIMP will log it for you.',
    },
    {
      icon: '📥',
      title: 'Email Invoice Capture',
      description:
        'No more manual entries! LEDGER CHIMP pulls invoices from emails, ensuring your records stay up to date effortlessly.',
    },
    {
      icon: '📊',
      title: 'Stay Organized & In Control',
      description:
        'Easily track, manage, and categorize expenses in one place—saving you time and keeping your finances in check.',
    },
  ];
}
