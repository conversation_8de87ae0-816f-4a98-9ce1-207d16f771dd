export class CoaTemplateMaster {
  id: number = 0;
  templateId: number = 0;
  templateName: string = '';
}

export class CoaTemplateHeader {
  id: number = 0;
  coaTemplateMasterId: CoaTemplateMaster = new CoaTemplateMaster();
  headerId: number = 0;
  series: number = 0;
  headerType: string = '';
}

export class CoaTemplateDetail {
  id: number = 0;
  coaTemplateHeaderId: CoaTemplateHeader = new CoaTemplateHeader();
  code: number = 0;
  accountName: string = '';
  defaultTaxCode: string = '';
}

export class CoaTemplate {
  templateId: number = 0;
  entityId: number = 0;
  templateName: string = '';
  description: string = '';
}

export class CoaHeaders {
  coaHeaderId: number = 0;
  templateId: number = 0;
  pAccount: string = '';
  accountHeaderName: string = '';
  accountHeaderDescription: string = '';
  accountHeaderType: string = '';
  series: string = '';
  entityId: number = 0;
}

export class CoaLedgerAccount {
  coaLedgerAccountId: number = 0;
  coaHeaderId: CoaHeaders = new CoaHeaders();
  ledgerAccountName: string = '';
  ledgerAccountCode: string = '';
  ledgerAccountDescription: string = '';
  taxAccount: string = '';
  status: string = '';
  defaultTaxCode: string = '';
  entityId: number = 0;
}
