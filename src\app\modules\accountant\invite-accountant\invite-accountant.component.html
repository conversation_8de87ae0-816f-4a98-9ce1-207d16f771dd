<app-admin-navigation></app-admin-navigation>

<div class="form-container">
  <div class="invite-accountant-form">
    <h2>Add New Accountant</h2>
    <form
      #inviteUserForm="ngForm"
      (ngSubmit)="inviteUserForm.form.valid && inviteAccountant()"
      novalidate="feedback-form"
      (keydown)="preventSubmit($event)"
    >
      <div class="user-name">
        <div class="form-group">
          <label for="firstName">First Name</label>
          <input
            type="text"
            id="firstName"
            name="firstName"
            [(ngModel)]="firstName"
            required
            class="form-control"
          />
          <div
            *ngIf="
              inviteUserForm.submitted &&
              inviteUserForm.controls['firstName'].invalid
            "
            class="text-danger"
          >
            <div
              *ngIf="inviteUserForm.controls['firstName'].errors?.['required']"
            >
              First Name is required.
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="lastName">Last Name</label>
          <input
            type="text"
            id="lastName"
            name="lastName"
            [(ngModel)]="lastName"
            required
            class="form-control"
          />
          <div
            *ngIf="
              inviteUserForm.submitted &&
              inviteUserForm.controls['lastName'].invalid
            "
            class="text-danger"
          >
            <div
              *ngIf="inviteUserForm.controls['lastName'].errors?.['required']"
            >
              Last Name is required.
            </div>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          name="email"
          [(ngModel)]="email"
          required
           email
          class="form-control"
        />
        <div
          *ngIf="
            inviteUserForm.submitted && inviteUserForm.controls['email'].invalid
          "
          class="text-danger"
        >
          <div *ngIf="inviteUserForm.controls['email'].errors?.['required']">
            Email is required.
          </div>
           <div *ngIf="inviteUserForm.controls['email']?.errors?.['email']">
           Invalid email format.
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="cancel" (click)="navigateToDashboard()">
          Cancel
        </button>
        <button type="submit" class="send-invite">Invite</button>
      </div>
    </form>
  </div>
</div>
