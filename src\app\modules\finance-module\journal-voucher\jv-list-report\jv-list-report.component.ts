import { Component, ElementRef, ViewChild } from '@angular/core';
import { JournalVoucherService } from '../journal-voucher.service';
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { HttpErrorResponse } from '@angular/common/http';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import Swal from 'sweetalert2';
import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import {GlPostingHead} from  '../journal-voucher'  ; 
@Component({
  selector: 'app-jv-list-report',
  templateUrl: './jv-list-report.component.html',
  styleUrls: ['./jv-list-report.component.css']
})
export class JvListReportComponent {
   @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
    @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
    private audio!: HTMLAudioElement;


  selectedReportType: any;
  fromDate: any;
  toDate: any;
  customers: BusinessPartner[] = [];
  journalVoucherData: GlPostingHead = new GlPostingHead();
  isLoading = false;
  getAllCustomers = false;  // New property
  status: string = '%';

 @ViewChild('journalVoucherPreviewFrame') journalVoucherPreviewFrame!: ElementRef;
quotationData: any;


constructor(
    private journalVoucherService: JournalVoucherService,
    private router: Router,
    public sanitizer: DomSanitizer,
    private businessPartnerService: BusinessPartnerService
  ) { }

 ngOnInit() {
    this.loadCustomers();
  }


previewJournalVouchers(fromDate: string, toDate: string, status: string, businessPartnerId: string) {

    // Validation checks for required fields
    if (!fromDate || !toDate) {
      Swal.fire({
        title: 'Warning!',
        text: 'Please select Date Range',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f'
      });
      return;
    }
    this.isLoading = true;
    const entityId = +((localStorage.getItem('entityId')) + "");
    // Adjust the status filter and pass getAllCustomers flag
    const statusFilter = status ? status : 'all';
    this.journalVoucherService.getJournalVoucherListReport(fromDate, toDate, status, entityId, businessPartnerId).subscribe(
      data => {
        const base64String = data.response;

        if (base64String) {
          this.loadPdfIntoIframe(base64String);
        } else {
          this.isLoading = false;
          alert('No journal voucher data for preview.');
        }
      },
      error => {
        this.isLoading = false;
        alert('Error loading journal voucher preview.');
      }
    );
  }


  private loadPdfIntoIframe(base64String: string) {
    let dataLoaded = false; // Flag to track if data was loaded successfully

    // Check if base64String is valid
    if (base64String && base64String.trim().length >= 50) { // Adjust the length check as needed
      const pdfData = 'data:application/pdf;base64,' + base64String;
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
      const iframe = this.journalVoucherPreviewFrame.nativeElement;

      iframe.onload = () => {
        this.isLoading = false;
        dataLoaded = true; // Set flag to true when data loads
      };

      iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
    }

    // Check load status after a short delay
    setTimeout(() => {
      if (!dataLoaded) {
        this.isLoading = false;
        Swal.fire({
          title: 'No Data',
          text: 'No journal voucher data for preview.',
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff'
        });
      }
    }, 2000);
  }



loadCustomers() {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.businessPartnerService.getSupplierListByEntity(entityId).subscribe(
      (customers: BusinessPartner[]) => {
        this.customers = customers;
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching customers', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load customers.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Failed to load customers.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
  }

  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(cust => cust.businessPartnerId === +selectedCustomerId);

    // Set the reference field
    this.quotationData.customerName = selectedCustomer?.bpName || '';
  }

  // Method to toggle all customers' data
  toggleAllCustomers() {
    this.getAllCustomers = !this.getAllCustomers;
    if (this.getAllCustomers) {
      this.quotationData.businessPartnerId = '';  // Clear selected customer when showing all
    }
  }
    
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
    
}




