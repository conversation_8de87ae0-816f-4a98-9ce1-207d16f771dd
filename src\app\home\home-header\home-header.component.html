<nav class="navbar navbar-expand-lg flex-column p-0">
  <!-- Top Section -->
  <div class="top-nav w-100">
    <div class="container col d-md-flex justify-content-center align-items-center">
      <div class="buy-btn-row">
        <p class="offer-text" style="font-weight: bold; white-space: nowrap;">
          Up to 90% off for 4 months 
        </p>
        <button 
          class="btn btn-light btn-sm my-auto" 
          style="white-space: nowrap; overflow-x: auto;"
          (click)="scrollToPricing()"
        >
          Buy Now
        </button>
      </div>
      <!-- <div class="ps-4 d-flex justify-content-between" style="margin-right: 10%;">
        <div class="form-check form-check-inline">
          <input class="form-check-input" type="radio" name="options" id="reports" />
          <label class="form-check-label" for="reports">Reports</label>
        </div>
        <div class="form-check form-check-inline">
          <input class="form-check-input" type="radio" name="options" id="invoices" />
          <label class="form-check-label" for="invoices">Invoices</label>
        </div>
        <div class="form-check form-check-inline">
          <input class="form-check-input" type="radio" name="options" id="expenses" />
          <label class="form-check-label" for="expenses">Expenses</label>
        </div>
      </div> -->
    </div>

  </div>

  <!-- Bottom Section -->
  <div class="bottom-nav w-100">
    <div class="container">
      <div class="d-flex justify-content-between align-items-center">
        <a class="navbar-brand" href="#">
          <img src="../../assets/images/Ledger_Chimp.png" alt="Chimp Icon" class="Ledger_Chimp" />
        </a>
        <div class="d-flex align-items-center">
          <!-- Navbar Links, visible on all screen sizes -->
          <ul class="navbar-nav" [ngClass]="{'d-none': isMenuHidden, 'd-flex': !isMenuHidden}">
            <li class="nav-item">
              <a class="nav-link" routerLink="/home" routerLinkActive="active"
                [routerLinkActiveOptions]="{ exact: true }">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" routerLink="/features" routerLinkActive="active">Features</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" routerLink="/contact-us" routerLinkActive="active">Contact Us</a>
            </li>
            <!-- <li class="nav-item">
              <a class="nav-link" routerLink="/faqs" routerLinkActive="active">FAQs</a>
            </li> -->
          </ul>
          <!-- Register and Login Buttons -->
          <div class="btnSet">
            <div class="loginBtn">
          <button class="btn-register" (click)="scrollToPricing()">Register</button>
        </div>
          <button class="btn-login" (click)="navigateToLogin()">Login</button>
  
          <!-- Toggle Button, visible only on mobile -->
          <button class="navbar-toggler" (click)="toggleMenu()">
            <span class="navbar-toggler-icon"></span>
          </button>
        </div>
        </div>
      </div>
    </div>
  </div>


</nav>
