import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ExpenceClaimsService } from '../expence-claims.service';
import { OtherExpensesHead, OtherExpensesDetail, CoaLedgerAccount } from '../expence-claims';
import Swal from 'sweetalert2';
import { NgForm } from '@angular/forms';

@Component({
  selector: 'app-edit-expence',
  templateUrl: './edit-expence.component.html',
  styleUrls: ['./edit-expence.component.css']
})
export class EditExpenceComponent implements OnInit {
  @ViewChild('expenseForm') expenseForm!: NgForm; // Form reference

  otherExpensesHeadData: OtherExpensesHead = new OtherExpensesHead();
  glAccounts: CoaLedgerAccount[] = []; // Stores GL Account list
  businessEntity: any = {}; // Stores entity details like tax and currency
  isLoading: boolean = true;
  id: number = 0;
  taxApplicable: boolean = false;

  constructor(
    private expenseService: ExpenceClaimsService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  async ngOnInit(): Promise<void> {
    this.id = +this.route.snapshot.paramMap.get('id')!;
    
    if (this.id) {
      await this.getExpenseHeadById();
      await this.getOtherExpensesDetailByHeadId();
    }
    
    this.fetchGLAccounts();
    this.loadBusinessEntity();
  }

  // Fetch Expense Head
  getExpenseHeadById(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.expenseService.getExpensesHeadById(this.id).subscribe(
        (data) => {
          this.otherExpensesHeadData = data;
          if (!this.otherExpensesHeadData.otherExpensesDetails) {
            this.otherExpensesHeadData.otherExpensesDetails = [];
          }
          resolve();
        },
        (error) => {
          console.error('Error fetching expense head:', error);
          Swal.fire('Error!', 'Failed to fetch expense data.', 'error');
          reject();
        }
      );
    });
  }

  // Fetch Expense Details
  getOtherExpensesDetailByHeadId(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.expenseService.getOtherExpensesDetailByHeadId(this.id).subscribe(
        (data) => {
          this.otherExpensesHeadData.otherExpensesDetails = data;
          console.log('Fetched Expense Details:', data);
          this.calculateSubTotal(); // Update totals after loading data
          resolve();
        },
        (error) => {
          console.error('Error fetching expense details:', error);
          reject();
        }
      );
    });
  }

  // Fetch GL Accounts
  fetchGLAccounts() {
    this.expenseService.getAllCoaLedgerAccounts().subscribe(
      (accounts) => {
        this.glAccounts = accounts;
      },
      (error) => {
        console.error('Error fetching GL accounts:', error);
      }
    );
  }

  // Load Business Entity Details
  loadBusinessEntity() {
    const entityId = +(localStorage.getItem('entityId') || '0');
    if (entityId) {
      this.expenseService.getBusinessEntityById(entityId).subscribe(
        (entity) => {
          this.businessEntity = entity;
          this.taxApplicable = entity.taxApplicability === 'yes';
        },
        (error) => {
          console.error('Error fetching business entity:', error);
        }
      );
    }
  }

  // // Remove an Expense Item from the List
  // removeItem(index: number): void {
  //   this.otherExpensesHeadData.otherExpensesDetails.splice(index, 1);
  //   this.calculateSubTotal();
  // }

  // Calculate subtotal and apply tax
  calculateSubTotal() {
    let subTotal = 0;
    let totalTax = 0;

    this.otherExpensesHeadData.otherExpensesDetails.forEach((detail) => {
      detail.total_amount = detail.amount;

      if (this.taxApplicable && detail.taxApplicability) {
        const taxRate = this.businessEntity?.countryId?.defaultTaxRate || 0;
        detail.tax = detail.amount * (taxRate / 100);
        detail.total_amount += detail.tax;
      } else {
        detail.tax = 0;
      }

      subTotal += detail.amount;
      totalTax += detail.tax;
    });

    this.otherExpensesHeadData.grossAmount = subTotal;
    this.otherExpensesHeadData.totalGst = totalTax;
    this.otherExpensesHeadData.netAmount = subTotal + totalTax;
  }

  // removeItem(index: number) {
  //   this.otherExpensesHeadData.otherExpensesDetails.splice(index, 1); // Remove the item
  //   this.calculateSubTotal(); // Recalculate totals
  // }

  // Update amount dynamically
  updateAmount(index: number) {
    this.calculateSubTotal();
  }

  // Handle tax applicability toggle
  onTaxApplicableChange(index: number): void {
    this.calculateSubTotal();
  }

  // Prevent Enter key from submitting the form
  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  // Validate Form Before Submission
  validateForm(): boolean {
    if (!this.otherExpensesHeadData.otherExpensesDetails.length) {
      Swal.fire('Error', 'At least one expense item is required.', 'error');
      return false;
    }

    if (this.otherExpensesHeadData.otherExpensesDetails.some(d => d.amount === 0)) {
      Swal.fire('Warning', 'Some items have zero amount. Do you want to continue?', 'warning');
      return false;
    }

    return true;
  }

  // Submit Updated Expense
  onUpdate() {
    if (!this.validateForm()) return;

    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to update this expense?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes, Update',
      cancelButtonText: 'Cancel',
    }).then((result) => {
      if (result.isConfirmed) {
        this.expenseService.updateOtherExpensesHead(
          this.id,
          this.otherExpensesHeadData
        ).subscribe(
          () => {
            Swal.fire('Success!', 'Expense updated successfully.', 'success')
              .then(() => this.router.navigate(['/expence-claims-list']));
          },
          (error) => {
            Swal.fire('Error!', 'Failed to update expense.', 'error');
          }
        );
      }
    });
  }

  // Cancel and Go Back
  onCancel() {
    this.router.navigate(['/expense-claims-list']);
  }

  addNewRow() {
    this.otherExpensesHeadData.otherExpensesDetails.push({
      expensesNumber: this.otherExpensesHeadData.expensesNumber,
      expenseType:'',
        description: '',
        amount: 0,
        total_amount:0,
        gl_account:'',
        tax: 0,
        coaLedgerAccountId: 0,
        ledgerAccountName: ''
    });
  }
}
