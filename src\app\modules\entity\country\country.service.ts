import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Country } from './country';

@Injectable({
  providedIn: 'root',
})
export class CountryService {
  private readonly baseURL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

   const authToken = this.getAuthToken();

      if (authToken) {
        headers = headers.set('Authorization', 'Bearer ' + authToken);
      } else {
        // Add secure API key for protected-but-public endpoints
        headers = headers.set('X-API-KEY', environment.secureApiKey);
      }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }
  createCountry(countryData: any): Observable<any> {
    return this.request('POST', '/saveCountry', countryData);
  } 

  getAllCountries(): Observable<Country[]> {
    return this.request('GET', '/countryList', null);
  }

  getCountryById(id: number): Observable<Country>{
    return this.request('GET', `/getCountryById/${id}`,{});
  }
  deleteCountry(id: number): Observable<void> {
    return this.request('DELETE', `/deleteCountryById/${id}`, null);
  }  
  updateCountry(id: number, country: Country): Observable<void> {
    return this.request('PUT', `/updateCountryById/${id}`, country); 
  }

  getCountryByName(countryName: string): Observable<Country> {
    return this.request('GET', '/getCountryByName', {}, { countryName: countryName });
  }
  
}
