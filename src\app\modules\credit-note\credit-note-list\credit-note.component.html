<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
    <div class="actions">
        <h1>Credit Notes</h1>

    </div>
    <div class="search-create">
        <button class="btn btn-outline-primary" (click)="createCreditNote()">
          Create Credit Note
      </button>
      <button (click)="exportToExcel()" class="export-btn">Export to Excel</button>

    </div>
    <div>

    </div>



    <div class="Card">
        <!-- Filter and Search Section -->
        <div class="row1">
            <!-- Input for number, reference -->
            <div class="row1_col1">
                <label for="search-input">Credit Note # or Customer Name</label>
                <div class="input-container">
                    <input type="text" class="search-input" title="Search by credit note number or customer name" id="search-input" [(ngModel)]="searchTerm" />
                    <i class="bi bi-search"></i>
                </div>
            </div>

            <div class="row1_col3">
                <label for="StartDate">Start Date</label>
                <input type="date" class="date-picker" id="StartDate" [(ngModel)]="startDate" />
            </div>

            <div class="row1_col4">
                <label for="EndDate">End Date</label>
                <input type="date" class="date-picker" id="EndDate" [(ngModel)]="endDate" />
            </div>
        </div>

        <div class="row2">
            <div class="row2_col3">
                <button type="button" class="secondary-button" (click)="resetFilters()">Reset</button>
            </div>
            <div class="row2_col1">
                <button type="button" class="primary-button" (click)="filterCreditNotes()">Search</button>
            </div>
        </div>
    </div>

    <div class="table-responsive">
    <table>
        <thead>
            <tr class="table-head">
                <th scope="col" class="valueCheckbox"><input type="checkbox" (change)="selectAll($event)" /></th>
                <th scope="col" class="valuehead">Credit Note Number</th>
                <th scope="col" class="valuehead">Invoice Number(s)</th>
                <th scope="col" class="valuehead">Customer Name</th>
                <th scope="col" class="valuehead">Document Date</th>
                <th style="text-align: right;" scope="col" class="valuehead">Credit Applied</th>
                <th style="text-align: right;" scope="col" class="valuehead">Credit Applied + GST</th>
                <th style="text-align: center;" scope="col" class="valuehead">Actions</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let creditNote of filteredCreditNotes; let i = index">
                <td class="valueCheckbox"><input type="checkbox" [(ngModel)]="creditNote.selected" /></td>
                <td class="value">{{ creditNote.creditNoteNumber }}</td>
                <td class="value">{{ creditNote.invoiceNumber }}</td>
                <td class="value">{{ creditNote.customerName }}</td>
                <td class="value">{{ creditNote.documentDate | date:'dd MMM yyyy' }}</td>
                <td style="text-align: right;" class="value">{{ creditNote.totalCreditAmount | currency}}</td>
                <td style="text-align: right;" class="value">{{ creditNote.totalCreditAmount| currency}}</td>
                <td style="text-align: center;" class="value">
                    <button (click)="deleteCreditNote(creditNote.creditNoteId)"     [disabled]="creditNote.isLocked" class="btn btn-danger btn-sm" style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" data-bs-toggle="tooltip" title="Delete Credit Note">
                        <i class="ri-delete-bin-line" style="color: #FF0000;"   [style.color]=" creditNote.isLocked
                             ? '#aaa'
                             : '#ff0000'
                             "></i>
                    </button>
                </td>
            </tr>
        </tbody>
    </table>

    </div>
</div>
