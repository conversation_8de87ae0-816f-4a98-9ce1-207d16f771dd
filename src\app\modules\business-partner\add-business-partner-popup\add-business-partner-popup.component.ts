import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { BusinessPartner, BusinessPartnerType } from '../business-partner';
import { BusinessPartnerService } from '../business-partner.service';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
@Component({
  selector: 'app-add-business-partner-popup',
  templateUrl: './add-business-partner-popup.component.html',
  styleUrls: ['./add-business-partner-popup.component.css']
})
export class AddBusinessPartnerPopupComponent {
 @Input() entityId!: number;
  @Input() showUpdateLink: boolean = true;

  @Output() partnerAdded = new EventEmitter<void>();
  @Output() partnerSaved = new EventEmitter<BusinessPartner>();

  @ViewChild('cuspop') cuspop!: NgForm;
  @ViewChild('closeCustomerPopUp') closeCustomerPopUp: any;

  businessPartner = new BusinessPartner();
  businessPartnerType: BusinessPartnerType[] = [];
  suggestedAddresses: any[] = [];
  suggestedDeliveryAddresses: any[] = [];
  isDeliveryAddressSynced: boolean = false;
  constructor(
    private partnerService: BusinessPartnerService,
    private swalAlertsService: SwalAlertsService,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.loadBusinessPartnerTypes();
  }

  loadBusinessPartnerTypes() {
    this.partnerService.getBusinessPartnerTypesList().subscribe(
      (types: BusinessPartnerType[]) => {
        this.businessPartnerType = types;
        const customerType = types.find(t => t.businessPartnerType.toLowerCase() === 'customer');
        if (customerType) {
          this.businessPartner.businessPartnerTypeId.businessPartnerTypeId = customerType.businessPartnerTypeId;
        }
      },
      () => this.swalAlertsService.showErrorDialog('Failed to load Business Partner Types')
    );
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  onSubmitCustomerForm() {
    this.businessPartner.entityId.entityId = this.entityId;

    this.partnerService.saveBusinessPartner(this.businessPartner).subscribe(
      () => {
        this.swalAlertsService.showSuccessDialog('Success!', 'Customer added successfully.', () => {
          this.partnerAdded.emit(); // Reload list
          this.partnerSaved.emit(this.businessPartner); // Pass selected partner
          this.cuspop.resetForm();
          this.closeCustomerPopUp.nativeElement.click();
        });
      },
      () => this.swalAlertsService.showErrorDialog('Error saving customer')
    );
  }


    checkBusinessAddress(): void {
      const query = this.businessPartner.businessAddress
        ? this.businessPartner.businessAddress.trim()
        : '';
      const apiUrl = `${environment.addressfinderBaseUrl}?key=${environment.addressFinderApiKey}&q=${encodeURIComponent(
        query
      )}&format=json&source=gnaf%2Cpaf`;
  
      if (!query) {
        this.suggestedAddresses = [];
        return;
      }
  
      this.http.get(apiUrl).subscribe(
        (response: any) => {
          this.suggestedAddresses = response?.completions || [];
        },
        (error) => {
          console.error('Error fetching business address suggestions:', error);
        }
      );
    }
  
    selectAddress(address: any): void {
      this.businessPartner.businessAddress = address.full_address;
      this.suggestedAddresses = [];
    }
  
    checkDeliveryAddress(): void {
      const query = this.businessPartner.deliveryAddress
        ? this.businessPartner.deliveryAddress.trim()
        : '';
      const apiUrl = `${environment.addressfinderBaseUrl}?key=${environment.addressFinderApiKey}&q=${encodeURIComponent(
        query
      )}&format=json&source=gnaf%2Cpaf`;
  
      if (!query) {
        this.suggestedDeliveryAddresses = [];
        return;
      }
  
      this.http.get(apiUrl).subscribe(
        (response: any) => {
          this.suggestedDeliveryAddresses = response?.completions || [];
        },
        (error) => {
          console.error('Error fetching delivery address suggestions:', error);
        }
      );
    }
  
    selectDeliveryAddress(address: any): void {
      this.businessPartner.deliveryAddress = address.full_address;
      this.suggestedDeliveryAddresses = [];
    }
  
    syncDeliveryAddress(event: Event): void {
      const checkbox = event.target as HTMLInputElement;
      this.isDeliveryAddressSynced = checkbox.checked;
      if (this.isDeliveryAddressSynced) {
        this.businessPartner.deliveryAddress = this.businessPartner.businessAddress;
      }
    }
}
