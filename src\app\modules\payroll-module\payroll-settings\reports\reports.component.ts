import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router, NavigationExtras } from '@angular/router';
import { PayPeriod } from '../payroll-setting';
import { PayPeriodService } from './../services/pay-period.service';
import { EmployeeService } from '../services/employee.service';
import { PayRunService } from '../../payroll-settings/services/pay-run.service';
import { DomSanitizer } from '@angular/platform-browser';
import Swal from 'sweetalert2';

// Define Employee Interface
interface Employee {
  firstName: string;
  lastName: string;
  email: string;
}

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.css'],
})
export class ReportsComponent implements OnInit {
  @ViewChild('reportPreviewFrame') reportPreviewFrame!: ElementRef; // Employee Details Report
  @ViewChild('payRunSummaryFrame') payRunSummaryFrame!: ElementRef; // PayRun Summary Report

  openPayPeriods: PayPeriod[] = [];
  selectedPayPeriod: PayPeriod | 'ALL' | null = null;
  employeesList: Employee[] = [];

  userId: number = +(localStorage.getItem('userid') || '0');
  entityId: number = +(localStorage.getItem('entityId') || '0');
  isLoading: boolean = false;
  isLoadingSummary: boolean = false;

  fromDate: Date | null = null;
  toDate: Date | null = null;
  filteredPayRuns: PayPeriod[] = [];

  constructor(
    private payPeriodService: PayPeriodService,
    private employeeService: EmployeeService,
    private sanitizer: DomSanitizer,
    private router: Router,
    private payRunService: PayRunService
  ) {}

  ngOnInit(): void {
    this.getpayPeriodsList();
  }

  navigateToPayrollPayRun(): void {
    window.location.assign('/payroll-pay-run');
  }

  private getpayPeriodsList(): void {
    this.payPeriodService.getAllPayPeriods(this.entityId).subscribe({
      next: (data) => {
        this.openPayPeriods = data;
      },
      error: (err) => {
        console.error('Error fetching pay periods:', err);
      },
    });
  }

  onPayPeriodChange(selectedPayPeriod: PayPeriod | 'ALL' | null): void {
    console.log(`Selected Pay Period: ${selectedPayPeriod}`);
  }

  generateReport(): void {
    if (!this.selectedPayPeriod) {
      Swal.fire({
        title: 'Warning!',
        text: 'Select a Payroll Calendar',
        icon: 'warning',
      });
      return;
    }

    this.isLoading = true;
    const payCalendarId =
      this.selectedPayPeriod === 'ALL'
        ? 'ALL'
        : this.selectedPayPeriod.payCalendar.payCalendarId;

    this.employeeService
      .generateEmployeeDetailReport(payCalendarId, this.entityId)
      .subscribe({
        next: (data: any) => {
          const base64String = data.response;

          if (base64String) {
            this.loadPdfIntoIframe(base64String);
          } else {
            this.isLoading = false;
            Swal.fire({
              title: 'No Data',
              text: 'No employee details found for the selected criteria.',
              icon: 'info',
              confirmButtonText: 'OK',
              confirmButtonColor: '#007bff',
            });
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error generating report:', error);
          Swal.fire({
            title: 'Error',
            text: 'Failed to generate the report.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        },
      });
  }
  private loadPdfIntoIframe(base64String: string): void {
    if (!this.reportPreviewFrame || !this.reportPreviewFrame.nativeElement) {
      console.error('Iframe not initialized.');
      this.isLoading = false;
      return;
    }

    let dataLoaded = false;

    if (base64String && base64String.trim().length >= 50) {
      const pdfData = 'data:application/pdf;base64,' + base64String;
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
        pdfData
      ) as any;
      const iframe = this.reportPreviewFrame.nativeElement;

      iframe.onload = () => {
        this.isLoading = false;
        dataLoaded = true;
      };

      iframe.setAttribute(
        'src',
        sanitizedUrl.changingThisBreaksApplicationSecurity
      );
    }

    setTimeout(() => {
      if (!dataLoaded) {
        this.isLoading = false;
        Swal.fire({
          title: 'No Data',
          text: 'No Employee data for preview.',
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff',
        });
      }
    }, 2000);
  }

  navigateToEmailPayslip(): void {
    if (!this.selectedPayPeriod) {
      Swal.fire({
        title: 'Warning!',
        text: 'Please select a Payroll Calendar',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f',
      });
      return;
    }

    const payCalendarId =
      this.selectedPayPeriod === 'ALL'
        ? 'ALL'
        : this.selectedPayPeriod.payCalendar.payCalendarId;

    this.employeeService
      .getEmployeesByPayCalendar(payCalendarId, this.entityId)
      .subscribe({
        next: (data: Employee[]) => {
          if (data.length > 0) {
            this.employeesList = data;
            const navigationExtras: NavigationExtras = {
              state: { employees: this.employeesList },
            };
            this.router.navigate(['/email-payslip'], navigationExtras);
          } else {
            Swal.fire({
              title: 'No Employees Found',
              text: 'No employees available for the selected payroll period.',
              icon: 'info',
              confirmButtonText: 'OK',
              confirmButtonColor: '#007bff',
            });
          }
        },
        error: (error) => {
          console.error('Error fetching employees:', error);
        },
      });
  }

filterPayRuns(): void {
  if (!this.fromDate || !this.toDate) {
    Swal.fire({
      title: 'Warning!',
      text: 'Select both From and To Date',
      icon: 'warning'
    });
    return;
  }

  const fromDateStr = this.formatDate(new Date(this.fromDate));
  const toDateStr = this.formatDate(new Date(this.toDate));

  this.isLoadingSummary = true;

  this.employeeService
    .getPayRunSummaryReportByPostingDate(fromDateStr, toDateStr, this.entityId)
    .subscribe(
      (data: any) => {
        if (data.response && data.response.trim().length >= 50) {
          this.loadPdfIntoIframeSummary(data.response);
        } else {
          this.isLoadingSummary = false;
          Swal.fire('No Data', 'No pay runs found for the selected date range.', 'info');
        }
      },
      (error) => {
        this.isLoadingSummary = false;
        Swal.fire('Error', 'An error occurred while fetching data.', 'error');
      }
    );
}

formatDate(date: Date): string {
  const yyyy = date.getFullYear();
  const mm = String(date.getMonth() + 1).padStart(2, '0');
  const dd = String(date.getDate()).padStart(2, '0');
  return `${yyyy}-${mm}-${dd}`;
}

private loadPdfIntoIframeSummary(base64String: string): void {
  if (!this.payRunSummaryFrame || !this.payRunSummaryFrame.nativeElement) {
    console.error('Iframe not initialized.');
    this.isLoadingSummary = false;
    return;
  }

  let dataLoaded = false;

  const pdfData = 'data:application/pdf;base64,' + base64String;
  const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
  const iframe = this.payRunSummaryFrame.nativeElement;

  iframe.onload = () => {
    this.isLoadingSummary = false;
    dataLoaded = true;
  };

  iframe.setAttribute(
    'src',
    sanitizedUrl.changingThisBreaksApplicationSecurity
  );

  setTimeout(() => {
    if (!dataLoaded) {
      this.isLoadingSummary = false;
      Swal.fire('Error', 'Failed to load PDF data.', 'error');
    }
  }, 3000);
}

}
