<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <form #f="ngForm" (ngSubmit)="f.form.valid && onSubmit(f)" class="row g-1" novalidate="feedback-form">
    <div class="heading">
      <h3>Edit Payable Bill</h3>
      <div class="button-group">
        <!--<button type="submit" class="transparent-button">
          Preview & Print
        </button>
        <button type="submit" class="transparent-button">
          Email
        </button>-->
      </div>
    </div>
    
    <div class="bd">
      <div class="form-section">
        <!-- First row: Reference Number and Valid Until -->
        <div class="form-row" style="display: flex; justify-content: space-between;">
          <!-- Reference Number -->
          <div class="form-group" style="display: flex;  flex-grow: 1; ">
            <label for="quotationNo" id="quotation" style="margin-right: 10px; white-space: nowrap;">Bill Number
              </label>
              <input
              class="input-style"
              type="text"
              id="quotationNo"
              [(ngModel)]="apInvoiceHeadData.referenceNo"
              name="referenceNo"
              style="background-color: #e9ecef;"
              #referenceNo="ngModel"
              readonly
              disabled
            />
          </div>

          <!-- Due Date -->
          <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1;">
            <div style="display: flex; " class="form-dataInput form-group">
              <label for="validityUntil" style="margin-right: 35px; white-space: nowrap;">Due Date</label>
              <div style="position: relative; width: 100%;">
                <input 
                  matInput
                  [matDatepicker]="dueDatePicker"
                  class="input-style"
                  id="validityUntil" 
                  [(ngModel)]="apInvoiceHeadData.dueDate"
                  name="validityUntil" 
                  (dateChange)="onValidUntilDateChange()" 
                  required 
                  [min]="minDate"
                  placeholder="dd/mm/yyyy"
                />
                <mat-datepicker-toggle matSuffix [for]="dueDatePicker" style="position: absolute; right: 0; top: 60%; transform: translateY(-50%);"></mat-datepicker-toggle>
              </div>
              <mat-datepicker #dueDatePicker></mat-datepicker>
            </div>

            <!-- Error message directly under the input field -->
            <div *ngIf="f.submitted && f.controls['validityUntil'].invalid" class="text-danger"
              style="margin-top: 5px; margin-left: 110px;">
              <div *ngIf="f.controls['validityUntil'].errors?.['required']">Due Date is required.</div>
            </div>
          </div>
        </div>

        <!-- Second row: Supplier and posting Date -->
        <div class="form-row" style="display: flex; justify-content: space-between; ">
          <!-- Supplier -->
          <div class="form-group" style="flex-grow: 1">
            <div style="display: flex" class="form-dataInput form-group">
              <label
                for="customer"
                style="margin-right: 32px; white-space: nowrap"
                class="InputLabel"
                >Supplier</label
              >
              <select
                class="form-select"
                id="customer"
                [(ngModel)]="apInvoiceHeadData.businessPartnerId"
                (change)="onCustomerChange($event)"
                name="customerName"
                required
              >
                <option value="" selected disabled>Select Supplier</option>
                <option
                  *ngFor="let customer of customers"
                  [value]="customer.businessPartnerId"
                >
                  {{ customer.bpName }}
                </option>
              </select>
            </div>

            <!-- "Create New Customer" button right below the dropdown -->
            <div class="create-customer-container" style="margin-top: 10px">
              <button
                type="button"
                (click)="loadBusinessPartnerTypes()"
                class="create-customer-btn"
                data-bs-target="#customerPopUpModal"
                data-bs-toggle="modal"
              >
                Create New Supplier
              </button>
            </div>

            <!-- Error message for Customer -->
            <div
              *ngIf="f.submitted && f.controls['customerName'].invalid"
              class="text-danger"
              style="margin-top: -30px; margin-left: 95px"
            >
              <div *ngIf="f.controls['customerName'].errors?.['required']">
                Supplier is required.
              </div>
            </div>
          </div>

          <!-- Posting Date -->
          <div class="form-group" style="display: flex;  flex-grow: 1; justify-content: space-between;">
            <label for="quotationDate" style="margin-right: 12px; white-space: nowrap;">Posting Date</label>
            <div style="position: relative; width: 100%;">
              <input 
                matInput
                [matDatepicker]="postingDatePicker"
                class="input-style" 
                id="quotationDate" 
                [(ngModel)]="apInvoiceHeadData.postingDate"
                name="quotationDate" 
                (change)="onQuoteDateChange()" 
                required 
                [min]="minDate"
              />
              <mat-datepicker-toggle matSuffix [for]="postingDatePicker" style="position: absolute; right: 0; top: 30%; transform: translateY(-50%);"></mat-datepicker-toggle>              
            </div>
            <mat-datepicker #postingDatePicker></mat-datepicker>
          </div>

          <!-- Error message for Quotation Date -->
          <div *ngIf="f.submitted && f.controls['quotationDate'].invalid" class="text-danger"
            style="flex-basis: 100%; margin-top: 5px;">
            <div *ngIf="f.controls['quotationDate'].errors?.['required']">Date is required.</div>
          </div>
        </div>


           <!-- third row: reference number -->
           <div
           class="form-row"
           style="display: flex; justify-content: space-between;">
           <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1">
             <div style="display: flex" class="form-dataInput form-group">
               <label
                 for="supplierReferenceNumber"
                 style="margin-right: 20px; white-space: nowrap"
                 class="InputLabel">
                 Reference
               </label>
               <input
               type="text"
               [(ngModel)]="apInvoiceHeadData.supplierReferenceNumber"
               id="supplierReferenceNumber"
               name="supplierReferenceNumber"
               class="input-style"
               placeholder="Enter Reference Number"
            
           />
              
             </div>
           </div>
          <div class="form-group" style="flex-grow: 1;"></div>
         </div>
      </div>

      <div class="form-row">


      </div>

      <div class="table-section">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th style="width: 33%;">Description</th>
                <th style="width: 7%;">Quantity</th>
                <th style="width: 17%; text-align: center;">Unit Price</th>
                <th style="width: 13%;">Account</th>
                <th style="width: 8%;">{{ businessEntity.countryId.defaultTaxType }}({{
                  businessEntity.countryId.defaultTaxRate }}%)</th>
                <th style="width: 5%; text-align: right;">Tax</th>
                <th style="width: 14%; text-align: right; padding-right: 0;">Amount ({{ businessEntity.countryId.defaultCurrency}})</th>
                <th style="width: 3%;"></th>

              </tr>
            </thead>




            <tbody *ngIf="apInvoiceHeadData.apInvoiceDetails.length > 0">
              <tr *ngFor="let detail of apInvoiceHeadData.apInvoiceDetails; let i = index">
                <td>
                  <input type="text" [(ngModel)]="apInvoiceHeadData.apInvoiceDetails[i].itemDescription"
                    name="itemDescription-{{i}}" class="form-control" placeholder="Enter Item Description" />
                </td>



                <td>
                  <input type="number" [(ngModel)]="apInvoiceHeadData.apInvoiceDetails[i].quantity"
                    (input)="updateQuantity(i)" name="quantity" name="quantity-{{i}}" class="form-control" min="0" />
                </td>

                <td>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">$</span>
                    </div>
                    <input type="number" [(ngModel)]="apInvoiceHeadData.apInvoiceDetails[i].unitPrice"
                      (input)="updateAmount(i)" name="unitPrice-{{i}}" class="form-control" min="0" step="0.01" />
                  </div>
                </td>
                <td>
                  <select class="form-control" name="coaLedgerAccountId-{{ i }}" required
                    [(ngModel)]="apInvoiceHeadData.apInvoiceDetails[i].coaLedgerAccountId"
                    (change)="onGLChange($event, i)">
                    <option value="" disabled selected>GL Account</option>
                    <option *ngFor="let account of glAccounts" [value]="account.coaLedgerAccountId">
                      {{ account.ledgerAccountName }}
                    </option>
                  </select>
                </td>

                <td>
                  <div class="form-check-tax">
                    <label>
                      In.Tax
                      <input 
                      type="checkbox" 
                        [disabled]="!taxApplicabilityEnabled"
                        [(ngModel)]="apInvoiceHeadData.apInvoiceDetails[i].taxApplicability"
                        (change)="onTaxApplicableChange(i)" name="taxApplicable-{{i}}" style="margin-left: 5px;" />
                    </label>
                  </div>
                </td>

                <td style="text-align: right">{{ apInvoiceHeadData.apInvoiceDetails[i].tax | currency }}</td>
                <td style="text-align: right">{{ apInvoiceHeadData.apInvoiceDetails[i].amount | currency }}</td>

                 <td>
                  <button
                    type="button"
                    class="btn btn-link"
                    (click)="removeItem(i)"
                  >
                    <i class="fa fa-trash" style="color: red"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <button type="button" class="btns btn-primarys" (click)="addNewRow()"><i class="bi bi-plus-circle-fill"></i> Add New Row</button>
        </div>
      </div>

      <div class="notes-totals-section">
        <div class="notes-section" style="padding: 0 10px 0 10px;">
          <label for="remarks">Notes</label>
          <textarea id="remarks" class="form-control" [(ngModel)]="apInvoiceHeadData.remarks" name="note" rows="10"></textarea>
        </div>

        <div class="totals-section" style="margin-top: 25px;">
          <div class="totals-row">
            <span class="totals-row1">Sub Total Amount </span>
            <span class="totals-row2">{{ apInvoiceHeadData.grossAmount | currency }}</span>
          </div>
          <div class="totals-row">
            <span class="totals-row1">Total {{ businessEntity.countryId.defaultTaxType }} </span>
            <span class="totals-row2">{{ apInvoiceHeadData.totalGst | currency }}</span>
          </div>
          <div class="totals-row">
            <strong class="totals-row1">Grand Total</strong>
            <strong class="totals-row2">{{ apInvoiceHeadData.netAmount | currency }}</strong>
          </div>
        </div>
      </div>

      <div class="footer-section" style="margin-bottom: 50px;">
        <div class="footer-btn-section">
         <!-- <button type="button" class="transparent-button">
            File Attach <i class="bi bi-paperclip"></i>
          </button>-->
          
        </div>

        <div class="footer-btn-section" style="">
          <button type="submit" class="add-btn">
            Update Bill
          </button>
          <button type="button"  (click)="onCancel()" class="cancel-btn">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </form>
</div>

<!-- Customer creation Popup -->
<div class="modal fade" id="customerPopUpModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" style="max-width: 740px;">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="simpleModalLabel">Add New Supplier</h5>
        <div class="icon-group ms-auto">
          <i class="bi bi-x-circle close-icon" #closeCustomerPopUp data-bs-dismiss="modal" aria-label="Close"
            title="Close"></i>
        </div>
      </div>

      <div class="modal-body">

        <form #cuspop="ngForm" name="cuspop" (ngSubmit)="cuspop.form.valid && onSubmitCustomerForm()" class="row g-1"
          novalidate="feedback-form" (keydown)="preventSubmit($event)">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="partner-type">Business Partner Type</label>
                <select class="input-style" id="partner-type"
                  [(ngModel)]="businessPartner.businessPartnerTypeId.businessPartnerTypeId" name="businessPartnerType"
                  style="border: 1px solid #c7c7c7; border-radius: 8px; width: 100%; height: 43.41px;" disabled>
                  <option value="" selected disabled>Select Partner Type</option>
                  <option *ngFor="let customer of businessPartnerType" [value]="customer.businessPartnerTypeId">
                    {{ customer.businessPartnerType }}
                  </option>
                </select>

                <div *ngIf="cuspop.submitted && cuspop.controls['businessPartnerType'].invalid" class="text-danger">

                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6" style="width: 100%;">
              <div class="form-group">
                <label for="bp-name">Business Partner Name</label>
                <input type="text" id="bp-name" [(ngModel)]="businessPartner.bpName" name="bpName" required />

                <div *ngIf="cuspop.submitted && cuspop.controls['bpName'].invalid" class="text-danger">
                  <div *ngIf="cuspop.controls['bpName'].errors?.['required']">Business Partner Name is required.</div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="email">Email </label>
                <input type="email" id="email" [(ngModel)]="businessPartner.email" name="email"
                  pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" />

                <div *ngIf="cuspop.submitted && cuspop.controls['email'].invalid" class="text-danger">

                  <div *ngIf="cuspop.controls['email'].errors?.['pattern']">Invalid email format.</div>
                </div>

              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="contactPhoneNumber">Contact Number</label>
                <input type="text" id="contactPhoneNumber" [(ngModel)]="businessPartner.contactPhoneNumber"
                  name="contactPhoneNumber" />
                <div *ngIf="cuspop.submitted && cuspop.controls['contactPhoneNumber'].invalid" class="text-danger">

                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="abn-number">ABN Number <small>(Optional)</small></label>
                <input type="text" id="abn-number" [(ngModel)]="businessPartner.abnNumber" name="abnNumber" />
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="acn-number">ACN Number</label>
                <input type="text" id="acn-number" [(ngModel)]="businessPartner.acnNumber" name="acnNumber" />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="default-payment-terms">Default Payment Terms <small>(Optional)</small></label>
                <select class="input-style" id="default-payment-terms" [(ngModel)]="businessPartner.defaultPaymentTerms"
                  name="defaultPaymentTerms"
                  style="border: 1px solid #c7c7c7; border-radius: 8px;width: 100%; height: 43.41px;">
                  <option value="" disabled selected>Select default payment terms</option>
                  <option value="net30">Net 30</option>
                  <option value="2/10Net30">2/10 Net 30</option>
                  <option value="dueOnReceipt">Due on Receipt</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6" style="width: 100%;">
              <div class="form-group">
                <label for="business-address">Business Address</label>
                <input type="text" id="business-address" [(ngModel)]="businessPartner.businessAddress"
                  name="businessAddress" />
                <div *ngIf="cuspop.submitted && cuspop.controls['businessAddress'].invalid" class="text-danger">

                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="delivery-address">Delivery Address</label>
                <input type="text" id="delivery-address" [(ngModel)]="businessPartner.deliveryAddress"
                  name="deliveryAddress" />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">

            </div>
          </div>
          <hr>
          <h3>Additional</h3>
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="bank-account-name">Bank Account Name</label>
                <input type="text" id="bank-account-name" [(ngModel)]="businessPartner.bankAccountName"
                  name="bankAccountName" />
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="bsb">BSB</label>
                <input type="text" id="bsb" [(ngModel)]="businessPartner.bsb" name="bsb" />
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="bank-account-number">Bank Account Number</label>
                <input type="text" id="bank-account-number" [(ngModel)]="businessPartner.accountNumber"
                  name="accountNumber" />
              </div>
            </div>
          </div>
          <!-- Footer Buttons -->
          <!--<div class="popup-footer">
            <a href="/create-quotation"  style="text-align: left;" >Update Customer</a>
           <button type="button" class="cancel-btn1" #closeCustomerPopUp data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="add-btn1">Add</button>
         
          </div>-->
          <div class="popup-footer">
            <div class="left-section">
              <a href="/business-partner" class="cancel-btn1" style="padding: 10px 20px;">Update Customer</a>
            </div>  
            <div class="right-section">
              <button type="button" class="cancel-btn1" #closeCustomerPopUp>Cancel</button>
              <button type="submit" class="add-btn1">Add</button>
            </div>
          </div>

        </form>
      </div>
    </div>
  </div>
</div>