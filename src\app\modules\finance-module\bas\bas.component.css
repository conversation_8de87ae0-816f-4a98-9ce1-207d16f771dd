* {
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.heading-section {
  display: flex;
  margin-bottom: 20px;
}

.heading-section h1 {
  flex: 1;
  font-family: Inter;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

.card {
  width: 100%;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.upper-section {
  display: flex;
  justify-content: space-between;
}

.left-first-row {
  font-size: 35px;
  font-weight: bold;
}

.left-second-row {
  margin-top: 4px;
}

.text-gray {
  color: gray;
}

.text-small {
  font-size: 14px;
  color: #6c757d;
  margin-top: 4px;
}

.right-first-row {
  color: gray;
  margin-top: 4px;
}

.right-price {
  color: #000;
  font-size: 35px;
  margin-left: 10px;
  font-weight: bold;
}

/* .main-container {
  background-color: rgb(241, 241, 241);
} */

.table-container {
  width: 100%;
  margin: 40px 0 0 0;
}

/* .actions .transparent-button {
  background: transparent;
  color: #4262ff;
  border: 1px solid #4262ff;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 29px;
  font-weight: bold;
  margin-bottom: 20px;
  margin-left: 15px;
} */

/* .actions .transparent-button:hover {
  background-color: #4262ff;
  color: white;
} */

.search-create {
  display: flex;
  justify-content: flex-end;
  /* align-items: center; */
  margin-bottom: 20px;
  gap: 10px;
}

.search-create .input-container {
  position: relative;
  width: 600px;
}

.search-create .input-container input.form-control {
  width: 100%;
  padding: 10px 30px 10px 10px;
  font-size: 16px;
  border-radius: 13px;
  border: 1px solid #ccc;
  box-sizing: border-box;
}

.search-create .input-container i.bi-search {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
}

.search-create input {
  width: 510px;
  padding: 10px;
  font-size: 16px;
  border-radius: 13px;
  border: 1px solid #ccc;
  flex-grow: 1;
}

.search-create button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 13px;
  border: none;
  margin-left: 10px;
  width: 350px;
  top: 356px;
  left: 879px;
  border-radius: 13px;
}

.search-create button:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

.flex-box {
  display: flex;
  margin-top: 5px;
  height: 50px;
  border: 2px solid;
  /* margin-right: 80px;
      margin-left: 80px; */
  flex-direction: row;
  /* padding-left: 100px;
      padding-right: 100px; */
  justify-content: space-between;
  align-items: center;
  border-top: none;
  background-color: white;
  border-left: none;
  border-right: none;
  border-color: #e6e6e6;
}

.flex-box-heading {
  font-weight: 600;
  font-size: 1rem;
  background-color: white;
}

table {
  width: 100%;
  margin-bottom: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.table-responsive {
  border-radius: none;
}

th {
  font-size: 25px;
  font-weight: bold;
  padding: 10px 10px 0 10px;
}

td {
  padding: 10px;
}

.table-head th {
  position: relative;
  padding: 10px;
  background-color: #d7dbf5;
  text-align: left;
  color: rgb(0, 0, 0);
  font-size: 14px;
  font-weight: bold;
}

.table-head th:first-child {
  border-top-left-radius: 10px;
}

.table-head th:last-child {
  border-top-right-radius: 10px;
}

tbody tr {
  background-color: white;
  border-bottom: rgb(171, 171, 171) 1px solid;
  font-size: 14px;
  color: #6c757d;
}

tbody tr:hover {
  background-color: #f1f1f1;
  /* Light grey color on hover */
}

th {
  padding: 10px 10px 0 10px;
}

td {
  padding: 10px;
}

td.valueCheckbox,
th.valueCheckbox {
  width: 5%;
}

td.value,
th.valuehead {
  width: 12%;
}

.Active,
.Closed,
.Draft,
.Invoiced,
.Open,
.Pending,
.Revised,
.Invoiced {
  display: inline-block;
  padding: 0 30px;
  border-radius: 15px;
  font-weight: bold;
}

.text-draft {
  color: #007bff;
  text-align: center;
  /* Primary (blue) for Draft */
}

.text-pending {
  color: #057c21;
  text-align: center;
  /* Success (green) for Pending */
}

.text-canceled {
  color: #dc3545;
  text-align: center;
  /* Danger (red) for Canceled */
}

.text-revised {
  color: #6c757d;
  text-align: center;
  /* Warning (yellow) for Revised */
}

.text-sent {
  color: #455cff;
  text-align: center;
  /* Info (cyan) for Sent */
}

.text-Invoiced {
  color: #5bc0de;
  text-align: center;
  /* Info (cyan) for Sent */
}

.text-paid {
  color: #28a745;
  text-align: center;
  /* Success (green) for Paid */
}

span.lable {
  border: none;
  border-radius: 20px;
  padding: 5px 10px;
  font-weight: bold;
}

.border-draft {
  border-color: #007bff;
  background-color: #cce0f5;
  /* Primary (blue) for Draft */
}

.border-pending {
  border-color: #057c21;
  background-color: #d7ecdc;
  /* Success (green) for Pending */
}

.border-canceled {
  border-color: #dc3545;
  background-color: #eedbdd;
  /* Danger (red) for Canceled */
}

.border-revised {
  border-color: #6c757d;
  background-color: #eeebeb;
  /* Warning (yellow) for Revised */
}

.border-sent {
  border-color: #5bc0de;
  background-color: #ebf0f7;
  /* Info (cyan) for Sent */
}

.border-Invoiced {
  border-color: #5bc0de;
  background-color: #ebf0f7;
  /* Info (cyan) for Sent */
}

.border-paid {
  border-color: #28a745;
  background-color: #d4edda;
  /* Success (green) for Paid */
}

td button {
  border: none;
  background: none;
  padding: 2px;
  font-size: 1.5rem;
}
.customer-popup {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 500px;
  /* Adjust the width as per your design */
  width: 30%;
  /* Ensure it doesn’t go beyond screen size */
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.popup-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

/* td button i {
  font-size: 1.5rem;
} */

td button.btn-link {
  padding: 0;
  margin: 0;
  font-size: inherit;
  color: inherit;
}

.text-draft {
  color: #007bff;
  /* Primary (blue) for Draft */
}

.text-pending {
  color: #057c21;
  /* Success (green) for Pending */
}

.text-canceled {
  color: #dc3545;
  /* Danger (red) for Canceled */
}

.text-revised {
  color: #6c757d;
  /* Warning (yellow) for Revised */
}

.text-sent {
  color: #455cff;
  /* Info (cyan) for Sent */
}

.text-Invoiced {
  color: #455cff;
  /* Info (cyan) for Sent */
}

.archive-markas {
  display: flex;
  margin-top: 10px;
  margin-bottom: 2px;
}

.archive {
  width: 140px;
  height: 45px;
  border: 2px solid #4262ff;
  color: #4262ff;
  font-family: Inter;
  font-size: 16px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  /* Gap between icon and text */
}

.markas {
  width: 164px;
  height: 45px;
  border: 2px solid #6822ff;
  color: #6822ff;
  font-family: Inter;
  font-size: 16px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.archive i,
.markas i {
  font-size: 20px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon-group {
  display: flex;
  gap: 20px;
}

.close-icon,
.send-icon {
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.close-icon:hover,
.send-icon:hover {
  color: #000;
}

.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 700px;
  /* Same height as the iframe */
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.nav-tabs .nav-link.active {
  border-top: 3px solid blue;
}

.nav-item {
  margin-right: 10px;
  width: 19.1%;
  text-align: center;
  font-family: Inter;
  font-size: 16px;
  font-weight: 500;
}

.Row1 {
  display: flex;
  padding-left: 80px;
  margin-top: 30px;
  padding-right: 80px;
  /* width: 100vw; */
  justify-content: space-between;
}

/* Row layout for filters */

.row1 {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.row1_col1 {
  width: 30%;
  /* Ensures enough space for input text */
}

.row1_col2,
.row1_col3,
.row1_col4,
.row1_col5 {
  width: 20%;
}

.row1_col1 input,
.row1_col2 select,
.row1_col3 input,
.row1_col4 input {
  width: 100%;
  height: 49px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
}

.add-btn {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-btn:hover {
  background-color: #0056b3;
}
.popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.popup-footer .add-btn {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 5px 40px;
  cursor: pointer;
  border-radius: 15px;
  border: none;
  margin-left: 10px;
  font-size: 17px;
}

.popup-footer .cancel-btn {
  background: transparent;
  color: #4262ff;
  border: 2px solid #4262ff;
  padding: 5px 40px;
  margin-right: 15px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  font-size: 17px;
}

.popup-footer .add-btn:hover {
  background-color: #218838;
}

/* General styles */

.Card {
  width: 100%;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.primary-button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 5px 30px;
  cursor: pointer;
  border-radius: 12px;
  border: 1px solid #4262ff;
  margin-left: 850px;
  margin-top: 10px;
  font-size: 17px;
}

.primary-button:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

.secondary-button {
  background: transparent;
  color: #4262ff;
  border: 1px solid #4262ff;
  padding: 5px 40px;
  margin-right: 15px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  font-size: 17px;
}

.secondary-button:hover {
  background-color: #4262ff;
  color: white;
}

.row2 {
  display: flex;
  align-items: right;
  justify-content: flex-end;
  margin-top: 20px;
}

.row2_col2 {
  padding: 8px 20px;
  margin: 0 10px;
  font-size: 14px;
  color: #555;
}

.input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.search-input {
  width: 100%;
  padding-right: 30px;
  /* Add space for the icon inside the input */
  padding-left: 10px;
  box-sizing: border-box;
  /* Ensures padding doesn't increase input size */
}

.input-container i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  pointer-events: none;
  /* Makes sure the icon doesn't block input clicks */
}

label {
  display: block;
  font-weight: bold;
  font-family: Inter;
  font-size: 15px;
  color: #333;
}

.secondary-button:hover {
  background-color: #4262ff;
  color: white;
}

.dropdown-menu .dropdown-item:hover {
  background-color: #4262ff;
}

.dropdown-menu .dropdown-item {
  cursor: pointer;
  /* This will change the cursor to a hand/finger icon */
}

.custom-close-btn {
  width: 50px;
  /* Adjust the width */
  height: 50px;
  /* Adjust the height */
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon-group {
  display: flex;
  gap: 20px;
}

.close-icon,
.send-icon {
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.close-icon:hover,
.send-icon:hover {
  color: #000;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #333;
}

.close-btn:hover {
  color: #ff0000;
}

/* Form input styles */

.form-group {
  margin-bottom: 10px;
  text-align: left;
  font-family: Arial, sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 25.41px;
  color: #444343;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 5px;
}

.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;
  /* Allow resizing only vertically */
  min-height: 100px;
  /* Minimum height for better visibility */
  max-height: 300px;
  /* Restrict max height */
}

.form-group input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 10px;
  font-size: 1rem;
}

.file-upload-group {
  display: flex;
  align-items: center;
  /* Align the label and input vertically in the middle */
  gap: 10px;
  /* Add space between the label and the input */
}

.file-upload-group input[type="file"] {
  border: 1px solid #ccc;
  padding: 5px;
  border-radius: 4px;
  font-size: 1rem;
}

.file-row {
  display: flex;
  align-items: center;
  gap: 20px;
  /* Space between label and attachment row */
  margin-top: 10px;
}

.gradient-btn {
  background: linear-gradient(to right, #512ca2, #4262ff);
  border: none; /* Remove border if desired */
  color: white; /* Ensure text/icon contrast */
}

.gradient-btn:hover {
  background: linear-gradient(
    to right,
    #4262ff,
    #512ca2
  ); /* Optional hover effect */
  color: white;
}
.alert-danger {
  display: inline-block;
  padding: 5px 10px;
  font-size: 14px;
}

.table-borderless tbody tr td {
  vertical-align: middle;
}

.text-right h1 {
  font-size: 2.5rem;
  color: #333;
}

.font-weight-bold {
  font-weight: bold;
}
.table {
  font-size: 0.9rem;
}

input[type="text"] {
  max-width: 400px;
}
.form-check-input {
  transform: scale(1.8);
  margin-left: 1px;
}
