import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CreateUserThroughInvitationComponent } from './create-user-through-invitation.component';

describe('CreateUserThroughInvitationComponent', () => {
  let component: CreateUserThroughInvitationComponent;
  let fixture: ComponentFixture<CreateUserThroughInvitationComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [CreateUserThroughInvitationComponent]
    });
    fixture = TestBed.createComponent(CreateUserThroughInvitationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
