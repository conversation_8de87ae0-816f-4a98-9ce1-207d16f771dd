* {
  font-family: "Inter", sans-serif !important;
}
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.actions h1 {
  flex: 1;
  margin-bottom: 20px;
  font-family: "Inter", sans-serif !important;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

.tab-view-button {
  border: 1px solid lightgray;
  border-bottom: 0;
  padding: 15px 25px;
  font-weight: 500;
  background: transparent;
}

.tab-view-button:not(:first-child) {
  border-left: 0;
}

.bottom-border {
  border-bottom: 1px solid lightgray;
}

.active-button {
  position: relative;
  background-color: white;
}
.active-button::after {
  position: absolute;
  content: "";
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: white;
}

.primary-button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: 600;
  padding: 8px 20px;
  cursor: pointer;
  border-radius: 5px;
  border: 1px solid #4262ff;
  margin-left: 8px;
  font-size: 14px;
}

.primary-button:disabled{
  background: rgb(241, 241, 241);
  color: black;
  border-color: gray;
  cursor: not-allowed;
}
.secondary-button {
  background:white;
  color: #4262ff;
  font-weight: 600;
  padding: 8px 20px;
  cursor: pointer;
  border-radius: 5px;
  border: 1px solid #4262ff;
  margin-left: 8px;
  font-size: 14px;
}

p {
  margin: 0;
  padding: 0;
}

.q-head {
  font-size: 15px;
  font-weight: 500;
  color: rgb(66, 66, 66);
}

table {
  border: 0;
  border-collapse: collapse;
  width: 100%;
}

table tr:not(:last-child) {
  border-bottom: 1px solid lightgray;
}

table tr td {
  padding: 12px;
}

.custom-input {
  outline: none !important;
  border: 1px solid rgb(200, 200, 200);
  color: rgb(100, 100, 100);
  border-radius: 5px;
  text-align: start;
  font-size: 15px;
  font-weight: 600 !important;
  padding: 8px 12px;
  width: 100%;
  appearance: textfield !important;
}

.custom-input::placeholder{
  font-style: italic;
  font-weight: 400;
  font-size: 13px;
}

.custom-input:disabled{
  background-color: rgb(236, 236, 236);
}


.custom-select {
  font-size: 14px !important;
  outline: none;
  width: 100%;
  min-width: 200px;
  border-radius: 5px;
  border: 1px solid lightgray;
  color: black;
  font-weight: 600;
  padding: 9px 36px 9px 12px;
  background-color: white;

  appearance: none; /* Removes default styling */
  -webkit-appearance: none;
  -moz-appearance: none;

  /* Custom arrow */
  background-image: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L5 5L9 1' stroke='%234262ff' stroke-width='1.5'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 10px 6px;
}

.custom-select option {
  font-weight: 400;
  text-transform: capitalize;
}

.custom-table {
  width: 100%;
  border-collapse: collapse;
}

.custom-table thead tr th {
  padding: 9px 12px;
  background-color: rgb(241, 241, 241);
  border: 1px solid lightgray;
  color: black;
  font-weight: 600;
  font-size: 14px;
}

.custom-table tbody tr td {
  padding: 9px 12px;
  border: 1px solid lightgray;
  background-color: white;
  color: black;
  font-weight: 500;
  font-size: 14px;
}

.custom-table tfoot tr td {
  padding: 12px 12px;
  background-color: white;
  color: black;
  font-weight: 600;
  font-size: 16px;
}

.bottom-border-line {
  position: relative;
  border-bottom: 1px solid lightgray;
}

.bottom-border-line::after {
  position: absolute;
  content: "";
  bottom: -4px;
  left: 0;
  height: 1px;
  width: 100%;
  background-color: lightgray;
}

.placeholder-style {
  color: rgb(170, 170, 170);
  font-style: italic;
  font-weight: 500;
}

option{
  color: black;
  font-style: normal;
}

.total-percentage-cell{
  position: relative;
  border-block: 1px solid lightgray;
  font-weight: 700;
  font-size: 15px;
}

.total-percentage-cell::before{
  position: absolute;
  content: '';
  width: 100%;
  height: 1px;
  background-color: white;
  border-bottom: 1px solid lightgray;
  left: 0;
  bottom: -5px;
}

