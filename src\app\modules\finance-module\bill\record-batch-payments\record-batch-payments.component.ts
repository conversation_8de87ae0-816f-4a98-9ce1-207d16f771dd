import { Component, ViewChild } from '@angular/core';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { ApInvoiceDetail, ApInvoiceHead, PaymentVoucherDetail, PaymentVoucherHeader } from '../bill';
import { Entity } from 'src/app/modules/entity/entity';
import { ActivatedRoute, Router } from '@angular/router';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { BillService } from '../bill.service';
import Swal from 'sweetalert2';
import { NgForm } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { GlAccountService } from '../../gl-account/gl-account.service';
import { CoaH<PERSON><PERSON>, CoaLedgerAccount } from '../../gl-account/gl-account';
import { DateAdapter } from '@angular/material/core';



@Component({
  selector: 'app-record-batch-payments',
  templateUrl: './record-batch-payments.component.html',
  styleUrls: ['./record-batch-payments.component.css'],
})

export class RecordBatchPaymentsComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;


  creditNoteBillHead: PaymentVoucherHeader = new PaymentVoucherHeader();
  details: PaymentVoucherDetail = new PaymentVoucherDetail();
  businessEntityId: number = 0;
  lastVoucherNumber: string = '';
  businessEntity: Entity = new Entity();
  selectedApInvoices: any[] = [];
  totalCreditAmount: number = 0;
  glAccounts: CoaLedgerAccount[] = [];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private entityService: EntityService,
    private billService: BillService,
    private glAccountService: GlAccountService,
    private dateAdapter: DateAdapter<Date>,
  ) { 
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }


  ngOnInit() {

this.businessEntityId = +(localStorage.getItem('entityId') || '');

this.glAccountService.getCoaLedgerAccountListByEntity(this.businessEntityId).subscribe(
  (accounts: CoaLedgerAccount[]) => {
    this.glAccounts = accounts.filter(
      account => account.coaHeaderId?.accountHeaderType === 'Bank or Cash'
    );
  },
  (error) => {
    console.error('Failed to load GL accounts', error);
  }
);



    //this.fetchGlAccounts();
    this.route.queryParams.subscribe(params => {
      const billIds = JSON.parse(params['billIds'] || '[]');

      if (billIds.length > 0) {
  this.loadBills(billIds).then(() => {
    this.fetchGlAccountsForBills(billIds); // Now runs AFTER selectedApInvoices is set
  });
}

      /**if (billIds.length > 0) {
        this.loadBills(billIds);
        this.fetchGlAccountsForBills(billIds);

      } **/
     else {
        Swal.fire({
          title: 'No Bills Found',
          text: 'No Bill IDs were provided.',
          icon: 'warning',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('No Bills Found');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    });
    this.getBusinessEntityById();

    if (!this.creditNoteBillHead.date) {
      this.creditNoteBillHead.date = this.getTodayDate();
    }
  }


  glAccountsMap: { [billId: number]: CoaLedgerAccount[] } = {}; // Store GL accounts per bill
/**
fetchGlAccountsForBills(billIds: number[]) {
  this.glAccountsMap = {}; // Reset previous GL account mappings

  billIds.forEach(billId => {
    this.billService.getApInvoiceDetailsByApInvoiceHeadId(billId).subscribe(
      (invoiceDetails: ApInvoiceDetail[]) => {
        this.glAccountsMap[billId] = invoiceDetails.map(detail => ({
          coaLedgerAccountId: detail.coaLedgerAccountId,
          ledgerAccountCode: detail.ledgerAccountCode || '',
          ledgerAccountName: detail.ledgerAccountName || '',
          coaHeaderId: new CoaHeaders(),
          ledgerAccountDescription: '',
          taxAccount: '',
          status: '',
          defaultTaxCode: '',
          entityId: 0,
          bankName: '',
          bankAccountId: 0
        }));

        // Ensure Angular detects the change
        this.glAccountsMap = { ...this.glAccountsMap };
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching GL Accounts for Bill ID:', billId, error);
        Swal.fire({
          title: 'Error!',
          text: `Failed to load GL Accounts for Bill ID ${billId}.`,
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonColor: '#be0032',
          showCancelButton: true,
        });
      }
    );
  });
}**/


fetchGlAccountsForBills(billIds: number[]) {
  this.glAccountsMap = {}; // Reset

  billIds.forEach(billId => {
    this.billService.getApInvoiceDetailsByApInvoiceHeadId(billId).subscribe(
      (invoiceDetails: ApInvoiceDetail[]) => {
        const accounts = invoiceDetails.map(detail => ({
          coaLedgerAccountId: detail.coaLedgerAccountId,
          ledgerAccountCode: detail.ledgerAccountCode || '',
          ledgerAccountName: detail.ledgerAccountName || '',
          coaHeaderId: new CoaHeaders(),
          ledgerAccountDescription: '',
          taxAccount: '',
          status: '',
          defaultTaxCode: '',
          entityId: 0,
          bankName: '',
          bankAccountId: 0
        }));

        this.glAccountsMap[billId] = accounts;
        this.glAccountsMap = { ...this.glAccountsMap }; // Trigger Angular change detection

        // 🔽 Automatically set first GL account for this bill
        const firstAccount = accounts[0];
        if (firstAccount) {
          const invoice = this.selectedApInvoices.find(inv => inv.apInvoiceHeadId === billId);
          if (invoice) {
            invoice.coaLedgerAccountId = firstAccount.coaLedgerAccountId;
            invoice.ledgerAccountName = firstAccount.ledgerAccountName;
          }
        }
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching GL Accounts for Bill ID:', billId, error);
        Swal.fire({
          title: 'Error!',
          text: `Failed to load GL Accounts for Bill ID ${billId}.`,
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonColor: '#be0032',
          showCancelButton: true,
        });
      }
    );
  });
}

onBankGLChange(event: any) {
  const selectedAccountId = +event.target.value;
  const selectedAccount = this.glAccounts.find(
    acc => acc.coaLedgerAccountId === selectedAccountId
  );

  if (selectedAccount) {
    this.creditNoteBillHead.coaLedgerAccountId = selectedAccount.coaLedgerAccountId;
    this.creditNoteBillHead.ledgerAccountName = selectedAccount.ledgerAccountName;
    this.creditNoteBillHead.bankName = selectedAccount.bankName;
  }
}


 /**

  onGLChange(event: any, index: number) {
    const selectedAccountId = +event.target.value;
    const billId = this.selectedApInvoices[index].apInvoiceHeadId; // Get the current bill ID
    const selectedAccount = this.glAccountsMap[billId]?.find(
      (account) => account.coaLedgerAccountId === selectedAccountId
    );

    if (selectedAccount) {
      this.selectedApInvoices[index].coaLedgerAccountId = selectedAccount.coaLedgerAccountId;
      this.selectedApInvoices[index].ledgerAccountName = selectedAccount.ledgerAccountName;
    }
  }**/




  // Utility function to get today's date in 'YYYY-MM-DD' format
  getTodayDate(): string {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const dd = String(today.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }


  loadBills(billIds: number[]): Promise<void> {
    const billRequests = billIds.map(id => this.billService.getApInvoiceHeadById(id).toPromise());
    return Promise.all(billRequests)
      .then(bills => {
        this.selectedApInvoices = bills;
      })
      .catch(error => {
        Swal.fire({
          title: 'Error Loading Bills',
          text: 'There was an error fetching the Bill details.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Error Loading Bills');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      });
  }



  getTotalPaidAmount(): number {
    return this.selectedApInvoices.reduce((total, expense) => total + (expense.paidAmount || 0), 0);
  }


  getExpenseNewBalance(expense: any): number {
    const balanceAmount = expense.dueAmount || 0;
    const paidAmount = expense.paidAmount || 0;
    return balanceAmount - paidAmount;
  }



  onCancel() {
    this.router.navigate(['record-batch-payments-list']);
  }



  preventEnter(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault(); // Prevent form submission on "Enter"
    }
  }




  onSubmit(f: NgForm) {
    // Check if credit amount is provided for all bills
    const invalidbills = this.selectedApInvoices.filter(expense => !expense.paidAmount || expense.paidAmount <= 0);

    if (invalidbills.length > 0) {
      // Show validation error
      Swal.fire({
        title: 'Warning!',
        text: 'Paid Amount is required and must be greater than 0.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f'
      });
    } else {
      // Pass the bills to the saveQuotation method
      this.creditNoteBillHead.totalPaidAmount = this.getTotalPaidAmount();
      this.creditNoteBillHead.details = this.selectedApInvoices.map(expense => {
        let detail = new PaymentVoucherDetail();
        detail.apInvoiceHeadId = expense.apInvoiceHeadId;
        detail.referenceNo = expense.referenceNo;
        detail.netAmount = expense.netAmount;
        detail.balanceAmount = expense.dueAmount;
        detail.paidAmount = expense.paidAmount;
        detail.coaLedgerAccountId = expense.coaLedgerAccountId;
        detail.ledgerAccountName = expense.ledgerAccountName;
        detail.tax = expense.netAmount > 0 ? (expense.totalGst / expense.netAmount * expense.paidAmount) : 0;

        return detail;
      });

      this.savePaymentVoucher(this.selectedApInvoices);
    }
  }

  savePaymentVoucher(bills: any[]) {

    // Ensure document date is set, default to today if not provided
    if (!this.creditNoteBillHead.date) {
      this.creditNoteBillHead.date = this.getTodayDate();
    }
    // Iterate over each expense to validate its credit amount
    const invalidbills = bills.filter(expense => expense.paidAmount > expense.dueAmount);

    if (invalidbills.length > 0) {
      Swal.fire({
        title: 'Paid Amount Exceeds Balance!',
        text: 'One or more Bills have Paid amounts greater than their balances. Do you want to continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, continue',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
      }).then((result) => {
        if (result.isConfirmed) {
          // If user confirms, set flag and continue
          invalidbills.forEach(expense => {
            expense.isCreditExceeded = true;
          });
          this.proceedToSave();
        } else if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData('Paid Amount Exceeds Balance!');
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });

    } else {
      // If all credit amounts are valid, proceed to save
      this.proceedToSave();
    }
  }

  proceedToSave() {
    // Assuming the selectedApInvoices array is already populated and validated
    if (this.selectedApInvoices.length > 0) {

      this.creditNoteBillHead.businessPartnerId = this.selectedApInvoices[0].businessPartnerId;
      this.creditNoteBillHead.payeeName = this.selectedApInvoices[0].supplierName;
      this.creditNoteBillHead.referenceNos = this.selectedApInvoices.map(expense => expense.referenceNo).join(',');

    }


    this.creditNoteBillHead.status = 'Open';
    this.creditNoteBillHead.userId = +(localStorage.getItem('userid') + "");
    this.creditNoteBillHead.entityId = +(localStorage.getItem('entityId') + "");

    // Save the credit note
    console.info(this.creditNoteBillHead);

    this.billService.savePaymentVoucher(this.creditNoteBillHead).subscribe(
      (response: any) => {
        this.updatePaymentVoucherNumber();

        // Update the balance amount for each selected expense
        this.selectedApInvoices.forEach(expense => {
          // Assuming you have already calculated the credit note amount for each expense
          const newBalanceAmount = expense.dueAmount - expense.paidAmount; // Calculate new balance
          this.updateBillBalance(expense.apInvoiceHeadId, newBalanceAmount); // Call method to update the expense balance
        });

        Swal.fire({
          title: 'Success!',
          text: 'The Payment has been successfully saved.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/record-batch-payments-list']);
          }
        });
      },
      (error: HttpErrorResponse) => {
        console.error('Error saving Payment Voucher', error);
        Swal.fire({
          title: 'Error!',
          text: 'Unable to save the  Payment Voucher. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Unable to save the  Payment Voucher. Please try again.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }

        });
      }
    );
  }

  // Method to update expense balance in the backend
  updateBillBalance(apInvoiceHeadId: number, newBalanceAmount: number) {
    this.billService.updateBillBalance(apInvoiceHeadId, newBalanceAmount).subscribe(
      (response: any) => {
      },
      (error: HttpErrorResponse) => {
        console.error(`Error updating balance for bill ${apInvoiceHeadId}:`, error);
      }
    );
  }


  getBusinessEntityById() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '');
    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
      (data) => {
        this.businessEntity = data;
        this.lastVoucherNumber = this.incrementPaymentNumber(
          this.businessEntity.voucherNumber
        );
        this.creditNoteBillHead.voucherNumber = this.lastVoucherNumber;
      },
      (error) => console.error(error)
    );
  }


  incrementPaymentNumber(voucherNumber: string): string {
    if (!voucherNumber) {
      return 'PV000001'; // Default value
    }
    // Ensure the numeric part exists and is valid
    const numericPart = voucherNumber.slice(2); // Remove the "PE" prefix
    if (isNaN(Number(numericPart))) {
      console.error(`Invalid Payment Voucher Number: ${voucherNumber}`);
      return 'PV000001';
    }
    const incrementedNumber = (Number(numericPart) + 1).toString().padStart(numericPart.length, '0');
    return 'PV' + incrementedNumber;
  }

  updatePaymentVoucherNumber() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '0');
    this.businessEntity.voucherNumber = this.creditNoteBillHead.voucherNumber;
    this.entityService.updatePaymentVoucherNumber(this.businessEntity, this.businessEntityId).subscribe(
      (data) => {
      },
      (error) => {
        console.error(error);
      }
    );
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}
