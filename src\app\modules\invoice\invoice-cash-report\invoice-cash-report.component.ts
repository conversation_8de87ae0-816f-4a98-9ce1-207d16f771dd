import { Component, ElementRef, ViewChild } from '@angular/core';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BusinessPartner } from '../../business-partner/business-partner';
import { InvoiceHead } from '../invoice';
import { InvoiceService } from '../invoice.service';
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import Swal from 'sweetalert2';
import { HttpErrorResponse } from '@angular/common/http';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-invoice-cash-report',
  templateUrl: './invoice-cash-report.component.html',
  styleUrls: ['./invoice-cash-report.component.css']
})
export class InvoiceCashReportComponent {
 @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  fromDate: string = '';
  toDate: string = '';
  status: string = '%';
  typef: string = 'pdf';
  reportType: string = '';
  overdueOption: string = '';
  overdueFromDate: string = '';
  overdueToDate: string = '';
  minAmount: number = 0;
  maxAmount: number = 0;
  reference: string = '';
  isLoading = false;
  customers: BusinessPartner[] = [];
  invoiceHead: InvoiceHead = new InvoiceHead();
  getAllCustomers = false;
  base64String: any;


  @ViewChild('invoiceCashListPreviewFrame') invoiceCashListPreviewFrame!: ElementRef;
 

  constructor(private invoiceService: InvoiceService,
    private swalAlerts: SwalAlertsService,
   private router: Router, 
   public sanitizer: DomSanitizer, 
   private businessPartnerService: BusinessPartnerService) { }


    ngOnInit() {
    this.invoiceHead.businessPartnerId = '0';
    this.loadCustomers();
  }


  
  previewInvoiceList(fromDate: string, toDate: string, businessPartnerId: any) {
  if (!fromDate || !toDate) {

    this.swalAlerts.showSwalWarning('Warning!', 'Please select Date Range', 'Missing date range for Sales Receivables Report .');
    return;
  }
  this.isLoading = true;
  const entityId = +localStorage.getItem('entityId')!;
  const entityUUID = localStorage.getItem('entityUuid')!;
  const bpId = (businessPartnerId === 0 || businessPartnerId === '' || businessPartnerId == null) ? null : +businessPartnerId;

  const requestData = {
    fromDate,
    toDate,
    entityId,
    businessPartnerId: bpId,
    entityUUID
  };

  this.invoiceService.getInvoiceCashListReport(requestData).subscribe(
    data => {
      const base64String = data.response;
      if (base64String) {
        this.loadPdfIntoIframe(base64String);
      } else {
        this.isLoading = false;
        alert('No Sales Receivables Report data for preview.');
      }
    },
    error => {
      this.isLoading = false;
      alert('Error loading Sales Receivables Report preview.');
    }
  );
}
  
// The download report function now uses the stored base64String
downloadReport(base64String: string, fileName: string, reportType: string) {
  const link = document.createElement('a');
  let source;

  if (reportType === 'pdf') {
    source = `data:application/pdf;base64,${base64String}`;
  } else if (reportType === 'excel') {
    source = `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${base64String}`;
  } else {
    return;
  }

  link.href = source;
  link.download = `${fileName}.${reportType === 'pdf' ? 'pdf' : 'xlsx'}`;
  link.click();
}

downloadAs(reportType: 'pdf' | 'excel') {
  if (!this.base64String) {
    Swal.fire({
      title: 'Error',
      text: 'No data available for download.',
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#be0032',
    });
    return;
  }

  const fileName = 'InvoiceCashListReport';
  this.downloadReport(this.base64String, fileName, reportType);
}



 private loadPdfIntoIframe(base64String: string) {
      if (base64String && base64String.trim().length >= 50) {
        const pdfData = 'data:application/pdf;base64,' + base64String;
        const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
        const iframe = this.invoiceCashListPreviewFrame.nativeElement;
    
        iframe.onload = () => {
          this.isLoading = false;
        };
    
        iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
    
        // Open modal manually using Bootstrap JS
        const modalElement = document.getElementById('simpleModal');
        const modal = new bootstrap.Modal(modalElement!);
        modal.show();
      } else {
         this.isLoading = false;
         this.swalAlerts.showSwalWarning('No Data', 'No  Sales Receivables Report data for preview.', 'No  Sales Receivables Report data was returned for the selected range.');
    
      }
    }
    

    
 loadCustomers() {
  const entityId = +((localStorage.getItem('entityId')) + '');
  this.businessPartnerService.getCustomerListByEntity(entityId).subscribe(
    (customers: BusinessPartner[]) => {
      this.customers = customers;
    },
    (error: HttpErrorResponse) => {
      console.error('Error fetching customers', error);

      //  Use SwalAlertsService for error with Chimp support
      this.swalAlerts.showErrorWithChimpSupport(
        'Failed to load customers.',
        'Unable to fetch customer list for this entity. Please check if the customer service is responding.'
      );
    }
  );
}

  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(cust => cust.businessPartnerId === +selectedCustomerId);

    // Set the reference field
    this.invoiceHead.reference = selectedCustomer?.bpName || '';
  }


  // Method to toggle all customers' data
  toggleAllCustomers() {
    this.getAllCustomers = !this.getAllCustomers;
    if (this.getAllCustomers) {
      this.invoiceHead.businessPartnerId = '';  // Clear selected customer when showing all
    }
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}

