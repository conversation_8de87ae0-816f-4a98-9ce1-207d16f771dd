import { Component, OnInit } from '@angular/core';
import { BankRuleDTO, BankRuleType } from '../bank-reconciliation';
import { BankReconciliationService } from '../bank-reconciliation.service';
import { BankAccount } from '../../bank/bank';
import { Router } from '@angular/router';

@Component({
  selector: 'app-bank-rule-list',
  templateUrl: './bank-rule-list.component.html',
  styleUrls: ['./bank-rule-list.component.css'],
})
export class BankRuleListComponent implements OnInit {
  selectedBankAccount!: BankAccount;
  activeTab: 'spend' | 'receive' = 'spend';
  spendRuleList: BankRuleDTO[] = [];
  receiveRuleList: BankRuleDTO[] = [];

  constructor(
    private bankRecService: BankReconciliationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.selectedBankAccount = history.state?.selectedBankAccount;

    if (!this.selectedBankAccount) {
      this.router.navigate(['/bank-account-list']);
      return;
    }

    this.loadBankRules();
  }

  navigateToBankRuleCreation(activeTab: 'spend' | 'receive') {
    if (!activeTab) {
      console.error('No Active Tab selected');
      return;
    }
    if (this.selectedBankAccount) {
      this.router.navigate(['/create-bank-rule'], {
        state: {
          selectedBankAccount: this.selectedBankAccount,
          activeTab: activeTab,
        },
      });
    } else {
      this.router.navigate(['/bank-account-list']);
    }
  }

  private loadBankRules() {
    const entityId = +(localStorage.getItem('entityId') || 0);

    if (!entityId) {
      console.warn('EntityId not Found');
      return;
    }
    this.spendRuleList = [];
    this.receiveRuleList = [];

    this.bankRecService
      .loadBankRulesByBankAccountIdAndEntityId(
        entityId,
        this.selectedBankAccount.bankAccountId
      )
      .subscribe(
        (ruleList: BankRuleDTO[]) => {
          ruleList.forEach((rule) => {
            if (rule.bankRuleHead.bankRuleType === BankRuleType.SPEND) {
              this.spendRuleList.push(rule);
            } else if (
              rule.bankRuleHead.bankRuleType === BankRuleType.RECEIVE
            ) {
              this.receiveRuleList.push(rule);
            }
          });
        },
        (error) => {
          console.error(error);
        }
      );
  }
}
