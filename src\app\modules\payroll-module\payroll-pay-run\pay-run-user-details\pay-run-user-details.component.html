<app-payroll-nevigation></app-payroll-nevigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <div class="actions sub-container">
    <h2 class="my-auto">Pay Run</h2>
    <div class="vertical-line my-auto"></div>
    <h2 class="my-auto">Details</h2>
  </div>

  <div>
    <!-- Main Content -->
    <div class="Leave">
      <div class="pay_item_head">
        <div class="pay_item_title">
          <h2>{{ payCalendarName }}</h2>
          <h3>{{ payStartDate }} - {{ payEndDate }}</h3>
        </div>
        <div class="delete-payrun">
          <button
            *ngIf="payRunStatus === 'UNSCHEDULED'"
            class="report"
            data-bs-toggle="modal"
            data-bs-target="#AddEmployeeModal"
            (click)="fetchUnscheduledEmployees()"
          >
            Add employees
          </button>
          <button
            *ngIf="payRunStatus !== 'POSTED'"
            class="post"
            (click)="postPayrun()"
            [disabled]="isSubmitting"
          >
            {{ isSubmitting ? "Posting..." : "Post" }}
          </button>
          <button
            *ngIf="payRunStatus !== 'POSTED'"
            class="delete-button"
            (click)="deletePayRun()"
          >
            Delete
          </button>
          <button
            *ngIf="payRunStatus === 'POSTED'"
            class="summary"
            data-bs-toggle="modal"
            data-bs-target="#simpleModal"
            (click)="previewPayRunSummary()"
          >
            Summary
          </button>
          <button
            *ngIf="payRunStatus === 'POSTED'"
            class="report"
            data-bs-toggle="modal"
            data-bs-target="#employeeDetailModal"
            (click)="previewPayrollSummary()"
          >
            Report
          </button>

          <button
            *ngIf="payRunStatus === 'POSTED'"
            class="report"
            data-bs-toggle="modal"
            data-bs-target="#bankPaymentsModal"
            (click)="previewBankPaymentsReport()"
          >
            Bank Report
          </button>

          <button
            *ngIf="payRunStatus === 'POSTED'"
            class="report"
            data-bs-toggle="modal"
            data-bs-target="#bankPaymentsModal"
            (click)="previewBankJournalReport()"
          >
            Journal Report
          </button>
        </div>
      </div>

      <div class="menu-row">
        <div class="menu-sub-col">
          <div class="menu-topic">Employees</div>
          <div class="menu-value">
            {{ payRun.totalEmployee | number : "3.0-0" }}
          </div>
        </div>

        <div class="line"></div>

        <div class="menu-sub-col">
          <div class="menu-topic">Earning</div>
          <div class="menu-value">{{ payRun.earnings | number : "1.2-4" }}</div>
        </div>

        <div class="line"></div>

        <div class="menu-sub-col">
          <div class="menu-topic">Deduction</div>
          <div class="menu-value">
            {{ payRun.deductions | number : "1.2-4" }}
          </div>
        </div>

        <div class="line"></div>

        <div class="menu-sub-col">
          <div class="menu-topic">Reimbursement</div>
          <div class="menu-value">
            {{ payRun.reimbursements | number : "1.2-4" }}
          </div>
        </div>

        <div class="line"></div>

        <div class="menu-sub-col">
          <div class="menu-topic">Superannuation</div>
          <div class="menu-value">
            {{ payRun.superAmount | number : "1.2-4" }}
          </div>
        </div>

        <div class="line"></div>

        <div class="menu-sub-col">
          <div class="menu-topic">PAYG</div>
          <div class="menu-value">{{ payRun.tax | number : "1.2-4" }}</div>
        </div>

        <div class="line"></div>

        <div class="menu-sub-col">
          <div class="menu-topic">Net Pay</div>
          <div class="menu-value">{{ payRun.netPay | number : "1.2-4" }}</div>
        </div>

        <!-- <div class="line"></div> -->

        <!-- <div class="menu-sub-col">
                    <div class="menu-topic">Status</div>
                    <div class="menu-value">3000</div>
                </div> -->
      </div>

      <!-- <button>hiran</button> -->
      <div *ngIf="payPeriod?.status !== 'UNSCHEDULED'">
        <div
          class="table-responsive"
          *ngIf="payProcesses.length > 0; else noEmployeesMessage"
        >
          <table class="table table-hover">
            <thead>
              <tr>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Earnings</th>
                <th>Deductions</th>
                <th>Reimbursements</th>
                <th>Super</th>
                <th>Tax</th>
                <th>Net Pay</th>
                <th *ngIf="payRunStatus !== 'POSTED'">Exclude</th>
                <th *ngIf="payRunStatus === 'POSTED'">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let process of payProcesses
                    | paginate : { itemsPerPage: 5, currentPage: leaveTwoPage }
                "
                (click)="
                  payRunStatus !== 'POSTED' &&
                    !process.excluded &&
                    navigateToPayTemplate(process.employeeId)
                "
                [class.disabled-row]="process.excluded"
              >
                <td>{{ process.firstName }}</td>
                <td>{{ process.lastName }}</td>
                <td>{{ process.totalEarnings | number : "1.2-4" }}</td>
                <td>{{ process.totalDeductions | number : "1.2-4" }}</td>
                <td>{{ process.totalReimbursements | number : "1.2-4" }}</td>
                <td>{{ process.superannuation | number : "1.2-4" }}</td>
                <td>{{ process.tax | number : "1.2-4" }}</td>
                <td>{{ process.netPay | number : "1.2-4" }}</td>
                <td *ngIf="payRunStatus !== 'POSTED'">
                  <input
                    type="checkbox"
                    [(ngModel)]="process.excluded"
                    (click)="$event.stopPropagation()"
                    (change)="
                      onCheckboxClick(process.employeeId, process.excluded)
                    "
                  />
                </td>
                <td>
                  <button
                    (click)="
                      navigateToPaySlip(process.employeeId);
                      $event.stopPropagation()
                    "
                    *ngIf="payRunStatus === 'POSTED'"
                    type="button"
                    class="viewPayslip-btn"
                  >
                    View PaySlip
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <pagination-controls
            class="d-flex justify-content-end"
            (pageChange)="leaveTwoPage = $event"
          >
          </pagination-controls>
        </div>
        <ng-template #noEmployeesMessage>
          <div class="alert alert-warning text-center" role="alert">
            There are no employees for this period.
          </div>
        </ng-template>
      </div>

      <div class="table-responsive" *ngIf="payPeriod?.status === 'UNSCHEDULED'">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>First Name</th>
              <th>Last Name</th>
              <th>Earnings</th>
              <th>Deductions</th>
              <th>Reimbursements</th>
              <th>Super</th>
              <th>Tax</th>
              <th>Net Pay</th>
              <th *ngIf="payRunStatus !== 'POSTED'">Exclude</th>
              <th *ngIf="payRunStatus === 'POSTED'">Action</th>
            </tr>
          </thead>
          <tbody>
            <ng-container 
            *ngFor="let process of payRunDetailsSummaryList | paginate : { itemsPerPage: 5, currentPage: leaveTwoPage }">
              <tr
                *ngIf="process.employee"
                (click)="
                  payRunStatus !== 'POSTED' &&
                    !process.excluded &&
                    navigateToPayTemplate(process.employee.employeeId)
                "
                [class.disabled-row]="process.excluded"
              >
                <td>{{ process.employee.firstName }}</td>
                <td>{{ process.employee.lastName }}</td>
                <td>{{ process.earningsTotal | number : "1.2-4" }}</td>
                <td>
                  {{ process.deductionsTotal | number : "1.2-4" }}
                </td>
                <td>
                  {{ process.reimbursementsTotal | number : "1.2-4" }}
                </td>
                <td>
                  {{ process.superannuationTotal | number : "1.2-4" }}
                </td>
                <td>{{ process.taxTotal | number : "1.2-4" }}</td>
                <td>{{ process.netPay | number : "1.2-4" }}</td>
                <td *ngIf="payRunStatus !== 'POSTED'">
                  <input
                    type="checkbox"
                    [(ngModel)]="process.excluded"
                    (click)="$event.stopPropagation()"
                    (change)="
                      onCheckboxClick(
                        process.employee.employeeId,
                        process.excluded
                      )
                    "
                  />
                </td>
                <td>
                  <button
                    (click)="
                      navigateToPaySlip(process.employee.employeeId);
                      $event.stopPropagation()
                    "
                    *ngIf="payRunStatus === 'POSTED'"
                    type="button"
                    class="viewPayslip-btn"
                  >
                    View PaySlip
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>

        <pagination-controls
          class="d-flex justify-content-end"
          (pageChange)="leaveTwoPage = $event"
        >
        </pagination-controls>
      </div>
    </div>
  </div>

  <!-- 
  <div
    class="modal fade"
    id="AddPayRun"
    tabindex="-1"
    aria-labelledby="Ordinary-Time_Earnings"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="ot-modal-header">
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
            style="margin-left: 95%"
            (click)="navigateToPayrollPayRun()"
          >
            <i class="bi bi-x-circle"></i>
          </button>
          <div class="modal-header">
            <h5
              class="modal-title"
              id="add_pay_calendar"
              style="margin-left: 30%"
            >
              Add PayRun
            </h5>
          </div>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="earnings-name" class="form-label"
                >Select Pay Period</label
              >
              <select
                name="earningsGlAccount"
                class="form-control"
                id="pay-period"
                [(ngModel)]="selectedPayPeriod"
              >
                <option value="" disabled selected>Select a Pay Period</option>
                <option
                  *ngFor="let period of payPeriods"
                  [value]="period.payPeriodId"
                >
                  {{ period.payCalendar.calendarName }}
                </option>
              </select>
            </div>
          </form>

          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
              (click)="navigateToPayrollPayRun()"
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              [disabled]="!selectedPayPeriod"
              (click)="onPayRunAdd()"
            >
              Add
            </button>
          </div>
        </div>
      </div>
    </div>
  </div> -->

  <!--  Summary Preview -->
  <div class="container">
    <div
      class="modal fade"
      id="simpleModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="simpleModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered custom-modal">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="simpleModalLabel">PayRun Summary</h5>
            <button
              type="button"
              class="btn-close"
              aria-label="Close"
              data-bs-dismiss="modal"
            >
              <i class="bi bi-x-circle"></i>
            </button>
          </div>
          <div class="modal-body">
            <!--Loading Spinner -->
            <div *ngIf="isLoading" class="spinner-container">
              <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </div>

            <!--  IFrame for Invoice Preview -->
            <div class="iframe-container" [ngClass]="{ 'd-none': isLoading }">
              <iframe
                #payRunSummaryPreviewFrame
                id="payRunSummaryPreviewFrame"
                width="700px"
                height="700px"
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Report Preview Modal -->
  <div class="container">
    <div
      class="modal fade"
      id="employeeDetailModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="employeeDetailLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered custom-modal">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="employeeDetailLabel">
              Payroll Employee Detail Summary
            </h5>
            <button
              type="button"
              class="btn-close"
              aria-label="Close"
              data-bs-dismiss="modal"
            >
              <i class="bi bi-x-circle"></i>
            </button>
          </div>
          <div class="modal-body">
            <!-- Loading Spinner -->
            <div *ngIf="isLoading" class="spinner-container">
              <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </div>

            <!-- IFrame for Report Preview -->
            <div class="iframe-container" [hidden]="isLoading">
              <iframe
                #reportPreviewFrame
                id="reportPreviewFrame"
                width="700px"
                height="700px"
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bank Payments Report Preview Modal -->
  <div class="container">
    <div
      class="modal fade"
      id="bankPaymentsModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="bankPaymentsLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered custom-modal">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="bankPaymentsLabel">
              Bank Payments Report
            </h5>
            <button
              type="button"
              class="btn-close"
              aria-label="Close"
              data-bs-dismiss="modal"
            >
              <i class="bi bi-x-circle"></i>
            </button>
          </div>
          <div class="modal-body">
            <!-- Loading Spinner -->
            <div *ngIf="isLoading" class="spinner-container">
              <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </div>

            <!-- IFrame for Bank Report Preview -->
            <div class="iframe-container" [hidden]="isLoading">
              <iframe
                #bankReportPreviewFrame
                id="bankReportPreviewFrame"
                width="700px"
                height="700px"
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Unscheduled Employee Modal -->
  <div
    class="modal fade"
    id="AddEmployeeModal"
    tabindex="-1"
    aria-labelledby="AddEmployeeModalLabel"
    aria-hidden="true"
    #AddEmployeeModal
  >
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <!-- Styled Header with Custom Close Button -->
        <div class="ot-modal-header">
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
            style="margin-left: 95%"
            (click)="clearSelection()"
          >
            <i class="bi bi-x-circle"></i>
          </button>
          <div class="modal-header">
            <h5
              class="modal-title"
              id="AddEmployeeModalLabel"
              style="margin-left: 30%"
            >
              Add Employee
            </h5>
          </div>
        </div>

        <div class="modal-body">
          <form>
            <!-- Dropdown -->
            <div class="mb-3">
              <label for="employeeDropdown" class="form-label"
                >Select Employee</label
              >
              <select
                class="form-select w-100"
                id="employeeDropdown"
                [(ngModel)]="selectedEmployeeId"
                name="employee"
                required
              >
                <option value="0" disabled selected>Select an employee</option>
                <option
                  *ngFor="let emp of unScheduledEmployeeDropDownList"
                  [value]="emp.employee.employeeId"
                >
                  {{ emp.employee.firstName }} {{ emp.employee.lastName }}
                </option>
              </select>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
            (click)="clearSelection()"
          >
            Cancel
          </button>
          <button
            type="button"
            class="btn btn-primary"
            [disabled]="!selectedEmployeeId"
            (click)="onAddEmployee()"
          >
            Add
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
