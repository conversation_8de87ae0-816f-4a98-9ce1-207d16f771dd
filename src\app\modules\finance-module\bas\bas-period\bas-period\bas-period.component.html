<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
    <div class="heading-section">
        <h1>BAS Period</h1>
    </div>

    <div class="button-section">
        <button type="button" class="primary-button" (click)="handleCreateBAS()" >Create New Statement</button>
        <i class="bi bi-info-circle"></i>
        <button type="button" class="primary-button">View Custom Date Report</button>
    </div>

    <!-- Needs Attention Card -->
    <div class="card mb-4">
        <div class="header-card">
            <strong>Needs attention </strong>
            <i class="bi bi-info-circle"></i>
        </div>
        <hr />
        <div class="body-card">
            <div>
                <h6>
                      {{ nextBasPeriodText }}
                    <span style="color: gray; margin-left: 10px">GST, PAYG W</span>
                </h6>
            </div>
            <button class="prepare-button" (click)="handleCreateBAS()">Prepare</button>
        </div>
    </div>

    <!-- Completed Card -->
    <div class="card">
        <div class="header-card">
            <strong>Completed</strong>
            <select class="custom-filter-select">
                <option value="" selected>Filter by tax year</option>
            </select>
        </div>
        <hr />
        <div class="group-list">
            <div *ngFor="let item of completedStatements" class="list-item">
                <div>
                    <div class="left-top-row">
                        <strong>{{ item.period }}</strong> 
                        <span style="color: gray; margin-left: 10px;">GST, PAYG W</span>
                         <span class="badge ms-2" [ngClass]="{
          'bg-success': item.status === 'Lodged' || item.status === 'Finalised',
          'bg-warning': item.status === 'Pending',
          'bg-secondary': item.status !== 'Lodged' && item.status !== 'Pending'
        }">{{ item.status }}</span>
                    </div>
                    <div class="text-small">{{ item.date }} &bull; {{ item.user }}</div>
                </div>
                <div>
                    <div class="right-top-row">
                        <strong>{{ item.amountText }}</strong> 
                        {{ item.amount | number }}
                        <a (click)="reviewBas(item.basReportId)" class="text-review">Review</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
