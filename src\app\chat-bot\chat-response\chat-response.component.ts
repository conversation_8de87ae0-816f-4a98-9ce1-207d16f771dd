import { AfterViewChecked, Component, ElementRef, ViewChild } from '@angular/core';
import { BotService } from '../bot.service';

interface ChatMessage {
  sender: string;
  text: string;
}

@Component({
  selector: 'app-chat-response',
  templateUrl: './chat-response.component.html',
  styleUrls: ['./chat-response.component.css']
})
export class ChatResponseComponent implements  AfterViewChecked {
  @ViewChild('conversationContainer') conversationContainer!: ElementRef;

  showPopup = false;
  isChatVisible = false;
  userInput = '';
  conversation: ChatMessage[] = [];
  input: string = '';
  responseData: string = '';
  response_2: string = '';
  isWaitingForResponse = false;
  private audio!: HTMLAudioElement;

  constructor(private httpServicejson: BotService) {} 

  sendData(): void {
    if (this.input && this.input.trim() && !this.isWaitingForResponse) {
      this.conversation.push({ sender: 'User', text: this.input });
      this.isWaitingForResponse = true;
      const params = { prompt: this.input };
      this.httpServicejson.request('GET', '/chat-assistant', null, params).subscribe(
        (response_assistant: any) => {
          this.conversation.push({ sender: 'Assistant', text: response_assistant });
          this.playLoadingSound();
          this.response_2 = response_assistant;
          this.isWaitingForResponse = false; 
          this.stopLoadingSound()          
        },
        error => {
          console.error('Error:', error);
          const errorMessage = 'ChatBot is not available!';
          this.conversation.push({ sender: 'Assistant', text: errorMessage });
          this.isWaitingForResponse = false;
          this.stopLoadingSound() 
        }
      );
      this.input = '';
      this.scrollToBottom(); 
    }
  }

  toggleChat(): void {
    this.isChatVisible = !this.isChatVisible;
    this.showPopup = !this.isChatVisible; 
  }
  closePopup(): void {
    this.showPopup = false;
    this.isChatVisible = false; 
  }
  scrollToBottom(): void {
    try {
      this.conversationContainer.nativeElement.scrollTop = this.conversationContainer.nativeElement.scrollHeight;
    } catch (err) {
    }
  }
  ngAfterViewChecked(): void {
    this.scrollToBottom();
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }
}
