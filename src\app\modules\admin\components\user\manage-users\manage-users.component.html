<app-admin-navigation></app-admin-navigation>
<app-logo-header></app-logo-header>
<app-chat-response></app-chat-response>

<div class="container">
  <div class="actions">
    <h1>Manage Users</h1>

    <div class="search-create">
      <button class="create-quote" (click)="navigateUserInvite()">Invite</button>
      <!--<button class="copy-quote" (click)="navigateUserRegister()">Register</button>-->
      <button class="export-btn" (click)="navigateRequests()">Request</button>
      <button class="export-btn" (click)="navigateBookkeeper()">Bookkeeper</button>
    </div>
  </div>

  <ul class="nav nav-tabs mb-3 justify-content-start">
    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'all'"
        (click)="setActiveTab('all')"
        >All</a
      >
    </li>
    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'Primary user'"
        (click)="setActiveTab('Primary user')"
        >Admin User</a
      >
    </li>
    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'General user'"
        (click)="setActiveTab('General user')"
        >Basic User</a
      >
    </li>

    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'Accountant'"
        (click)="setActiveTab('Accountant')"
        >Accountant</a
      >
    </li>
  </ul>

  <div class="table-wrapper">
    <table>
      <thead>
        <tr class="table-head">
          <th scope="col" class="valuehead">First Name</th>
          <th scope="col" class="valuehead">Last Name</th>
          <th scope="col" class="valuehead">Email</th>
          <th scope="col" class="valuehead">User Type</th>
          <!--<th style="text-align: center;" scope="col" class="valuehead">Actions</th>-->
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let user of filteredUsers; let i = index">
          <td class="value">{{ user.firstName }}</td>
          <td class="value">{{ user.lastName }}</td>
          <td class="value">{{ user.username }}</td>
          <td class="value">
            {{
              user.userTypeId.userType === "Primary user"
                ? "Admin User"
                : user.userTypeId.userType === "General user"
                ? "Basic User"
                : user.userTypeId.userType === "Accountant"
                ? "Adviser – Accountant/Book keeper"
                : user.userTypeId.userType
            }}
          </td>

          <!--<td style="text-align: center;" class="value">

               
                      <button class="btn btn-danger btn-sm" 
                            (click)="deleteUser(user. userId)" 
                            style="margin-right: 2px; border: none; background: none; padding: 4px; font-size: 1.2rem;" title="Delete">
                            <i class="ri-delete-bin-line" style="color: #FF0000;"></i>
                        </button>
                </td>-->
        </tr>
      </tbody>
    </table>
  </div>
</div>
