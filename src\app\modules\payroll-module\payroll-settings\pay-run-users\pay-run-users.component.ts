import { Component } from '@angular/core';
import { PayRunService } from '../services/pay-run.service';
import { ReimbursementsService } from '../services/reimbursements.service';
import { DeductionService } from '../services/deduction.service';
import { Router } from '@angular/router';
import { EarningService } from '../services/earning.service';
import { PayCalendarService } from '../services/pay-calendar.service';

@Component({
  selector: 'app-pay-run-users',
  templateUrl: './pay-run-users.component.html',
  styleUrls: ['./pay-run-users.component.css']
})
export class PayRunUsersComponent {
  activeTab: string = 'DraftPayRun';

  constructor(private payCalendarService: PayCalendarService,private earningService: EarningService, private router: Router, private deductionService: DeductionService,private reimbursementsService: ReimbursementsService, private payRunMasterService: PayRunService) {}

  setActiveTab(tab: string) {
    this.activeTab = tab;
    if (tab === 'employee') {
      this.router.navigate(['/payroll-settings-employee']);

    }
  }

  isActiveTab(tab: string): boolean {
    return this.activeTab === tab;
  }

  activeSide: string = 'Earnings';

  isActiveSide(side: string): boolean {
    return this.activeSide === side;
  }

  setActiveSide(side: string): void {
    this.activeSide = side;
  }

  activeSides: string = 'DraftPayRun';

  isActiveSides(side: string): boolean {
    return this.activeSides === side;
  }

  setActiveSides(side: string): void {
    this.activeSides = side;
  }
}
