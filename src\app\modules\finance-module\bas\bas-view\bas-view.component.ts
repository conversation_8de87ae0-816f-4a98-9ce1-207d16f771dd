import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BillService } from '../../bill/bill.service';
import { BasReport } from '../bas';

@Component({
  selector: 'app-bas-view',
  templateUrl: './bas-view.component.html',
  styleUrls: ['./bas-view.component.css']
})
export class BasViewComponent implements OnInit {
  basId!: number;
  basSummary: any = {};
  fromDate!: string;
  toDate!: string;

  constructor(private route: ActivatedRoute, private billService: BillService, ) {}

  ngOnInit() {
    this.basId = +this.route.snapshot.paramMap.get('id')!;
    this.loadBasReport();
  }

loadBasReport() {
  this.billService.getBasReportById(this.basId).subscribe({
    next: (data: BasReport) => {
      this.basSummary = {
        G1: data.gstOnSales,
        G10: data.gstOnPurchases,
        G11: data.capitalPurchase,
        G12: data.totalPurchase,
        '1A': data.gstCollected,
        '1B': data.gstPaid,
        g1FreeIncome: 0,       // if you have these in API response, map accordingly
        g10FreeExpenses: 0     // else keep zero or fetch similarly
      };
      this.fromDate = data.periodStart;
      this.toDate = data.periodEnd;
    },
    error: (err) => {
      console.error('Failed to load BAS', err);
    }
  });
}
}
