import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { InvoiceDetail, InvoiceHead, SaveInvoiceHeadDTO } from '../invoice';
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';
import { InvoiceService } from '../invoice.service';
import { QuotationService } from '../../quotation/quotation.service';
import {
  QuotationDetail,
  QuoteHead,
  SalesItem,
} from '../../quotation/quotation';
import { Entity, EntityTradingName } from '../../entity/entity';
import { EntityService } from '../../entity/entity.service';
import {
  BusinessPartner,
  BusinessPartnerType,
} from '../../business-partner/business-partner';
import { HttpErrorResponse } from '@angular/common/http';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { NgForm } from '@angular/forms';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { catchError, debounceTime, map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { StorageService } from '../../entity/storage.service';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { PeriodClosingService } from '../../finance-module/period-closing/period-closing.service';
import { CoaLedgerAccount } from '../../finance-module/bill/bill';
import { BillService } from '../../finance-module/bill/bill.service';

@Component({
  selector: 'app-create-invoice',
  templateUrl: './create-invoice.component.html',
  styleUrls: ['./create-invoice.component.css'],
})
export class CreateInvoiceComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent)
  chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  invoiceHead: InvoiceHead = new InvoiceHead();
  invoiceDetail: InvoiceDetail = new InvoiceDetail();
  quotationDetail: QuotationDetail[] = [];
  quotationHead: QuoteHead = new QuoteHead();
  invoiceHeadList: InvoiceHead[] = [];
  invoiceDetails: InvoiceDetail[] = [];
  businessPartnerType: BusinessPartnerType[] = [];
  transactionData: any;

  id: number = 0;
  businessEntityId: number = 0;
  lastInvoiceNumber: string = '';

  salesItems: SalesItem[] = [];

  itemPopUp: boolean = false;
  businessEntity: Entity = new Entity();
  newItem: SalesItem = {
    salesItemId: 0,
    entityId: 1,
    userId: 1,
    itemTypeId: 1,
    itemCode: '',
    itemName: '',
    sellingPrice: 0,
    standardDiscount: 0,
    salesAccount: '',
    taxApplicability: '',
    itemStatus: 'active',
    description: '',
    unitPrice: 0,
    amount: 0,
    transactionDate: '2023-07-15',
  };

  unitPriceInvalid: boolean = false;
  itemCode: string = '';
  taxApplicable: boolean = false;
  businessPartner: BusinessPartner = new BusinessPartner();
  customers: BusinessPartner[] = [];
  entityTradingNames: EntityTradingName[] = [];
  
  showTaxApplicabilityDropdown: boolean = true;
  //url = '';
  url: string | null = null;
  selectedFile: File | null = null;
  uploadedFileName: string | null = null;
  entityId: number = 0;
  userId: number = 0;
  showUpdateLink = true;
  isSaving: boolean = false;
  glAccounts: CoaLedgerAccount[] = [];
  constructor(
    private quotationService: QuotationService,
    private entityService: EntityService,
    private router: Router,
    private invoiceService: InvoiceService,
    private businessPartnerService: BusinessPartnerService,
    private storageService: StorageService,
    private swalAlertsService: SwalAlertsService,
    private periodClosingService: PeriodClosingService,
    private billService: BillService,

    // Related to Bank Rec
    @Optional() private dialogRef: MatDialogRef<CreateInvoiceComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public transactionDetails: any
  ) {
    this.transactionData = transactionDetails || null;
    console.info(this.transactionData);
    this.entityId = this.storageService.getEntityId();
    this.userId = this.storageService.getUserId();
  }

  ngOnInit() {
    this.setTodayDate();
    this.loadCustomers();
    this.getBusinessEntityById();
    this.getEntityTradingNamesByEntityId();
    this.fetchAllSalesItems();
    this.addEmptyRows(1);
    this.fetchGlAccounts();

    if (!this.entityId) {
      this.showTaxApplicabilityDropdown = false;
      this.newItem.taxApplicability = 'no';
      return;
    }

    this.entityService.getBusinessEntityById(this.entityId).subscribe(
      (entity: Entity) => {
        this.showTaxApplicabilityDropdown = entity.taxApplicability === 'yes';
        this.newItem.taxApplicability = entity.taxApplicability || 'no';
      
      },
      (error) => {
        console.error('Error fetching entity:', error);
        this.showTaxApplicabilityDropdown = false;
        this.newItem.taxApplicability = 'no';
      }
    );

      const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      const userType = user.userType; // assuming userType is here

      if (userType === 'General user' || userType === 'Accountant') {
        this.showUpdateLink = false;
      }
    }
  }

  
    fetchGlAccounts() {
      const entityId = +localStorage.getItem('entityId')!;
      this.billService
        .getActiveCoaLedgerAccountListByEntitySales(entityId)
        .subscribe(
          (glAccounts: CoaLedgerAccount[]) => {
            this.glAccounts = glAccounts;
          },
          (error: HttpErrorResponse) => {
            console.error('Error fetching GL Accounts', error);
            Swal.fire({
              title: 'Error!',
              text: 'Failed to load GL Accounts.',
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#be0032',
              cancelButtonColor: '#007bff',
              showCancelButton: true,
            }).then((result) => {
              if (
                result.isDismissed &&
                result.dismiss === Swal.DismissReason.cancel
              ) {
                if (this.chatBotComponent) {
                  Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while Chimp processes your request.',
                    allowOutsideClick: false,
                    didOpen: () => {
                      Swal.showLoading();
                      this.chatBotComponent.setInputData(
                        'Failed to load gl Accounts.'
                      );
                      this.chatBotComponent.responseReceived.subscribe(
                        (response) => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound();
                        }
                      );
                    },
                  });
                } else {
                  console.error('ChatBotComponent is not available.');
                }
              }
            });
          }
        );
  
      // Initialize ledgerAccountName for each item in invoiceDetails
      this.invoiceHead.invoiceDetails.forEach((detail) => {
        detail.ledgerAccountName = '';
      });
    }

    onGLChange(event: any, index: number) {
    const selectedAccountId = +event.target.value; // Get the selected ID
    const selectedAccount = this.glAccounts.find(
      (account) => account.coaLedgerAccountId === selectedAccountId
    ); // Find the selected account object

    if (selectedAccount) {
      // Update the corresponding detail with the selected account's information
      this.invoiceHead.invoiceDetails[index].coaLedgerAccountId =
        selectedAccount.coaLedgerAccountId;
      this.invoiceHead.invoiceDetails[index].ledgerAccountName =
        selectedAccount.ledgerAccountName;
       this.invoiceHead.invoiceDetails[index].ledgerAccountCode =
      selectedAccount.ledgerAccountCode;
    }
  }
  customSearchFn(term: string, item: any) {
    term = term.toLowerCase();
    const itemCode = (item.itemCode || '').toLowerCase(); // Default to empty string if undefined
    const description = (item.description || '').toLowerCase(); // Default to empty string if undefined
    return itemCode.includes(term) || description.includes(term);
  }

  addEmptyRows(count: number) {
    if (this.transactionData != null) {
      this.addNewDetailedRow(this.transactionData);
      this.updateAmount(0);
    } else {
      for (let i = 0; i < count; i++) {
        this.addNewRow();
      }
    }
  }

  addNewRow() {
    this.invoiceHead.invoiceDetails.push(this.createEmptyRow());
  }

  addNewDetailedRow(transactionDetails: any) {
    this.invoiceHead.invoiceDetails.push(
      this.createTransactionDetailsRow(transactionDetails)
    );
  }

  // Helper method to create an empty row
  createEmptyRow() {
    return {
      salesItem: new SalesItem(),
      taxCategoryId: 0,
      invoiceNumber: this.invoiceHead.invoiceNumber,
      quantity: 0,
      unitPrice: 0,
      description: '',
      discount: 0,
      tax: 0,
      amount: 0,
      discountType: '$',
      notes: '',
      subTotal: 0,
      totalDiscountPercentage: 0,
      coaLedgerAccountId:0,
      ledgerAccountName: '',
      ledgerAccountCode: '',
    };
  }

  createTransactionDetailsRow(transactiondetails: any) {
    return {
      salesItem: new SalesItem(),
      taxCategoryId: 0,
      invoiceNumber: this.invoiceHead.invoiceNumber,
      quantity: 1,
      unitPrice: transactiondetails.pendingBalance,
      description: transactiondetails.description,
      discount: 0,
      tax: 0,
      amount: 0,
      discountType: '$',
      notes: '',
      subTotal: 0,
      totalDiscountPercentage: 0,
      coaLedgerAccountId:0,
      ledgerAccountName: '',
      ledgerAccountCode: '',
    };
  }

  // New method to reset taxApplicability
  resetTaxApplicability(index: number): void {
    this.invoiceHead.invoiceDetails[index].taxApplicability = false; // Reset to unchecked
    this.invoiceHead.invoiceDetails[index].tax = 0; // Reset tax value
  }

  updateInvoiceDetails(selectedItem: SalesItem, index: number) {
    // Update the details for the selected index
    this.invoiceHead.invoiceDetails[index].salesItem = selectedItem; // Set the selected item
    this.invoiceHead.invoiceDetails[index].unitPrice = selectedItem.unitPrice; // Set the unit price
    this.invoiceHead.invoiceDetails[index].amount =
      this.invoiceHead.invoiceDetails[index].quantity * selectedItem.unitPrice; // Calculate the amount

    this.checkTaxApplicability(index);

    this.calculateSubTotal(); // Recalculate subtotal after item is selected

    // Check if this is the last row, if so, add a new row
    if (index === this.invoiceHead.invoiceDetails.length - 1) {
      // this.addNewRow(); // Add a new row for the next item
    }
  }

  onDescriptionInput(index: number) {
    // Check if this is the last row, if so, add a new row
    if (index === this.invoiceHead.invoiceDetails.length - 1) {
      // this.addNewRow();
    }
  }

  //calculation

  applyDiscount(detail: InvoiceDetail, itemAmount: number) {
    if (detail.quantity === 0) {
      detail.amount = 0;
    } else {
      if (detail.discountType === 'B') {
        detail.amount = itemAmount - itemAmount * (detail.discount / 100);
      } else if (detail.discountType === '$') {
        detail.amount = itemAmount - detail.discount;
      } else {
        detail.amount = itemAmount;
      }

      // Ensure amount doesn't go below zero
      detail.amount = Math.max(0, detail.amount);
    }
  }

  updateAmount(index: number): void {
    const detail = this.invoiceHead.invoiceDetails[index];
    const itemAmount = detail.quantity * detail.unitPrice;

    if (detail.quantity < 0 || detail.unitPrice < 0 || detail.discount < 0) {
      this.swalAlertsService.showWarning(
        'Negative values are not allowed.',
        () => {
          this.resetInvalidInput(detail);
        }
      );
      return;
    }

    if (
      detail.quantity === 0 &&
      (detail.discount > 0 || detail.discountType !== '')
    ) {
      this.swalAlertsService.showWarning(
        'Please specify a quantity first.',
        () => {
          detail.discount = 0;
          detail.discountType = '$';
          detail.amount = 0;
          detail.tax = 0;
        }
      );
      return;
    }

    this.applyDiscount(detail, itemAmount);

    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (!this.invoiceHead.invoiceDetails[index].taxApplicability) {
            this.checkTaxApplicability(index);
          }

          if (
            this.invoiceHead.invoiceDetails[index].taxApplicability &&
            this.taxApplicable
          ) {
            this.applyFlatTax(index, taxRate);
          } else {
            detail.tax = 0;
          }

          this.calculateSubTotal();
        },
        (error) => {
          console.error('Error fetching entity data:', error);
          detail.tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      detail.tax = 0;
      this.calculateSubTotal();
    }
  }

  resetInvalidInput(detail: InvoiceDetail) {
    if (detail.quantity < 0) {
      detail.quantity = 0;
    }

    if (detail.discount < 0) {
      detail.discount = 0; // Reset discount
    }

    if (detail.unitPrice < 0) {
      detail.unitPrice = 0;
    }
    detail.discount = detail.discount; // Reset discount
    detail.discountType = detail.discountType; // Reset discount type
    detail.amount = detail.amount; // Reset amount
    detail.unitPrice = detail.unitPrice;
    detail.quantity = detail.quantity;
    detail.tax = detail.tax; // Reset tax
  }

  updateDiscountType(index: number, value: string) {
    const detail = this.invoiceHead.invoiceDetails[index];
    const itemAmount = detail.quantity * detail.unitPrice;
    detail.discountType = value;

    // Reset discount amount when changing type
    detail.discount = 0;

    if (detail.quantity === 0) {
      this.swalAlertsService.showWarning(
        'Please add a quantity before applying a discount',
        () => {
          detail.discountType = '$';
        }
      );
    } else {
      // this.applyDiscount(detail, itemAmount);
      this.updateAmount(index);
    }

    this.calculateSubTotal();
  }

  updateQuantity(index: number) {
    this.updateAmount(index); // Ensure amount is updated whenever quantity changes
  }

  calculateTotalDiscount(): number {
    let totalDiscount = 0;
    this.invoiceHead.invoiceDetails.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;
      let discountAmount = 0;

      if (detail.discountType === 'B') {
        discountAmount = itemAmount * (detail.discount / 100);
      } else if (detail.discountType === '$') {
        discountAmount = detail.discount;
      }

      totalDiscount += discountAmount;
    });

    this.invoiceHead.totalDiscAmount = totalDiscount;
    return totalDiscount;
  }

  checkTaxApplicability(index: number): void {
    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          this.updateDetailsTaxApplicability(index, taxRate);
          this.calculateSubTotal();
        },
        (error) => {
          console.error('Error fetching entity data:', error);
          this.updateDetailsTaxApplicability(index, 0);
          this.calculateSubTotal();
        }
      );
    } else {
      this.updateDetailsTaxApplicability(index, 0);
      this.calculateSubTotal();
    }
  }

  updateDetailsTaxApplicability(index: number, taxRate: number): void {
    const detail = this.invoiceHead.invoiceDetails[index];
    const itemTaxApplicable = detail.salesItem.taxApplicability === 'yes';

    if (this.taxApplicable && itemTaxApplicable) {
      detail.taxApplicability = true; // Auto-check the checkbox
      detail.tax = detail.amount * (taxRate / 100);
    } else {
      detail.taxApplicability = false; // Auto-uncheck the checkbox
      detail.tax = 0;
    }
  }

  onTaxApplicableChange(index: number): void {
    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (
            this.taxApplicable &&
            this.invoiceHead.invoiceDetails[index].taxApplicability
          ) {
            this.applyFlatTax(index, taxRate);
          } else {
            this.invoiceHead.invoiceDetails[index].tax = 0;
          }

          this.calculateSubTotal();
        },
        (error) => {
          console.error('Error fetching entity data:', error);
          this.invoiceHead.invoiceDetails[index].tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      this.invoiceHead.invoiceDetails[index].tax = 0;
      this.calculateSubTotal();
    }
  }

  applyFlatTax(index: number, taxRate: number): void {
    const detail = this.invoiceHead.invoiceDetails[index];
    detail.tax = detail.amount * (taxRate / 100);
    this.calculateSubTotal();
  }

  removeItem(index: number) {
    this.salesItems.splice(index, 1); // Remove the item at the specified index
    this.invoiceHead.invoiceDetails.splice(index, 1); // Remove corresponding detail entry
    this.calculateSubTotal();
  }

  calculateSubTotal() {
    let subTotal = 0;
    let totalTax = 0;
    let totalDiscount = 0;

    this.invoiceHead.invoiceDetails.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;

      if (detail.quantity > 0) {
        this.applyDiscount(detail, itemAmount);
        subTotal += detail.amount;
        totalTax += detail.tax;
        totalDiscount +=
          detail.discountType === 'B'
            ? itemAmount * (detail.discount / 100)
            : detail.discount;
      } else {
        subTotal += 0;
        totalTax += 0;
        totalDiscount += 0;
      }
    });

    this.invoiceHead.totalAmount = subTotal;
    this.invoiceHead.totalGst = totalTax;
    this.invoiceHead.totalDiscAmount = totalDiscount;
    this.calculateGrandTotal();
  }

  calculateGrandTotal() {
    const { totalAmount, totalGst } = this.invoiceHead;
    this.invoiceHead.grandTotal = totalAmount + totalGst;
  }

  onItemSelected(selectedItem: SalesItem, index: number): void {
    if (selectedItem && selectedItem.itemCode) {
      // Check if the selected item already exists in the invoiceDetails array excluding the current index
      const existingItemIndex = this.invoiceHead.invoiceDetails.findIndex(
        (detail, i) =>
          detail.salesItem.itemCode === selectedItem.itemCode && i !== index
      );

      if (existingItemIndex !== -1) {
        // If the item exists at a different index, show a confirmation dialog
        Swal.fire({
          title: 'Item already exists',
          text: 'Do you want to add this item again?',
          icon: 'question',
          showCancelButton: true,
          confirmButtonText: 'Yes',
          confirmButtonColor: '#ff7e5f',
          cancelButtonText: 'No',
          cancelButtonColor: '#be0032',
        }).then((result) => {
          if (result.isConfirmed) {
            this.updateInvoiceDetails(selectedItem, index); // Update the current row with the selected item
            this.resetTaxApplicability(index); // Reset taxApplicability when item is selected
          } else if (result.dismiss === Swal.DismissReason.cancel) {
            // Remove the entire row if the user clicks "No"
            this.invoiceHead.invoiceDetails.splice(index, 1);
          }
        });
      } else {
        // If the item doesn't exist, directly assign it to the current index
        this.updateInvoiceDetails(selectedItem, index);
        this.resetTaxApplicability(index); // Reset taxApplicability when item is selected
      }
    } else {
      Swal.fire({
        title: 'Error!',
        text: 'Please select a valid item.',
        icon: 'error',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
      }).then((result) => {
        if (
          result.isDismissed &&
          result.dismiss === Swal.DismissReason.cancel
        ) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData(
                  'Please select a valid item.'
                );
                this.chatBotComponent.responseReceived.subscribe((response) => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });
      return;
    }
  }

  getEntityTradingNamesByEntityId() {
    this.businessEntityId = +(localStorage.getItem('entityId') + '');

    this.entityService
      .getEntityTradingNamesByEntityId(this.businessEntityId)
      .subscribe(
        (data) => {
          this.entityTradingNames = data;

          // If there is only one trading name, set it automatically
          if (this.entityTradingNames.length === 1) {
            this.invoiceHead.entityTradingNameId =
              this.entityTradingNames[0].entityTradingNameId;
          } else {
            this.invoiceHead.entityTradingNameId = ''; // Reset the selection for multiple options
          }
        },
        (error) => console.error(error)
      );
  }

  getBusinessEntityById() {
    this.businessEntityId = +(localStorage.getItem('entityId') + '');

    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
      (data) => {
        this.businessEntity = data;

        this.lastInvoiceNumber = this.incrementInvoiceNumber(
          this.businessEntity.invoiceNumber
        );

        this.invoiceHead.invoiceNumber = this.lastInvoiceNumber;

        // Set today's date
        const today = new Date();
        this.invoiceHead.postingDate = today.toISOString().split('T')[0]; // Format to 'YYYY-MM-DD'

        // Calculate due date
        const futureDate = new Date(today);
        const creditPeriod = parseInt(this.businessEntity.defaultCreditPeriod); // Parse string to integer
        futureDate.setDate(today.getDate() + creditPeriod);
        this.invoiceHead.dueDate = futureDate.toISOString().split('T')[0]; // Format to 'YYYY-MM-DD'
      },
      (error) => console.error(error)
    );
  }

  incrementInvoiceNumber(invoiceNumber: string): string {
    // If the invoice number is null or empty, initialize it to 'I000001'
    if (!invoiceNumber) {
      return 'I000001';
    }

    // Assuming the invoice number is in the format 'I000039'
    const prefix = invoiceNumber.charAt(0); // Extract the 'I'
    const numericPart = invoiceNumber.slice(1); // Extract the '000039'

    // Increment the numeric part and pad with leading zeros to maintain the format
    const incrementedNumber = (Number(numericPart) + 1)
      .toString()
      .padStart(numericPart.length, '0');

    // Combine the prefix and the incremented number
    return prefix + incrementedNumber;
  }

  validateUnitPrice() {
    if (this.newItem.unitPrice < 0) {
      this.unitPriceInvalid = true;
    } else {
      this.unitPriceInvalid = false;
    }
  }

  filteredSalesItems: SalesItem[] = [];
  allSalesItems: SalesItem[] = [];
  showDropdown: boolean = false;

  // Show all items when focus is gained on the search bar
  showAllItems() {
    this.filteredSalesItems = this.allSalesItems; // Show all items
    this.showDropdown = true; // Show dropdown
  }

  // Search function with filter logic
  onSearch(event: any) {
    const searchTerm = event.target.value.toLowerCase();

    // If search term exists, filter items
    if (searchTerm) {
      this.filteredSalesItems = this.allSalesItems.filter(
        (item) =>
          item.itemCode.toLowerCase().includes(searchTerm) ||
          item.description.toLowerCase().includes(searchTerm)
      );
    } else {
      // If no search term, show all items
      this.filteredSalesItems = this.allSalesItems;
    }
    this.showDropdown = true; // Keep the dropdown open
  }

  // Item selection method
  selectItem(item: SalesItem) {
    this.itemCode = item.itemCode;
    this.filteredSalesItems = []; // Clear the filtered items
    this.showDropdown = false; // Hide the dropdown
    this.fetchItemByCode(); // Fetch the item by its code
  }

  // Clear search and hide dropdown when focus is lost
  hideDropdown() {
    setTimeout(() => {
      this.showDropdown = false;
    }, 200); // Delay to allow selection to occur before hiding
  }

  fetchAllSalesItems() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.quotationService.getAllSalesItemsByEntity(entityId).subscribe(
      (items: SalesItem[]) => {
        this.allSalesItems = items.filter(
          (item) => item.itemCode !== 'SISSERVICE'
        ); // Store all items
        this.filteredSalesItems = items; // Initialize filtered items with all items
      },
      (error: any) => {
        console.error('Error fetching items', error);
        Swal.fire({
          title: 'Error!',
          text: 'Unable to fetch items.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Unable to fetch items.');
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
    );
  }

  fetchItemByCode() {
    if (!this.itemCode) {
      Swal.fire({
        title: 'Error!',
        text: 'Please enter an item code.',
        icon: 'error',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
      }).then((result) => {
        if (
          result.isDismissed &&
          result.dismiss === Swal.DismissReason.cancel
        ) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData(
                  'Please enter an item code.'
                );
                this.chatBotComponent.responseReceived.subscribe((response) => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });
      return;
    }

    const existingItemIndex = this.salesItems.findIndex(
      (item) => item.itemCode === this.itemCode
    );
    if (existingItemIndex !== -1) {
      Swal.fire({
        title: 'Item already exists',
        text: 'Do you want to add this item again?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        confirmButtonColor: '#ff7e5f',
        cancelButtonText: 'No',
        cancelButtonColor: '#be0032',
      }).then((result) => {
        if (result.isConfirmed) {
          this.addItemByCode();
        }
      });
    } else {
      this.addItemByCode();
    }
  }

  addItemByCode() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.quotationService
      .getSalesItemByCodeAndEntityId(this.itemCode, entityId)
      .subscribe(
        (item: SalesItem) => {
          if (item) {
            // Check if there is an empty row (where both salesItem and description are empty)
            const emptyRowIndex = this.invoiceHead.invoiceDetails.findIndex(
              (detail) =>
                (!detail.salesItem || detail.salesItem.itemCode === '') &&
                (!detail.description || detail.description.trim() === '')
            );

            if (emptyRowIndex !== -1) {
              // If an empty row exists, populate it with the new item details
              this.invoiceHead.invoiceDetails[emptyRowIndex] = {
                salesItem: item,
                taxCategoryId: 0,
                invoiceNumber: this.invoiceHead.invoiceNumber,
                quantity: 0,
                unitPrice: item.unitPrice,
                description: '',
                discount: 0,
                tax: 0,
                amount: 0,
                discountType: '$',
                notes: '',
                subTotal: 0,
                totalDiscountPercentage: 0,
                coaLedgerAccountId:0,
                ledgerAccountName: '',
                ledgerAccountCode: '',
              };
            } else {
              // If no empty row, push a new row with item details
              this.invoiceHead.invoiceDetails.push({
                salesItem: item,
                taxCategoryId: 0,
                invoiceNumber: this.invoiceHead.invoiceNumber,
                quantity: 0,
                unitPrice: item.unitPrice,
                description: '',
                discount: 0,
                tax: 0,
                amount: 0,
                discountType: '$',
                notes: '',
                subTotal: 0,
                totalDiscountPercentage: 0,
                coaLedgerAccountId:0,
                ledgerAccountName: '',
                ledgerAccountCode: '',
              });
            }

            this.calculateSubTotal();
            this.addNewRow();
          } else {
            Swal.fire({
              title: 'Error!',
              text: 'Item not found.',
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#be0032',
              cancelButtonColor: '#007bff',
              showCancelButton: true,
            }).then((result) => {
              if (
                result.isDismissed &&
                result.dismiss === Swal.DismissReason.cancel
              ) {
                if (this.chatBotComponent) {
                  Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while Chimp processes your request.',
                    allowOutsideClick: false,
                    didOpen: () => {
                      Swal.showLoading();
                      this.chatBotComponent.setInputData('Item not found.');
                      this.chatBotComponent.responseReceived.subscribe(
                        (response) => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound();
                        }
                      );
                    },
                  });
                } else {
                  console.error('ChatBotComponent is not available.');
                }
              }
            });
            return;
          }
        },
        (error: any) => {
          console.error('Error fetching item', error);
          Swal.fire({
            title: 'Error!',
            text: 'Error fetching item.',
            icon: 'error',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#be0032',
            cancelButtonColor: '#007bff',
            showCancelButton: true,
          }).then((result) => {
            if (
              result.isDismissed &&
              result.dismiss === Swal.DismissReason.cancel
            ) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData('Error fetching item.');
                    this.chatBotComponent.responseReceived.subscribe(
                      (response) => {
                        Swal.close();
                        this.chatResponseComponent.showPopup = true;
                        this.chatResponseComponent.responseData = response;
                        this.playLoadingSound();
                        this.stopLoadingSound();
                      }
                    );
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
          return;
        }
      );
  }

  loadCustomers() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.businessPartnerService.getCustomerListByEntity(entityId).subscribe(
      (customers: BusinessPartner[]) => {
        this.customers = customers;
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching customers', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load customers.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
          cancelButtonText: 'Ask Chimp',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Failed to load customers.'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
    );
    this.invoiceHead.businessPartnerId = '';
  }

  loadBusinessPartnerTypes() {
    this.businessPartnerService.getBusinessPartnerTypesList().subscribe(
      (businessPartnerType: BusinessPartnerType[]) => {
        // Find the "Customer" type from the list
        const customerType = businessPartnerType.find(
          (type) => type.businessPartnerType.toLowerCase() === 'customer'
        );

        if (customerType) {
          // Assign the customer type to the businessPartner object
          this.businessPartner.businessPartnerTypeId.businessPartnerTypeId =
            customerType.businessPartnerTypeId;
        }

        // Optionally store the filtered list if needed
        this.businessPartnerType = businessPartnerType;
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching Business Partner Type', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load Business Partner Type.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Failed to load Business Partner Type.'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
    );
  }

  setTodayDate() {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const dd = String(today.getDate()).padStart(2, '0');

    this.invoiceHead.postingDate = `${yyyy}-${mm}-${dd}`;
    this.updateValidityMinDate();
  }

  updateValidityMinDate() {
    const invoiceDate = this.invoiceHead.postingDate;
    if (invoiceDate) {
      const invoiceDateObj = new Date(invoiceDate);
      const yyyy = invoiceDateObj.getFullYear();
      const mm = String(invoiceDateObj.getMonth() + 1).padStart(2, '0');
      const dd = String(invoiceDateObj.getDate()).padStart(2, '0');

      const minValidUntilDate = `${yyyy}-${mm}-${dd}`;
      (document.getElementById('dueDate') as HTMLInputElement).min =
        minValidUntilDate;
    }
  }

  invoiceStatus: any;

  setStatus(status: string) {
    this.invoiceStatus = status;
  }

  onSubmit(f: NgForm) {
    // Check if the form is valid
    if (f.valid) {
      // Validate if each row has either a selected item or a description
      const hasInvalidRows = this.invoiceHead.invoiceDetails.some(
        (detail) =>
          (!detail.salesItem || !detail.salesItem.itemCode) &&
          (!detail.description || detail.description.trim() === '')
      );

      if (hasInvalidRows) {
        // If any row does not have either a sales item or a description, show an error message
        this.isSaving = false; // Reset saving state when showing error
        Swal.fire({
          title: 'Error!',
          text: 'Each row must have either an item selected or a description provided.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        }).then(() => {
          this.isSaving = false; // Reset saving state when dialog is dismissed
        });
        return; // Prevent form submission
      }

      // If all rows are valid, proceed with the form submission
      this.isSaving = true;
      this.invoiceHead.invoiceStatus = this.invoiceStatus;
      this.checkForZeroQuantity(this.invoiceStatus);
    }
  }

  checkForZeroQuantity(status: string) {
    const hasZeroQuantity = this.invoiceHead.invoiceDetails.some(
      (detail) => detail.quantity === 0
    );

    if (hasZeroQuantity) {
      Swal.fire({
        title: 'Warning!',
        text: 'Selected item(s) have zero quantity. Do you want to continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
        footer:
          '<a href="#" id="custom-link" style="background-color: #007bff; color: white; padding: 10px 24px; border-radius: 4px; text-decoration: none; font-weight: 500;">Ask Chimp</a>',
      }).then((result) => {
        if (result.isConfirmed) {
          // Proceed with saving or drafting
          this.saveOrDraftInvoice(status);
        } else {
          // Reset saving state if user cancels
          this.isSaving = false;
        }
      });

      // Attach chatbot logic to the footer link
      document
        .getElementById('custom-link')
        ?.addEventListener('click', (event) => {
          event.preventDefault(); // Prevent default anchor behavior
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData(
                  'Selected item(s) have zero quantity. Do you want to continue?'
                );
                this.chatBotComponent.responseReceived.subscribe((response) => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        });
    } else if (
      (status === 'Pending' && this.invoiceHead.invoiceDetails.length === 0) ||
      (status === 'Draft' && this.invoiceHead.invoiceDetails.length === 0)
    ) {
      this.isSaving = false; // Reset saving state when showing error
      Swal.fire({
        title: 'Error!',
        text: 'You must add at least one item.',
        icon: 'error',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
      }).then((result) => {
        if (
          result.isDismissed &&
          result.dismiss === Swal.DismissReason.cancel
        ) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData(
                  'You must add at least one item.'
                );
                this.chatBotComponent.responseReceived.subscribe((response) => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });
      return;
    } else {
      // No zero quantity, proceed with saving or drafting
      this.saveOrDraftInvoice(status);
    }
  }

  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(
      (cust) => cust.businessPartnerId === +selectedCustomerId
    );

    // Set the reference field
    this.invoiceHead.reference = selectedCustomer?.bpName || '';
  }

  
  onInvoiceDateChange() {
    this.updateValidityMinDate();
    this.validateDates();
  
    const entityId = +localStorage.getItem('entityId')!;
    const date = this.invoiceHead.postingDate;
  
    if (date) {
      this.periodClosingService.isDateLocked(entityId, date).subscribe({
        next: (isLocked: boolean) => {
          if (isLocked) {
            Swal.fire({
              icon: 'error',
              title: 'Posting Date is Locked',
              text: 'The selected date falls within a closed accounting period. Please choose another date.',
              confirmButtonColor: '#ff7e5f'
            }).then(() => {
              this.isSaving = false; // Reset saving state when dialog is dismissed
            });
  
            // Reset the posting date
              this.setTodayDate();
          }
        },
        error: (err) => {
          console.error('Error validating posting date lock', err);
        }
      });
    }
  }
  onValidUntilDateChange(): void {
    this.validateDates();
  }

  validateDates(): boolean {
    const postingDate = new Date(this.invoiceHead.postingDate);
    const dueDate = new Date(this.invoiceHead.dueDate);

    if (dueDate < postingDate) {
      this.isSaving = false; // Reset saving state when showing error
      Swal.fire({
        title: 'Invalid Date',
        text: 'The Due Date cannot be before the "Posting Date".',
        icon: 'warning',
        confirmButtonText: 'OK',
      }).then(() => {
        this.isSaving = false; // Reset saving state when dialog is dismissed
      });
      return false;
    }
    return true;
  }



  onInvoiceFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;
      this.uploadedFileName = file.name;

      const imageTypes = [
        'image/jpeg',
        'image/png',
        'image/jpg',
        'image/webp',
        'image/gif',
      ];
      const isImage = imageTypes.includes(file.type);

      if (isImage) {
        const reader = new FileReader();
        reader.onload = (e: any) => {
          this.url = e.target.result;
        };
        reader.readAsDataURL(file);
      } else {
        this.url = null; // Clear preview for non-image files
      }
    }
  }

  saveOrDraftInvoice(status: string) {
    this.invoiceHead.balanceAmount = this.invoiceHead.grandTotal;
    this.invoiceHead.invoiceStatus = status; // Set status based on parameter
    this.invoiceHead.userId = +(localStorage.getItem('userid') + '');
    this.invoiceHead.entityId = +(localStorage.getItem('entityId') + '');

    const firstName = localStorage.getItem('firstName');
    const lastName = localStorage.getItem('lastName');
    this.invoiceHead.createdBy = `${firstName} ${lastName}`;

    // Validate dates before proceeding
    if (!this.validateDates()) {
      return; // Stop execution if dates are invalid
    }

    // Create SaveInvoiceHeadDTO
    const invoiceDTO: SaveInvoiceHeadDTO = {
      invoiceHead: this.invoiceHead,
      autoPaymentReceipt: this.dialogRef ? true : false,
    };

    this.invoiceService.saveInvoice(invoiceDTO).subscribe(
      (response: any) => {
        this.invoiceHead = response;
        this.id = this.invoiceHead.invoiceHeadId;
        this.updateBusinessEntityInvoiceNumber();

        if (this.selectedFile) {
          this.invoiceService
            .saveInvoiceHeadFile(
              this.invoiceHead.invoiceHeadId,
              this.selectedFile
            )
            .subscribe((response) => {
              this.invoiceHead = response;
            });
        }

        let alertTitle = '';
        let alertText = '';

        if (status === 'Draft') {
          alertTitle = 'Draft Saved';
          alertText =
            'The Invoice was saved as a draft. You can resume and save later.';
        } else if (status === 'Pending') {
          alertTitle = 'Success!';
          alertText = 'The Invoice saved Successfully.';
        }
        this.router.navigate([`/view-invoice/${this.id}`]);
        Swal.fire({
          title: alertTitle,
          text: alertText,
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then((result) => {
          if (result.isConfirmed) {
            if (this.dialogRef) {
              // Create a payment receipt here
              this.closeDialog(true,this.invoiceHead);
            } else {
              
            }
          }
          this.isSaving = false;
        });
      },
      (error: HttpErrorResponse) => {
        console.error('Error saving invoice', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save invoice.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Failed to save invoice.');
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });

        this.isSaving = false;
        if (this.dialogRef) {
          this.closeDialog(false);
        }
        return;
      }
    );
  }

  updateBusinessEntityInvoiceNumber() {
    this.businessEntityId = +(localStorage.getItem('entityId') + '');
    this.businessEntity.invoiceNumber = this.invoiceHead.invoiceNumber;
    this.entityService
      .updateInvoiceNumber(this.businessEntity, this.businessEntityId)
      .subscribe(
        (data) => {},
        (error) => {
          console.error(error);
        }
      );
  }


  //item
  onItemAdded(savedItem: SalesItem) {
  this.fetchAllSalesItems(); 
  this.itemCode = savedItem.itemCode; 
}
  

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

  closeDialog(status: boolean, invoiceHead?: InvoiceHead) {
    this.dialogRef.close({ success: status, invoice: invoiceHead });
  }
}
