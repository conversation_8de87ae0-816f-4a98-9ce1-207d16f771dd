import { Component } from '@angular/core';
import { forkJoin } from 'rxjs';
import { GlPostingDetails, GlPostingHead } from '../../journal-voucher/journal-voucher';
import { JournalVoucherService } from '../../journal-voucher/journal-voucher.service';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';

@Component({
  selector: 'app-gl-view-each-account',
  templateUrl: './gl-view-each-account.component.html',
  styleUrls: ['./gl-view-each-account.component.css']
})
export class GlViewEachAccountComponent {
  quotes:  GlPostingHead[] = []; 
  quotesDetails: GlPostingDetails[] = []; 
  selectedQuotesDetails: GlPostingDetails[] = [];
  processedData: any[] = []; 
  filteredRecords: any[] = [];
  businessPartners: BusinessPartner[] = [];
  accountCode: string | null = null; 
  businessPartner:  number | null = null;
  businessPartnerDetails?: BusinessPartner | null;
  searchTerm: string = ''; 
  startDate: string | null = null; 
  endDate: string | null = null; 
  activeTab = 'all';

  constructor(
    private journalVoucherService: JournalVoucherService,
    private businessPartnerService: BusinessPartnerService,
    public sanitizer: DomSanitizer,
    private route: ActivatedRoute, 
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.accountCode = JSON.parse(params['selectedAccountCode']); 
      this.businessPartner = JSON.parse(params['selectedBusinessPartner']);

       // Optional from/to date if passed
    const from = params['from'];
    const to = params['to'];

    if (from && to) {
      this.startDate = from;
      this.endDate = to;
    }
    });
        
          forkJoin({
            businessPartners: this.businessPartnerService.getBusinessPartnerList(),
            quotesDetails: this.journalVoucherService.getGlPostingDetailsList(
              parseInt(localStorage.getItem('entityId') || '0', 10)
            ),
            glPostingHead: this.journalVoucherService.getGlPostingHeadList(
              parseInt(localStorage.getItem('entityId') || '0', 10)
            ),
          }).subscribe(
            ({ businessPartners, quotesDetails, glPostingHead }) => {
              this.businessPartners = businessPartners;
              this.quotesDetails = quotesDetails;
              this.quotes = glPostingHead;
        
              if (this.accountCode) {
                this.selectedQuotesDetails = this.quotesDetails.filter(detail =>
                  detail.coaLedgerAccount?.ledgerAccountCode == this.accountCode
                );
              }
        
              this.processedData = this.aggregateData(this.quotes, this.selectedQuotesDetails);
              this.filteredRecords = this.processedData;
              console.log("Processed Data: ",this.processedData);
            },
            error => console.error('Error fetching data:', error)
          );
        }

        aggregateData(glPostingHead: GlPostingHead[], glPostingDetails: GlPostingDetails[])  {

          const mergedData = glPostingHead.map(head => {
            const filteredDetails = glPostingDetails.filter(detail => 
              detail.glTransactionId?.glTransactionId === head.glTransactionId
            );
          
            return {
              businessPartnerId: head.businessPartnerId,
              details: filteredDetails
            };
          });
          
          console.log("mergedData:", mergedData);

          const supplierDetails = mergedData.filter(mergeDetail => 
            mergeDetail.businessPartnerId === this.businessPartner 
          );

          this.businessPartnerDetails = this.businessPartners.find(partner =>
            partner.businessPartnerId === this.businessPartner
          ) || null;
      
          console.log("supplierDetails:", supplierDetails);

          return supplierDetails;
    
        }

        onSearchChange() {
          this.filterQuotes();
        }

        setActiveTab(tab: string) {
          this.activeTab = tab;
          this.filterQuotes();
        }

        filterQuotes() {
          const searchTermLower = this.searchTerm?.toLowerCase().trim() || '';
          let filtered = this.processedData;
        
          // Filter by active tab
          if (this.activeTab !== 'all') {
            filtered = filtered.filter(record =>
              record.details?.some((transaction: any) => 
                transaction.glTransactionId?.status?.toLowerCase() === this.activeTab
              )
            );
          }
        
          // Filter by search term
          if (searchTermLower) {
            filtered = filtered.filter(record =>
              record.details?.some((transaction: any) =>
                String(transaction.glTransactionId?.glTransactionId).toLowerCase().includes(searchTermLower)
              )
            );
          }
        
          // Filter by start date
          if (this.startDate) {
            const startDate = new Date(this.startDate);
            filtered = filtered.filter(record =>
              record.details?.some((transaction: any) =>
                new Date(transaction.glTransactionId?.date) >= startDate
              )
            );
          }
        
          // Filter by end date
          if (this.endDate) {
            const endDate = new Date(this.endDate);
            filtered = filtered.filter(record =>
              record.details?.some((transaction: any) =>
                new Date(transaction.glTransactionId?.date) <= endDate
              )
            );
          }
        
          this.filteredRecords = filtered;
        }
        
        resetFilters() {
          this.searchTerm = '';
          this.activeTab = 'all';
          this.startDate = '';
          this.endDate = '';
          this.filteredRecords = this.processedData;
        }

}
