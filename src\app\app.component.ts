import { Component } from '@angular/core';
import { AuthService } from './auth.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  title = 'LedgerChimp';
constructor(private authService: AuthService) {
  if (this.authService.isLoggedIn()) {
    this.authService.startInactivityTimer(); // ✅ This will now add listeners
  }
}

}
