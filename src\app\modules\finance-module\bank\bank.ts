export class BankAccount {
    bankAccountId: number = 0;
    openingBalanceId: number = 0;
    entityId: number = 0;
    userId: number = 0;
    accountType: string = '';
    bankName: string = '';
    accountName: string = '';
    accountNumber: string = '';
    bsbNo: string = '';
    balanceType: string = '';
    balance: number = 0.00;
    statementBalance: number = 0.00;
    systemBalance: number = 0.00;
    basiqAccountId: string = '';
    basiqConnectionId: string = '';
    coaLedgerAccountId?:number;
}
