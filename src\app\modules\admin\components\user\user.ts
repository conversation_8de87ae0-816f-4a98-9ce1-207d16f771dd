export class User {
  userId: number = 0;
  firstName: string = '';
  lastName: string = '';
  password: string = '';
  username: string = '';
  email: string = '';
  status: string = '';
  entityIds: number[] = [];
  userTypeId: UserType = new UserType();
  subscriptionPlanId?: number; 
}

export class UserType {
  userTypeId: any = 0;
  userType: string = '';
}


export class AccountantDto{
   firstName: string = '';
  lastName: string = '';
  username: string= '';
}
