import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { FiscalPeriod } from './fiscal-period';

@Injectable({
  providedIn: 'root',
})
export class FiscalPeriodService {
  private readonly baseURL = environment.financeApiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  getFiscalPeriodList(): Observable<FiscalPeriod[]> {
    return this.request('GET', '/fiscal-periods/list', null);
  }

  createFiscalPeriodForEntity(entityId: number): Observable<FiscalPeriod> {
    return this.request('POST', '/fiscal-periods/for-entity', null, {
      entityId: entityId,
    });
  }
}
