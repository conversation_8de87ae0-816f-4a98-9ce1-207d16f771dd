import { Compo<PERSON>, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgForm } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';

// Third-party libraries
import { Observable, map, catchError, of, Subscription, fromEvent } from 'rxjs';

// Services

import { HttpService } from 'src/app/http.service';
import { EntityService } from '../../entity/entity.service';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { StorageService } from '../../entity/storage.service';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';

// Models

import { Entity, EntityTradingName } from '../../entity/entity';
import { BusinessPartner, BusinessPartnerType } from '../../business-partner/business-partner';
import { EmailTemplate, EmailTemplateService } from '../../settings/email-template.service';
import { InvoiceHead, InvoiceLog } from '../invoice';
import { SalesItem } from '../../quotation/quotation';
import { InvoiceService } from '../invoice.service';
import { DomSanitizer } from '@angular/platform-browser';
import Swal from 'sweetalert2';


@Component({
  selector: 'app-view-invoice',
  templateUrl: './view-invoice.component.html',
  styleUrls: ['./view-invoice.component.css']
})

export class ViewInvoiceComponent implements OnInit {
  @ViewChild('sendInvoice') sendInvoice!: NgForm;


  quotationData: InvoiceHead = {} as InvoiceHead;
  quotes: InvoiceHead[] = [];
  customers: BusinessPartner[] = [];
  businessPartner: BusinessPartner = new BusinessPartner();
  quotationStatus: any;
  emailTemplates: any[] = [];
  invoiceTemplate: any = null;
  subject: string = '';
  content: string = '';
  templateHtmlContent: string = '';
  recipientEmail: string = ''; // Loaded from the database
  TempRecipientEmail: string = '';
  filteredQuotes: InvoiceHead[] = [];
  businessEntityId: number = 0;
  entityTradingNames: EntityTradingName[] = [];
  salesItems: SalesItem[] = [];
  allSalesItems: SalesItem[] = [];
  businessEntity: Entity = new Entity();
  isSending: boolean = false;
   invoiceLogs: InvoiceLog[] = [];
    showLogs: boolean = false;
  id: number = 0;
  entityId: number = 0;
  userId: number = 0;
  url: string | null = null;
  uploadedFileName: string | null = null;
   userRole: string = '';
  constructor(
    
    private route: ActivatedRoute,
    private invoiceService: InvoiceService,
    private router: Router,
    private emailTemplateService: EmailTemplateService,
    private entityService: EntityService,
    private businessPartnerService: BusinessPartnerService,
    private storageService: StorageService,
    private swalAlertsService: SwalAlertsService,
    public sanitizer: DomSanitizer

  ) {
    this.entityId = this.storageService.getEntityId();
    this.userId = this.storageService.getUserId();
  }


  ngOnInit() {
    this.id = this.route.snapshot.params['id'];
    this.getBusinessEntityById();
    this.getEntityTradingNamesByEntityId();
    this.loadCustomers();
    this.getInvoiceHeadById();
    this.getInvoiceDetailsByInvoiceHeadId();
    this.getAllInvoiceLogsByInvoiceHeadId();
    this.getInvoiceHeadListByEntity();

     const userStr = localStorage.getItem('user');
    if (userStr) {
    const user = JSON.parse(userStr);
    this.userRole = user.roleName; // e.g., "Free", "Premium"
  }
  }

  getBusinessEntityById() {

    this.businessEntityId = this.entityId;

    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(data => {
      this.businessEntity = data;

    }, error => console.error(error));

  }

  getAllInvoiceLogsByInvoiceHeadId() {

    this.invoiceService.getAllInvoiceLogsByInvoiceHeadId(this.id).subscribe(data => {
      this.invoiceLogs = data;

    }, error => console.error(error));

  }

  
  getInvoiceHeadById() {

    this.invoiceService.getInvoiceHeadById(this.id).subscribe(data => {
      this.quotationData = data;
          // If image file exists
      if (data.fileBase64) {
        this.uploadedFileName = 'Uploaded Image';
        this.url = 'data:image/png;base64,' + data.fileBase64;
      }

    }, error => console.error(error));

  }


  
  getInvoiceDetailsByInvoiceHeadId() {
    this.quotationData.invoiceDetails = [];
    this.salesItems = [];
    this.invoiceService.getInvoiceDetailsByInvoiceHeadId(this.id).subscribe(data => {

      this.quotationData.invoiceDetails = data;
      this.salesItems = this.quotationData.invoiceDetails.map(detail => detail.salesItem);

    }, error => console.error(error));

  }


  //dropdown
  isDropdownOpen = false;
  
    @ViewChild('dropdownRef') dropdownRef!: ElementRef;
  
    toggleDropdown(): void {
      this.isDropdownOpen = !this.isDropdownOpen;
    }
  
    closeDropdown() {
      this.isDropdownOpen = false;
    }
  
        
    // Listen for clicks on the whole document
          @HostListener('document:click', ['$event'])
          handleClickOutside(event: MouseEvent) {
            if (
              this.dropdownRef && !this.dropdownRef.nativeElement.contains(event.target) 
            ) {
              this.closeDropdown();
            }
          }
  

  handleReviseInvoice(id: number) {
    this.invoiceService.getInvoiceHeadById(id).subscribe(invoice => {
      if (invoice.invoiceStatus === 'Sent') {
        this.router.navigate(['/revise-invoice', id]);
      } else {
        this.swalAlertsService.showWarning("Only invoices with status 'Sent' can be revised.", () => {});
      }
    }, error => {
      console.error("Error fetching invoice:", error);
      this.swalAlertsService.showErrorDialog("Failed to fetch invoice details.");
    });
  }
  
  editInvoice(id: number) {
    this.invoiceService.getInvoiceHeadById(id).subscribe(invoice => {
      if (invoice.invoiceStatus === 'Pending') {
        this.router.navigate(['/update-invoice', id]);
      } else {
        this.swalAlertsService.showWarning("Only invoices with status 'Pending' can be edited.", () => {});
      }
    }, error => {
      console.error("Error fetching invoice:", error);
      this.swalAlertsService.showErrorDialog("Failed to fetch invoice details.");
    });
  }
  

  handleCreateInvoice() {
    this.router.navigate(['/create-invoice']);

  }


  handleSendInvoice() {
    this.router.navigate(['/create-invoice']);

  }

 
  onCreateCreditNote(id: number): void {
    const revised = this.quotes.filter(inv => inv.invoiceStatus === 'Revised');

    if (revised.length > 0) {
      this.swalAlertsService.showError('Error!', 'The selected invoice is in Revise status and cannot be sent.');
      return;
    }
    
  this.router.navigate(['/create-credit-note'], {
    queryParams: { invoiceIds: JSON.stringify([id]) }
  });
}

  onPaymentreceipt(id: number): void {
    const revised = this.quotes.filter(inv => inv.invoiceStatus === 'Revised');

    if (revised.length > 0) {
      this.swalAlertsService.showError('Error!', 'The selected invoice is in Revise status and cannot be sent.');
      return;
    }

  this.router.navigate(['/create-payment-receipt'], {
    queryParams: { invoiceIds: JSON.stringify([id]) }
  });
}


  @ViewChild('invoicePreviewFrame') invoicePreviewFrame!: ElementRef;
  isLoading = false;
  private invoiceCache = new Map<number, string>();



  previewInvoice(invoiceId: number) {
    this.isLoading = true;

    const cachedBase64String = this.invoiceCache.get(invoiceId);
    if (cachedBase64String) {
      this.loadPdfIntoIframe(cachedBase64String);
      return;
    }
    const entityId = +((localStorage.getItem('entityId')) + "");
    const entityUuid = localStorage.getItem('entityUuid');

  if (!entityUuid) {
    this.isLoading = false;
    alert('Missing entity UUID.');
    return;
  }

    this.invoiceService.getInvoiceReport(invoiceId, entityId, entityUuid).subscribe(
      data => {
        const base64String = data.response;

        if (base64String) {
          this.invoiceCache.set(invoiceId, base64String);
          this.loadPdfIntoIframe(base64String);
        } else {
          this.isLoading = false;
          alert('No invoice data for preview.');
        }
      },
      error => {
        this.isLoading = false;
        alert('Error loading invoice preview.');
      }
    );
  }

  private loadPdfIntoIframe(base64String: string) {
    const pdfData = 'data:application/pdf;base64,' + base64String;
    const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
    const iframe = this.invoicePreviewFrame.nativeElement;
    iframe.onload = () => {
      this.isLoading = false;
    };
    iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
  }

  modalHideSubscription?: Subscription;

  ngAfterViewInit() {
    const modalElement = document.getElementById('invoicePreviewModal');
    if (modalElement) {
      this.modalHideSubscription = fromEvent(modalElement, 'hide.bs.modal').subscribe(() => {
      
      });
    }
  }

  ngOnDestroy() {
    if (this.modalHideSubscription) {
      this.modalHideSubscription.unsubscribe();
    }
  }


  loadRecipientEmail(): void {
  if (this.quotationData?.businessPartnerId) {
    this.businessPartnerService
      .getBusinessPartnerById(this.quotationData.businessPartnerId)
      .subscribe(
        (businessPartner) => {
          if (businessPartner?.email) {
            this.recipientEmail = businessPartner.email;
            this.TempRecipientEmail = businessPartner.email; // 👈 populate input
          } else {
            this.recipientEmail = '';
            this.TempRecipientEmail = ''; // clear input
            console.warn('No email found for the selected business partner.');
          }
        },
        (error) => {
          console.error('Error fetching recipient email:', error);
          this.recipientEmail = '';
          this.TempRecipientEmail = ''; // clear input
        }
      );
  }
}

  
/** 
  loadEmailTemplate(): void {
    
    this.emailTemplateService.getEmailTemplateByEntityId(this.entityId).subscribe(
      (data: EmailTemplate[]) => {
        if (data?.length > 0) {
          this.emailTemplates = data;
          this.invoiceTemplate = this.emailTemplates.find(template => template.emailType === 'invoice');

          if (this.invoiceTemplate && this.quotationData) {
            // Use `this.quotationData` as the selected quote
            this.subject = this.invoiceTemplate.subject.replace('${invoiceNumber}', this.quotationData.invoiceNumber);
            this.loadRecipientEmail();

          //  Parse HTML content
            const parser = new DOMParser();
            const htmlDocument = parser.parseFromString(this.invoiceTemplate.content, 'text/html');
            this.templateHtmlContent = this.invoiceTemplate.content;
            this.content = htmlDocument.body.textContent || "";
          }
        }
      }
    );
  }**/



    loadEmailTemplate(): void {
      this.emailTemplateService.getEmailTemplateByEntityId(this.entityId).subscribe(
        (data: EmailTemplate[]) => {
          if (data?.length > 0) {
            this.emailTemplates = data;
            this.invoiceTemplate = this.emailTemplates.find(template => template.emailType === 'invoice');
    
            if (this.invoiceTemplate && this.quotationData) {
              this.subject = this.invoiceTemplate.subject.replace('${invoiceNumber}', this.quotationData.invoiceNumber);
              this.loadRecipientEmail();
    
              // Fetch business partner details before replacing placeholders
              this.businessPartnerService.getBusinessPartnerById(this.quotationData.businessPartnerId).subscribe(
                (businessPartner) => {
                  const businessPartnerName = businessPartner?.bpName || 'Valued Customer';
                  const invoiceNumber = this.quotationData.invoiceNumber || 'N/A';
    
                   const today = new Date();
                   const currentYear = today.getFullYear().toString();
                 
                  // Replace placeholders in email content
                  this.templateHtmlContent = this.invoiceTemplate.content
                    .replace('${businessPartnerName}', businessPartnerName)
                    .replace('${invoiceNumber}', invoiceNumber)
                    .replace('${currentYear}', currentYear);

    
                  const parser = new DOMParser();
                  const htmlDocument = parser.parseFromString(this.templateHtmlContent, 'text/html');
                  this.content = htmlDocument.body.textContent || "";
                },
                (error) => {
                  console.error('Failed to fetch business partner details:', error);
                  this.templateHtmlContent = this.invoiceTemplate.content.replace('${businessPartnerName}', 'Valued Customer');
                }
              );
            }
          }
        }
      );
    }
    
     private getInvoiceHeadListByEntity() {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.invoiceService.getInvoiceHeadListByEntity(entityId).subscribe(data => {
      this.quotes = data;
      
    });
  }

  @ViewChild('closePreview') closePreview: any;
  @ViewChild('closeSendInvoice') closeSendInvoice: any;
  sendSelectedInvoices(): void {

    const userString = localStorage.getItem('user');
        if (!userString) {
          // Handle case where user is not found
          return;
        }
    
        const currentUser = JSON.parse(userString);
        const userRole = currentUser.roleName;
        const userId = currentUser.id;
    
        if (userRole === 'Free') {
          const currentDate = new Date();
          const currentMonth = currentDate.getMonth();
          const currentYear = currentDate.getFullYear();
    
          // ✅ Count invoices created by this user with status "Sent" this month
          const sentInvoicesThisMonth = this.quotes.filter(invoice => {
            const createdDate = new Date(invoice.postingDate);
            return (
              invoice.userId === userId &&
              invoice.invoiceStatus === 'Sent' &&
              createdDate.getMonth() === currentMonth &&
              createdDate.getFullYear() === currentYear
            );
          }).length;
    
          if (sentInvoicesThisMonth >= 5) {
    
            Swal.fire({
              title: 'Limit Reached!',
              text: 'As a Free user, you can only send up to 5 invoices per month.',
              icon: 'info',
              confirmButtonText: 'Upgrade Plan',
              confirmButtonColor: '#007bff',
            }).then((result) => {
              if (result.isConfirmed) {
    
                window.location.href = '/manage-subscription';
              }
            });
    
            return;
          }
        }
    
    
    let selectedQuotes: InvoiceHead[] = [];

    if (this.quotationData) {
      // If a quote is loaded in the view, use it as the selected quote
      selectedQuotes = [this.quotationData];
    } else {
      // Otherwise, use manually selected quotes from the list
      selectedQuotes = this.filteredQuotes.filter(quote => quote.selected);
    }

    if (selectedQuotes.length === 0) {
      this.swalAlertsService.showWarning('Please select a Invoice to send.', () => { });
      return;
    }

    const pendingQuotes = selectedQuotes.filter(quote => quote.invoiceStatus === 'Pending' || quote.invoiceStatus === 'Sent' || quote.invoiceStatus === 'Paid' || quote.invoiceStatus === 'Overdue' || quote.invoiceStatus === 'Awaiting Payment'); 
   
   const canceled = selectedQuotes.filter(inv => inv.invoiceStatus === 'Canceled');
    const revised = selectedQuotes.filter(inv => inv.invoiceStatus === 'Revised');
  
    if (canceled.length > 0) {
      this.swalAlertsService.showError('Error!', 'The Selected invoice is already canceled and cannot be sent.');
      return;
    }
  
    if (revised.length > 0) {
      this.swalAlertsService.showError('Error!', 'The selected invoice is in Revise status and cannot be sent.');
      return;
    }

    if (pendingQuotes.length === 0) {
      this.swalAlertsService.showWarning('No Valid Invoice selected for sending.', () => { });
      return;
    }

    if (!this.TempRecipientEmail) {
      this.swalAlertsService.showErrorDialog('Please enter a recipient email address.');
      return;
    }

    this.swalAlertsService.showConfirmationDialog(
      'Send Invoice',
      `Do you want to send the selected Invoice to ${this.TempRecipientEmail}?`,
      () => this.processQuoteSending(pendingQuotes)
    );
  }


  private processQuoteSending(pendingQuotes: InvoiceHead[]): void {
    this.isSending = true;

    Promise.all(
      pendingQuotes.map(quote => this.businessPartnerService.getBusinessPartnerById(quote.businessPartnerId).toPromise())
    )
      .then(businessPartners => {
        businessPartners.forEach((businessPartner, index) => {
          if (businessPartner) {
            pendingQuotes[index].recipient = businessPartner.email;
          
        //Dynamically replace placeholders in the template
        this.templateHtmlContent = this.templateHtmlContent
        .replace('${businessPartnerName}', businessPartner.bpName)
        .replace('${invoiceNumber}', pendingQuotes[index].invoiceNumber);

     
          } else {
            console.warn('Business partner not found for Invoice:', pendingQuotes[index].invoiceHeadId);
          }
        });

        const finalContent = this.templateHtmlContent.replace(
          /<body[^>]*>.*<\/body>/is,
          `<body><p>${this.content.replace(/\n/g, '</p><p>')}</p></body>`
        );

        const emailData = {
          TempRecipientEmail: this.TempRecipientEmail,
          subject: this.subject,
          content: finalContent,
          invoices: pendingQuotes,
        };

        const entityUuid = localStorage.getItem('entityUuid');
         const entityId = +((localStorage.getItem('entityId')) + "");
        
          if (!entityUuid) {
            alert('Missing entity UUID.');
            return;
          }

        this.invoiceService.sendInvoicesWithEmail(emailData, entityUuid,entityId).subscribe(
          () => this.handleSuccessfulSend(),
          (error) => this.handleSendError('Failed to send Invoices.', error)
        );
      })
      .catch(error => this.handleSendError('Failed to fetch business partner details.', error));
  }

  private handleSuccessfulSend(): void {
    this.swalAlertsService.showSuccessDialog('Success!', 'Invoices sent successfully.', () => {
      this.isSending = false;
      this.closeSendInvoice?.nativeElement?.click();
      this.closePreview?.nativeElement?.click();

      //this.fetchQuotations();
      this.router.navigate(['/invoice']);
    });
  }

  private handleSendError(message: string, error?: any): void {
    console.error(message, error);
    this.isSending = false;
    this.swalAlertsService.showErrorDialog(message);
  }



  loadCustomers() {
    this.businessPartnerService.getCustomerListByEntity(this.entityId).subscribe(
      (customers: BusinessPartner[]) => {
        this.customers = customers;
      },
      (error: HttpErrorResponse) => {
        this.handleApiError("Failed to load customers.");
      }
    );
    this.quotationData.businessPartnerId = "";
  }


  /** Entity **/
  getEntityTradingNamesByEntityId() {
    this.businessEntityId = this.entityId;

    this.entityService.getEntityTradingNamesByEntityId(this.businessEntityId).subscribe(data => {
      this.entityTradingNames = data;

    }, error => console.error(error));
  }


/** 
  loadQuotationData() {
    const id = +this.route.snapshot.paramMap.get('id')!;
    this.invoiceService.getSalesQuotesHeadById(id).subscribe(
      (data: InvoiceHead) => {
        this.quotationData = data;
        this.loadQuotationDetails(id);
      },
      (error) => this.handleApiError('Failed to fetch quotation data.', error)
    );
  }

  loadQuotationDetails(id: number) {
    this.quotationData.details = [];
    this.salesItems = [];

    this.invoiceService.getQuoteDetailsByQuoteHeadId(id).subscribe(
      (details: QuotationDetail[]) => {
        this.quotationData.details = details;
        this.salesItems = details.map(detail => detail.salesItemId);
      },
      (error) => this.handleApiError('Failed to fetch quotation details.', error)
    );
  }
**/

  calculateTotalDiscount(): number {
    let totalDiscount = 0;

      if (!this.quotationData?.invoiceDetails || this.quotationData.invoiceDetails.length === 0) {
    this.quotationData.totalDiscAmount = 0;
    return 0;
  }
  
    this.quotationData.invoiceDetails.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;
      let discountAmount = 0;

      if (detail.discountType === 'B') {
        discountAmount = itemAmount * (detail.discount / 100);
      } else if (detail.discountType === '$') {
        discountAmount = detail.discount;
      }

      totalDiscount += discountAmount;
    });

    this.quotationData.totalDiscAmount = totalDiscount;
    return totalDiscount;
  }

  handleApiError(errorMessage: string, error: any = null) {
    this.swalAlertsService.showErrorDialog(errorMessage);
  }


}
