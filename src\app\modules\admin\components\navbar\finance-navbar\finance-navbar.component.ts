import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Entity, EntityTradingName } from 'src/app/modules/entity/entity';
import { EntityService } from 'src/app/modules/entity/entity.service';

@Component({
  selector: 'app-finance-navbar',
  templateUrl: './finance-navbar.component.html',
  styleUrls: ['./finance-navbar.component.css']
})
export class FinanceNavbarComponent implements OnInit {

    isDropdownOpen = false;
    isDropdownOpen1 = false;
    entity: Entity = new Entity();
    entityTradingName: EntityTradingName = new EntityTradingName();
    entityId: number = 0;
    logoUrl: string | null = null;
  
    constructor(private router: Router, private entityService: EntityService) {}
  
    ngOnInit(): void {
      this.entityId = JSON.parse(localStorage.getItem('entityId') || '[]');
      this.loadEntityData();
    }
  
    private loadEntityData(): void {
      this.getEntity();
    }
  
    private getEntity(): void {
      this.entityService.getBusinessEntityByIdCached(this.entityId).subscribe((data) => {
        this.entity = data;
        this.logoUrl = this.entity.logo ? `data:image/png;base64,${this.entity.logo}` : null;
      });
    }
  
    toggleDropdown(): void {
      this.isDropdownOpen = !this.isDropdownOpen;
      if (this.isDropdownOpen) {
        this.isDropdownOpen1 = false; // Close Reports dropdown if Subscription is opened
      }
    }
  
    toggleDropdown1(): void {
      this.isDropdownOpen1 = !this.isDropdownOpen1;
      if (this.isDropdownOpen1) {
        this.isDropdownOpen = false; // Close Subscription dropdown if Reports is opened
      }
    }
  
    onStatementChange(event: Event): void {
      const selectedStatement = (event.target as HTMLSelectElement).value;
      if (selectedStatement === 'supplier') {
        this.navigateSupplierStatementReport();
      } else if (selectedStatement === 'bs') {
        this.navigateBSStatementReport();
      }else if (selectedStatement === 'pnl') {
        this.navigatePnlStatementReport();
      }else if (selectedStatement === 'basloadge') {
        this.navigateBAS();
      }
      this.isDropdownOpen1 = false;
    }

    onListingsChange(event: Event): void {
      const selectedStatement = (event.target as HTMLSelectElement).value;
      if (selectedStatement === 'jv') {
        this.navigateJVReport();
      } else if (selectedStatement === 'bill') {
        this.navigateBillReport();
      }else if (selectedStatement === 'creditNote') {
        this.navigateCreditNoteReport();
      }else if (selectedStatement === 'payments') {
        this.navigatePaymentReport();
      }
      this.isDropdownOpen1 = false;
    }
  
    navigateDashboard(): void {
      this.router.navigate(['/dashboard']);
    }
  
    navigateQuotation(): void {
      this.router.navigate(['/quotation']);
    }
  
    navigateInvoice(): void {
      this.router.navigate(['/invoice']);
    }
  
    navigateCreditNote(): void {
      this.router.navigate(['/credit-note']);
    }
  
    navigateRecordPayment(): void {
      this.router.navigate(['/payment-receipt']);
    }
    navigateBankReconciliation(): void {
      this.router.navigate(['/bank-account-list']);
    }
  
    navigateManageSubscription(): void {
      this.router.navigate(['/manage-subscription']);
      this.isDropdownOpen = false;
    }
  
    navigateUserInvitation(): void {
      this.router.navigate(['/invite-user']);
      this.isDropdownOpen = false;
    }
  
    navigateUserRegistration(): void {
      this.router.navigate(['/user-registration']);
      this.isDropdownOpen = false;
    }


    navigateFinanceReport(): void {
      this.router.navigate(['/finance-reports']);
    }
  
    navigateCreditNoteReport(): void {
      this.router.navigate(['/credit-note-bill-list-report']);
      this.isDropdownOpen1 = false;
    }
  
    navigateBillReport(): void {
      this.router.navigate(['/payable-bill-list-report']);
      this.isDropdownOpen1 = false;
    }
  
    navigateJVReport(): void {
      this.router.navigate(['/jv-list-report']);
      this.isDropdownOpen1 = false;
    }
  
    navigatePaymentReport(): void {
      this.isDropdownOpen1 = false;
    }
  
    navigateSupplierStatementReport(): void {
      this.router.navigate(['/supplier-statement']);
    }
    navigatePnlStatementReport(): void {
      this.router.navigate(['/pnl-report']);
    }

    navigateBSStatementReport(): void {
      this.router.navigate(['/bs-report']);
    }

    navigateBAS(): void {
      this.router.navigate(['/bas-period']);
    }
  
  
    navigateBusinessEntityEdit(): void {
      this.router.navigate(['/business-entity-edit']);
    }

    navigateExpenceClaims(): void {
      this.router.navigate(['/expence-claims-list']);
    }
  
    navigateJournalVoucher(): void {
      this.router.navigate(['/journal-voucher-list']);
    }
  
    navigatePaybleBill(): void {
      this.router.navigate(['/payable-bill-list']);
    }

    navigateRecordBatchPayment(): void {
      this.router.navigate(['/record-batch-payments-list']);
    }

    navigateExpencePayment(): void {
      this.router.navigate(['/payment-expenses-list']);
    }
    
    navigateCreditNoteBill(): void {
      this.router.navigate(['/credit-note-bill-list']);
    }

    navigateGeneralPosting(): void {
      this.router.navigate(['/gl-posting-list']);
    }

    navigateGlReport(): void {
      this.router.navigate(['/gl-report']);
      this.isDropdownOpen1 = false;
    }

}
