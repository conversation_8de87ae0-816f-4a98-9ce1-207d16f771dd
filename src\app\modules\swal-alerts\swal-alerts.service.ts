import { Injectable, ViewChild} from '@angular/core';
import Swal from 'sweetalert2';

//Components
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';

// Types for better type safety
export interface SwalConfig {
  title: string;
  text: string;
  icon: 'success' | 'error' | 'warning' | 'info' | 'question';
  confirmButtonText?: string;
  cancelButtonText?: string;
  confirmButtonColor?: string;
  cancelButtonColor?: string;
  showCancelButton?: boolean;
  allowOutsideClick?: boolean;
}

export interface SwalResult {
  isConfirmed: boolean;
  isDismissed: boolean;
  dismiss?: any;
}

@Injectable({
  providedIn: 'root'
})
export class SwalAlertsService {
    @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
    @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
    private audio!: HTMLAudioElement;
  constructor() { }

  // Unified alert method
  private showAlert(config: SwalConfig): Promise<SwalResult> {
    return Swal.fire({
      title: config.title,
      text: config.text,
      icon: config.icon,
      confirmButtonText: config.confirmButtonText || 'OK',
      cancelButtonText: config.cancelButtonText,
      confirmButtonColor: config.confirmButtonColor || '#007bff',
      cancelButtonColor: config.cancelButtonColor || '#6c757d',
      showCancelButton: config.showCancelButton || false,
      allowOutsideClick: config.allowOutsideClick !== false
    });
  }

  // Simplified success dialog
  showSuccessDialog(title: string, message: string, callback?: () => void): Promise<SwalResult> {
    return this.showAlert({
      title,
      text: message,
      icon: 'success',
      confirmButtonColor: '#28a745'
    }).then((result) => {
      if (result.isConfirmed && callback) {
        callback();
      }
      return result;
    });
  }

  // Simplified warning dialog with chatbot support
  showWarning(message: string, callback?: () => void, chatbotPrompt?: string): Promise<SwalResult> {
    return this.showAlert({
      title: 'Warning!',
      text: message,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#007bff'
    }).then((result) => {
      if (result.isConfirmed && callback) {
        callback();
      } else if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        this.handleChatBotError(chatbotPrompt || message);
      }
      return result;
    });
  }

  // Warning with custom cancel handling
  showWarningWithCancel(message: string, onConfirm: () => void, onCancel?: () => void, chatbotPrompt?: string): Promise<SwalResult> {
    return this.showAlert({
      title: 'Warning!',
      text: message,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#007bff'
    }).then((result) => {
      if (result.isConfirmed) {
        onConfirm();
      } else if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        this.handleChatBotError(chatbotPrompt || message);
        if (onCancel) {
          onCancel();
        }
      }
      return result;
    });
  }


  // Simplified error dialog with chatbot support
  showErrorDialog(message: string, chatbotPrompt?: string): Promise<SwalResult> {
    return this.showAlert({
      title: 'Error!',
      text: message,
      icon: 'error',
      showCancelButton: true,
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#be0032',
      cancelButtonColor: '#007bff'
    }).then((result) => {
      if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        this.handleChatBotError(chatbotPrompt || message);
      }
      return result;
    });
  }

  // Simplified confirmation dialog
  showConfirmationDialog(title: string, message: string, onConfirm: () => void, onCancel?: () => void): Promise<SwalResult> {
    return this.showAlert({
      title,
      text: message,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes',
      cancelButtonText: 'No',
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#be0032'
    }).then((result) => {
      if (result.isConfirmed) {
        onConfirm();
      } else if (result.dismiss === Swal.DismissReason.cancel && onCancel) {
        onCancel();
      }
      return result;
    });
  }
  
  // Improved chatbot error handling with better error checking
  handleChatBotError(message: string): void {
    if (!this.chatBotComponent) {
      console.error("ChatBotComponent is not available.");
      // Fallback: show a simple error message
      this.showAlert({
        title: 'Service Unavailable',
        text: 'Chat support is currently unavailable. Please try again later.',
        icon: 'error'
      });
      return;
    }

    this.showAlert({
      title: "Processing...",
      text: "Please wait while Chimp processes your request.",
      icon: 'info',
      allowOutsideClick: false
    });

    Swal.showLoading();

    try {
      this.chatBotComponent.setInputData(message);
      this.chatBotComponent.responseReceived.subscribe({
        next: (response: any) => {
          Swal.close();
          if (this.chatResponseComponent) {
            this.chatResponseComponent.showPopup = true;
            this.chatResponseComponent.responseData = response;
            this.playLoadingSound();
            this.stopLoadingSound();
          }
        },
        error: (error: any) => {
          console.error('Chatbot error:', error);
          Swal.close();
          this.showAlert({
            title: 'Error',
            text: 'Failed to process your request. Please try again.',
            icon: 'error'
          });
        }
      });
    } catch (error) {
      console.error('Error setting chatbot input:', error);
      Swal.close();
      this.showAlert({
        title: 'Error',
        text: 'Failed to initialize chat support.',
        icon: 'error'
      });
    }
  }

  // Improved audio handling with error checking
  playLoadingSound(): void {
    try {
      const audio = new Audio();
      audio.src = "../assets/google_chat.mp3";
      audio.load();
      audio.play().catch(error => {
        console.warn('Could not play notification sound:', error);
      });
    } catch (error) {
      console.warn('Audio not supported or failed to initialize:', error);
    }
  }

  stopLoadingSound(): void {
    try {
      if (this.audio) {
        this.audio.pause();
        this.audio.currentTime = 0;
      }
    } catch (error) {
      console.warn('Could not stop audio:', error);
    }
  }


  
  // Generic error method (consolidated from showError)
  showError(title: string, text: string, chatbotInput?: string): Promise<SwalResult> {
    return this.showAlert({
      title,
      text,
      icon: 'error',
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#be0032',
      cancelButtonColor: '#007bff',
      showCancelButton: !!chatbotInput
    }).then(result => {
      if (chatbotInput && result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        this.handleChatBotError(chatbotInput);
      }
      return result;
    });
  }




  // Consolidated error with chatbot support (replaces showErrorWithChimpSupport)
  showErrorWithChimpSupport(message: string, chatbotPrompt: string): Promise<SwalResult> {
    return this.showError('Error!', message, chatbotPrompt);
  }

  // Consolidated warning with chatbot support (replaces showSwalWarning)
  showSwalWarning(title: string, text: string, chatbotInput: string): Promise<SwalResult> {
    return this.showAlert({
      title,
      text,
      icon: 'warning',
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#007bff',
      showCancelButton: true
    }).then(result => {
      if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        this.handleChatBotError(chatbotInput);
      }
      return result;
    });
  }

  // Utility methods for common alert patterns
  showInfo(title: string, message: string): Promise<SwalResult> {
    return this.showAlert({
      title,
      text: message,
      icon: 'info'
    });
  }

  showSimpleError(message: string): Promise<SwalResult> {
    return this.showAlert({
      title: 'Error',
      text: message,
      icon: 'error',
      confirmButtonColor: '#be0032'
    });
  }

  showSimpleSuccess(message: string): Promise<SwalResult> {
    return this.showAlert({
      title: 'Success',
      text: message,
      icon: 'success',
      confirmButtonColor: '#28a745'
    });
  }

  showSimpleWarning(message: string): Promise<SwalResult> {
    return this.showAlert({
      title: 'Warning',
      text: message,
      icon: 'warning',
      confirmButtonColor: '#ff7e5f'
    });
  }

  // Loading dialog
  showLoading(title: string = 'Loading...', message: string = 'Please wait...'): void {
    Swal.fire({
      title,
      text: message,
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });
  }

  // Close any open Swal dialog
  close(): void {
    Swal.close();
  }
}
