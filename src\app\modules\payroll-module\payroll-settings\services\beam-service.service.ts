import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpService } from 'src/app/http.service';
import { environment } from 'src/environments/environment';
import { ValidationResponse } from '../../superannuation/superannuation';
import { PayRunDetail } from '../payroll-setting';

@Injectable({
  providedIn: 'root'
})
export class BeamServiceService {

 private readonly baseURL = environment.payrollApiUrl;
 
 
   constructor(private http: HttpClient, private httpService: HttpService) { }
 
   getAuthToken(): string | null {
     return window.sessionStorage.getItem('auth_token');
   }
 
   request<T>(
    method: string,
    url: string,
    data?: any,
    params?: any
  ): Observable<T> {
    let headers = new HttpHeaders();
  
    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }
  
    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };
  
    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get<T>(this.baseURL + url, options);
      case 'POST':
        return this.http.post<T>(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put<T>(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete<T>(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }
  


    //  validateContributions(member: Member): Observable<any> {
    //      return this.request('POST', '/api/contributions/validate', member,{});
    //    }

  //   validateContributions(payRunDetails: PayRunDetail[], entityId: any): Observable<ValidationResponse> {
  //     return this.request<ValidationResponse>('POST', '/api/contributions/validate', payRunDetails, { entityId: entityId });
  // }      

  validateContributions(payRunDetails: PayRunDetail[], entityId: any): Observable<ValidationResponse[]> {
    return this.request<ValidationResponse[]>('POST', '/api/contributions/validate', payRunDetails, { entityId: entityId });
  }
  
  
  submitContributions(payRunDetails: PayRunDetail[], entityId: any): Observable<any> {
      return this.request('POST', '/api/contributions/submit', payRunDetails, {entityId: entityId});
  }
  
  cancel(beamProcessId: string, entityId: number): Observable<any> {
  let headers = new HttpHeaders();
  const token = this.getAuthToken();
  if (token) {
    headers = headers.set('Authorization', 'Bearer ' + token);
  }

  return this.http.request('PUT', `${this.baseURL}/api/contributions/cancel/${beamProcessId}/${entityId}`, {
    body: {},
    headers,
    responseType: 'text' as 'json' // ✅ This line fixes everything
  });
}

getNotifications(entityId: any): Observable<any> {
  return this.request('GET', `/api/contributions/notifications`, {}, { entityId: entityId });
}

getAndProcessNotifications(entityId: any): Observable<any> {
  return this.request('GET', `/api/contributions/notifications/process`, {}, { entityId: entityId });
}

}
