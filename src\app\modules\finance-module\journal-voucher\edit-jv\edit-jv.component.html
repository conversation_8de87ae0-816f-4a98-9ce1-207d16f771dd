<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <form
    #f="ngForm"
    (ngSubmit)="(f.form.valid)"
    class="row g-1"
    novalidate="feedback-form"
  >
    <div class="heading" (keydown)="preventSubmit($event)">
      <h3>Create Journal Voucher</h3>
    </div>
    <div class="bd">
      <div class="form-section">
        <div
          class="form-row"
          style="
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
          "
        >
          <div class="form-group" style="display: flex; flex-grow: 1">
            <label
              for="jvNumber"
              id="quotation"
              style="margin-right: 10px; white-space: nowrap"
              >JV Number</label
            >
            <input
              class="input-style"
              type="text"
              id="jvNumber"
              [(ngModel)]="glPostingHead.jvNumber"
              name="jvNumber"
              #jvNumber="ngModel"
              readonly
            />
          </div>

          <div
            class="form-group"
            style="display: flex; flex-direction: column; flex-grow: 1"
          >
            <div style="display: flex" class="form-dataInput">
              <label for="date" style="margin-right: 40px; white-space: nowrap"
                >Date</label
              >
              <input
                class="input-style"
                type="date"
                id="date"
                [(ngModel)]="glPostingHead.date"
                name="date"
                required
              />
            </div>

            <!-- Error message directly under the input field -->
            <div
              *ngIf="f.submitted && f.controls['date'].invalid"
              class="text-danger"
              style="margin-top: 5px; margin-left: 110px"
            >
              <div *ngIf="f.controls['date'].errors?.['required']">
                Date is required.
              </div>
            </div>
          </div>
        </div>

        <!-- Narration -->
        <div class="form-rows">
            <div
              class="form-group"
              style="flex-grow: 1"
            >
              <div class="narration">
                <label
                  for="description"
                  style="margin-right: 20px; white-space: nowrap"
                >
                  Narration
                </label>
                <input
                  class="input-style"
                  type="text"
                  id="description"
                  [(ngModel)]="glPostingHead.description"
                  name="description"
                  required
                />
              </div>
            </div>

            <div
              *ngIf="f.submitted && f.controls['description'].invalid"
              class="text-danger"
              style="flex-basis: 100%; margin-top: 5px"
            >
              <div *ngIf="f.controls['description'].errors?.['required']">
                Narration is required.
              </div>
            </div>
        </div>
      </div>

      <div class="table-section">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th style="width: 33%">Description</th>
                <th style="width: 17%">Account</th>
                <th style="width: 17%; text-align: center">Dr</th>
                <th style="width: 13%; text-align: center">Cr</th>
                <th style="width: 3%"></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let row of rows; let i = index">
                <td>
                  <input
                    type="text"
                    [(ngModel)]="row.description"
                    name="itemDescription-{{ i }}"
                    class="form-control"
                    placeholder="Enter Description"
                    (input)="onDescriptionInput(i)"
                  />
                </td>
                <td>
                  <select
                    class="form-control"
                    [(ngModel)]="selectedCoaLedgerAccount.coaLedgerAccountId"
                    required
                  >
                    <option value="" disabled selected>GL Account</option>
                    <option
                      *ngFor="let account of coaLedgerAccounts"
                      [value]="account.coaLedgerAccountId"
                    >
                      {{ account.ledgerAccountName }}
                    </option>
                  </select>
                </td>
                <td>
                  <input
                    type="number"
                    [(ngModel)]="row.debit"
                    name="debit-{{ i }}"
                    class="form-control"
                    placeholder="Enter Debit Amount"
                    (input)="updateTotals()"
                    [required]="!row.credit"
                  />
                </td>
                <td>
                  <input
                    type="number"
                    [(ngModel)]="row.credit"
                    name="credit-{{ i }}"
                    class="form-control"
                    placeholder="Enter Credit Amount"
                    (input)="updateTotals()"
                    [required]="!row.debit"
                  />
                </td>

                <td>
                  <button
                    type="button"
                    class="btn btn-link"
                    (click)="removeRow(i)"
                  >
                    <i class="fa fa-trash" style="color: red"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="notes-totals-section">
        <div class="totals-section">
          <div class="totals-row">
            <span class="totals-row1">Total Debit </span>
            <span class="totals-row2">{{ totalDebit | currency }}</span>
          </div>
          <div class="totals-row">
            <span class="totals-row1">Total Credit </span>
            <span class="totals-row2">{{ totalCredit | currency }}</span>
          </div>
        </div>
      </div>

      <div class="footer-section">
          <button class="add-btn" type="submit" (click)="saveJV()">Save</button>
          <button
            type="submit"
            class="cancel-btn"
            type="reset"
            (click)="navigateJVList()"
          >
            Cancel
          </button>
      </div>
    </div>
  </form>
</div>
