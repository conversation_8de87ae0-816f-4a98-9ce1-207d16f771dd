<app-home-header></app-home-header>
<body>
  <div class="container">
    <div class="form">
      <h2>Reset Password</h2>
      <p>
        Remember your password?
        <a (click)="navigateUserLogin()">Login here</a>
      </p>

      <!-- Show server errors -->
      <div *ngIf="serverError" class="text-danger">
        {{ serverError }}
      </div>

      <form #f="ngForm" (ngSubmit)="onSubmit()" novalidate>
        <!-- Password Input -->
        <div class="input-group">
          <input
            name="password"
            type="password"
            [(ngModel)]="password"
            placeholder="Enter new password"
            required
            #passwordInput="ngModel"
            (input)="checkPasswordMatch()"
            [class.is-invalid]="passwordInput.invalid && passwordInput.touched || !passwordValid"
          />
          <div *ngIf="passwordInput.invalid && passwordInput.touched" class="text-danger">
            <div *ngIf="passwordInput.errors?.['required']">Password is required.</div>
          </div>
          <div *ngIf="!passwordValid && passwordInput.touched" class="text-danger">
            Password must be at least 8 characters long, and include one uppercase letter, one lowercase letter, one digit, and one special character.
          </div>
        </div>

        <!-- Confirm Password Input -->
        <div class="input-group">
          <input
            name="repassword"
            type="password"
            [(ngModel)]="repassword"
            placeholder="Confirm new password"
            required
            #repasswordInput="ngModel"
            (input)="checkPasswordMatch()"
            [class.is-invalid]="repasswordInput.invalid && repasswordInput.touched"
          />
          <div *ngIf="repasswordInput.invalid && repasswordInput.touched" class="text-danger">
            <div *ngIf="repasswordInput.errors?.['required']">Confirm Password is required.</div>
          </div>
          <!-- Password Mismatch Message -->
          <div *ngIf="!repasswordInput.errors?.['required'] && mismatchError && repasswordInput.touched" class="text-danger">
            Passwords do not match.
          </div>
        </div>

        <button type="submit" [disabled]="mismatchError || f.invalid || !passwordValid || isSubmitting">
          <span *ngIf="isSubmitting">Submitting...</span>
          <span *ngIf="!isSubmitting">Reset Password</span>
        </button>
      </form>
    </div>
  </div>
</body>
