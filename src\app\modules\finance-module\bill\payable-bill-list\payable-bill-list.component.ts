import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { BillService } from '../bill.service';
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { fromEvent, Subscription } from 'rxjs';
import { ApInvoiceDetail, ApInvoiceHead } from '../bill';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import Swal from 'sweetalert2';
import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { PeriodClosingService } from '../../period-closing/period-closing.service';
import { DateAdapter } from '@angular/material/core';
@Component({
  selector: 'app-payable-bill-list',
  templateUrl: './payable-bill-list.component.html',
  styleUrls: ['./payable-bill-list.component.css']
})
export class PayableBillListComponent implements OnInit {
  
    @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
    @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
    private audio!: HTMLAudioElement;

  filteredApInvoices: ApInvoiceHead[] = [];
  searchTerm: string = ''; 
  quotes: ApInvoiceHead[] = []; 
  isAllSelected: boolean = false;
  selectedApInvoices: Set<ApInvoiceHead> = new Set<ApInvoiceHead>();
  businessPartner: BusinessPartner = new BusinessPartner();
  activeTab = 'all';
  startDate: string | null = null; // Bind to the start date input
  endDate: string | null = null;   // Bind to the end date input

  constructor (
    private billService: BillService, 
    private businessPartnerService: BusinessPartnerService, 
    private periodClosingService: PeriodClosingService , 
    private router: Router, public sanitizer: DomSanitizer,
    private dateAdapter: DateAdapter<Date>,
    
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }

  ngOnInit() {
    this.fetchApInvoices();
  }

  modalHideSubscription?: Subscription;

  ngAfterViewInit() {
    const modalElement = document.getElementById('quotePreviewModal');
    if (modalElement) {
      this.modalHideSubscription = fromEvent(modalElement, 'hide.bs.modal').subscribe(() => {
        this.clearSelectedCheckboxes();
      });
    }
  }

  ngOnDestroy() {
    if (this.modalHideSubscription) {
      this.modalHideSubscription.unsubscribe();
    }
  }
  
  addPaybleBill() {
    this.router.navigate(['/add-payable-bill']);
  }

  //dropdown
  isDropdownOpen = false;

  @ViewChild('dropdownRef') dropdownRef!: ElementRef;

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown() {
    this.isDropdownOpen = false;
  }

      
  // Listen for clicks on the whole document
        @HostListener('document:click', ['$event'])
        handleClickOutside(event: MouseEvent) {
          if (
            this.dropdownRef && !this.dropdownRef.nativeElement.contains(event.target) 
          ) {
            this.closeDropdown();
          }
        }


    fetchApInvoices() {
  const entityId = +((localStorage.getItem('entityId')) + "");
  this.billService.getAllApInvoiceHeadList(entityId).subscribe(
    (data: ApInvoiceHead[]) => {
      this.quotes = data;
      this.filteredApInvoices = this.quotes;

      //Check lock for each bill after loading them
      this.filteredApInvoices.forEach((quote) => {
        const date =  quote.postingDate;
        this.periodClosingService.isDateLocked(entityId, date).subscribe({
          next: (locked) => {
            quote.isLocked = locked;
          },
          error: (err) => {
            console.error('Error checking period lock', err);
            quote.isLocked = false;
          }
        });
      });
    },
    (error) => {
      console.error('Error fetching ApInvoices:', error);
    }
  );
}


  onSearchChange() {
    this.filterQuotes();
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    if (this.searchTerm || this.startDate || this.endDate) {
      this.filterQuotes(); // Filter quotes only if search criteria are provided
    } else {
      this.filteredApInvoices = this.activeTab === 'all' 
        ? this.quotes 
        : this.quotes.filter(quote => quote.status.toLowerCase() === this.activeTab);
    }
  }

  filterQuotes(): void {
    if (!this.searchTerm && !this.startDate && !this.endDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Search Criteria',
        text: 'Please provide at least one search criterion: Reference No, Supplier Name, Start Date, or End Date.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    let filtered = this.quotes;

    if (this.activeTab !== 'all') {
      filtered = filtered.filter(quote => 
        quote.status.toLowerCase() === this.activeTab
      );
    }

    if (searchTermLower) {
      filtered = filtered.filter(quote =>
        quote.referenceNo.toString().toLowerCase().includes(searchTermLower) ||
        quote.supplierName.toLowerCase().includes(searchTermLower)
      );
    }
  
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(quote => new Date(quote.postingDate) >= startDate);
    }
  
    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(quote => new Date(quote.dueDate) <= endDate);
    }
  
    this.filteredApInvoices = filtered;
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.activeTab = 'all';
    this.filteredApInvoices = this.quotes;
  }
  
  clearSelectedCheckboxes() {
    this.selectedApInvoices.clear();
  }


  selectAll(event: any): void {
      this.isAllSelected = event.target.checked;
  
      if (this.isAllSelected) {
        this.quotes.forEach(expense => {
          this.selectedApInvoices.add(expense);
        });
      } else {
        this.selectedApInvoices.clear();
      }
    }
  
      
    
    toggleSelection(quote: ApInvoiceHead, event: any): void {
      if (event.target.checked) {
        this.selectedApInvoices.add(quote);
      } else {
        this.selectedApInvoices.delete(quote);
      }
    }
    
    recordBatchPayment(): void {
      if (this.selectedApInvoices.size > 0) {
      

        const expensesToCredit = Array.from(this.selectedApInvoices);



        // check for locked bills
        const lockedBills = expensesToCredit.filter(expense => expense.isLocked);
        if (lockedBills.length > 0) {
          
          Swal.fire({
            title: 'Locked Period Detected',
            text: `Selected Bills that belong to a closed accounting period and cannot be paid: ${lockedBills.map(b => b.referenceNo).join(', ')}`,
            icon: 'warning',
            confirmButtonText: 'OK',
            confirmButtonColor: '#ff7e5f'
          });
          
          return;
        }


        // Extract customer names (or IDs) from selected expenses
      const customerNames = expensesToCredit.map(expense => expense.businessPartnerId);

      // Check if all selected expenses have the same customer
      const allSameCustomer = customerNames.every((name, _, arr) => name === arr[0]);

      if (!allSameCustomer) {
        // Show a message if selected expenses have different customers
        Swal.fire({
          title: 'Different Suppliers Selected',
          text: 'Please select Bill that belong to the same Supplier.',
          icon: 'warning',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#ff7e5f',
          cancelButtonColor: '#be0032',
          showCancelButton: true
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Different Suppliers Selected');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
  
        // Filter valid expenses: balance > 0 and status is 'Pending', 'Sent', or 'Overdue'
        const validexpenses = expensesToCredit.filter(expense => {
          return expense.dueAmount > 0 &&
            ['Pending', 'Sent', 'Overdue', 'Awaiting Payment'].includes(expense.status);
        });
  
        // Filter invalid expenses: balance <= 0 or status is not 'Pending', 'Sent', or 'Overdue'
        const invalidexpenses = expensesToCredit.filter(expense => {
          return expense.dueAmount <= 0 ||
            !['Pending', 'Sent', 'Overdue', 'Awaiting Payment'].includes(expense.status);
        });
  
        if (validexpenses.length > 0) {
          // Extract only expenseHeadIds from valid expenses
          const billIds = validexpenses.map(expense => expense.apInvoiceHeadId);
  
          // Pass the valid expense ids in query params
          this.router.navigate(['/record-batch-payments'], {
            queryParams: { billIds: JSON.stringify(billIds) }
          });
  
          // If there are invalid expenses, show a warning message
          if (invalidexpenses.length > 0) {
            Swal.fire({
              title: 'Some Bills Were Skipped',
              text: `The following Bills were excluded because they either have a zero balance or are not marked with a status of 'Pending,' or 'Overdue.': ${invalidexpenses.map(expense => expense.referenceNo).join(', ')}`,
              icon: 'warning',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#ff7e5f',
              cancelButtonColor: '#be0032',
              showCancelButton: true,
            }).then((result) => {
              if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                if (this.chatBotComponent) {
                  Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while Chimp processes your request.',
                    allowOutsideClick: false,
                    didOpen: () => {
                      Swal.showLoading();
                      this.chatBotComponent.setInputData('Some Bills Were Skipped');
                      this.chatBotComponent.responseReceived.subscribe(response => {
                        Swal.close();
                        this.chatResponseComponent.showPopup = true;
                        this.chatResponseComponent.responseData = response;
                        this.playLoadingSound();
                        this.stopLoadingSound()
                      });
                    },
                  });
                } else {
                  console.error('ChatBotComponent is not available.');
                }
              }
            });
          }
  
        } else {
          // If no valid Bills are found, show a message
          Swal.fire({
            title: 'No Valid Bills',
            text: 'Only Bills with a status of Pending or Overdue and a balance greater than 0 are eligible for Paid.',
            icon: 'warning',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#ff7e5f',
            cancelButtonColor: '#be0032',
            showCancelButton: true,
          }).then((result) => {
            if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData('No Valid Bills');
                    this.chatBotComponent.responseReceived.subscribe(response => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound()
                    });
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
        }
      } else {
        // No Bills selected, show a message
        Swal.fire({
          title: 'No Bills Selected',
          text: 'Please select an Bill to create a Credit Note.',
          icon: 'warning',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#ff7e5f',
          cancelButtonColor: '#be0032',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('No Bills Selected');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
  
        });
        this.router.navigate(['/record-batch-payments']);
      }
  

    }

    
    
    
    
    onCreditNoteMultiple(): void {
      if (this.selectedApInvoices.size > 0) {
        const expensesToCredit = Array.from(this.selectedApInvoices);
        
        //  Check for locked bills

        const lockedBills = expensesToCredit.filter(expense => expense.isLocked);

        if (lockedBills.length > 0) {
  
          Swal.fire({
            title: 'Locked Period Detected',
            text: `Selected Bills that belong to a closed accounting period and cannot be credited: ${lockedBills.map(b => b.referenceNo).join(', ')}`,
            icon: 'warning',
            confirmButtonText: 'OK',
            confirmButtonColor: '#ff7e5f'
          });
          return;
        }



        // Extract customer names (or IDs) from selected expenses
      const customerNames = expensesToCredit.map(expense => expense.businessPartnerId);

      // Check if all selected expenses have the same customer
      const allSameCustomer = customerNames.every((name, _, arr) => name === arr[0]);

      if (!allSameCustomer) {
        // Show a message if selected expenses have different customers
        Swal.fire({
          title: 'Different Suppliers Selected',
          text: 'Please select Bill that belong to the same Supplier.',
          icon: 'warning',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#ff7e5f',
          cancelButtonColor: '#be0032',
          showCancelButton: true
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Different Suppliers Selected');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
  
        // Filter valid expenses: balance > 0 and status is 'Pending', 'Sent', or 'Overdue'
        const validexpenses = expensesToCredit.filter(expense => {
          return expense.dueAmount > 0 &&
            ['Pending', 'Sent', 'Overdue', 'Awaiting Payment'].includes(expense.status);
        });
  
        // Filter invalid expenses: balance <= 0 or status is not 'Pending', 'Sent', or 'Overdue'
        const invalidexpenses = expensesToCredit.filter(expense => {
          return expense.dueAmount <= 0 ||
            !['Pending', 'Sent', 'Overdue', 'Awaiting Payment'].includes(expense.status);
        });
  
        if (validexpenses.length > 0) {
          // Extract only expenseHeadIds from valid expenses
          const billIds = validexpenses.map(expense => expense.apInvoiceHeadId);
  
          // Pass the valid expense ids in query params
          this.router.navigate(['/credit-note-bill'], {
            queryParams: { billIds: JSON.stringify(billIds) }
          });
  
          // If there are invalid expenses, show a warning message
          if (invalidexpenses.length > 0) {
            Swal.fire({
              title: 'Some Bills Were Skipped',
              text: `The following Bills were excluded because they either have a zero balance or are not marked with a status of 'Pending,'  or 'Overdue.': ${invalidexpenses.map(expense => expense.referenceNo).join(', ')}`,
              icon: 'warning',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#ff7e5f',
              cancelButtonColor: '#be0032',
              showCancelButton: true,
            }).then((result) => {
              if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                if (this.chatBotComponent) {
                  Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while Chimp processes your request.',
                    allowOutsideClick: false,
                    didOpen: () => {
                      Swal.showLoading();
                      this.chatBotComponent.setInputData('Some Bills Were Skipped');
                      this.chatBotComponent.responseReceived.subscribe(response => {
                        Swal.close();
                        this.chatResponseComponent.showPopup = true;
                        this.chatResponseComponent.responseData = response;
                        this.playLoadingSound();
                        this.stopLoadingSound()
                      });
                    },
                  });
                } else {
                  console.error('ChatBotComponent is not available.');
                }
              }
            });
          }
  
        } else {
          // If no valid Bills are found, show a message
          Swal.fire({
            title: 'No Valid Bills',
            text: 'Only Bills with a status of Pending or Overdue and a balance greater than 0 are eligible for Paid.',
            icon: 'warning',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#ff7e5f',
            cancelButtonColor: '#be0032',
            showCancelButton: true,
          }).then((result) => {
            if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData('No Valid Bills');
                    this.chatBotComponent.responseReceived.subscribe(response => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound()
                    });
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
        }
      } else {
        // No Bills selected, show a message
        Swal.fire({
          title: 'No Bills Selected',
          text: 'Please select an Bill to create a Credit Note.',
          icon: 'warning',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#ff7e5f',
          cancelButtonColor: '#be0032',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('No Bills Selected');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
  
        });
        this.router.navigate(['/credit-note-bill']);
      }
  

    }




    

    playLoadingSound() {
      let audio = new Audio();
      audio.src = "../assets/google_chat.mp3";
      audio.load();
      audio.play();
    }
  
    stopLoadingSound(): void {
      if (this.audio) {
        this.audio.pause();
        this.audio.currentTime = 0;
      }
    }

    viewBill(id: number) {
      this.router.navigate(['/view-payable-bill', id]);
    }

    //Delete Button Function
    deleteApInvoice(apInvoiceHeadId: number): void {

    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you really want to delete this Bill?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (result.isConfirmed) {
        
         
        this.billService.deleteBillHeaders(apInvoiceHeadId)
          .subscribe(
            () => {

              Swal.fire({
                title: 'Deleted!',
                text: 'Bill has been deleted.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
              }).then(() => {
                const entityId = +((localStorage.getItem('entityId')) + "");
                // Optionally update the UI or reload data
                this.billService.getAllApInvoiceHeadList(entityId);
              });
            },
            (error) => {
              console.error('Failed to delete Bill:', error);

              Swal.fire({
                title: 'Error!',
                text: 'Failed to delete Bill.',
                icon: 'error',
                confirmButtonText: 'OK',
                cancelButtonText: 'Ask Chimp',
                confirmButtonColor: '#be0032',
                cancelButtonColor: '#007bff',
                showCancelButton: true,
              }).then((result) => {
                if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                  if (this.chatBotComponent) {
                    Swal.fire({
                      title: 'Processing...',
                      text: 'Please wait while Chimp processes your request.',
                      allowOutsideClick: false,
                      didOpen: () => {
                        Swal.showLoading();
                        this.chatBotComponent.setInputData('Failed to delete Bill.');
                        this.chatBotComponent.responseReceived.subscribe(response => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound()
                        });
                      },
                    });
                  } else {
                    console.error('ChatBotComponent is not available.');
                  }
                }
              });
            }
          );
      }
    });
  }

    
    updateBill(id: number) {
        const invoice = this.filteredApInvoices.find(invoice => invoice.apInvoiceHeadId === id);
    
        if (invoice && invoice.status === 'Canceled') {
          Swal.fire({
            title: 'Error!',
            text: "Canceled bills cannot be edited!",
            icon: 'error',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#be0032',
            cancelButtonColor: '#007bff',
            showCancelButton: true,
          }).then((result) => {
            if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData('Canceled Bills can not be edited! ');
                    this.chatBotComponent.responseReceived.subscribe(response => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound()
                    });
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
        } else {
          this.router.navigate(['/update-bill', id]);
        }
      }




      exportToExcel(): void {
        const exportData = this.filteredApInvoices.map(invoice => ({
          'Invoice ID': invoice.apInvoiceHeadId,
          'Reference No': invoice.referenceNo,
          'Supplier Name': invoice.supplierName,
          'Gross Amount': invoice.grossAmount,
          'Net Amount': invoice.netAmount,
          'Total GST': invoice.totalGst,
          'Status': invoice.status,
          'Posting Date': invoice.postingDate,
        }));
    
        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Payable Bills');
    
        const excelBuffer: any = XLSX.write(workbook, {
          bookType: 'xlsx',
          type: 'array',
        });
    
        this.saveAsExcelFile(excelBuffer, 'Payable_Bills');
      }
    
      private saveAsExcelFile(buffer: any, fileName: string): void {
        const data: Blob = new Blob([buffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
        });
        saveAs(data, `${fileName}.xlsx`);
      }


  cancelSelectedBills(): void {
  const selectedBills = Array.from(this.selectedApInvoices);

  const lockedBills = selectedBills.filter(expense => expense.isLocked);
if (lockedBills.length > 0) {
  Swal.fire({
    title: 'Locked Period Detected',
    text: `Selected Bill(s) that belong to a closed accounting period and cannot be cancelled: ${lockedBills.map(b => b.referenceNo).join(', ')}`,
    icon: 'warning',
    confirmButtonText: 'OK',
    confirmButtonColor: '#ff7e5f'
  });
  return;
}


  if (selectedBills.length === 0) {
    this.isAllSelected = false;
    this.selectedApInvoices.clear();
    Swal.fire({
      title: 'No Bills Selected',
      text: 'Please select at least one bill to cancel.',
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f',
    });
    return;
  }

  const pendingBills = selectedBills.filter(bill => bill.status === 'Pending');

  if (pendingBills.length === 0) {
    this.isAllSelected = false;
    this.selectedApInvoices.clear();
    Swal.fire({
      title: 'No Pending Bills',
      text: 'Only bills with a status of "Pending" can be canceled.',
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f',
    });
    return;
  }

  Swal.fire({
    title: 'Confirm Cancellation',
    text: `Are you sure you want to cancel the selected bill(s)?`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Yes, Cancel',
    cancelButtonText: 'No',
    confirmButtonColor: '#ff7e5f',
    cancelButtonColor: '#be0032',
  }).then(result => {
    if (result.isConfirmed) {
      if (pendingBills.length === 1) {
        // Single bill cancellation
        const billId = pendingBills[0].apInvoiceHeadId;
        this.billService.cancelBill(billId).subscribe(
          () => {
            this.isAllSelected = false;
            this.selectedApInvoices.clear();
            Swal.fire({
              title: 'Success',
              text: 'Bill canceled successfully.',
              icon: 'success',
              confirmButtonText: 'OK',
            });
            this.fetchApInvoices(); // Refresh the list
          },
          error => {
            console.error('Error canceling bill:', error);
            Swal.fire({
              title: 'Error',
              text: 'Failed to cancel the bill. Please try again.',
              icon: 'error',
              confirmButtonText: 'OK',
            });
          }
        );
      } else {
        // Batch bill cancellation
        const billIds = pendingBills.map(bill => bill.apInvoiceHeadId);
        this.billService.cancelBills(billIds).subscribe(
          () => {
            this.isAllSelected = false;
            this.selectedApInvoices.clear();
            Swal.fire({
              title: 'Success',
              text: 'Bills canceled successfully.',
              icon: 'success',
              confirmButtonText: 'OK',
            });
            this.fetchApInvoices(); // Refresh the list
          },
          error => {
            console.error('Error canceling bills:', error);
            Swal.fire({
              title: 'Error',
              text: 'Failed to cancel the bills. Please try again.',
              icon: 'error',
              confirmButtonText: 'OK',
            });
          }
        );
      }
    }
  });
}

    
}
