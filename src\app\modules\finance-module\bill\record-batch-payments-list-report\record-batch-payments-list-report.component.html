<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<br>
<div class="container">
    <h4>Record Batch Payments List </h4>
    <hr>
    <div class="report-section">
        <div class="report-form-container">

            <div class="report-form">
                <div class="form-group">
                    <label for="fromDate">Date </label>
                    <div class="date-range">
                        <input type="date" id="fromDate" [(ngModel)]="fromDate">
                        <span>to</span>
                        <input type="date" id="toDate" [(ngModel)]="toDate">
                    </div>
                </div>
                <br>
                <div class="form-row">
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-select" id="status" [(ngModel)]="status">
                        <option value="%">All</option>
                        <option value="Open">Open</option>
                        <!--<option value="draft">Draft</option>-->
                        <!--<option value="pending">Pending</option>-->
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="supplier">Supplier</label>
                        <select class="form-select" id="supplier" [(ngModel)]="recordBatchPaymentsData.businessPartnerId" (change)="onSupplierChange($event)" name="payeeName" required>
                            <option value="0">All Suppliers</option>
                            <option *ngFor="let supplier of suppliers" [value]="supplier.businessPartnerId">
                                {{ supplier.bpName }}
                            </option>
                        </select>
                    </div>
                </div>
                <br>
                 <button class="generate-report-btn" (click)="previewRecordBatchPayments(fromDate, toDate, status, recordBatchPaymentsData.businessPartnerId);">
                    Generate Report
                </button>
            </div>
        </div> 
                
        <div class="modal fade" id="simpleModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 740px;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="simpleModalLabel">Record Batch Payments </h5>
                        <button type="button" class="btn-close custom-close-btn" aria-label="Close" data-bs-dismiss="modal">
                            <span aria-hidden="true"></span>
                        </button>

                    </div>
                    <div class="modal-body">
                        <!-- Loading Spinner -->
                        <div *ngIf="isLoading" class="spinner-container">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>

                        <!-- IFrame for Invoice Preview -->
                        <div style="margin-top: 20px;" [ngClass]="{'d-none': isLoading}">

                            <iframe #recordBatchPaymentsPreviewFrame id="#recordBatchPaymentsPreviewFrame" width="700px" height="700px"></iframe>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

</div>
