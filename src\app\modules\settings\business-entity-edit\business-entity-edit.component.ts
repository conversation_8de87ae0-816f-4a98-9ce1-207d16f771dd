import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { FormBuilder, FormGroup, NgForm, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { EmailTemplate, EmailTemplateService } from '../email-template.service';
import { Entity, EntityTradingName, Industry } from '../../entity/entity';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { StripeService } from '../stripe.service';
import { BeamConnectService } from './beam-connect.service';
import { BasiqService } from '../basiq.service';
import * as bootstrap from 'bootstrap';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

declare var tinymce: any;
@Component({
  selector: 'app-business-entity-edit',
  templateUrl: './business-entity-edit.component.html',
  styleUrls: ['./business-entity-edit.component.css'],
})
export class BusinessEntityEditComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent)
  chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  entityId: number = 0;
  selectedFile: File | null = null;
  attachments: any[] = [];
  quoteTemplateForm: FormGroup;
  invoiceTemplateForm: FormGroup;
  emailTemplates: any[] = [];
  invoiceTemplate: any = null;
  quoteTemplate: any = null;
  sameAsTemplateName = false;
  @ViewChild('templatePreviewFrame') templatePreviewFrame: any;
  isLoading: any;
  entity: Entity = new Entity();
  entityTradingName: EntityTradingName = new EntityTradingName();
  logoUrl: string | null = null;
  selectedTemplate: any = {
    templateName: '',
    subject: '',
    content: '',
  };
  emailTemplate: EmailTemplate = {
    id: null,
    templateName: '',
    subject: '',
    content: '',
  };
  templateNameControl: any;
  subjectControl: any;
  contentControl: any;
  showEditTradingDetails = false;
  tradingNameOption: string = 'single';
  additionalTradingName: boolean = false;
  tradingName: string = '';
  newTradingName: string[] = [];
  industryList: Industry[] = [];
  tradingNameList: EntityTradingName[] = [];
  @ViewChild('closeCustomerPopUp') closeCustomerPopUp: any;
  transactions: any[] = [];
  accounts: any[] = [];
  selectedAccountId: string | null = null;
  isPremiumOrEnterpriseUser: boolean = false;
  suggestedAddresses: any[] = [];

  constructor(
    private fb: FormBuilder,
    private emailTemplateService: EmailTemplateService,
    private entityService: EntityService,
    private router: Router,
    private stripeService: StripeService,
    private beamConnectService: BeamConnectService,
    private basiqConnectService: BasiqService,
    private http: HttpClient
  ) {
    this.quoteTemplateForm = this.fb.group({
      quoteTemplateName: ['', Validators.required],
      quoteSubject: ['', Validators.required],
      quoteContent: ['', Validators.required],
      sameAsTemplateName: [false],
    });

    this.invoiceTemplateForm = this.fb.group({
      invoiceTemplateName: ['', Validators.required],
      invoiceSubject: ['', Validators.required],
      invoiceContent: ['', Validators.required],
      sameAsTemplateName: [false],
    });
    this.quoteTemplate = {
      templateName: '',
      subject: '',
      content: '',
    };
    this.invoiceTemplate = {
      templateName: '',
      subject: '',
      content: '',
    };
  }

  ngOnInit(): void {
    
    this.entityId = +(localStorage.getItem('entityId') || '0');
    if (this.entityId > 0) {
      this.getBusinessEntityById(this.entityId);
    } else {
      console.error('Invalid entityId:', this.entityId);
    }
     this.loadIndustryList();
    this.getParticipantDetails();
    this.loadEmailTemplates(this.entityId);
    this.fetchIndstryList();

     const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      const roleName = user.roleName;
      this.isPremiumOrEnterpriseUser = roleName === 'Premium' || roleName === 'Enterprise';
    }
  
  }

  loadIndustryList() {
  this.entityService.getIndustryList().subscribe(industryList => {
    this.industryList = industryList;

    //call fetchTradingNameList only after industryList is ready
    this.fetchTradingNameList();
  });
}
  stripHtmlTags(html: string): string {
  const div = document.createElement('div');
  div.innerHTML = html;
  return div.textContent || div.innerText || '';
}


  loadEmailTemplates(entityId: number): void {
    this.emailTemplateService
      .getEmailTemplateByEntityId(entityId)
      .subscribe((data: EmailTemplate[]) => {
        if (data && data.length > 0) {
          this.emailTemplates = data;

          this.invoiceTemplate = this.emailTemplates.find(
            (template) => template.emailType === 'invoice'
          );
          this.quoteTemplate = this.emailTemplates.find(
            (template) => template.emailType === 'quote'
          );

          if (this.quoteTemplate) {
            this.quoteTemplateForm.patchValue({
              quoteTemplateName: this.quoteTemplate.templateName,
              quoteSubject:  this.stripHtmlTags(this.quoteTemplate.subject),
              quoteContent: this.stripHtmlTags(this.quoteTemplate.content),
            });
          }

          if (this.invoiceTemplate) {
            this.invoiceTemplateForm.patchValue({
              invoiceTemplateName: this.invoiceTemplate.templateName,
              invoiceSubject:  this.stripHtmlTags(this.invoiceTemplate.subject),
              invoiceContent:  this.stripHtmlTags(this.invoiceTemplate.content),
            });
          }
        } else {
          this.emailTemplates = [];
          this.invoiceTemplate = null;
          this.quoteTemplate = null;
        }
      });
  }

  getBusinessEntityById(entityId: number): void {
    this.entityService.getBusinessEntityById(entityId).subscribe(
      (data) => {
        if (data) {
          this.entity = data;
          if (this.entity.logo) {
            const byteString = atob(this.entity.logo);
            const arrayBuffer = new ArrayBuffer(byteString.length);
            const intArray = new Uint8Array(arrayBuffer);

            for (let i = 0; i < byteString.length; i++) {
              intArray[i] = byteString.charCodeAt(i);
            }

            const blob = new Blob([intArray], { type: 'image/png' });

            this.selectedFile = new File([blob], 'logo.png', {
              type: 'image/png',
            });
            this.logoUrl = `data:image/png;base64,${this.entity.logo}`;
          }

              if (this.entity.basiqId) {
                this.basiqAccounts();
                }
        }
      },
      (error) => {
        console.error('Error fetching business entity:', error);
      }
    );
  }

  onFileSelected(event: any) {
    if (event.target.files && event.target.files[0]) {
      this.selectedFile = event.target.files[0];

      if (this.selectedFile) {
        const reader = new FileReader();

        reader.readAsDataURL(this.selectedFile);

        reader.onload = (event: ProgressEvent<FileReader>) => {
          const result = event.target?.result;
          if (typeof result === 'string') {
            this.logoUrl = result;
          }
        };
      }
    }
  }

  onGSTCheckboxChanged(event: Event): void {
  const isChecked = (event.target as HTMLInputElement).checked;

  if (isChecked) {
    this.entity.taxApplicability = 'yes';
    
  } else {
    this.entity.taxApplicability = 'no';
    this.entity.gstReturnFrequency = ''; 
  }
}


  onCancel(): void {
    this.router.navigate(['/business-entity-edit']);
  }

  onSubmit(): void {
    this.updateBusinessEntity();
  }

  updateBusinessEntity() {
  if (!this.selectedFile) {
    console.error('No file selected.');
    Swal.fire({
      title: 'No File Selected',
      text: 'Please upload a file before updating the business entity.',
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f',
    });
    return;
  }

  this.entityService
    .updateBusinessEntity(this.entityId, this.entity, this.selectedFile)
    .subscribe(
      (response) => {
        Swal.fire({
          title: 'Update Successful',
          text: 'The business entity was successfully updated.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        });
      },
      (error) => {
        console.error('Error updating business entity:', error);
        Swal.fire({
          title: 'Update Failed',
          text: 'There was an issue updating the business entity details.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
}


  handleAttachment(event: any) {
    const files = event.target.files;
    if (files.length > 0) {
      this.attachments.push(files[0]);
    }
  }

  removeAttachment(attachment: File) {
    this.attachments = this.attachments.filter((a) => a !== attachment);
  }

  saveTemplate(): void {
    const entityId = 1; // Replace with actual entityId
    if (this.selectedTemplate.templateName && this.selectedTemplate.content) {
      this.isLoading = true;
      this.emailTemplateService
        .addEmailTemplate(entityId, this.selectedTemplate)
        .subscribe(
          (response) => {
            this.isLoading = false;
          },
          (error) => {
            console.error('Error saving email template:', error);
            this.isLoading = false;
          }
        );
    } else {
      console.error('Template Name and Content are required.');
    }
  }

  deleteTemplate() {}

  format(command: string) {
    document.execCommand(command, false);
  }

  insertLink() {
    const url = prompt('Enter the URL');
    if (url) {
      document.execCommand('createLink', false, url);
    }
  }

  insertImage() {
    const url = prompt('Enter the image URL');
    if (url) {
      document.execCommand('insertImage', false, url);
    }
  }

  onSubmitEmail(form: FormGroup) {
    if (form.valid) {
    } else {
      if (form === this.quoteTemplateForm) {
      } else if (form === this.invoiceTemplateForm) {
      }
    }
  }

  onSaveIt(form: FormGroup): void {
    if (form.valid) {
      const emailTemplate = form.value;

      if (form === this.quoteTemplateForm) {
        emailTemplate.emailType = 'quote';
        emailTemplate.templateName = emailTemplate.quoteTemplateName;
        emailTemplate.subject = emailTemplate.quoteSubject;
        emailTemplate.content = emailTemplate.quoteContent;
      } else if (form === this.invoiceTemplateForm) {
        emailTemplate.emailType = 'invoice';
        emailTemplate.templateName = emailTemplate.invoiceTemplateName;
        emailTemplate.subject = emailTemplate.invoiceSubject;
        emailTemplate.content = emailTemplate.invoiceContent;
      }
      this.isLoading = true;

      this.emailTemplateService
        .addEmailTemplate(this.entityId, emailTemplate)
        .subscribe(
          (response) => {
            this.isLoading = false;

            Swal.fire({
              title: 'Success!',
              text: 'Email template saved successfully.',
              icon: 'success',
              confirmButtonText: 'OK',
            }).then((result) => {
              if (result.isConfirmed) {
                location.reload();
              }
            });
          },
          (error) => {
            console.error('Error saving email template:', error);
            this.isLoading = false;
            Swal.fire({
              title: 'Error!',
              text: 'There was an error saving the email template.',
              icon: 'error',
              confirmButtonText: 'OK',
            });
          }
        );
    }
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

  // connectStripe() {
  //   const entityId = +(localStorage.getItem('entityId') + '');
  //   this.stripeService.getOAuthLink(entityId).subscribe((response) => {
  //     window.location.href = response.url; // Redirect user to Stripe OAuth link
  //   });
  // }


isRemovingStripe = false;
@ViewChild('closeStripeModal') closeStripeModal: any
connectStripe() {
  const entityId = +(localStorage.getItem('entityId') + '');

  if (!this.entity.stripeAccountId) {
    this.stripeService.getOAuthLink(entityId).subscribe((response) => {
      window.location.href = response.url;
    });
  } else {
    // Open modal if already connected
    const modal = new bootstrap.Modal(document.getElementById('stripeModal')!);
    modal.show();
  }
}

confirmRemoveStripeAccount(): void {
  Swal.fire({
    title: 'Are you sure?',
    text: 'This will disconnect the linked Stripe account.',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Yes, remove it!',
    cancelButtonText: 'Cancel',
  }).then((result) => {
    if (result.isConfirmed) {
      this.removeStripeAccount();
    }
  });
}

removeStripeAccount(): void {
  this.isRemovingStripe = true;  // start spinner + disable button

  const entityId = +(localStorage.getItem('entityId') + '');
  this.stripeService.disconnectStripeAccount(entityId).subscribe({
    next: () => {
      Swal.fire('Removed!', 'Stripe account disconnected successfully.', 'success');
      this.entity.stripeAccountId = '';  // Clear local variable
      this.closeStripeModal.nativeElement.click(); // Close modal if using ViewChild
      this.isRemovingStripe = false;
    },
    error: (err) => {
      console.error('Stripe disconnect failed:', err);
      Swal.fire('Error', 'Failed to disconnect Stripe account.', 'error');
      this.isRemovingStripe = false;
    }
  });
}
goToStripeDashboard() {
  window.open('https://dashboard.stripe.com', '_blank');
}

  connectBeam() {
    const entityId = +(localStorage.getItem('entityId') + '');
    const userId = +(localStorage.getItem('userid') + '');
    this.beamConnectService
      .getBeamLink(entityId, userId)
      .subscribe((response) => {
        window.location.href = response.url; // Redirect user to Beam link
      });
  }

  getParticipantDetails() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.beamConnectService
      .getParticipantDetails(entityId)
      .subscribe((response) => {
      });
  }

  mobileNumber: string = '';
  isConnecting: boolean = false; // Track button state
  @ViewChild('closebasiqModal') closebasiqModal: any;

  connectBasiq() {
    if (!this.mobileNumber || !/^\+?[1-9]\d{1,14}$/.test(this.mobileNumber)) {
      console.error(
        'Invalid mobile number format. Please enter a valid number.'
      );
      return;
    }

    this.isConnecting = true; // Disable button & show spinner
    const entityId = Number(localStorage.getItem('entityId'));
    const userId = Number(localStorage.getItem('userid'));

    this.basiqConnectService
      .getBasiqLink(entityId, userId, this.mobileNumber)
      .subscribe(
        (response) => {
          if (response && response.authLink) {
            // window.open(response.authLink, '_blank');
            window.location.href = response.authLink;
          } else {
            console.error('Failed to retrieve Basiq auth link');
          }
          this.isConnecting = false; // Re-enable button
          this.closebasiqModal.nativeElement.click();
        },
        (error) => {
          console.error('Error fetching Basiq auth link:', error);
          this.isConnecting = false; // Re-enable button on error
        }
      );
  }

  // basiqTransactions() {
  //   const entityId = +(localStorage.getItem('entityId') + '');
  //   this.basiqConnectService.getTransactions(entityId).subscribe({
  //     next: (data) => {
  //       this.transactions = data;
  //     },
  //     error: (err) => console.error('Error fetching transactions', err)
  //   });
  // }
  
  basiqAccounts() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.basiqConnectService.getAccounts(entityId).subscribe({
      next: (data) => {
        this.accounts = data;
      },
      error: (err) => console.error('Error fetching accounts', err)
    });
  }

  onAccountClick(account: any) {
  this.selectedAccountId = account.id;
  const entityId = +(localStorage.getItem('entityId') + '');
  // const postDate = '2025-05-27'; 

  this.basiqConnectService.getTransactions(entityId, account.id).subscribe({
    next: (data) => {
      this.transactions = data;
    },
    error: (err) => console.error('Error loading transactions', err)
  });
}

selectedAction: string = 'connect';

connectAdditionalAccount(action: string = 'update') {
  const basiqUserId = this.entity.basiqId;
  if (!basiqUserId) {
    console.error('No existing Basiq user ID found.');
    return;
  }

  this.isConnecting = true;

  this.basiqConnectService.getReauthLink(basiqUserId, action).subscribe(
    (response) => {
      if (response && response.authLink) {
        window.location.href = response.authLink;
      } else {
        console.error('Failed to retrieve re-auth link');
      }
      this.isConnecting = false;
    },
    (error) => {
      console.error('Error fetching re-auth link:', error);
      this.isConnecting = false;
    }
  );
}

isDeleting = false;

confirmDeleteUser(): void {
  Swal.fire({
    title: 'Are you sure?',
    text: 'This will delete the linked Basiq user.',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Yes, delete it!',
    cancelButtonText: 'Cancel',
  }).then((result) => {
    if (result.isConfirmed) {
      this.deleteBasiqUser();
    }
  });
}

deleteBasiqUser(): void {
  this.isDeleting = true;  // start spinner + disable button

  const entityId = +(localStorage.getItem('entityId') + '');
  this.basiqConnectService.deleteBasiqUser(entityId).subscribe({
    next: () => {
      Swal.fire('Deleted!', 'Basiq user deleted successfully.', 'success');
      this.entity.basiqId = "";
      this.closebasiqModal.nativeElement.click();
      this.isDeleting = false; // stop spinner + enable button
    },
    error: (err) => {
      console.error('Delete failed:', err);
      Swal.fire('Error', 'Failed to delete Basiq user.', 'error');
      this.isDeleting = false; // stop spinner + enable button
    }
  });
}

  toggleEditTradingDetails() {
    this.showEditTradingDetails = !this.showEditTradingDetails;
  }

  singleTradingName(): void {
    this.additionalTradingName = false;
  }

  multipleTradingName(): void {
    this.additionalTradingName = true;
  }

  noTradingName(): void {
    this.tradingName = '';
    this.entityTradingName.tradingName = '';
    this.additionalTradingName = false;
  }

  addAdditionalTradingName(): void {
    this.additionalTradingName = true;
    this.newTradingName.push('');
  }

  trackByIndex(index: number): number {
    return index;
  }

  removeTradingName(index: number): void {
    this.newTradingName.splice(index, 1);
  }

  fetchIndstryList(): void {
    this.entityService.getIndustryList().subscribe((response) => {
      this.industryList = response;
    });
  }

  fetchTradingNameList(): void {
    this.entityService
      .getEntityTradingNamesByEntityId(this.entityId)
      .subscribe((response) => {
        this.tradingNameList = response;

        if (this.tradingNameList.length === 0) {
          this.tradingNameOption = 'none';
        } else if (this.tradingNameList.length === 1) {
          this.tradingNameOption = 'single';
          this.entityTradingName = { ...this.tradingNameList[0] };
        } else {
          this.tradingNameOption = 'multiple';
          this.entityTradingName = { ...this.tradingNameList[0] };
          this.newTradingName = this.tradingNameList
            .slice(1)
            .map((tn) => tn.tradingName || '');
        }

        if (this.entityTradingName.industryId && this.industryList.length > 0) {
          const matchedIndustry = this.industryList.find(
            (industry) =>
              industry.industryId ===
              this.entityTradingName.industryId.industryId
          );
          if (matchedIndustry) {
            this.entityTradingName.industryId = matchedIndustry;
          }
        }
      });
  }

  submitTradingNames(): void {
    const tradingNames: EntityTradingName[] = [];

    if (
      this.tradingNameOption !== 'none' &&
      this.entityTradingName.tradingName
    ) {
      tradingNames.push({
        entityTradingNameId: this.entityTradingName.entityTradingNameId,
        entityId: this.entityTradingName.entityId,
        industryId: this.entityTradingName.industryId,
        tradingName: this.entityTradingName.tradingName,
      });
    }

    if (this.tradingNameOption === 'multiple') {
      this.newTradingName.forEach((name) => {
        if (name && name.trim() !== '') {
          tradingNames.push({
            tradingName: name,
            entityId: this.entityTradingName.entityId,
            industryId: this.entityTradingName.industryId,
            entityTradingNameId: 0,
          });
        }
      });
    }

    this.entityService
      .updateEntityTradingNames(this.entityId, tradingNames)
      .subscribe((response) => {
        this.fetchTradingNameList();
        Swal.fire({
          title: 'Success!',
          text: 'Successfully updated.',
          icon: 'success',
          iconColor: '#28a745',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        });
      });
  }

  checkBusinessAddress(): void {
    const query = this.entity.businessAddress
      ? this.entity.businessAddress.trim()
      : '';
    const apiUrl = `${environment.addressfinderBaseUrl}?key=${environment.addressFinderApiKey}&q=${encodeURIComponent(query)}&format=json&source=gnaf%2Cpaf`;

    if (!query) {
      this.suggestedAddresses = [];
      return;
    }

    this.http.get(apiUrl).subscribe(
      (response: any) => {
        this.suggestedAddresses = response?.completions || [];
      },
      (error) => {
        console.error('Error fetching address suggestions:', error);
      }
    );
  }

  selectAddress(address: any): void {
    this.entity.businessAddress = address.full_address;
    this.suggestedAddresses = [];
  }
}
