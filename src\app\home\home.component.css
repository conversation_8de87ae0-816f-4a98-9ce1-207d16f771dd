body {
  line-height: 1.6;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background-color: blue;
}

*{
font-family: "Inter",sans-serif !important;
}

h1,
p {
  margin: 0;
  padding: 0;
}

.homeBanner1 {
  width: 400px;
}

/* Newly Added CSS */
.homeBanner2 {
  width: 250px;
}

.home-text-centent-root {
  padding-block: 50px;
}

.home-text-content p:first-child {
  font-family: "Inter", sans-serif;
  font-size: 21px;
  font-weight: 600;
  line-height: 25px;
  text-align: left;
  color: black;
}

.home-text-content h1 {
  font-family: "Inter", sans-serif;
  width: max-content !important;
  font-size: 48px;
  font-weight: 700;
  line-height: 58.09px;
  text-align: left;
  background: linear-gradient(264.02deg, #4262ff -6.25%, #283b99 99.58%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.home-text-content p:last-child {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  text-align: left;
  width: 90%;
  color: rgba(0, 0, 0, 0.85);
}

.try-now-button {
  display: inline-block;
  border-radius: 5px;
  background: linear-gradient(90deg, #4262ff 0%, #512ca2 100%);
  padding: 10px 20px;
  cursor: pointer;
  text-decoration: none;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  color: white;
  margin-top: 10px;
}

.contact-now-button {
  display: inline-block;
  border-radius: 5px;
  background: linear-gradient(90deg, #4262ff 0%, #512ca2 100%);
  padding: 10px 20px;
  cursor: pointer;
  text-decoration: none;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  color: white;
}

/* Newly Added CSS */

.main-content .image-1 {
  margin-left: 100px;
}

.improve-business-root {
  text-align: center;
  padding: 30px 0px;
  background-color: #4262ff;
}

.improve-business h2 {
  font-family: "Inter", Sansation;
  font-size: 40px;
  font-weight: 700;
  line-height: 50px;
  text-align: center;
  color: white;
}

.improve-business .highlight {
  color: black;
  font-family: "Inter", sans-serif;
  font-size: 40px;
  font-weight: 700;
  line-height: 77.45px;
  text-align: center;
}

/* New Pricing Section Styles - Table Layout */
.pricing-section {
  text-align: center;
  background: white;
  max-width: 1500px;
  width: 100%;
  margin: 0 auto;
  overflow-x: auto;
}

.pr-section .planTxt {
  text-align: center;
  font-size: 2.5em;
  font-family: "Inter", sans-serif;
  font-weight: 700;
  margin-bottom: 40px;
  margin-top: 40px;
  color: #000;
}

.pricing-comparison-table {
  background: white;
  border-radius: 10px;
  /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
  margin-bottom: 30px;
  width: 100%;
}

/* Pricing Cards Container */
.pricing-cards-container {
  display: flex;
  gap: 15px;
  margin: 20px;
}

.pricing-card {
  background: white;
  border-radius: 15px;
  /* box-shadow: 0px 1px 20px 15px #4262ff1a; */
  padding: 25px 0;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  min-height: 200px;
  width: 220px;
  display: flex;
  flex-direction: column;
  border: 3px solid white;
}

.pricing-card-head{
  position: relative;
  background: white;
  border-radius: 15px;
  box-shadow: 4px 10px 20px 5px #4262ff1a;
  padding: 25px 5px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  height: 300px;
}

.pricing-card.featured {
  border: 3px solid #4262ff;
  transition: all 0.2s ease-in-out;
}

.plan-title {
  font-family: "Inter", sans-serif;
  font-size: 18px;
  font-weight: 700;
  color: #4262ff;
  margin-bottom: 5px;
}

.plan-price {
  margin-bottom: 10px;
}

.plan-price .price {
  font-family: "Inter", sans-serif;
  font-size: 28px;
  font-weight: bold;
  color: black;
}

.plan-price .period {
  font-family: "Inter", sans-serif;
  font-size: 12px;
  font-weight: 500;
  margin-left: 5px;
}

.plan-promo {
  font-size: 13px;
  line-height: 1.3;
  margin-top: auto;
}

.plan-promo div {
  margin-bottom: 5px;
}

/* .promo-note {
  font-weight: 600;
  color: #666;
} */

.employee-note {
  font-weight: 600;
  color: #7e7eff;
}

/* Feature Comparison Table */
.feature-comparison-row {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.feature-comparison-row:last-child {
  border-bottom: none;
}

/* .feature-header-row {
  background-color: #f8f9ff;
  border-bottom: 2px solid #e0e0e0;
  font-weight: 600;
} */

.feature-name {
  white-space: nowrap;
  flex: 0 0 260px;
  text-align: left;
  font-family: "Inter", sans-serif;
  font-weight: bold;
  padding-right: 20px;
  font-size: 13px;
}

.feature-cell {
  flex: 1; /* Let them equally share the remaining space */
  text-align: center;
}

.feature-value {
  font-size: 14px;
  color: #666;
}

.feature-icon {
  font-size: 18px;
}

/* .feature-icon.check {
  color: #4262ff;
} */

.feature-icon.cross {
  color: #ccc;
}

.pricing-buttons {
  display: flex;
  gap: 15px;
}

.btn-get-now {
  width: 100%;
  background: linear-gradient(135deg, #4262ff, #512ca2);
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 10px;
}

.btn-get-now:hover {
  background: linear-gradient(135deg, #512ca2, #4262ff);
  color: white;
}

button.btn-get-now:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

/* .btn-trial {
  background-color: white;
  color: #4262ff;
  padding: 15px 20px;
  border-radius: 8px;
  font-weight: 500;
  border: 2px solid #4262ff;
  cursor: pointer;
  width: 100%;
} */

/* .btn-trial:hover {
  background-color: white;
} */

/* tools section css styles */
.tools-section {
  background: rgba(66, 98, 255, 0.05);
  padding-block: 30px 50px;
}

.tools-section .toolTxt {
  font-family: Inter;
  font-size: 40px;
  font-weight: bold;
  line-height: 77.45px;
  text-align: center;
  color: #000;
  margin-bottom: 40px;
}

.tools {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.tool-card {
  background-color: white;
  border-radius: 20px;
  box-shadow: 0px 4px 22px 11px #0000001a;
  padding: 20px;
  width: 260px;
  text-align: left;
}

.tool-card h3 {
  background: linear-gradient(to bottom, #4262ff, #512ca2);
  color: white;
  padding: 20px;
  margin: -20px -20px 20px -20px;
  border-radius: 20px 20px 0 0;
  font-family: Inter;
  font-size: 18px;
  font-weight: 700;
  /* line-height: 50px; */
  text-align: center;
}

.tool-card p {
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 15px;
  font-weight: 400;
  text-align: left;
  color: #000000;
  text-align: center;
}

/* contact section css styles */
.contact-section {
  padding: 40px 10px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 1);
  display: flex;
  flex-direction: row;
}

.contact-section .image-2 {
  margin-left: 200px;
}

.improve-business .textbnner {
  font-family: "Inter", Sansation;
  font-size: 26px;
  font-weight: 700;
  line-height: 20px;
  text-align: center;
  color: white;
}

.contact-section .info {
  margin-left: 100px;
  margin-right: 200px;
}

.contact-section h2 {
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 35px;
  font-weight: normal;
  line-height: 50px;
  text-align: left;
}

.contact-section button {
  padding: 10px 20px;
  display: inline-block;
  border: none;
  border-radius: 5px;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  cursor: pointer;
  font-family: Inter;
  font-size: 16px;
  font-weight: 600;

  text-align: center;
}

.heading {
  padding-right: 20px;
  font-size: 48px;
  font-weight: 700;
  line-height: 58.09px;
  background: linear-gradient(264.02deg, #4262ff -6.25%, #283b99 99.58%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  padding-block: 10px;
}

.info-text {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
}

/* Responsive Designs */
/* for mobile */
@media (max-width: 768px) {
  /* .pricing-section {
    padding: 30px 10px;
  } */

  /* .pricing-header {
    grid-template-columns: 1fr;
    gap: 10px;
  } */

  /* .pricing-plan-header.featured-plan {
    transform: none;
  } */

  /* .plan-title {
    font-size: 16px;
  } */

  /* .plan-price .price {
    font-size: 24px;
  } */

  /* .plan-price .period {
    font-size: 11px;
  } */

  /* .plan-promo {
    font-size: 9px;
  } */

  /* .feature-comparison-row {
    grid-template-columns: 1fr;
    text-align: center;
    padding: 8px;
  } */

  /* .feature-name {
    text-align: center;
    font-weight: 600;
    margin-bottom: 10px;
    padding-right: 0;
  } */

  /* .feature-cell {
    display: none;
  } */

  /* .pricing-buttons {
    grid-template-columns: 1fr;
    gap: 10px;
  } */

  /* Show mobile-friendly version */
  /* .pricing-comparison-table {
    display: none;
  } */

  .heading {
    padding-right: 20px;
    font-size: 36px;
    font-weight: 700;
    line-height: 42.09px;
    background: linear-gradient(264.02deg, #4262ff -6.25%, #283b99 99.58%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding-block: 10px;
  }

  .home-text-content p:first-child {
    font-family: "Inter", sans-serif;
    font-size: 18px;
    font-weight: 600;
    line-height: 25px;
    text-align: left;
    color: black;
  }

  .home-text-content p:last-child {
    font-family: "Inter", sans-serif;
    font-style: normal;
    font-size: 18px;
    font-weight: 600;
    line-height: 25px;
    text-align: left;
    padding-top: 10px;
    width: 90%;
    color: rgba(0, 0, 0, 1);
  }

  .try-now-button {
    width: max-content !important;
    height: auto;
    border-radius: 10px;
    background: linear-gradient(90deg, #4262ff 0%, #512ca2 100%);
    padding: 10px 25px;
    cursor: pointer;
  }

  .try-now-button a {
    text-decoration: none;
    font-family: "Inter", sans-serif;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    color: white;
  }

  .improve-business .highlight {
    color: black;
    font-family: "Inter", sans-serif;
    font-size: 25px;
    font-weight: 700;
    line-height: 60.45px;
    text-align: center;
  }

  .improve-business-root {
    text-align: center;
    background-color: #4262ff;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .home-text-centent-root {
    padding-block: 50px;
  }

  .contact-section {
    padding-block: 50px;
  }

  .tools-section {
    padding-block: 50px;
  }
}

/* for tablets or small laptops */
@media (max-width: 950px) and (min-width: 769px) {
  /* .pricing-header {
    gap: 10px;
  } */

  /* .pricing-plan-header {
    padding: 15px 10px;
  } */

  /* .plan-title {
    font-size: 16px;
  } */

  /* .plan-price .price {
    font-size: 24px;
  } */

  /* .feature-comparison-row {
    padding: 10px 12px;
    font-size: 12px;
  } */

  /* .feature-name {
    padding-right: 15px;
  } */

  /* .btn-get-now,
  .btn-buy-now {
    padding: 10px 12px;
    font-size: 13px;
  } */
}

/* for desktops */
/* @media (min-width: 1200px) {
  .pricing-plans-container {
    gap: 25px;
  }

  .pricing-plan {
    width: 260px;
  }
} */

/* for very large displays */
/* @media (min-width: 1500px) {
  .pricing-plans-container {
    gap: 30px;
  }

  .pricing-plan {
    width: 280px;
  }
} */

/* for very small mobile screens */
@media (max-width: 500px) {
  .homeBanner1 {
    width: 280px;
  }

  .improve-business .textbnner {
    font-family: "Inter", Sansation;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    text-align: center;
    color: white;
  }

  .improve-business .highlight {
    color: black;
    font-family: "Inter", sans-serif;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    text-align: center;
  }
  .heading {
    padding-right: 20px;
    font-size: 28px;
    font-weight: 700;
    line-height: 32.09px;
    background: linear-gradient(264.02deg, #4262ff -6.25%, #283b99 99.58%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding-block: 10px;
  }

  .home-text-content p:first-child {
    font-family: "Inter", sans-serif;
    font-size: 12px;
    font-weight: 600;
    line-height: 25px;
    text-align: left;
    color: black;
  }

  .home-text-content p:last-child {
    font-family: "Inter", sans-serif;
    font-style: normal;
    font-size: 12px;
    font-weight: 600;
    line-height: 16px;
    text-align: left;
    padding-top: 10px;
    width: 90%;
    color: rgba(0, 0, 0, 1);
  }

  .tools-section .toolTxt {
    font-family: Inter;
    font-size: 20px;
    font-weight: bold;
    line-height: 20.45px;
    text-align: center;
    color: #000;
    margin-bottom: 40px;
  }

  .homeBanner2 {
    width: 150px;
  }

  .contact-section .image-2 {
    margin-left: 0px;
  }

  .info-text {
    font-family: "Inter", sans-serif;
    font-style: normal;
    font-size: 12px;
    font-weight: 600;
    line-height: 16px;

    color: rgba(0, 0, 0, 1);
  }

  .contact-section .info {
    margin-left: 0px;
    margin-right: 0px;
  }

  .contact-now-button {
    border-radius: 5px;
    background: linear-gradient(90deg, #4262ff 0%, #512ca2 100%);
    padding: 6px 12px;
    cursor: pointer;
    font-size: 12px;
  }
}

/* for tablets or small laptops (≤ 1024 px, ≥ 768 px) */
@media (max-width: 1024px) and (min-width: 768px) {
  /* Remove the fixed width */
  .pricing-comparison-table { min-width: unset; }

  /* Resize card and text */
  .pricing-card             { width: 160px; padding: 18px 0; }
  .plan-title               { font-size: 15px; }
  .plan-price .price        { font-size: 22px; }
  .plan-price .period       { font-size: 10px; }
  .plan-promo               { font-size: 11px; }
  .feature-name             { flex: 0 0 225px; font-size: 12px; padding-right: 12px; }
  .feature-icon,
  .feature-value            { font-size: 12px; }

  .btn-get-now              { padding: 12px 14px; font-size: 13px; }
  .try-now-button {
    border-radius: 5px;
    background: linear-gradient(90deg, #4262ff 0%, #512ca2 100%);
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
  }
}

/* Mobile View (below 768px) */
@media (max-width: 767px) {
  /* Wrap entire pricing section in horizontal scroll container */
  .pricing-section {
    overflow-x: auto;
    padding: 0 15px;
    -webkit-overflow-scrolling: touch;
  }

  .pricing-section::-webkit-scrollbar {
    display: none;
  }

  .planTxt {
    margin-top: 20px;
  }

  /* Set minimum width for entire pricing content */
  .pricing-comparison-table,
  .pricing-cards-container {
    width: 100%;
  }

  .feature-comparison-row {
    min-width: 1050px;
  }

  /* Keep the same tablet sizing */
  .pricing-card {
    width: 160px;
    padding: 18px 0;
  }

  .plan-title {
    font-size: 15px;
  }

  .plan-price .price {
    font-size: 22px;
  }

  .plan-price .period {
    font-size: 10px;
  }

  .plan-promo {
    font-size: 11px;
  }

  .feature-name {
    flex: 0 0 225px;
    font-size: 10px;
    padding-right: 12px;
  }

  .feature-icon,
  .feature-value {
    font-size: 12px;
  }

  .btn-get-now {
    padding: 12px 14px;
    font-size: 13px;
  }
  .try-now-button {
    border-radius: 5px;
    background: linear-gradient(90deg, #4262ff 0%, #512ca2 100%);
    padding: 6px 12px;
    cursor: pointer;
    font-size: 12px;
  }
}

.content-middle{
  display: grid;
}
.content-middle div{
  align-content: center;
}
