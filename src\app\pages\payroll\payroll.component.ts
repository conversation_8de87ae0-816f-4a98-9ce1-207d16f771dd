import { Component } from '@angular/core';

@Component({
  selector: 'app-payroll',
  templateUrl: './payroll.component.html',
  styleUrls: ['./payroll.component.css'],
})
export class PayrollComponent {
  features = [
    {
      icon: '💼',
      title: 'All-in-One Payroll Management',
      description:
        'Run pay processes effortlessly with LEDGER CHIMP Business. Keep compliance simple so you can focus on growing your business.',
    },
    {
      icon: '📊',
      title: 'Automated Calculations',
      description:
        'Tax, superannuation, and annual leave are calculated for you in just a few clicks. Plus, smart error messages help you catch mistakes before they become problems.',
    },
    {
      icon: '🔗',
      title: 'Flexible Payroll Options',
      description:
        'Whether you’re managing a team of up to four employees or need standalone payroll, LEDGER CHIMP Business Payroll Only has you covered.',
    },
    {
      icon: '💰',
      title: 'Meet your Superannuation Obligations',
      description:
        'Stay on Top of Your Superannuation Obligations with LEDGER CHIMP! Never miss a super payment again! With LEDGER CHIMP, managing superannuation is seamless and stress-free.',
    }
  ];
}
