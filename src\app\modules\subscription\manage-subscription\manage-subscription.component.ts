import { Component, OnInit } from '@angular/core';
import { SubscriptionService } from '../subscription.service';
import { Subscription, SubscriptionFee } from '../subscription';
import { Entity } from '../../entity/entity';
import Swal from 'sweetalert2';
import { EntityService } from '../../entity/entity.service';
import { Router } from '@angular/router';
import { BusinessEntity } from '../../settings/business-entity-edit';

@Component({
  selector: 'app-manage-subscription',
  templateUrl: './manage-subscription.component.html',
  styleUrls: ['./manage-subscription.component.css'],
})
export class ManageSubscriptionComponent implements OnInit {
  selectedSubscriptionPlan: any = '';
  subscriptionPlans: SubscriptionFee[] = [];
  entity: Entity = new Entity();
  isFreePlan: boolean = false;
  subscription: Subscription = new Subscription();
  subscriptionfee: SubscriptionFee = new SubscriptionFee();
  businessEntity: Entity = new Entity();
  isSubscriptionExpired: boolean = false;
  isPrimaryUser: boolean = false;
  isOnGracePeriod: boolean = false;
  constructor(
    private subscriptionService: SubscriptionService,
    private entityService: EntityService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getSubscriptionPlanList();
    this.loadCurrentPlan();
    this.loadBusinessEntity();
    this.checkSubscription();
    this.setUserRole();
    this.checkEntityOnGracePeriod();
  }

  setUserRole(): void {
    const userData = localStorage.getItem('user');
    if (userData) {
      const user = JSON.parse(userData);
      this.isPrimaryUser = user.userType === 'Primary user';
    }
  }
  checkSubscription(): void {
    const entityId = Number(localStorage.getItem('entityId'));

    this.subscriptionService
      .getSubscriptionByEntityId(entityId)
      .subscribe((subscription) => {
        if (!subscription) {
          Swal.fire({
            title: 'No Subscription Found',
            text: 'Please subscribe to a plan.',
            icon: 'warning',
            confirmButtonText: 'OK',
          });
          return;
        }

        this.subscriptionService
          .checkSubscriptionExpiry(entityId)
          .subscribe((response) => {
            this.isSubscriptionExpired = response;
            if (this.isSubscriptionExpired) {
              Swal.fire({
                title: 'Subscription Expired',
                text: 'Your subscription has expired. Please renew the current plan.',
                icon: 'warning',
                confirmButtonText: 'OK',
              });
            }
          });
      });
  }

  private getSubscriptionPlanList() {
    this.subscriptionService.getAllSubscriptionFee().subscribe((data) => {
      this.subscriptionPlans = data.filter(
        (plan) => plan.subscriptionFeeId !== 1
      );
    });
  }

  private checkEntityOnGracePeriod() {
    const entityId = Number(localStorage.getItem('entityId'));

    if (!entityId) {
      console.error('Entity Id not found');
      return;
    }

    this.entityService
      .getBusinessEntityById(entityId)
      .subscribe((entityData: Entity) => {
        if (entityData && entityData.createdAt) {
          const createdDate: Date = new Date(entityData.createdAt);
          const today: Date = new Date();
          const expiryDate = new Date(createdDate);
          expiryDate.setMonth(expiryDate.getMonth() + 4);
          this.isOnGracePeriod = today < expiryDate;
        }
      });
  }

  private loadCurrentPlan(): void {
    if (Number(localStorage.getItem('entityId'))) {
      this.subscriptionService
        .getSubscriptionByEntityId(Number(localStorage.getItem('entityId')))
        .subscribe((data) => {
          this.subscription = data;
          if (
            this.subscription.subscriptionFeeId?.subscriptionPlanId
              .subscriptionPlanId == 1
          ) {
            this.isFreePlan = true;
          }
          this.selectedSubscriptionPlan =
            data.subscriptionFeeId?.subscriptionFeeId;
        });
    }
  }

  private loadBusinessEntity(): void {
    if (Number(localStorage.getItem('entityId'))) {
      this.entityService
        .getBusinessEntityById(Number(localStorage.getItem('entityId')))
        .subscribe((data) => {
          this.businessEntity = data;
        });
    }
  }

  changePlan(planId: number): void {
    if (!this.isPrimaryUser) {
      Swal.fire({
        title: 'Access Denied',
        text: 'Only the primary user can change or renew subscription plans.',
        icon: 'error',
        confirmButtonText: 'OK',
      });
      return;
    }
    /**   if (!this.isSubscriptionExpired) {
      Swal.fire({
        title: 'Subscription Still Active',
        text: 'You can only renew or change your subscription after it expires.',
        icon: 'info',
        confirmButtonText: 'OK',
      });
      return;
    }**/

    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to select this plan?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes',
      cancelButtonText: 'No',
    }).then((result) => {
      if (result.isConfirmed) {
        this.navigateToPayment(planId);
      } else {
        Swal.fire({
          title: 'Cancelled',
          text: 'Plan selection was cancelled.',
          icon: 'info',
          timer: 1500,
          showConfirmButton: false,
        });
      }
    });
  }

  navigateToPayment(planId: number): void {
    if (!this.isPrimaryUser) {
      Swal.fire({
        title: 'Access Denied',
        text: 'Only the primary user can change or renew subscription plans.',
        icon: 'error',
        confirmButtonText: 'OK',
      });
      return;
    }
    const subscriptionPlanId = planId;
    const onGrace = this.isOnGracePeriod;
    this.router.navigate(['/payment'], {
      queryParams: { subscriptionPlanId ,onGrace},
    });
  }
}
