// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  apiUrl: 'http://localhost:8185',
  salesApiUrl: 'http://localhost:8187',
  financeApiUrl: 'http://localhost:8189',
  payrollApiUrl: 'http://localhost:8191',
  stripePublicKey:
    'pk_test_51QkGziIpTeGoc4k4Hc41lExeecR8vxTbrHW67DkXnir1hIInlP16ZxldJzfGQXqwldMqjiRDYWY3EnfM7QvkCkmc00OpDjkDmP',
  addressFinderApiKey: 'WFLPYDN4Q8CTK7RVEMUG',
  addressfinderBaseUrl:
    'https://api.addressfinder.io/api/au/address/autocomplete',
  abnLookUpBaseUrl: 'https://abr.business.gov.au/json/AbnDetails.aspx',
  abnLookUpGuid: '7a6ed436-63ae-4b07-bcef-73a2095892d9',
   secureApiKey:
    '9X#pZs@84YqF!wT6rLm29vAq#KtEiJ$0MhRdPnVzLsUgCbXeGtDwQkNbLyZuXcEj',
     recaptchaSiteKey: '6LcVIEMrAAAAAL8f2ZTZmpj6tfIsVWBgtsJjJVuL',
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
