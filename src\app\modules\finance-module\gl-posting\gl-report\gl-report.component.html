<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <!-- Heading And Button -->
  <div class="actions">
    <h1>General Ledger</h1>
    <button (click)="exportToExcel()" class="btn btn-primary">Export to Excel</button>

    <!-- <h1>General Ledger Accounts</h1> -->

    <!-- <div class="btn-group">

      <button type="button" class="btn btn-secondary dropdown-toggle gradient-btn" data-bs-toggle="dropdown"
        aria-expanded="false">
        <i class="bi bi-three-dots-vertical"></i>

      </button>
      <ul class="dropdown-menu dropdown-menu-end">
        <li>
          <a class="dropdown-item" (click)="recordBatchPayment()">Record Batch Payment</a>
        </li>
        <li>
          <a class="dropdown-item">Preview & Print</a>
        </li>
      </ul>
    </div> -->
  </div>

  <!-- <div class="search-create">
    <button type="button" (click)="addPaybleBill()" class="invoice-convert">
      Add Payable Bill
    </button>
  </div> -->

  <!-- Tabs -->
  <!-- <div>
    <ul class="nav nav-tabs mb-3 justify-content-start">
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'all'" (click)="setActiveTab('all')">All</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'pending'" (click)="setActiveTab('pending')">Pending</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'paid'" (click)="setActiveTab('paid')">Paid</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'overdue'" (click)="setActiveTab('overdue')">Overdue</a>
      </li>
    </ul>
  </div> -->

  <div class="Card">
    <!-- Filter and Search Section -->
    <div class="row1">
      <!-- Input for number, reference -->
      <div class="row1_col1">
        <label for="search-input">GL Account Code or Account Name</label>
        <div class="input-container">
          <input type="text" class="search-input" id="search-input" [(ngModel)]="searchTerm" />
          <i class="bi bi-search"></i>
        </div>
      </div>

      <!-- <div class="row1_col3">
        <label for="StartDate">Start Date</label>
        <input type="date" class="date-picker" id="StartDate" [(ngModel)]="startDate" />
      </div>

      <div class="row1_col4">
        <label for="EndDate">End Date</label>
        <input type="date" class="date-picker" id="EndDate" [(ngModel)]="endDate" />
      </div> -->
    </div>

    <div class="row2">
      <div class="row2_col3">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>
      <div class="row2_col1">
        <button type="button" class="primary-button" (click)="filterQuotes()">
          Search
        </button>
      </div>
    </div>
  </div>


  <div class="table-responsive">
    <div *ngIf="isLoading" class="loading-indicator">
      <p>Loading...</p>
      <!-- You can add a spinner or other animation here -->
    </div>

    <div *ngIf="!isLoading">
      <table>
        <thead>
          <tr class="table-head">
            <th scope="col" class="valuehead" style="text-align: left">GL Account Code</th>
            <th scope="col" class="valuehead" style="text-align: left">GL Account</th>
            <th scope="col" class="valuehead" style="text-align: right">Dr</th>
            <th scope="col" class="valuehead" style="text-align: right">Cr</th>
            <th scope="col" class="valuehead" style="text-align: left">Action</th>
          </tr>
        </thead>
        <tbody>
        <tbody *ngFor="let data of filteredRecords; let i = index">
          <tr>
            <td class="value" style="text-align: left">{{ data.accountCode}}</td>
            <td class="value" style="text-align: left">{{ data.accountName}}</td>
            <td class="value" style="text-align: right">
              {{ data.totalDr | currency }}
            </td>
            <td class="value" style="text-align: right">
              {{ data.totalCr | currency }}
            </td>
            <td class="value" style="text-align: left">
              <button *ngIf="data.accountCode === '800' || data.accountCode === '600'"  class="btn btn-orange btn-sm"
                (click)="openDetailsPage(data.accountCode)"
                style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" title="View">
                <i class="bi bi-arrows-fullscreen" style="color: #4262ff"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>