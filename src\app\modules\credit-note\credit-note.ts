export class CreditNote {

  creditNoteId: number = 0;
  details: CreditNoteDetail[] = [];
  businessPartnerId: any = 0;
  entityId: number = 0;
  userId: number = 0;
  creditNoteNumber: string = '';
  documentType: string = '';
  documentDate: string = '';
  contactNumber: string = '';
  paidAmount: number = 0;
  balanceDue: number = 0;
  documentStatus: string = '';
  transactionDate: string = '';
  remarks: string = '';
  totalCreditAmount: number = 0;
  customerName: string = '';
  invoiceNumber: string = '';
  selected: boolean = false; 
  invoiceNumberInput: string ='';
   isLocked?: boolean;
}


export class CreditNoteDetail {
  creditNoteDetailId: number = 0;
  creditNote: CreditNote = new CreditNote();
  invoiceHead: number = 0;
  invoiceNumber: string = '';
  postingDate: string = '';
  grandTotal: number = 0.0;
  balanceAmount: number = 0.0;
  creditAmount: number = 0.0;
}