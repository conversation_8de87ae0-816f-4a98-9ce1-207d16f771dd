import { Component, OnInit, ViewChild } from '@angular/core';
import { UserService } from '../../admin/components/user/user.service';
import { Entity } from '../entity';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';

@Component({
  selector: 'app-entity-selection',
  templateUrl: './entity-selection.component.html',
  styleUrls: ['./entity-selection.component.css'],
})
export class EntitySelectionComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  userId: number = 0;
  entities: Entity[] = [];

  constructor(private router: Router, private userService: UserService) {}

  ngOnInit(): void {
    const storedUserId = localStorage.getItem('userid');
    this.userId = storedUserId ? +storedUserId : 0;

    this.loadEntities();
  }

  private loadEntities(): void {
  this.userService.getEntitiesForUser(this.userId).subscribe(
    (response) => {
      // Filter out entities with the name "Default Entity"
      this.entities = response.filter(entity => entity.entityName !== 'Default Entity');
    },
    (error) => {
      console.error('Error fetching entities:', error);
      Swal.fire({
        title: 'Error!',
        text: 'Failed to load entities. Please try again later.',
        icon: 'error',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
      }).then((result) => {
        if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData('Failed to load entities. Please try again later');
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });
    }
  );
}

/**setEntityId(entityId: number): void {
  const currentEntityId = localStorage.getItem('entityId');

  if (currentEntityId === entityId.toString()) {
    // Same entity, just update and go to dashboard
    localStorage.setItem('entityId', entityId.toString());
    this.router.navigate(['/sales-dashboard']);
  } else {
    // Different entity: logout and redirect to login with entityId
    this.router.navigate(['/user-login'], { queryParams: { entityId } });
  }
}

**/ 
setEntityId(entityId: number): void {
  const userStr = localStorage.getItem('user');
  if (!userStr) return;

  const user = JSON.parse(userStr);
  const currentEntityId = localStorage.getItem('entityId');

  if (currentEntityId === entityId.toString()) {
    this.router.navigate(['/sales-dashboard']);
  } else {
    // Call switch-entity endpoint to get a new token
    const payload = {
      username: user.username,
      entityId: entityId
    };

    this.userService.request('POST', '/switch-entity', payload).subscribe({
      next: (response: any) => {
        // Update token and entity
        this.userService.setAuthToken(response.token);
        localStorage.setItem('user', JSON.stringify(response));
        localStorage.setItem('entityId', String(response.entityIds[0]));

        // ✅ Fetch UUID using updated entityId
        this.userService.request('GET', `/business-entity/uuid/${entityId}`, null).subscribe({
          next: (uuidResponse: any) => {
            if (uuidResponse && uuidResponse.entityUuid) {
              localStorage.setItem('entityUuid', uuidResponse.entityUuid);
            }
            this.router.navigate(['/sales-dashboard']);
          },
          error: (err) => {
            console.error('Failed to fetch entity UUID:', err);
            // Still navigate even if UUID fetch fails
            this.router.navigate(['/sales-dashboard']);
          }
        });
      },
      error: () => {
        alert("Failed to switch entity. Please try again.");
      }
    });
  }
}



  requestEntity(): void {
    this.router.navigate(['/request-businessEntity']);
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }
}
