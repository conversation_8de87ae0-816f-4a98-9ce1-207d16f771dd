<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <form
    #f="ngForm"
    (ngSubmit)="(f.form.valid)"
    class="row g-1"
    novalidate="feedback-form"
  >
    <div class="heading" (keydown)="preventSubmit($event)">
      <h3>Create Journal Voucher</h3>
    </div>

    <div class="bd">
      <div class="form-section">
        <!-- first row -->
        <div class="form-row" style="display: flex; justify-content: space-between; margin-bottom: 20px;">
          <div class="form-group" style="display: flex; flex-grow: 1">
            <label for="jvNumber" id="quotation" style="margin-right: 10px; white-space: nowrap;">JV Number</label>
            <input class="input-style" type="text" id="jvNumber" [(ngModel)]="glPostingHead.jvNumber" name="jvNumber" #jvNumber="ngModel" readonly />
          </div>

          <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1">
            <div style="display: flex" class="form-dataInput">
              <label for="date" style="margin-right: 28px; white-space: nowrap">Date</label>
              <div style="position: relative; width: 100%;">
                <input 
                  matInput
                  [matDatepicker]="datePicker"
                  class="input-style" 
                  id="date"
                  [(ngModel)]="glPostingHead.date" 
                  name="date" 
                  (change)="onJvDateChange()"
                  required
                />
                <mat-datepicker-toggle matSuffix [for]="datePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
              </div>
              <mat-datepicker #datePicker></mat-datepicker>
            </div>

            <div *ngIf="f.submitted && f.controls['date'].invalid" class="text-danger" style="margin-top: 5px; margin-left: 110px">
              <div *ngIf="f.controls['date'].errors?.['required']">Date is required.</div>
            </div>
          </div> 
        </div>

        <!-- second row -->
        <div class="form-row" style="display: flex; justify-content: space-between;">
          <div class="form-group" style="flex-grow: 1">
            <div class="form-dataInput" style="display: flex;">
              <label for="customer" style="margin-right: 13px; white-space: nowrap" class="InputLabel">Supplier</label>
              <select class="form-select custom-dropdown" id="customer" [(ngModel)]="glPostingHead.businessPartnerId" name="customerName">
                <option value="" selected disabled>Select Supplier</option>
                <option *ngFor="let customer of customers" [value]="customer.businessPartnerId">{{ customer.bpName }}</option>
              </select>
            </div>

            <div class="create-customer-container" style="margin-top: 5px;">
              <button type="button" class="create-customer-btn" (click)="loadBusinessPartnerTypes()" data-bs-target="#customerPopUpModal" data-bs-toggle="modal">Create New Supplier</button>
            </div>

            <div *ngIf="f.submitted && f.controls['customerName'].invalid" class="text-danger" style="margin-top: 30px; margin-left: 140px">
              <div *ngIf="f.controls['customerName'].errors?.['required']">Supplier is required.</div>
            </div>
          </div>

          <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1">
            <div style="display: flex;" class="form-dataInput">
              <label for="description" style="white-space: nowrap">Narration</label>
              <textarea class="input-style auto-resize"
                id="description"
                [(ngModel)]="glPostingHead.description"
                name="description"
                placeholder="Brief Description"
                required>
              </textarea>
            </div>
          </div>
        </div>
      </div>

      <div class="table-section">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th style="width: 33%">Description</th>
                <th style="width: 17%">Account</th>
                <th style="width: 17%; text-align: center">Dr</th>
                <th style="width: 13%; text-align: center">Cr</th>
                <th style="width: 3%"></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let row of rows; let i = index">
                <td>
                  <input
                    type="text"
                    [(ngModel)]="row.description"
                    name="itemDescription-{{ i }}"
                    class="form-control"
                    placeholder="Enter Description"
                    (input)="onDescriptionInput(i)"
                  />
                </td>
                <td>
                  <select
                    class="form-control"
                    name="coaLedgerAccount-{{ i }}"
                    [(ngModel)]="row.coaLedgerAccountId"
                    required
                  >
                    <option value="" disabled>GL Account</option>
                    <option
                      *ngFor="let account of coaLedgerAccounts"
                      [value]="account.coaLedgerAccountId"
                    >
                      {{ account.ledgerAccountName }}
                    </option>
                  </select>
                </td>
                <td>
                  <input
                    type="number"
                    [(ngModel)]="row.debit"
                    name="debit-{{ i }}"
                    class="form-control"
                    placeholder="Enter Debit Amount"
                    (input)="updateTotals()"
                    [required]="!row.credit"
                  />
                </td>
                <td>
                  <input
                    type="number"
                    [(ngModel)]="row.credit"
                    name="credit-{{ i }}"
                    class="form-control"
                    placeholder="Enter Credit Amount"
                    (input)="updateTotals()"
                    [required]="!row.debit"
                  />
                </td>

                <td>
                  <button
                    type="button"
                    class="btn btn-link"
                    (click)="removeRow(i)"
                  >
                    <i class="fa fa-trash" style="color: red"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

                    <button type="button" class="btn btn-primary" (click)="addRow()"><i class="bi bi-plus-circle-fill"></i> Add New Row</button>
      
        </div>
      </div>

      <div class="notes-totals-section">
        <div class="totals-section">
          <div class="totals-row">
            <span class="totals-row1">Total Debit </span>
            <span class="totals-row2" [class]="isAmountEqual ? 'equal' : 'not-equal'">{{ totalDebit | currency }}</span>
          </div>
          <div class="totals-row">
            <span class="totals-row1">Total Credit </span>
            <span class="totals-row2" [class]="isAmountEqual ? 'equal' : 'not-equal'">{{ totalCredit | currency }}</span>
          </div>
        </div>
      </div>

      <div class="footer-section">
        <div class="footer-btn-section">
          <button type="button" class="cancel-btn" type="reset" (click)="navigateJVList()">
            Cancel
          </button>
          <button 
            class="add-btn" 
            type="submit" 
            (click)="saveJV()" 
            [disabled]="!isAmountEqual || !glPostingHead.date || !glPostingHead.description || !areAllRowsValid()">
            Post
          </button>
          
         <!--<button type="submit" class="add-btn" (click)="saveDraftJV()">
            Save as Draft
          </button>-->
        </div>
      </div>
    </div>
  </form>
</div>

<div
  class="modal fade"
  id="customerPopUpModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="simpleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" style="max-width: 740px">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="simpleModalLabel">Add New Supplier</h5>
        <div class="icon-group ms-auto">
          <i
            class="bi bi-x-circle close-icon"
            #closeCustomerPopUp
            data-bs-dismiss="modal"
            aria-label="Close"
            title="Close"
          ></i>
        </div>
      </div>

      <div class="modal-body">
        <form
          #cuspop="ngForm"
          name="cuspop"
          (ngSubmit)="cuspop.form.valid && onSubmitCustomerForm()"
          class="row g-1"
          novalidate="feedback-form"
          (keydown)="preventSubmit($event)"
        >
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="partner-type">Business Partner Type</label>
                <select
                  class="form-select"
                  id="partner-type"
                  [(ngModel)]="
                    businessPartner.businessPartnerTypeId.businessPartnerTypeId
                  "
                  name="businessPartnerType"
                  style="
                    border: 1px solid #c7c7c7;
                    border-radius: 8px;
                    width: 100%;
                    height: 43.41px;
                  "
                  disabled
                >
                  <option value="" selected disabled>
                    Select Partner Type
                  </option>
                  <option
                    *ngFor="let customer of businessPartnerType"
                    [value]="customer.businessPartnerTypeId"
                  >
                    {{ customer.businessPartnerType }}
                  </option>
                </select>

                <div
                  *ngIf="
                    cuspop.submitted &&
                    cuspop.controls['businessPartnerType'].invalid
                  "
                  class="text-danger"
                ></div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6" style="width: 100%;">
              <div class="form-group">
                <label for="bp-name">Business Partner Name</label>
                <input
                  type="text"
                  id="bp-name"
                  [(ngModel)]="businessPartner.bpName"
                  name="bpName"
                  required
                />

                <div
                  *ngIf="cuspop.submitted && cuspop.controls['bpName'].invalid"
                  class="text-danger"
                >
                  <div *ngIf="cuspop.controls['bpName'].errors?.['required']">
                    Business Partner Name is required.
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="email">Email </label>
                <input
                  type="email"
                  id="email"
                  [(ngModel)]="businessPartner.email"
                  name="email"
                  pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                />

                <div
                  *ngIf="cuspop.submitted && cuspop.controls['email'].invalid"
                  class="text-danger"
                >
                  <div *ngIf="cuspop.controls['email'].errors?.['pattern']">
                    Invalid email format.
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="contactPhoneNumber">Contact Number</label>
                <input
                  type="text"
                  id="contactPhoneNumber"
                  [(ngModel)]="businessPartner.contactPhoneNumber"
                  name="contactPhoneNumber"
                />
                <div
                  *ngIf="
                    cuspop.submitted &&
                    cuspop.controls['contactPhoneNumber'].invalid
                  "
                  class="text-danger"
                ></div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="abn-number"
                  >ABN Number <small>(Optional)</small></label
                >
                <input
                  type="text"
                  id="abn-number"
                  [(ngModel)]="businessPartner.abnNumber"
                  name="abnNumber"
                />
              </div>
            </div>
            <!-- <div class="col-md-6">
              <div class="form-group">
                <label for="acn-number">ACN Number</label>
                <input
                  type="text"
                  id="acn-number"
                  [(ngModel)]="businessPartner.acnNumber"
                  name="acnNumber"
                />
              </div>
            </div> -->
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="default-payment-terms"
                  >Default Payment Terms <small>(Optional)</small></label
                >
                <select
                  class="form-select"
                  id="default-payment-terms"
                  [(ngModel)]="businessPartner.defaultPaymentTerms"
                  name="defaultPaymentTerms"
                  style="
                    border: 1px solid #c7c7c7;
                    border-radius: 8px;
                    width: 100%;
                    height: 43.41px;
                  "
                >
                  <option value="" disabled selected>
                    Select default payment terms
                  </option>
                  <option value="net30">Net 30</option>
                  <option value="2/10Net30">2/10 Net 30</option>
                  <option value="dueOnReceipt">Due on Receipt</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6" style="width: 100%;">
              <div class="form-group">
                <label for="business-address">Business Address</label>
                <input
                  type="text"
                  id="business-address"
                  [(ngModel)]="businessPartner.businessAddress"
                  name="businessAddress"
                />
                <div
                  *ngIf="
                    cuspop.submitted &&
                    cuspop.controls['businessAddress'].invalid
                  "
                  class="text-danger"
                ></div>
              </div>
            </div>
            <!-- <div class="col-md-6">
              <div class="form-group">
                <label for="delivery-address">Delivery Address</label>
                <input
                  type="text"
                  id="delivery-address"
                  [(ngModel)]="businessPartner.deliveryAddress"
                  name="deliveryAddress"
                />
              </div>
            </div> -->
          </div>
          <div class="row">
            <div class="col-md-6"></div>
          </div>
          <hr />
          <h3>Additional</h3>
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="bank-account-name">Bank Account Name</label>
                <input
                  type="text"
                  id="bank-account-name"
                  [(ngModel)]="businessPartner.bankAccountName"
                  name="bankAccountName"
                />
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="bsb">BSB</label>
                <input
                  type="text"
                  id="bsb"
                  [(ngModel)]="businessPartner.bsb"
                  name="bsb"
                />
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="bank-account-number">Bank Account Number</label>
                <input
                  type="text"
                  id="bank-account-number"
                  [(ngModel)]="businessPartner.accountNumber"
                  name="accountNumber"
                />
              </div>
            </div>
          </div>
          <!-- Footer Buttons -->
          <!--<div class="popup-footer">
            <a href="/create-quotation"  style="text-align: left;" >Update Customer</a>
           <button type="button" class="cancel-btn1" #closeCustomerPopUp data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="add-btn1">Add</button>
         
          </div>-->

          <div class="popup-footer">
            <div class="left-section">
              <a href="/business-partner" class="cancel-btn1" style="padding: 10px 20px;">Update Customer</a>
            </div>  
            <div class="right-section">
              <button type="button" class="cancel-btn1" #closeCustomerPopUp>Cancel</button>
              <button type="submit" class="add-btn1">Add</button>
            </div>
          </div>

        </form>
      </div>
    </div>
  </div>
</div>