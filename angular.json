{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"lc-frontend": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["sweetalert2", "file-saver", "munkres-js", "pdfjs-dist", "moment"], "outputPath": "dist/lc-frontend", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/datatables.net-bs/css/dataTables.bootstrap.min.css", "node_modules/@ng-select/ng-select/themes/default.theme.css", "node_modules/bootstrap-icons/font/bootstrap-icons.css", "node_modules/@mdi/font/css/materialdesignicons.min.css", "src/assets/vendor/boxicons/css/boxicons.min.css", "src/assets/vendor/quill/quill.snow.css", "src/assets/vendor/quill/quill.bubble.css", "src/assets/vendor/remixicon/remixicon.css", "src/styles.css", "src/assets/css/style.css"], "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/datatables.net/js/jquery.dataTables.js", "node_modules/datatables.net-bs/js/dataTables.bootstrap.min.js", "node_modules/bootstrap/dist/js/bootstrap.bundle.min.js", "src/js/scripts.js", "src/js/all.min.js"], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "6mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "30kb"}], "outputHashing": "all", "sourceMap": false, "extractLicenses": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "lc-frontend:build:production"}, "development": {"browserTarget": "lc-frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "lc-frontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"karmaConfig": "./karma.conf.js", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": "8daa9dcc-fa62-4135-b9eb-23f071e14e20"}}