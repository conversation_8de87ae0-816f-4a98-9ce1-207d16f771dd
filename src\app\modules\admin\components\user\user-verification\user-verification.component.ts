import { Component, OnInit } from '@angular/core';
import { UserService } from '../user.service';
import { ActivatedRoute, Router } from '@angular/router';
import { User } from '../user';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-user-verification',
  templateUrl: './user-verification.component.html',
  styleUrls: ['./user-verification.component.css'],
})
export class UserVerificationComponent implements OnInit {
  token: string = '';
  userId: string = '';
  user: User = new User();

  constructor(
    private route: ActivatedRoute,
    private userService: UserService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.token = this.route.snapshot.queryParamMap.get('token') || '';
    this.userId = this.route.snapshot.queryParamMap.get('userId') || '';

    if (this.userId !== null) {
      this.userService.getUserById(Number(this.userId)).subscribe(
        (response: User) => {
          this.user = response;
        },
        (error) => {
          console.error('Error fetching user data', error);
        }
      );
    } else {
      console.error('User not found');
    }
  }

  onSubmit() {
    this.userService.setUserVerified(this.token).subscribe((response) => {
      Swal.fire({
        title: 'Success!',
        text: 'User verified successfully.',
        icon: 'success',
        confirmButtonText: 'OK',
        confirmButtonColor: '#28a745',
      }).then((result) => {
        if (result.isConfirmed) {
          this.router.navigate(['user-login']);
        }
      });
    });
  }
}
