<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response> 

<div class="container">
    <div class="actions">
        <h1>Sales Quotes</h1>
        <div class="btn-group" #dropdownRef [class.show]="isDropdownOpen">
          <!-- Dropdown button with vertical dots -->
          <button
            type="button"
            class="btn btn-secondary dropdown-toggle gradient-btn"
            data-bs-toggle="dropdown"
            aria-expanded="false"
            (click)="toggleDropdown()"
            >
            <!-- Vertical dots icon -->
            <i class="bi bi-three-dots-vertical"></i>
          </button>

          <ul class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen">
              <li>
                <a
                  class="dropdown-item"
              
                  (click)="handleSendQuoteClick(); closeDropdown()"
                >Send</a>
              </li>
              <li><a class="dropdown-item" (click)="handleReviseQuotation(); closeDropdown()">Revise Quote</a></li>
              <li><a class="dropdown-item" (click)="handleCreateInvoice(); closeDropdown()">Create Invoice</a></li>
          </ul>
        </div>
    </div>

    <div class="search-create">
      <button class="create-quote" (click)="createQuote()">Create New Quote</button>
      <button class="copy-quote" (click)="handleCopyFromQuote()">Copy From Existing Quote</button>
      <button class="export-btn" (click)="exportToExcel()">Export to Excel</button>
    </div>
    <!-- Tabs -->
    <ul class="nav nav-tabs mb-3 justify-content-start">
        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'all'" (click)="setActiveTab('all')">All</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'pending'" (click)="setActiveTab('pending')">Pending</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'sent'" (click)="setActiveTab('sent')">Sent</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'revised'" (click)="setActiveTab('revised')">Revised</a>
        </li>
    </ul>

      <div class="Card">
    <!-- Filter and Search Section -->
    <div class="row1">
      <!-- Input for number, reference -->
      <div class="row1_col1">
        <label for="search-input">Quote Number or Customer</label>
        <div class="input-container">
          <input
            type="text"
            class="search-input"
            id="search-input"
            [(ngModel)]="searchTerm"
            (keydown.enter)="filterQuotes()"
          />
          <i class="bi bi-search"></i>
        </div>
      </div>

      <div class="row1_col3">
        <label for="StartDate">Start Date</label>
        <input
          type="date"
          class="date-picker"
          id="StartDate"
          [(ngModel)]="startDate"
        />
      </div>

      <div class="row1_col4">
        <label for="EndDate">End Date</label>
        <input
          type="date"
          class="date-picker"
          id="EndDate"
          [(ngModel)]="endDate"
        />
      </div>
    </div>

    <div class="row2">
      <div class="row2_col3">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>
      <div class="row2_col1">
        <button type="button" class="primary-button" (click)="filterQuotes()">
          Search
        </button>
      </div>
    </div>
  </div>

    <div class="table-wrapper">
    <table>
        <thead>
            <tr class="table-head">
                <th scope="col" class="valueCheckbox"><input type="checkbox" (change)="selectAll($event)" /></th>
                <th scope="col" class="valuehead">Quote Number</th>
                <th scope="col" class="valuehead">Customer</th>
                <th scope="col" class="valuehead">Quote Date</th>
                <th style="text-align: center;" scope="col" class="valuehead">Valid Until</th>
                <th style="text-align: right;" scope="col" class="valuehead">Amount</th>
                <th style="text-align: center;" scope="col" class="valuehead">Status</th>
                <th style="text-align: center;" scope="col" class="valuehead">Actions</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let quote of filteredQuotes; let i = index"
              [ngStyle]="{
            color: quote.status === 'Expired' ? '#b81735' : 'inherit'
          }">
                <td class="valueCheckbox"><input type="checkbox" [(ngModel)]="quote.selected" /></td>
                <td class="value">{{ quote.quoteNumber }}</td>
                <td class="value">{{ quote.customerName }}</td>
                <td class="value">{{ quote.quoteDate | date:'dd-MM-yyyy' }}</td>
                <td style="text-align: center;" class="value">{{ quote.validUntilDate | date:'dd-MM-yyyy' }}</td>
                <td style="text-align: right;" class="value">{{ quote.grandTotal | currency}}</td>

                         <td class="value" style="padding-left: 15px;" [ngClass]="{
                    'text-pending': quote.status === 'Pending',
                    'text-canceled': quote.status === 'Canceled',
                    'text-revised': quote.status === 'Revised',
                    'text-sent': quote.status === 'Sent',
                    'text-paid': quote.status === 'Paid',
                    'text-Invoiced': quote.status === 'To Invoice',
                    'text-awaiting-payment': quote.status === 'Awaiting Payment',   
                    'text-expired': quote.status === 'Expired' 
                }">
                    <span class="lable" [ngClass]="{
                                'border-pending': quote.status === 'Pending',
                                'border-canceled': quote.status === 'Canceled',
                                'border-revised': quote.status === 'Revised',
                                'border-sent': quote.status === 'Sent',
                                'border-paid': quote.status === 'Paid',
                                'border-Invoiced': quote.status === 'To Invoice',
                                'border-awaiting-payment': quote.status === 'Awaiting Payment',   
                                'border-expired': quote.status === 'Expired'   
                            }">{{ quote.status }}</span>
                </td>
                <td style="text-align: center;" class="value">
                     <button (click)="viewQuotation(quote.quoteId)"

                     [disabled]="
                    quote.status === 'Canceled' ||
                    quote.status === 'Revised'
                    "
                   
                  class="btn btn-orange btn-sm" style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" 
                  title="View">
                    <i class="bi bi-arrows-fullscreen" style="color: #4262FF;"
                      [style.color]="
                      quote.status === 'Revised' 
                       ? '#aaa' 
                        : '#4262ff' 
                          "></i>
                  </button>

                    <button class="btn btn-warning btn-sm"
                     data-bs-target="#quotePreviewModal"
                     data-bs-toggle="modal" 
                     (click)="previewQuotation(quote.quoteId);"
                     style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" 
                     [disabled]="
                     quote.status === 'Revised'
                     "
                     title="Preview">
                    <i class="bi bi-eye" style="color: #debe15"
                     [style.color]="
    
                     quote.status === 'Revised'      
                     ? '#aaa'      
                     : '#debe15'  
                     "></i>
                    </button>

                    <button (click)="deleteQuotation(quote.quoteId, i)" class="btn btn-danger btn-sm"
                     style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" data-bs-toggle="tooltip" title="Delete">
                        <i class="ri-delete-bin-line" style="color: #FF0000;"></i>
                    </button>
                </td>
            </tr>
        </tbody>
    </table>
  </div>
</div>

<!-- Simple Popup Modal -->
<div class="modal fade" id="quotePreviewModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 740px;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="simpleModalLabel">Quote</h5>
                <div class="ml-auto icon-group">
                    <i class="bi bi-x-circle close-icon" #closePreview data-bs-dismiss="modal" aria-label="Close" title="Close"></i>
                </div>
            </div>

            <div class="modal-body">
                <!-- Loading Spinner -->
                <div *ngIf="isLoading" class="spinner-container">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>

                <!-- IFrame for Quotation Preview -->
                <div style="margin-top: 20px;" [ngClass]="{ 'd-none': isLoading}">
                    <iframe #quotationPreviewFrame id="quotationPreviewFrame" width="700px" height="700px"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Send Quote -->
<div
  class="modal fade"
  id="sendQuoteModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="simpleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" style="max-width: 640px; max-height: 1080px;">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="simpleModalLabel">Send Quotes</h5>
        <button
          type="button"
          class="btn-close custom-close-btn"
          aria-label="Close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <form #sendQuote="ngForm" name="sendQuote" (ngSubmit)="sendQuote.form.valid && sendSelectedQuotes()" novalidate="feedback-form">
          <div class="form-group">
            <label for="recipientEmail">Recipient Email</label>
            <input
              type="email"
              id="TempRecipientEmail1"
              name="TempRecipientEmail"
              [(ngModel)]="TempRecipientEmail"
              placeholder="Enter a temporary email or  loaded email"
              class="form-control"
              required
              email
              #TempRecipientEmailRef="ngModel"
            />
            <p *ngIf="!TempRecipientEmail">Using stored email: {{ recipientEmail }}</p>
            <div *ngIf="sendQuote.submitted && TempRecipientEmailRef.invalid" class="text-danger">
            <div *ngIf="TempRecipientEmailRef.errors?.['required']">Email is required.</div>
            <div *ngIf="TempRecipientEmailRef.errors?.['email']">Enter a valid email address.</div>
            </div>

          </div>
          <div class="class-name">
            <div class="form-group">
              <label for="subject">Subject</label>
              <input
              type="text"
              id="subject"
              name="subject"
              [(ngModel)]="subject"
              required
            />
            <div *ngIf="sendQuote.submitted && sendQuote.controls['subject'].invalid" class="text-danger">
              <div *ngIf="sendQuote.controls['subject'].errors?.['required']">Subject is required.</div>
            </div>
            </div>
          </div>

          <div class="class-name">
            <div class="form-group">
              <label for="message">Message</label>
              <textarea
                id="message"
                name="message"
                rows="4"
                style="min-height: 350px; height: fit-content;"
                [(ngModel)]="content"
                required
              ></textarea>
              <div *ngIf="sendQuote.submitted && sendQuote.controls['message'].invalid" class="text-danger">
                <div *ngIf="sendQuote.controls['message'].errors?.['required']">Message is required.</div>
              </div>
            </div>
          </div>
          <div class="popup-footer">
            <button
              type="button"
              #closeSendQuote
              class="cancel-btn"
              data-bs-dismiss="modal"
            >
              Close
            </button>
            <button
              type="submit"
              class="add-btn"
              [disabled]="isSending"
            >
            {{ isSending ? 'Sending...' : 'Send' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
