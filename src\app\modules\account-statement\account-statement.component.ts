import { Component, ElementRef, ViewChild } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { BusinessPartner } from '../business-partner/business-partner';
import { BusinessPartnerService } from '../business-partner/business-partner.service';
import { InvoiceHead } from '../invoice/invoice';
import { InvoiceService } from '../invoice/invoice.service';
import { CustomerStatementService } from '../customer-statement/customer-statement.service';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { SwalAlertsService } from '../swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-account-statement',
  templateUrl: './account-statement.component.html',
  styleUrls: ['./account-statement.component.css']
})
export class AccountStatementComponent {
  @ViewChild('accountStatemnetPreviewFrame') accountStatemnetPreviewFrame!: ElementRef;
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  fromDate: string = '';
  toDate: string = '';
  status: string = '';
  overdueOption: string = '';
  overdueFromDate: string = '';
  overdueToDate: string = '';
  minAmount: string  ='';
  maxAmount: string = '';
  reference:string ='';
  isLoading = false;
  customers: BusinessPartner[] = [];
  invoiceHead: InvoiceHead = new InvoiceHead();
  getAllCustomers = false; 

  constructor(
    private invoiceService: InvoiceService,
    private swalAlerts: SwalAlertsService,
    private router: Router, 
    public sanitizer: DomSanitizer, 
    private businessPartnerService: BusinessPartnerService, 
    private customerStatementService: CustomerStatementService) {}
    
    ngOnInit() {
    this.invoiceHead.businessPartnerId = '0';
    this.loadCustomers();
  }


   previewAccountStatemnet(fromDate: string, toDate: string,
            businessPartnerId: any) {
            fromDate = '2025-01-01';
          if (!fromDate || !toDate) {
        
            this.swalAlerts.showSwalWarning('Warning!', 'Please select Date Range', 'Missing date range for Account Statementt.');
            return;
          }
          this.isLoading = true;
          const entityId = +localStorage.getItem('entityId')!;
          const entityUUID = localStorage.getItem('entityUuid')!;
          const bpId = (businessPartnerId === 0 || businessPartnerId === '' || businessPartnerId == null) ? null : +businessPartnerId;
        
          const requestData = {
            fromDate,
            toDate,
            entityId,
            businessPartnerId: bpId,
            entityUUID
          };
        
          this.customerStatementService.getAccountsStatementReport(requestData).subscribe(
            data => {
              const base64String = data.response;
              if (base64String) {
                this.loadPdfIntoIframe(base64String);
              } else {
                this.isLoading = false;
                alert('No Account Statement data for preview.');
              }
            },
            error => {
              this.isLoading = false;
              alert('Error loading Account Statement preview.');
            }
          );
        }
        
        private loadPdfIntoIframe(base64String: string) {
          if (base64String && base64String.trim().length >= 50) {
            const pdfData = 'data:application/pdf;base64,' + base64String;
            const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
            const iframe = this.accountStatemnetPreviewFrame.nativeElement;
        
            iframe.onload = () => {
              this.isLoading = false;
            };
        
            iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
        
            // Open modal manually using Bootstrap JS
            const modalElement = document.getElementById('simpleModal');
            const modal = new bootstrap.Modal(modalElement!);
            modal.show();
          } else {
             this.isLoading = false;
             this.swalAlerts.showSwalWarning('No Data', 'No Account Statement data for preview.', 'No Account Statement data was returned for the selected range.');
        
          }
        }
        
        
         loadCustomers() {
          const entityId = +((localStorage.getItem('entityId')) + '');
          this.businessPartnerService.getCustomerListByEntity(entityId).subscribe(
            (customers: BusinessPartner[]) => {
              this.customers = customers;
            },
            (error: HttpErrorResponse) => {
              console.error('Error fetching customers', error);
      
              this.swalAlerts.showErrorWithChimpSupport(
                'Failed to load customers.',
                'Unable to fetch customer list for this entity. Please check if the customer service is responding.'
              );
            }
          );
        }
    
    


  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(cust => cust.businessPartnerId === +selectedCustomerId);

    // Set the reference field
    this.invoiceHead.reference = selectedCustomer?.bpName || '';
  }


  // Method to toggle all customers' data
  toggleAllCustomers() {
    this.getAllCustomers = !this.getAllCustomers;
    if (this.getAllCustomers) {
      this.invoiceHead.businessPartnerId = '';  // Clear selected customer when showing all
    }
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }
}


