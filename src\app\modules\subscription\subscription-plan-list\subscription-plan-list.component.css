.upper-content {
  padding: 10px 20px;
  text-align: center;
  background: #f3f2ef;
}

.upper-content h2 {
  font-size: 2.5em;
  font-family: sans-serif;
  font-weight: 600;
  margin: 20px;
  color: #000;
}

.upper-content p {
  font-family: Inter;
}

.plans {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
  padding-bottom: 20px;
}

.plan {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0px 4px 22px 11px rgba(0, 0, 0, 0.1);
  padding: 20px;
  width: 400px;
  height: 375px;
  text-align: left;
  margin-top: 40px;
  flex: 1 1 100%;
  max-width: 100%;
}

.plan h2 {
  font-family: Inter;
  font-size: 40px;
  font-weight: 700;
  line-height: 1.7;
  text-align: center;
  color: #000;
}

.plan p {
  font-family: "Inter", sans-serif;
  font-size: 15 px;
  font-weight: 500;
  text-align: center;
  color: #000;
}

.plan h4 {
  font-family: "Inter", sans-serif;
  font-size: 25px;
  font-weight: 800;
  line-height: 1.7;
  text-align: center;
  color: #4262ff;
}

.plan ul {
  list-style-type: none;
  padding: 0;
  margin-bottom: 20px;
  flex-grow: 1;
}

.plan ul li {
  margin-bottom: 10px;
  font-family: "Inter", sans-serif;
  font-size: 14px;
}

.plan ul li:before {
  content: "✔️";
  color: #4262ff;
  margin-right: 10px;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.change-plan {
  width: 100%;
  padding: 10px;
  border: none;
  border-radius: 10px;
  font-size: 1em;
  font-weight: 600;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
}

.change-plan:hover {
  background: linear-gradient(to left, #4262ff, #512ca2);
}

@media (max-width: 690px) {
  .plan {
    flex: 1 1 100%;
    max-width: 100%;
    margin-inline: 20px;
  }
}

@media (min-width: 691px) and (max-width: 1024px) {
  .plan {
    flex: 1 1 calc(50% - 60px);
    max-width: calc(50% - 60px);
  }
}

@media (min-width: 1024px) {
  .plan {
    flex: 1 1 calc(25% - 60px);
    max-width: calc(25% - 60px);
    height: 450px;
  }
}
