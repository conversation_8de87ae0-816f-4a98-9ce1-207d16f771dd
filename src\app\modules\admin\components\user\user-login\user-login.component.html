<app-home-header></app-home-header>

<body>
  <div class="login-container">
    <div class="login-form">
      <h2>Welcome back</h2>
      <p>Login to your account</p>
      <div *ngIf="errorMessage" class="alert alert-danger">
        {{ errorMessage }}
      </div>

      <form>
        <div class="input-group email">
          <input
            type="text"
            id="username"
            placeholder="Email"
            [(ngModel)]="username"
            [ngModelOptions]="{ standalone: true }"
            required
          />
        </div>

        <div class="input-group d-flex align-items-center">
          <input
            [type]="isPasswordVisible ? 'text' : 'password'"
            id="password"
            placeholder="Password"
            [(ngModel)]="password"
            [ngModelOptions]="{ standalone: true }"
            style="flex: 1;"
            required
          />
          <button
            type="button"
            class="btn btn-outline-secondary input-group-text d-flex align-items-center justify-content-center"
            (click)="isPasswordVisible = !isPasswordVisible"
            tabindex="-1"
            style="width: 27px; height: 34px;"
          >
            <i [class]="isPasswordVisible ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
          </button>
        </div>

        <div class="forget-password">
          <a class="forgot-password" (click)="navigateForgotPassword()">Forgot my Password</a>
        </div>
        
        <div *ngIf="showCaptcha">
          <re-captcha
            (resolved)="onCaptchaResolved($event)"
            [siteKey]="siteKey"
          ></re-captcha>
        </div>

        <div class="actions">
          <button
            type="submit"
            class="login-btn"
            (click)="onSubmitLogin($event)"
            [disabled]="isLoggingIn"
          >
            <span *ngIf="!isLoggingIn">Login</span>
            <span *ngIf="isLoggingIn">
              <i class="fa fa-spinner fa-spin"></i> Logging in...
            </span>
          </button>
        </div>

        <div class="actions">
          <button
            type="button"
            class="create-account"
            (click)="scrollToPricing()"
          >
            Create your account
          </button>
          <!-- <a (click)="navigateUserRegistration()" class="create-account"
              >Create your account</a> -->
        </div>
      </form>
    </div>
  </div>
</body>
