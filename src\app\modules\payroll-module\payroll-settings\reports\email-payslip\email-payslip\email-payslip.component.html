<app-payroll-nevigation></app-payroll-nevigation>
<div class="container">
    <div class="d-flex justify-content-between align-items-center">
        <h2>Send Payslips via Email</h2>
        <!-- Moved "Email to Selected" button parallel to heading -->
        <!-- <button class="btn btn-success" [disabled]="selectedEmployees.length === 0" (click)="sendEmailToSelected()">
            Email to Selected
        </button> -->
    </div>
    
    <table class="table">
        <thead>
            <tr>
                <th>Name</th>
                <th>Email</th>
                <th style="text-align: center;">Action</th>
                <!-- <th style="width: 5%; text-align: center;">
                    <input type="checkbox" [(ngModel)]="allSelected" (change)="toggleSelectAll()">
                </th> -->
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let employee of employees">
                <td>{{ employee.firstName }} {{ employee.lastName }}</td>
                <td>{{ employee.email }}</td>
                <td style="text-align: center;">
                    <button class="btn btn-primary btn-sm" (click)="sendEmailToEmployee(employee)">
                        Send to Mail
                    </button>
                </td>
                <!-- <td style="width: 5%; text-align: center;">
                    <input type="checkbox" [checked]="selectedEmployees.includes(employee)" (change)="toggleSelection(employee)">
                </td> -->
            </tr>
        </tbody>
    </table>
</div>
