import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpService } from 'src/app/http.service';
import { environment } from 'src/environments/environment';
import { Deduction } from '../payroll-setting';

@Injectable({
  providedIn: 'root'
})
export class DeductionService {

  private readonly baseURL = environment.payrollApiUrl;


  constructor(private http: HttpClient, private httpService: HttpService) { }

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  getDeductionList(entityId: number): Observable<Deduction[]> {
    return this.request('GET',  `/deduction/getAll?entityId=${entityId}`, {});
  }

  
  saveDeduction(deduction: Deduction): Observable<any> {
    return this.request('POST', '/saveDeduction', deduction);
  }

  deleteDeduction(deductionId: number): Observable<any> {
    return this.request('DELETE', `/deleteDeductionById/${deductionId}`, {});
  }

  getAllDeductions(id: number): Observable<Deduction> {
    return this.request('GET', `/getDeductionById/${id}`, null);
  }
  updateDeduction(id: number, payload: Deduction): Observable<Deduction> {
    return this.request('PUT', `/update/${id}`, payload);
  }
}
