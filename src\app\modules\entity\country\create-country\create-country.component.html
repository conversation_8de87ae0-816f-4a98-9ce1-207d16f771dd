<app-admin-navigation></app-admin-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<body>
  <div  class="body">
    <div class="container">
      <div class="popup">
        <div class="popup-header">
          <h3>Create Country</h3>
        </div>
          <form #f="ngForm" (ngSubmit)="f.form.valid && onSubmit(f)" class="row g-1" novalidate="feedback-form" (keydown)="preventSubmit($event)">
            <div class="class-name">
              <div class="form-group">
                <label for="countryName">Country Name</label>
                <select  id="countryName" [(ngModel)]="country.countryName" name="countryName" required>
                  <option value="" disabled selected>Select the Country</option>
                  <option *ngFor="let country of countries" [value]="country.countryName">
                    {{ country.countryName }}
                  </option>
                </select>
                <div *ngIf="f.submitted && f.controls['countryName'].invalid" class="text-danger">
                  <div *ngIf="f.controls['countryName'].errors?.['required']">Country Name is required.</div>
                </div>
              </div>
              <div class="form-group">

              </div>
            </div>
            <div class="class-name">
              <div class="form-group">
                <label for="defaultTaxRate">Tax Type</label>
                <input
                  type="text"
                  id="defaultTaxType"
                  name="defaultTaxType"
                  [(ngModel)]="country.defaultTaxType"
                  #defaultTaxType="ngModel"
                  class="input-style"
                  required
                />
                <div *ngIf="f.submitted && f.controls['defaultTaxType'].invalid" class="text-danger">
                  <div *ngIf="f.controls['defaultTaxType'].errors?.['required']">Tax Type is required.</div>
                </div>
              </div>
              <div class="form-group">
                <label for="defaultTaxRate">Tax Rate (%)</label>
                <input
                  type="number"
                  id="defaultTaxRate"
                  name="defaultTaxRate"
                  [(ngModel)]="country.defaultTaxRate"
                  #defaultTaxRate="ngModel"
                  class="input-style"
                  required
                />
                <div *ngIf="f.submitted && f.controls['defaultTaxRate'].invalid" class="text-danger">
                  <div *ngIf="f.controls['defaultTaxRate'].errors?.['required']">Tax Rate is required.</div>
                </div>
              </div>
              
            </div>
            <div class="class-name">  
              <div class="form-group">
                <label for="defaultCurrency">Default Currency</label>
                <input
                  type="text"
                  id="defaultCurrency"
                  name="defaultCurrency"
                  [(ngModel)]="country.defaultCurrency"
                  #defaultCurrency="ngModel"
                  class="input-style"
                  required
                />
                <div *ngIf="f.submitted && f.controls['defaultCurrency'].invalid" class="text-danger">
                  <div *ngIf="f.controls['defaultCurrency'].errors?.['required']">Default Currency is required.</div>
                </div>
              </div>
              <div class="form-group">
                <label for="defaultCurrency">Currency Symbol</label>
                <input
                  type="text"
                  id="currencySymbol"
                  name="currencySymbol"
                  [(ngModel)]="country.currencySymbol"
                  #currencySymbol="ngModel"
                  class="input-style"
                  required
                />
                <div *ngIf="f.submitted && f.controls['currencySymbol'].invalid" class="text-danger">
                  <div *ngIf="f.controls['currencySymbol'].errors?.['required']">Currency Symbol is required.</div>
                </div>
              </div>
            </div>
            <div class="class-name">
              <div class="form-group">
                <label for="defaultTaxRate">Financial Year Start</label>
                <input
                  type="text"
                  id="financialYearStart"
                  name="financialYearStart"
                  [(ngModel)]="country.financialYearStart"
                  #financialYearStart="ngModel"
                  class="input-style"
                  required
                />
                <div *ngIf="f.submitted && f.controls['financialYearStart'].invalid" class="text-danger">
                  <div *ngIf="f.controls['financialYearStart'].errors?.['required']">Financial Year Start is required.</div>
                </div>
              </div>  
              <div class="form-group">
                <label for="defaultTaxRate">Financial Year End</label>
                <input
                  type="text"
                  id="financialYearEnd"
                  name="financialYearEnd"
                  [(ngModel)]="country.financialYearEnd"
                  #financialYearEnd="ngModel"
                  class="input-style"
                  required
                />
                <div *ngIf="f.submitted && f.controls['financialYearEnd'].invalid" class="text-danger">
                  <div *ngIf="f.controls['financialYearEnd'].errors?.['required']">Financial Year End is required.</div>
                </div>
              </div>  
            </div>
            <div class="popup-footer">
              <button type="button" class="cancel-btn">Cancel</button>
              <button type="submit" class="add-btn">Save</button>
            </div> 
          </form>
        </div>
    </div>
  </div>
</body>
