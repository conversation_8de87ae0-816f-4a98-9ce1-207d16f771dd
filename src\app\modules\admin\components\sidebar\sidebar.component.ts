import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from '../user/user.service';
import { User } from '../user/user';
import { SubscriptionService } from 'src/app/modules/subscription/subscription.service';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent implements OnInit {
  isSystemAdmin: boolean = false;
  isPrimaryUser: boolean = false;
  isAccountant: boolean = false;
  isBasicPlan: boolean = false;
  isPremiumPlan: boolean = false;
  isPayrollOnlyPlan: boolean = false;
  showUserSubmenu: boolean = false;
  showFinanceSubmenu: boolean = false;
  showSystemSettingsSubmenu: boolean = false;
  showMasterSubMenu: boolean = false;
  user: User = new User();

  constructor(
    private router: Router,
    private userService: UserService,
    private subscriptionService: SubscriptionService
  ) {}

  ngOnInit(): void {
    this.loadUserData();
    this.loadSubscriptionData();
    this.loadUserType();
  }

  private loadUserData(): void {
    const userId = Number(localStorage.getItem('userid'));
    this.userService.getUserByIdCached(userId).subscribe(
      (response) => {
        this.user = response;
        this.isPrimaryUser = response.userTypeId.userType === 'Primary user';
        this.isSystemAdmin = response.userTypeId.userType === 'System Admin';
      },
      (error) => {
        console.error('Error fetching user data:', error);
      }
    );
  }

  
  private loadSubscriptionData(): void {
    const entityId = Number(localStorage.getItem('entityId'));
    this.subscriptionService.getSubscriptionByEntityIdCached(entityId).subscribe(
      (response) => {
        if (response === false) {
          return;
        }
        const plan =
          response.subscriptionFeeId?.subscriptionPlanId.subscriptionPlan;
        this.isBasicPlan = plan === 'Basic Plan';
        this.isPremiumPlan = plan === 'Premium Plan';
        this.isPayrollOnlyPlan = plan === 'Payroll Only Plan';
      },
      (error) => {
        console.error('Error fetching subscription data:', error);
      }
    );
  }

  loadUserType() {
    const userTypeId = Number(localStorage.getItem('userTypeId'));

    this.userService.getUserTypeByIdCached(userTypeId).subscribe(
      (response) => {
        if (response.userType === 'Accountant') {
          this.isAccountant = true;
        }
      },
      (error) => {
        console.error('Error fetching user type data:', error);
      }
    );
  }

  toggleFinanceMenu(): void {
    this.showFinanceSubmenu = !this.showFinanceSubmenu;
  }

  toggleSystemSettingMenu(): void {
    this.showSystemSettingsSubmenu = !this.showSystemSettingsSubmenu;
  }

  toggleMasterSubmenu(): void {
    this.showMasterSubMenu = !this.showMasterSubMenu;
  }

  navigateSalesDashboard(): void {
    this.router.navigate(['/sales-dashboard']);
  }

  navigateFinanceDashboard(): void {
    this.router.navigate(['/finance-dashboard']);
  }

  navigateQutation(): void {
    this.router.navigate(['/quotation']);
  }

  navigateBankAccounts(): void {
    this.router.navigate(['/bank-accounts']);
  }

  navigatePeriodClosing(): void {
    this.router.navigate(['/period-closing']);
  }

  navigateExpenceClaims(): void {
    this.router.navigate(['/ExpenceClaims']);
  }

  navigatePayroll(): void {
    this.router.navigate(['/payroll-settings']);
  }

  payItems(): void {
    this.router.navigate(['/payroll-payroll-setting']);
  }

  navigateCountry(): void {
    this.router.navigate(['/country']);
  }

  reconciliation(): void {
    this.router.navigate(['/bank-account-list']);
  }

  navigateSubscription(): void {
    this.router.navigate(['/manage-subscription']);
  }

  navigateManageUsers(): void {
    this.router.navigate(['/manage-users']);
  }

  navigateAccountantInvite(): void {
    this.router.navigate(['/invite-accountant']);
  }

  navigateRequestEntity(): void {
    this.router.navigate(['/request-businessEntity']);
  }

  navigateInviteEntity(): void {
    this.router.navigate(['/invite-entity']);
  }

  navigateBusinessPartnerList(): void {
    this.router.navigate(['/business-partner']);
  }

  navigateBusinessPartner(): void {
    this.router.navigate(['/create-business-partner'], {
      queryParams: { isFinance: 'true' },
    });
  }

  navigateSalesItem(): void {
    this.router.navigate(['/item']);
  }

  navigateEntitySettings(): void {
    this.router.navigate(['/business-entity-edit']);
  }

  navigateGlAccount(): void {
    this.router.navigate(['/gl-account']);
  }

  navigateJournalVoucher(): void {
    this.router.navigate(['/journal-voucher-list']);
  }

  navigatePaybleBill(): void {
    this.router.navigate(['/payable-bill-list']);
  }
}
