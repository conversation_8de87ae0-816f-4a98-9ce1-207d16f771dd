import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-home-footer',
  templateUrl: './home-footer.component.html',
  styleUrls: ['./home-footer.component.css'],
})
export class HomeFooterComponent {
  constructor(private router: Router) {}

  scrollToPricing() {
    this.router.navigate(['/home']).then(() => {
      // Wait a bit to let the DOM render the pricing section
      setTimeout(() => {
        const element = document.getElementById('pricing');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    });
  }

  currentYear = new Date().getFullYear();

}
