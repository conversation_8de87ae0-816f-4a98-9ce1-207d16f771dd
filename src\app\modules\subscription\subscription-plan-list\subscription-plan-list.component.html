<app-home-header></app-home-header>
<div class="upper-content">
  <h2>Select Subscription Plan</h2>
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean sollicitudin
    blandit urna, euismod posuere justo scelerisque quis.
  </p>
</div>
<div class="plans" *ngIf="subscriptionPlans.length > 0">
  <div class="plan" *ngFor="let plan of subscriptionPlans">
    <h2>{{ plan.subscriptionPlanId.subscriptionPlan }}</h2>
    <p>
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Felis mauris
      pretium dui, ac bibendum elit lectus sit amet est.
    </p>
    <h4>${{ plan.amount }}/Month</h4>
    <div class="button-container">
      <button
        class="change-plan"
        (click)="navigateUserRegistration(plan.subscriptionFeeId)"
      >
        Select
      </button>
    </div>
  </div>
</div>
