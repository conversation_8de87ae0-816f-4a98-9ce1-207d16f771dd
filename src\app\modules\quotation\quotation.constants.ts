// Quotation-related constants and enums

export enum QuoteStatus {
  PENDING = 'Pending',
  SENT = 'Sent',
  TO_INVOICE = 'To Invoice',
  REVISED = 'Revised',
  CANCELED = 'Canceled',
  PAID = 'Paid',
  AWAITING_PAYMENT = 'Awaiting Payment',
  EXPIRED = 'Expired'
}

export enum UserRole {
  FREE = 'Free',
  PREMIUM = 'Premium',
  ENTERPRISE = 'Enterprise'
}

export enum EmailType {
  QUOTE = 'quote',
  INVOICE = 'invoice',
  REMINDER = 'reminder'
}

export enum TabType {
  ALL = 'all',
  PENDING = 'pending',
  SENT = 'sent',
  REVISED = 'revised'
}

export class QuotationConstants {
  // Status-related constants
  static readonly VALID_SEND_STATUSES = [
    QuoteStatus.PENDING,
    QuoteStatus.SENT,
    QuoteStatus.TO_INVOICE
  ];

  static readonly VALID_INVOICE_CREATION_STATUSES = [
    QuoteStatus.PENDING,
    QuoteStatus.SENT
  ];

  static readonly DISABLED_VIEW_STATUSES = [
    QuoteStatus.CANCELED,
    QuoteStatus.REVISED
  ];

  static readonly DISABLED_PREVIEW_STATUSES = [
    QuoteStatus.REVISED
  ];

  // User limits
  static readonly FREE_USER_MONTHLY_LIMIT = 5;

  // UI Messages
  static readonly MESSAGES = {
    NO_QUOTES_SELECTED: 'Please select at least one quote before sending.',
    INVALID_STATUS_FOR_SENDING: 'Selected quotes must be in Pending, Sent, or To Invoice status.',
    NO_VALID_QUOTES: 'No Valid quotes selected for sending.',
    MISSING_EMAIL: 'Please enter a recipient email address.',
    MULTIPLE_QUOTES_REVISION: 'You cannot revise multiple invoiced quotes at the same time. Please select only one.',
    NO_SENT_QUOTE_SELECTED: 'Please select a Sent quote to revise.',
    NO_QUOTE_TO_COPY: 'Please select a Quote to Copy.',
    MULTIPLE_QUOTES_COPY: 'Please select only one Quote to Copy.',
    NO_QUOTE_TO_PREVIEW: 'Please select an sales quote to preview.',
    MULTIPLE_QUOTES_PREVIEW: 'You can only preview one sales quote at a time. Please select only one.',
    MULTIPLE_QUOTES_INVOICE: 'You cannot create multiple quotes simultaneously. Please select only one quote with a status of "Pending" or "Sent."',
    NO_PENDING_QUOTE_FOR_INVOICE: 'Kindly select a single quote with a status of "Pending" or "Sent" to proceed with creating an invoice.',
    FREE_USER_LIMIT_REACHED: 'As a Free user, you can only send up to 5 Quotes per month.',
    SEARCH_CRITERIA_MISSING: 'Please provide at least one search criterion: Quote Number or Customer, Start Date, or End Date.',
    DELETE_CONFIRMATION: 'Are you sure you want to Delete this Sales Quote?',
    DELETE_SUCCESS: 'Quotation has been deleted.',
    DELETE_ERROR: 'Failed to delete quotation.',
    DELETE_CANCELLED: 'Quotation deletion cancelled.',
    SEND_SUCCESS: 'Quotes sent successfully.',
    SEND_ERROR: 'Failed to send quotes.',
    FETCH_BP_ERROR: 'Failed to fetch business partner details.',
    NO_DATA_TO_EXPORT: 'No data available to export.',
    MISSING_ENTITY_UUID: 'Missing entity UUID.',
    NO_QUOTATION_DATA: 'No quotation data for preview.',
    QUOTATION_PREVIEW_ERROR: 'Error loading quotation preview.'
  };

  // File and export settings
  static readonly EXCEL_EXPORT = {
    SHEET_NAME: 'Quotes',
    FILE_PREFIX: 'Quotes_',
    MIME_TYPE: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
  };

  // Audio settings
  static readonly AUDIO_PATH = '../assets/google_chat.mp3';

  // Modal IDs
  static readonly MODAL_IDS = {
    QUOTE_PREVIEW: 'quotePreviewModal',
    SEND_QUOTE: 'sendQuoteModal'
  };

  // Local storage keys
  static readonly STORAGE_KEYS = {
    ENTITY_ID: 'entityId',
    ENTITY_UUID: 'entityUuid',
    USER: 'user'
  };

  // Date format
  static readonly DATE_FORMAT = 'dd-MM-yyyy';

  // Template placeholders
  static readonly TEMPLATE_PLACEHOLDERS = {
    BUSINESS_PARTNER_NAME: '${businessPartnerName}',
    QUOTE_NUMBER: '${quoteNumber}',
    CURRENT_YEAR: '${currentYear}'
  };

  // Default values
  static readonly DEFAULTS = {
    CUSTOMER_NAME: 'Valued Customer',
    QUOTE_NUMBER: 'N/A',
    MULTIPLE_QUOTES_SUBJECT: 'Your Quotations are Ready!',
    MULTIPLE_QUOTES_QUOTE_NUMBER: 'Multiple'
  };
}

// Utility functions for status checking
export class QuoteStatusUtils {
  static isValidForSending(status: string): boolean {
    return QuotationConstants.VALID_SEND_STATUSES.includes(status as QuoteStatus);
  }

  static isValidForInvoiceCreation(status: string): boolean {
    return QuotationConstants.VALID_INVOICE_CREATION_STATUSES.includes(status as QuoteStatus);
  }

  static isViewDisabled(status: string): boolean {
    return QuotationConstants.DISABLED_VIEW_STATUSES.includes(status as QuoteStatus);
  }

  static isPreviewDisabled(status: string): boolean {
    return QuotationConstants.DISABLED_PREVIEW_STATUSES.includes(status as QuoteStatus);
  }

  static getStatusClass(status: string): string {
    const statusMap: { [key: string]: string } = {
      [QuoteStatus.PENDING]: 'text-pending',
      [QuoteStatus.CANCELED]: 'text-canceled',
      [QuoteStatus.REVISED]: 'text-revised',
      [QuoteStatus.SENT]: 'text-sent',
      [QuoteStatus.PAID]: 'text-paid',
      [QuoteStatus.TO_INVOICE]: 'text-Invoiced',
      [QuoteStatus.AWAITING_PAYMENT]: 'text-awaiting-payment',
      [QuoteStatus.EXPIRED]: 'text-expired'
    };
    return statusMap[status] || '';
  }

  static getBorderClass(status: string): string {
    const borderMap: { [key: string]: string } = {
      [QuoteStatus.PENDING]: 'border-pending',
      [QuoteStatus.CANCELED]: 'border-canceled',
      [QuoteStatus.REVISED]: 'border-revised',
      [QuoteStatus.SENT]: 'border-sent',
      [QuoteStatus.PAID]: 'border-paid',
      [QuoteStatus.TO_INVOICE]: 'border-Invoiced',
      [QuoteStatus.AWAITING_PAYMENT]: 'border-awaiting-payment',
      [QuoteStatus.EXPIRED]: 'border-expired'
    };
    return borderMap[status] || '';
  }
}
