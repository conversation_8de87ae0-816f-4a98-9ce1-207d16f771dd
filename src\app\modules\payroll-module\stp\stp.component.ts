import { Component, OnInit } from '@angular/core';
import { PayRunService } from '../payroll-settings/services/pay-run.service';
import { Earning, PayItemType, PayPeriod, PayRunMaster } from '../payroll-settings/payroll-setting';
import Swal from 'sweetalert2';
import { interval, Subscription } from 'rxjs';
import { switchMap, takeWhile } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-stp',
  templateUrl: './stp.component.html',
  styleUrls: ['./stp.component.css']
})
export class StpComponent implements OnInit {

  pollingSubscriptions: { [messageId: string]: Subscription } = {};
  payRunMaster: PayRunMaster = new PayRunMaster();
  payRun: PayRunMaster[] = [];
  draftPayRuns: PayRunMaster[] = [];
  skippedPayRuns: PayRunMaster[] = [];
  postPayRuns: PayRunMaster[] = [];
  leaveTwoPage = 1;
  payPeriods: PayPeriod[] = [];
  openPayPeriods: PayPeriod[] = [];
  earning: Earning = new Earning();
  PayitemTypes: PayItemType[] = [];
  selectedPayPeriodId: number | null = null;
  payPeriodId!: number;
  isfiling: boolean = false;
  isFiled = false;
  filingStatus: { [runId: number]: 'idle' | 'submitting' | 'filed' } = {};
  userId: number = +(localStorage.getItem('userid') + '');
  entityId: number = +(localStorage.getItem('entityId') + '');
  pageSize = 10;
  currentPage = 0;
  totalPages = 0;
  pagedPayRunList: PayRunMaster[] = [];
  pageNumbers: number[] = []
  visiblePages: number[] = [];
  maxVisiblePages = 3;


  constructor(
    
    private payRunService: PayRunService,
    private http: HttpClient
  
  ) {}

  ngOnInit(): void {
    this.getAllPayPeriods();
    this.getAllPayRuns();
  }

   getAllPayPeriods(): void {
    this.payRunService.getAllPayPeriods(this.entityId).subscribe({
      next: (data: PayPeriod[]) => {
        this.payPeriods = data;
        this.openPayPeriods = this.payPeriods.filter(
          (period: PayPeriod) => period.status === 'OPEN'
        );
      },
      error: (err) => {
        console.error('Error fetching PayPeriods:', err);
      },
    });
  }

  getAllPayRuns(): void {
  const entityId = +(localStorage.getItem('entityId') + '');

  this.payRunService.getPayRunList(entityId).subscribe({
    next: (data: PayRunMaster[]) => {
      this.payRun = [...data].sort((a, b) => {
        const aStatus = a.stpFilling ?? '';
        const bStatus = b.stpFilling ?? '';
        if (aStatus === '' && bStatus !== '') return -1;
        if (aStatus !== '' && bStatus === '') return 1;
        return 0;
      });

      // Apply filters for other lists (not paginated)
      this.draftPayRuns = this.payRun.filter(
        (payRunMaster: PayRunMaster) =>
          payRunMaster.payPeriod?.status === 'DRAFT'
      );
      this.skippedPayRuns = this.payRun.filter(
        (payRunMaster: PayRunMaster) =>
          payRunMaster.payPeriod?.status === 'SKIPPED'
      );
      this.postPayRuns = this.payRun.filter(
        (payRunMaster: PayRunMaster) =>
          payRunMaster.payPeriod?.status === 'POSTED'
      );

      // Setup pagination
      this.totalPages = Math.ceil(this.payRun.length / this.pageSize);
      this.updatePagedData();
      this.updateVisiblePages();
    },
    error: (err) => {
      console.error('Error fetching PayRuns:', err);
    },
  });
}

goToPage(page: number) {
  if (page >= 0 && page < this.totalPages) {
    this.currentPage = page;
    this.updatePagedData();
    this.updateVisiblePages();
  }
}

prevPage() {
  if (this.currentPage > 0) {
    this.currentPage--;
    this.updatePagedData();
    this.updateVisiblePages();
  }
}

nextPage() {
  if (this.currentPage + 1 < this.totalPages) {
    this.currentPage++;
    this.updatePagedData();
    this.updateVisiblePages();
  }
}

updatePagedData() {
  const start = this.currentPage * this.pageSize;
  const end = start + this.pageSize;
  this.pagedPayRunList = this.payRun.slice(start, end);
}

updateVisiblePages() {
  const half = Math.floor(this.maxVisiblePages / 2);
  let start = Math.max(0, this.currentPage - half);
  let end = start + this.maxVisiblePages;

  if (end > this.totalPages) {
    end = this.totalPages;
    start = Math.max(0, end - this.maxVisiblePages);
  }

  this.visiblePages = [];
  for (let i = start; i < end; i++) {
    this.visiblePages.push(i);
  }
}

  
    stpFile(event: MouseEvent, payCalendarId: number, payPeriodId: number, payRunId: number): void {
    event.stopPropagation(); 
    this.filingStatus[payRunId] = 'submitting';
  
    this.payRunService.stpFileSubmit(payCalendarId, payPeriodId, payRunId, this.entityId, this.userId).subscribe({
      next: (_response: any) => {
        Swal.fire({
          title: 'Success',
          text: '✅ ATO submission started', 
          icon: 'success',
          timer: 5000,
          timerProgressBar: true,
        }).then(() => {
          this.filingStatus[payRunId] = 'filed';
          this.getAllPayRuns();
          setTimeout(() => {
            this.payRun.forEach(row => {
              if (row.atoStatus !== 'DELIVERED') {
                this.startPolling(row.atoMessageId);
              }
            });
          }, 2000); 
        });
      },
      error: (err) => {
        console.error('STP submission failed:', err);
        Swal.fire({
          title: 'Error',
          text: '❌ Failed to file STP. Please try again.',
          icon: 'error',
        }).then(() => {
          this.filingStatus[payRunId] = 'idle';
        });
      }
    });
  }
  
    viewDetails(payCalendarId: number, payPeriodId: number, payRunId: number): void {
    window.location.assign(
      `/payRun-user-details/${payCalendarId}/${payPeriodId}/${payRunId}`
    );
  }

  
startPolling(messageId: string) {
  if (this.pollingSubscriptions[messageId]) return;

  const polling$ = interval(30000).pipe(
    switchMap(() => this.payRunService.stpFileSubmitByMessageId(messageId)),
    takeWhile((status: string) => status !== 'DELIVERED', true)
  );

  this.pollingSubscriptions[messageId] = polling$.subscribe((status: string) => {
    const target = this.payRun.find(p => p.atoMessageId === messageId);
    if (target) {
      target.atoStatus = status; 
    }

    if (status === 'DELIVERED') {
      this.pollingSubscriptions[messageId]?.unsubscribe();
      delete this.pollingSubscriptions[messageId];
    }
  });
}


refreshStatus(): void {
  this.getAllPayRuns(); 
}

  
}