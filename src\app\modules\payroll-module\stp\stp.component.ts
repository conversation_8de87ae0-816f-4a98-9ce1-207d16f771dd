import { Component, OnInit, OnDestroy } from '@angular/core';
import { PayRunService } from '../payroll-settings/services/pay-run.service';
import { Earning, PayItemType, PayPeriod, PayProcessDTO, PayRunDetail, PayRunMaster } from '../payroll-settings/payroll-setting';
import Swal from 'sweetalert2';
import { interval, Subscription } from 'rxjs';
import { switchMap, takeWhile, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { of } from 'rxjs';
import { EmployeeService } from '../payroll-settings/services/employee.service';

@Component({
  selector: 'app-stp',
  templateUrl: './stp.component.html',
  styleUrls: ['./stp.component.css']
})
export class StpComponent implements OnInit, OnDestroy {

  pollingSubscriptions: { [messageId: string]: Subscription } = {};
  private schedulerInterval: any;
  isAllSelected: boolean = false;
  isSubmitting: boolean = false;
  payRunMaster: PayRunMaster = new PayRunMaster();
  selectedPayProcessDTO: Set<PayProcessDTO> = new Set<PayProcessDTO>();
  payRun: PayRunMaster[] = [];
  draftPayRuns: PayRunMaster[] = [];
  skippedPayRuns: PayRunMaster[] = [];
  postPayRuns: PayRunMaster[] = [];
  pagedPayRunDetails: any[] = [];
  payRunDetailList: PayRunDetail[] = [];
  leaveTwoPage = 1;
  payPeriods: PayPeriod[] = [];
  openPayPeriods: PayPeriod[] = [];
  earning: Earning = new Earning();
  PayitemTypes: PayItemType[] = [];
  selectedPayPeriodId: number | null = null;
  payPeriodId!: number;
  isfiling: boolean = false;
  isFiled = false;
  filingStatus: { [runId: number]: 'idle' | 'submitting' | 'filed' } = {};
  userId: number = +(localStorage.getItem('userid') + '');
  entityId: number = +(localStorage.getItem('entityId') + '');
  pageSize = 10;
  currentPage = 0;
  totalPages = 0;
  pagedPayRunList: PayRunMaster[] = [];
  pageNumbers: number[] = []
  visiblePages: number[] = [];
  maxVisiblePages = 3;
  errorDetails: string = '';
  currentMessageId: string = '';
  isLoadingError: boolean = false;
  errorLoadFailed: boolean = false;
  payCalendarId!: number;
  payRunId!: number;
  employeeId!: number;
  totalEarnings: number = 0;
  totalEmployees: string = '';
  totalDeduction: number = 0;
  totalReimbursements: number = 0;
  totalSuper: number = 0;
  totalTax: number = 0;
  totalNetpay: number = 0;
  fromYear: number | null = null;
  toYear: number | null = null;
  availableYears: number[] = [];
  toYearWarning: string = '';
  payProcesses: PayProcessDTO[] = [];


  constructor(
    private payRunService: PayRunService,
    private http: HttpClient,
    private employeeService: EmployeeService,
  ) {

  }

  ngOnInit(): void {
    this.getAllPayPeriods();
    this.getAllPayRuns();
    this.startScheduler();
    this.getPayRunDetailsByEntityIdAndType();
    const currentYear = new Date().getFullYear();
    this.availableYears = Array.from({ length: 30 }, (_, i) => currentYear - 10 + i);
  }

  ngOnDestroy(): void {
    this.stopScheduler();
  }

  startScheduler(): void {
    setTimeout(() => {
      this.startPollingForAllPendingPayRuns();
      this.schedulerInterval = setInterval(() => {
        this.startPollingForAllPendingPayRuns();
      }, 30000); 
    }, 3000);
  }

  stopScheduler(): void {
    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval);
      this.schedulerInterval = null;
    }
    
    Object.keys(this.pollingSubscriptions).forEach(messageId => {
      this.pollingSubscriptions[messageId]?.unsubscribe();
      delete this.pollingSubscriptions[messageId];
    });
  }

   private getPayRunDetailsByEntityIdAndType() {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.employeeService.getPayRunDetailsByEntityIdAndType(entityId).subscribe(data => {
      this.pagedPayRunDetails = data;

      this.totalPages = Math.ceil(this.pagedPayRunDetails.length / this.pageSize);
      this.updatePagedData();
      this.updateVisiblePages();
      // this.filteredPayRunDetailList.forEach(row => {
      //         if (row.beamStatus == 'PENDING') {
      //           this.getBeamStatus(row.beamProcessId);
      //         }
      //       });
    });
  }

   getAllPayProcessData(fromYear: any , toYear: any): void {
      const entityId = +(localStorage.getItem('entityId') + '');

      this.payRunService
        .getPayprocessDetailsByYear(entityId, fromYear, toYear)
        .subscribe({
          next: (data: PayProcessDTO[]) => {
            this.payProcesses = data;
            const totals = this.payProcesses.reduce(
              (acc, process) => {
                acc.totalEarnings += process.totalEarnings || 0;
                acc.totalDeductions += process.totalDeductions || 0;
                acc.totalReimbursements += process.totalReimbursements || 0;
                acc.totalSuper += process.superannuation || 0;
                acc.totalTax += process.tax || 0;
                acc.totalNetpay += process.netPay || 0;
                return acc;
              },
              {
                totalEarnings: 0,
                totalDeductions: 0,
                totalReimbursements: 0,
                totalSuper: 0,
                totalTax: 0,
                totalNetpay: 0,
              }
            );
  
            this.totalEarnings = parseFloat(totals.totalEarnings.toFixed(4));
            this.totalDeduction = parseFloat(totals.totalDeductions.toFixed(4));
            this.totalReimbursements = parseFloat(
              totals.totalReimbursements.toFixed(4)
            );
            this.totalSuper = parseFloat(totals.totalSuper.toFixed(4));
            this.totalTax = parseFloat(totals.totalTax.toFixed(4));
            this.totalNetpay = parseFloat(totals.totalNetpay.toFixed(4));
  
            this.totalEmployees = this.payProcesses.length
              .toString()
              .padStart(2, '0');
          },
          error: (err) => {
            console.error('Error fetching PayCycles:', err);
          },
        });
    }

  startPollingForAllPendingPayRuns(): void { 
    const pendingPayRuns = this.payRun.filter(row => 
      row.atoStatus !== 'DELIVERED' && row.atoStatus !== 'ERROR' && row.atoMessageId
    );

    if (pendingPayRuns.length > 0) {
      pendingPayRuns.forEach(row => {
        this.startPolling(row.atoMessageId);
      });
    } else {
      console.log('No pending payRuns to poll');
    }
  }

  getAllPayPeriods(): void {
    this.payRunService.getAllPayPeriods(this.entityId).subscribe({
      next: (data: PayPeriod[]) => {
        this.payPeriods = data;
        this.openPayPeriods = this.payPeriods.filter(
          (period: PayPeriod) => period.status === 'OPEN'
        );
      },
      error: (err) => {
        console.error('Error fetching PayPeriods:', err);
      },
    });
  }

  getAllPayRuns(): void {
    const entityId = +(localStorage.getItem('entityId') + '');

    this.payRunService.getPayRunList(entityId).subscribe({
      next: (data: PayRunMaster[]) => {
        this.payRun = [...data].sort((a, b) => {
          const aStatus = a.stpFilling ?? '';
          const bStatus = b.stpFilling ?? '';
          
          if (aStatus !== 'Submitted' && bStatus !== 'Submitted') return 0;         
          if (aStatus === 'Submitted' && bStatus === 'Submitted') return 1;
          if (aStatus !== 'Submitted' && bStatus === 'Submitted') return -1;
          if (aStatus === 'Submitted' && bStatus !== 'Submitted') return 1;
          
          return 0;
        });

        this.draftPayRuns = this.payRun.filter(
          (payRunMaster: PayRunMaster) =>
            payRunMaster.payPeriod?.status === 'DRAFT'
        );
        this.skippedPayRuns = this.payRun.filter(
          (payRunMaster: PayRunMaster) =>
            payRunMaster.payPeriod?.status === 'SKIPPED'
        );
        this.postPayRuns = this.payRun.filter(
          (payRunMaster: PayRunMaster) =>
            payRunMaster.payPeriod?.status === 'POSTED'
        );

        this.totalPages = Math.ceil(this.payRun.length / this.pageSize);
        this.updatePagedData();
        this.updateVisiblePages();
      },
      error: (err) => {
        console.error('Error fetching PayRuns:', err);
      },
    });
  }

  goToPage(page: number) {
    if (page >= 0 && page < this.totalPages) {
      this.currentPage = page;
      this.updatePagedData();
      this.updateVisiblePages();
    }
  }

  prevPage() {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.updatePagedData();
      this.updateVisiblePages();
    }
  }

  nextPage() {
    if (this.currentPage + 1 < this.totalPages) {
      this.currentPage++;
      this.updatePagedData();
      this.updateVisiblePages();
    }
  }

  updatePagedData() {
    const start = this.currentPage * this.pageSize;
    const end = start + this.pageSize;
    this.pagedPayRunList = this.payRun.slice(start, end);
  }

  updateVisiblePages() {
    const half = Math.floor(this.maxVisiblePages / 2);
    let start = Math.max(0, this.currentPage - half);
    let end = start + this.maxVisiblePages;

    if (end > this.totalPages) {
      end = this.totalPages;
      start = Math.max(0, end - this.maxVisiblePages);
    }

    this.visiblePages = [];
    for (let i = start; i < end; i++) {
      this.visiblePages.push(i);
    }
  }

  stpFile(event: MouseEvent, payCalendarId: number, payPeriodId: number, payRunId: number): void {
    event.stopPropagation(); 
    this.filingStatus[payRunId] = 'submitting';

    this.payRunService.stpFileSubmit(payCalendarId, payPeriodId, payRunId, this.entityId, this.userId).subscribe({
      next: (_response: any) => {
        Swal.fire({
          title: 'Success',
          text: '✅ ATO submission started', 
          icon: 'success',
          timer: 5000,
          timerProgressBar: true,
        }).then(() => {
          this.filingStatus[payRunId] = 'filed';
          this.getAllPayRuns(); 
          setTimeout(() => {
            this.startPollingForAllPendingPayRuns();
          }, 2000);         
          setTimeout(() => {
            this.getAllPayRuns();
          }, 12000);         
        });
      },
      error: (err) => {
        console.error('STP submission failed:', err);
        Swal.fire({
          title: 'Error',
          text: '❌ Failed to file STP. Please try again.',
          icon: 'error',
        }).then(() => {
          this.filingStatus[payRunId] = 'idle';
        });
      }
    });
  }

  viewDetails(payCalendarId: number, payPeriodId: number, payRunId: number): void {
    window.location.assign(
      `/payRun-user-details/${payCalendarId}/${payPeriodId}/${payRunId}`
    );
  }

  startPolling(messageId: string): void {

    if (this.pollingSubscriptions[messageId]) {
      console.log(`Already polling messageId: ${messageId}`);
      return;
    }

    const polling$ = interval(30000).pipe(
      switchMap(() => this.payRunService.stpFileSubmitByMessageId(messageId)),
      takeWhile((status: string) => status !== 'DELIVERED' && status !== 'ERROR', true),  
    );
     
    this.pollingSubscriptions[messageId] = polling$.subscribe({
      next: (status: string) => {
        const target = this.payRun.find(p => p.atoMessageId === messageId);
        if (target) {
          target.atoStatus = status;

          if (status === 'ERROR') {
          (target as any).showErrorViewIcon = true;
          } else {
            (target as any).showErrorViewIcon = false;
          }

          this.updatePagedData();
        }
        this.getAllPayRuns();

        if (status === 'DELIVERED' || status === 'ERROR') {
          this.stopPollingForMessage(messageId);
          this.getAllPayRuns();
        }
      },
      error: (error) => {
        console.error(`Polling subscription error for ${messageId}:`, error);
        this.stopPollingForMessage(messageId);
      }
    });
  }

  stopPollingForMessage(messageId: string): void {
    if (this.pollingSubscriptions[messageId]) {
      this.pollingSubscriptions[messageId].unsubscribe();
      delete this.pollingSubscriptions[messageId];
      console.log(`Stopped polling for messageId: ${messageId}`);
    }
  }

  refreshStatus(): void {
    this.getAllPayRuns();
    setTimeout(() => {
      this.startPollingForAllPendingPayRuns();
    }, 1000);
  }

  // Get current polling status for debugging
  getPollingStatus(): any {
    return {
      activePolls: Object.keys(this.pollingSubscriptions).length,
      pollingMessageIds: Object.keys(this.pollingSubscriptions),
      pendingPayRuns: this.payRun.filter(p => p.atoStatus !== 'DELIVERED').length
    };
  }

 viewErrorDetails(event: Event, atoMessageId: any): void {
    event.stopPropagation();
    this.currentMessageId = atoMessageId;
    this.isLoadingError = true;
    this.errorLoadFailed = false;
    this.errorDetails = '';
    
    // Then fetch error details
    this.payRunService.viewErrorDetails(atoMessageId).subscribe({
      next: (response: any) => {
        this.isLoadingError = false;
        this.errorDetails = response || 'No error details available';
      },
      error: (err: any) => {
        this.isLoadingError = false;
        this.errorLoadFailed = true;
        console.error('Error fetching error details:', err);
      }
    });
  }

shouldShowResubmit(payRunMasters: any): boolean {
  return payRunMasters.atoStatus === 'ERROR' &&
         this.filingStatus[payRunMasters.payRunId] !== 'submitting' &&
         this.filingStatus[payRunMasters.payRunId] !== 'filed' &&
         payRunMasters.stpFilling !== 'Submitted';
}


resubmitStp(event: Event, atoMessageId: any, payRunId: any): void {
  event.stopPropagation(); 
  this.filingStatus[payRunId] = 'submitting';

    this.payRunService.stpFileResubmit(atoMessageId, payRunId, this.entityId, this.userId).subscribe({
      next: (_response: any) => {
        Swal.fire({
          title: 'Success',
          text: '✅ ATO resubmission started', 
          icon: 'success',
          timer: 5000,
          timerProgressBar: true,
        }).then(() => {
          this.filingStatus[payRunId] = 'filed';
          this.getAllPayRuns(); 
          setTimeout(() => {
            this.startPollingForAllPendingPayRuns();
          }, 2000);         
          setTimeout(() => {
            this.getAllPayRuns();
          }, 12000);         
        });
      },
      error: (err) => {
        console.error('STP resubmission failed:', err);
        Swal.fire({
          title: 'Error',
          text: '❌ Failed to file STP. Please try again.',
          icon: 'error',
        }).then(() => {
          this.filingStatus[payRunId] = 'idle';
        });
      }
    });
}

selectAll(event: any): void {
    this.isAllSelected = event.target.checked;
    console.log("Select All Toggled:", this.isAllSelected);

    if (this.isAllSelected) {
      this.payProcesses.forEach(payProcesse => {
        this.selectedPayProcessDTO.add(payProcesse);
      });
    } else {
      this.selectedPayProcessDTO.clear();
    }
  }

  toggleSelection(payProcessDTO: PayProcessDTO, event: any): void {
      if (event.target.checked) {
          this.selectedPayProcessDTO.add(payProcessDTO);
      } else {
          this.selectedPayProcessDTO.delete(payProcessDTO);
      }
  }

  submitSuperannuations() {

  }

   search(fromDate: any, toDate: any): void {
      if (!fromDate || !toDate) {
        Swal.fire({
          icon: 'warning',
          title: 'Missing Dates',
          text: 'Please select both dates to proceed.',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff',
        });
        return;
      }
  
      const from = new Date(fromDate);
      const to = new Date(toDate);
  
      // Parse DB date string: '2025-05-27 00:00:00.0000000'
      this.pagedPayRunDetails = this.payRunDetailList.filter(detail => {
        const detailTimestamp = detail.payRun?.date;
        if (!detailTimestamp) return false;
  
        // Replace space with 'T' and remove extra fractional seconds for ISO compatibility
        let isoString = detailTimestamp.replace(' ', 'T').replace(/\.\d+$/, '');
        // If still not valid, fallback to just the date part
        let detailDate = new Date(isoString);
        if (isNaN(detailDate.getTime())) {
          detailDate = new Date(detailTimestamp.split(' ')[0]);
        }
  
        // Set time to 0:00:00 for from and 23:59:59 for to to include the full days
        const fromDay = new Date(from);
        fromDay.setHours(0,0,0,0);
        const toDay = new Date(to);
        toDay.setHours(23,59,59,999);
  
        return detailDate >= fromDay && detailDate <= toDay;
      });
  
      // Reset pagination after filtering
      this.currentPage = 0;
      this.totalPages = Math.ceil(this.pagedPayRunDetails.length / this.pageSize);
      this.updatePagedData();
      this.updateVisiblePages();
  
      console.log("Filtered Results:", this.pagedPayRunDetails);
    }
    
  onStartYearChange() {
  if (this.fromYear) {
    const startYearNumber = Number(this.fromYear);
    if (!isNaN(startYearNumber)) {
      this.toYear = startYearNumber + 1;
      this.toYearWarning = '';
    } else {
      this.toYear = null;
      this.toYearWarning = 'Invalid start year selected.';
    }
  } else {
    this.toYear = null;
    this.toYearWarning = 'Please select a start year.';
  }
}


isSearchDisabled(): boolean {
  return !this.fromYear;
}

searchByYear() {
  // Your search logic
}

resetSearch() {
  this.fromYear = null;
  this.toYear = null;
  this.toYearWarning = '';
}

}