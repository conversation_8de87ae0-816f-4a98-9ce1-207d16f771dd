import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { QuotationConstants, QuoteStatusUtils } from '../quotation.constants';
import { FilterCriteria, ExportData } from '../quotation.interfaces';
import { QuoteHead } from '../quotation';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root'
})
export class QuotationBusinessService {

  constructor() { }

  filterQuotes(quotes: QuoteHead[], criteria: FilterCriteria): QuoteHead[] {
    const searchTermLower = criteria.searchTerm.toLowerCase().trim();
    let filtered = quotes;

    // Filter by active tab
    if (criteria.activeTab !== 'all') {
      filtered = filtered.filter(quote => quote.status.toLowerCase() === criteria.activeTab);
    }

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(quote =>
        quote.quoteNumber.toString().toLowerCase().includes(searchTermLower) ||
        quote.customerName.toLowerCase().includes(searchTermLower)
      );
    }

    // Filter by date range
    if (criteria.startDate) {
      const startDate = new Date(criteria.startDate);
      filtered = filtered.filter(quote => new Date(quote.quoteDate) >= startDate);
    }

    if (criteria.endDate) {
      const endDate = new Date(criteria.endDate);
      filtered = filtered.filter(quote => new Date(quote.quoteDate) <= endDate);
    }

    return filtered;
  }

  getSelectedQuotes(quotes: QuoteHead[]): QuoteHead[] {
    return quotes.filter(quote => quote.selected);
  }

  getValidQuotesForSending(quotes: QuoteHead[]): QuoteHead[] {
    return quotes.filter(quote => QuoteStatusUtils.isValidForSending(quote.status));
  }

  getValidQuotesForInvoiceCreation(quotes: QuoteHead[]): QuoteHead[] {
    return quotes.filter(quote => QuoteStatusUtils.isValidForInvoiceCreation(quote.status));
  }

  getSentQuotes(quotes: QuoteHead[]): QuoteHead[] {
    return quotes.filter(quote => quote.status === 'Sent');
  }

  clearQuoteSelections(quotes: QuoteHead[]): void {
    quotes.forEach(quote => {
      quote.selected = false;
    });
  }

  selectAllQuotes(quotes: QuoteHead[], isSelected: boolean): void {
    quotes.forEach(quote => {
      quote.selected = isSelected;
    });
  }

  prepareExportData(quotes: QuoteHead[]): ExportData[] {
    return quotes.map(quote => ({
      'Quote ID': quote.quoteId,
      'Quote Number': quote.quoteNumber,
      'Customer Name': quote.customerName,
      'Quote Date': new Date(quote.quoteDate).toISOString().split('T')[0],
      'Valid Until': new Date(quote.validUntilDate).toISOString().split('T')[0],
      'Amount': quote.grandTotal,
      'Status': quote.status
    }));
  }

  exportToExcel(data: ExportData[]): void {
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, QuotationConstants.EXCEL_EXPORT.SHEET_NAME);

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    this.saveAsExcelFile(excelBuffer, `${QuotationConstants.EXCEL_EXPORT.FILE_PREFIX}${timestamp}`);
  }

  private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: QuotationConstants.EXCEL_EXPORT.MIME_TYPE
    });

    saveAs(data, `${fileName}.xlsx`);
  }

  attachBusinessPartnerEmails(quotes: QuoteHead[], businessPartners: any[]): void {
    businessPartners.forEach((businessPartner, index) => {
      if (businessPartner && quotes[index]) {
        quotes[index].recipient = businessPartner.email;
      }
    });
  }

  createQuotationCache(): Map<number, string> {
    return new Map<number, string>();
  }

  getCachedQuotation(cache: Map<number, string>, quoteId: number): string | undefined {
    return cache.get(quoteId);
  }

  setCachedQuotation(cache: Map<number, string>, quoteId: number, data: string): void {
    cache.set(quoteId, data);
  }

  generatePdfDataUrl(base64String: string): string {
    return 'data:application/pdf;base64,' + base64String;
  }

  getModalElement(modalId: string): HTMLElement | null {
    return document.getElementById(modalId);
  }

  playAudioNotification(): void {
    const audio = new Audio();
    audio.src = QuotationConstants.AUDIO_PATH;
    audio.load();
    audio.play().catch(error => {
      console.warn('Could not play audio notification:', error);
    });
  }

  stopAudioNotification(audio: HTMLAudioElement | null): void {
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
    }
  }

  navigateToRoute(router: any, route: string, params?: any[]): void {
    if (params) {
      router.navigate([route, ...params]);
    } else {
      router.navigate([route]);
    }
  }

  reloadPage(router: any, currentRoute: string): void {
    router.navigate([currentRoute]);
  }

  redirectToSubscriptionPage(): void {
    window.location.href = '/manage-subscription';
  }
}
