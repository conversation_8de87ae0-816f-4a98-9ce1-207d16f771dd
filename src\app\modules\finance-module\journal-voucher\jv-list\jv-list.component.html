<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <!-- Heading And Button -->
  <div class="actions">
    <h1>List of Journal Vouchers</h1>
   <!-- <div class="btn-group" [class.show]="isDropdownOpen">
 
      <button
        type="button"
        class="btn btn-secondary dropdown-toggle gradient-btn"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        (click)="toggleDropdown()"
      >
        <i class="bi bi-three-dots-vertical"></i>

      </button>
      <ul class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen">
        <li>
          <a class="dropdown-item">Cancel</a>
        </li>
         <li>
          <a class="dropdown-item">Preview & Print</a>
        </li> 
      </ul>
    </div>-->
  </div>

  <div class="search-create">
    <button
      type="button"
      (click)="navigateJVCreationPage()"
      class="invoice-convert"
    >
    Create Journal Voucher
    </button>
  </div>

  <div class="Card">
    <!-- Filter and Search Section -->
    <div class="row1">
      <!-- Input for jv number -->
      <div class="row1_col1">
        <label for="search-input">Voucher Number</label>
        <div class="input-container">
          <input type="text" class="search-input" id="search-input" [(ngModel)]="searchTerm"/>
          <i class="bi bi-search"></i>
        </div>
      </div>

      <div class="row1_col3">
        <label for="StartDate">Start Date</label>
        <div style="position: relative; width: 100%;">
          <input
            matInput
            [matDatepicker]="startDatePicker"
            class="date-picker"
            id="StartDate"
            [(ngModel)]="startDate"
            placeholder="dd/mm/yyyy"
          />
          <mat-datepicker-toggle matSuffix [for]="startDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
        </div>
        <mat-datepicker #startDatePicker></mat-datepicker>
      </div>

      <div class="row1_col4">
        <label for="EndDate">End Date</label>
        <div style="position: relative; width: 100%;">
          <input
            matInput
            [matDatepicker]="endDatePicker"
            class="date-picker"
            id="EndDate"
            [(ngModel)]="endDate"
            placeholder="dd/mm/yyyy"
          />
          <mat-datepicker-toggle matSuffix [for]="endDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
        </div>
        <mat-datepicker #endDatePicker></mat-datepicker>
      </div>
    </div>

    <div class="row2">
      <div class="row2_col3">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>
      <div class="row2_col1">
        <button type="button" class="primary-button" (click)="filterJournalVouchers()">
          Search
        </button>
      </div>
    </div>
  </div>

  <div class="table-responsive">
    <table>
      <thead>
        <tr class="table-head">
          <th scope="col" class="valueCheckbox"><input type="checkbox" /></th>
          <th scope="col" class="valuehead">JV Number</th>
          <th scope="col" class="valuehead">Narration</th>
          <th scope="col" class="valuehead">Date</th>
          <th scope="col" class="valuehead" style="text-align: right">Dr</th>
          <th scope="col" class="valuehead" style="text-align: right">Cr</th>
          <th scope="col" class="valuehead" style="text-align: center">Status</th>
          <!--<th scope="col" class="valuehead">Action</th>-->
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let glPostingHead of filteredGlPostingHeadList; let i = index">
          <td class="valueCheckbox"><input type="checkbox" /></td>
          <td class="value">{{ glPostingHead.jvNumber }}</td>
          <td class="value">{{ glPostingHead.description }}</td>
          <td class="value">{{ glPostingHead.date | date : "dd-MM-yyyy" }}</td>
          <td class="value" style="text-align: right">{{ glPostingHead.totalDr | currency }}</td>
          <td class="value" style="text-align: right">{{ glPostingHead.totalCr | currency }}</td>
          <td
            style="padding-left: 15px"
            class="value"
            [ngClass]="{
                'text-draft': glPostingHead.status === 'Draft',
                'text-posted': glPostingHead.status === 'Posted',
                
            }"
          >
            <span
              class="lable"
              [ngClass]="{
                            'border-draft': glPostingHead.status === 'Draft',
                            'border-posted': glPostingHead.status === 'Posted',
                        }"
              >{{ glPostingHead.status }}</span
            >
          </td>

          <!--<td class="value">
            <button
              class="btn btn-orange btn-sm"
              style="
                margin-right: 2px;
                border: none;
                background: none;
                padding: 2px;
                font-size: 1rem;
              "
              title="Edit"
              (click)="
                glPostingHead.glTransactionId !== null &&
                  editJV(glPostingHead.glTransactionId)
              "
            >
              <i class="ri-edit-box-line" style="color: #4262ff"></i>
            </button>
            <button
              class="btn btn-danger btn-sm"
              style="
                margin-right: 2px;
                border: none;
                background: none;
                padding: 2px;
                font-size: 1rem;
              "
              data-bs-toggle="tooltip"
              title="Delete"
            >
              <i class="ri-delete-bin-line" style="color: #ff0000"></i>
            </button>
          </td>-->
        </tr>
      </tbody>
    </table>
  </div>
</div>
