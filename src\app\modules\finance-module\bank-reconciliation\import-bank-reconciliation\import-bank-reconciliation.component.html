<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center">
    <h1 class="h1">Bank Reconciliation</h1>
    <button class="primary-button2" (click)="navigateToBankRule()">
      Bank Rules
    </button>
    &nbsp;&nbsp;&nbsp;&nbsp;
    <button class="primary-button2" (click)="navigateToRecReport()">
      Bank Rec Reports
    </button>
    &nbsp;&nbsp;&nbsp;&nbsp;
    <button class="primary-button2" *ngIf="this.bankStatementHeader" (click)="navigateToMatchTransactions()">
      Reconcile
    </button>
  </div>
  <!-- Header -->

  <!-- Select Source Panel -->
  <div class="col-12">
    <div class="row g-0">
      <div class="col-12">
        <div class="row g-0">
          <div class="col-6 pe-2">
            <button
              class="btn p-3 col-6 text-center my-auto w-100"
              [ngClass]="{
                'active-button': selectedSource === 'statement',
                'bordered-button': selectedSource !== 'statement'
              }"
              (click)="onTabChange('statement')"
            >
              Import Bank Statement
            </button>
          </div>
          <div class="col-6 ps-2">
            <button
              class="btn p-3 col-6 text-center my-auto w-100"
              [ngClass]="{
                'active-button': selectedSource === 'bankfeed',
                'bordered-button': selectedSource !== 'bankfeed'
              }"
              (click)="onTabChange('bankfeed')"
            >
              From Bank Feed
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Select Source Panel -->

  <!-- Toggle Panel View -->
  <div class="col-12 mt-4" [ngSwitch]="selectedSource">
    <ng-container *ngSwitchCase="'statement'">
      <ng-container *ngTemplateOutlet="statmenet"></ng-container>
    </ng-container>

    <ng-container *ngSwitchCase="'bankfeed'">
      <ng-container *ngTemplateOutlet="bankfeed"></ng-container>
    </ng-container>
  </div>
  <!-- Toggle Panel View -->

  <ng-template #statmenet>
    <div class="col-12">
      <div class="row g-0">
        <div class="col-12">
          <div class="card m-0 bg-white">
            <div
              class="col-12 border-1 border border-secondary-subtle rounded-3 p-4"
            >
              <div *ngIf="this.bankStatementHeader; else fileUpload">
                <div class="d-inline-flex gap-5 p-0 m-0">
                  <div class="text-start" style="line-height: 20px">
                    <small class="text-black-50">Bank Name</small> <br /><strong
                      class="fs-6"
                      >{{ this.bankStatementHeader.bankName }}</strong
                    >
                  </div>
                  <div class="text-start" style="line-height: 20px">
                    <small class="text-black-50">Bank ID </small><br />
                    <strong class="fs-6">{{
                      this.bankStatementHeader.bankStatementId
                    }}</strong>
                  </div>
                </div>
                <div class="float-end">
                  <select
                    class="amount-type-select"
                    (change)="filterTransactions()"
                    [(ngModel)]="selectedFilter"
                  >
                    <option value="all" selected>All</option>
                    <option value="credit">Received</option>
                    <option value="debit">Spent</option>
                  </select>
                  &nbsp;&nbsp;&nbsp;
                  <button
                    type="button"
                    class="secondary-button"
                    (click)="clear()"
                  >
                    Clear
                  </button>
                </div>
              </div>
              <ng-template #fileUpload>
                <div class="d-flex justify-content-between my-auto">
                  <!-- Left: Upload Section -->
                  <div class="d-flex align-items-center gap-3">
                    <input
                      #fileInput
                      type="file"
                      class="file-input d-none"
                      (change)="onFileSelected($event)"
                      accept=".xlsx, .csv"
                      id="fileUpload"
                    />

                    <!-- Upload Label -->
                    <label
                      *ngIf="!selectedFile"
                      for="fileUpload"
                      class="upload-button d-flex align-items-center gap-3 m-0"
                    >
                      <p
                        class="border border-1 border-secondary-subtle rounded-2 m-0 px-3 py-2"
                      >
                        <i class="fas fa-upload text-black-50"></i>
                      </p>
                      <div style="line-height: 1.2">
                        <strong>Select File</strong><br />
                        <small class="text-black-50" style="font-weight: 400">
                          Accepted: Excel (.xlsx) or CSV (.csv)
                          <a
                            href="assets/ledger_chimp_bank_statement_sample.xlsx"
                            download
                            style="
                              text-decoration: underline;
                              border-radius: 10px;
                              font-size: 12px;
                              padding: 0px 5px;
                            "
                            >Download Template</a
                          >
                        </small>
                      </div>
                    </label>
                    <!-- Selected file name -->
                    <div
                      *ngIf="selectedFile"
                      class="d-flex align-items-center gap-2"
                    >
                      <i class="fas fa-file-alt text-success"></i>
                      <span class="file-name text-primary">
                        {{ selectedFile.name }}
                      </span>
                    </div>
                  </div>

                  <!-- Right: Action Buttons -->
                  <div class="d-flex">
                    <button
                      type="button"
                      class="primary-button"
                      (click)="uploadReport()"
                      [disabled]="isUploading || !selectedFile"
                    >
                      {{ isUploading ? "Wait..." : "Upload" }}
                    </button>
                    <button
                      type="button"
                      class="secondary-button"
                      (click)="clear()"
                    >
                      Clear
                    </button>
                  </div>
                </div>
              </ng-template>
            </div>
          </div>

          <div class="card m-0 mt-4 bg-white" *ngIf="selectedFilter != 'all'">
            <div class="row g-0">
              <ng-container
                *ngIf="selectedFilter === 'credit'"
                [ngTemplateOutlet]="creditTransactions"
              ></ng-container>

              <ng-container
                *ngIf="selectedFilter === 'debit'"
                [ngTemplateOutlet]="debitTransactions"
              ></ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #bankfeed>
    <div class="col-12">
      <div class="row">
        <div class="col-12">
          <div class="card w-100 m-0 bg-white col-12">
            <div class="row">
              <div class="col-12">
                <div class="float-start d-flex ps-2 my-auto">
                  <h1 class="fw-bold my-auto pe-3">{{ tranasctionCount }}</h1>
                  <p
                    class="text-black-50 text-start my-auto divider-float ps-3"
                    style="font-size: 14px; line-height: 14px"
                  >
                    Transaction <br />
                    Count
                  </p>
                </div>
                <div class="float-end mt-1 d-inline-flex">
                  <p class="text-danger px-3 fst-italic" style="font-size: 12px; width: 500px;">
                    <i class="fa fa-exlamation"></i>
                    For ease of UAT, the date must be selected manually. In the
                    production environment, it will be automatically loaded
                    daily through a scheduled process.
                  </p>
                  <input
                    type="date"
                    class="custom-date-selector"
                    (input)="filterByDateChange()"
                    [(ngModel)]="selectedDateFilter"
                    style="height: 40px;"
                  />&nbsp;&nbsp;&nbsp;
                  <select
                    class="amount-type-select"
                    (change)="filterTransactions()"
                    [(ngModel)]="selectedFilter"
                    style="height: 40px;"
                    [disabled]="isLoadingBankFeed"
                  >
                    <option value="all" selected>All</option>
                    <option value="credit">Credit</option>
                    <option value="debit">Debit</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <br />
          <div
            class="card w-100 m-0 justify-content-center align-items-center col-12 bg-white"
            style="height: 400px !important"
            *ngIf="isLoadingBankFeed"
          >
            <div class="col-8 py-5 mx-auto g-0 d-inline-flex">
              <div class="col-4 text-center my-auto">
                <img
                  src="assets/images/fetching.svg"
                  alt="data fetching image"
                  style="height: 200px"
                />
              </div>
              <div
                class="col-8 text-start ps-4 my-auto"
                style="line-height: 20px"
              >
                <h5 style="font-weight: 600">
                  Fetching Data from Your Bank Feed
                </h5>
                <p class="text-black-50" style="font-size: 14px">
                  We’re retrieving your latest bank transactions. This process
                  may take a few seconds—please don’t close the window.
                </p>
              </div>
            </div>
          </div>
          <div
            class="card w-100 m-0 justify-content-center align-items-center col-12 bg-white"
            style="height: 400px !important"
            *ngIf="noDataFromFeed"
          >
            <div class="col-8 py-5 mx-auto g-0 d-inline-flex">
              <div class="col-4 text-center my-auto">
                <img
                  src="assets/images/no-data-bank-rec.svg"
                  alt="data fetching image"
                  style="height: 180px"
                />
              </div>
              <div
                class="col-8 text-start ps-4 my-auto"
                style="line-height: 20px"
              >
                <h5 style="font-weight: 600">No Transactions Available</h5>
                <p class="text-black-50" style="font-size: 14px">
                  Your bank feed returned no transactions. Make sure your
                  account is connected properly or try selecting a different
                  date range.
                </p>
              </div>
            </div>
          </div>
          <div
            class="card w-100 m-0 bg-white"
            *ngIf="!isLoadingBankFeed && !noDataFromFeed"
          >
            <div class="row g-0">
              <ng-container
                *ngIf="selectedFilter === 'credit'"
                [ngTemplateOutlet]="creditTransactions"
              ></ng-container>

              <ng-container
                *ngIf="selectedFilter === 'debit'"
                [ngTemplateOutlet]="debitTransactions"
              ></ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #creditTransactions>
    <div class="col-12">
      <div class="row g-0">
        <!-- full-credit-match -->
        <div class="col-12 d-grid">
          <div
            class="row bottom-space g-0"
            *ngFor="let pair of matchedCreditPairs; let i = index"
          >
            <div class="col-7">
              <div class="col-12 g-0 d-flex">
                <div
                  class="col-9 border border-1 border-secondary-subtle rounded-3 p-3"
                >
                  <div class="row g-0">
                    <div
                      class="col-6 d-flex flex-column justify-content-center"
                    >
                      <!-- Transaction Info -->
                      <p class="text-black-50" style="font-size: 13px">
                        {{ pair.transaction.date | date : "dd MMM yyyy" }}
                      </p>
                      <p style="font-size: 15px; font-weight: 600">
                        {{ pair.transaction.description }}
                      </p>
                    </div>
                    <div class="col-3 text-end pe-3 middle-item">
                      <!-- Transaction Credit -->
                      <p class="text-black-50" style="font-size: 13px">
                        <i
                          class="fas fa-arrow-up my-auto"
                          style="
                            color: limegreen;
                            font-weight: 500;
                            font-size: 11px;
                          "
                        ></i>
                        Cr
                      </p>
                      <p style="font-size: 16px; font-weight: 600">
                        {{ pair.transaction.credit | currency }}
                      </p>
                    </div>
                    <div class="col-3 text-end">
                      <!-- Pending Balance -->
                      <p class="text-black-50" style="font-size: 13px">
                        Balance to Rec
                      </p>
                      <p style="font-size: 16px; font-weight: 600">
                        {{ pair.transaction.pendingBalance | currency }}
                      </p>
                    </div>
                  </div>
                </div>
                <div class="col-3 m-auto">
                  <div class="row g-0 px-3">
                    <div
                      class="col-6 p-1"
                      *ngIf="pair.transaction.matched === 'green'"
                    >
                      <button
                        class="btn btn-sm w-100 mini-match-button"
                        (click)="saveSuggestedTransaction(pair.transaction)"
                        title="Matched"
                      >
                        Confirm
                      </button>
                    </div>
                    <div class="col-6 p-1">
                      <button
                        class="btn w-100 btn-sm mini-search-button"
                        (click)="openFindPopup(pair.transaction)"
                        title="Find"
                      >
                        Find
                      </button>
                    </div>
                    <div class="col-6 p-1">
                      <button
                        class="btn w-100 btn-sm mini-create-button"
                        (click)="openDialog(pair.transaction)"
                        title="Create"
                      >
                        Create
                      </button>
                    </div>
                    <div class="col-6 p-1">
                      <button
                        class="btn w-100 btn-sm mini-transfer-button"
                        title="Transfer"
                        (click)="openBankTransferDialog(pair.transaction)"
                      >
                        Transfer
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-5">
              <div class="col-12 g-0" *ngIf="pair.type === 'PRI'">
                <div
                  *ngIf="pair.paymentReceiptOrInvoice.type === 'receipt'"
                  class="row g-0 border border-1 border-secondary-subtle rounded-3 p-3"
                  [ngClass]="{
                    'matched-green': pair.transaction.matched === 'green',
                    'matched-yellow': pair.transaction.matched === 'yellow',
                    'matched-red': pair.transaction.matched === 'orange',
                  }"
                >
                  <ng-container
                    *ngTemplateOutlet="
                      paymentReceiptCardView;
                      context: { $implicit: pair.paymentReceiptOrInvoice }
                    "
                  ></ng-container>
                </div>
                <div
                  *ngIf="pair.paymentReceiptOrInvoice.type === 'invoice'"
                  class="row g-0 border border-1 border-secondary-subtle rounded-3 p-3"
                  [ngClass]="{
                    'matched-green': pair.transaction.matched === 'green',
                    'matched-yellow': pair.transaction.matched === 'yellow',
                    'matched-red': pair.transaction.matched === 'orange',
                  }"
                >
                  <ng-container
                    *ngTemplateOutlet="
                      invoiceHeadCardView;
                      context: { $implicit: pair.paymentReceiptOrInvoice }
                    "
                  ></ng-container>
                </div>
              </div>
              <div class="col-12 g-0" *ngIf="pair.type === 'SR'">
                <ng-container
                  *ngTemplateOutlet="
                    appliedRuleView;
                    context: {
                      $implicit: pair.assignedRuleGLDetails,
                      rowIndex: i,
                      transaction: pair.transaction
                    }
                  "
                ></ng-container>
              </div>
            </div>
          </div>
        </div>
        <!-- full-credit-match -->

        <!-- left-side -->
        <div class="col-7 d-grid">
          <div class="col-12 g-0 d-grid">
            <div class="col-12 g-0 transaction-list">
              <div
                class="col-12 g-0"
                *ngFor="let transaction of filteredTransactions"
              >
                <div
                  class="row transaction-item g-0 bottom-space"
                  *ngIf="!transaction.matched"
                  style="height: 80px"
                >
                  <div
                    class="col-9 border border-1 border-secondary-subtle rounded-3 p-3"
                    [ngClass]="{
                      'matched-yellow': transaction.matched === 'yellow',
                      'matched-green': transaction.matched === 'green',
                      'bg-warning': transaction.matched === 'orange',
                    }"
                  >
                    <div class="row g-0">
                      <div class="col-6">
                        <p class="text-black-50" style="font-size: 13px">
                          {{ transaction.date | date : "dd MMM yyyy" }}
                        </p>
                        <p style="font-size: 15px; font-weight: 600">
                          {{ transaction.description }}
                        </p>
                      </div>
                      <div class="col-3 text-end middle-item pe-3">
                        <p class="text-black-50" style="font-size: 13px">
                          <i
                            *ngIf="transaction.credit"
                            class="fas fa-arrow-up my-auto"
                            style="
                              color: limegreen;
                              font-weight: 500;
                              font-size: 11px;
                            "
                          ></i>
                          <i
                            *ngIf="transaction.debit"
                            class="fas fa-arrow-down my-auto"
                            style="
                              color: red;
                              font-weight: 500;
                              font-size: 11px;
                            "
                          ></i>
                          {{ transaction.credit ? "Cr" : "Dr" }}
                        </p>
                        <p style="font-size: 16px; font-weight: 600">
                          {{
                            transaction.credit
                              ? (transaction.credit | currency)
                              : (transaction.debit | currency)
                          }}
                        </p>
                      </div>
                      <div class="col-3 text-end">
                        <p class="text-black-50" style="font-size: 13px">
                          Balance to Rec
                        </p>
                        <p style="font-size: 16px; font-weight: 600">
                          {{ transaction.pendingBalance | currency }}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="col-3 m-auto">
                    <div class="row g-0 px-3">
                      <div class="col-6 p-1">
                        <button
                          class="btn btn-sm w-100 mini-search-button"
                          (click)="openFindPopup(transaction)"
                          title="Find"
                        >
                          Find
                        </button>
                      </div>
                      <div class="col-6 p-1">
                        <button
                          class="btn mini-create-button w-100 btn-sm"
                          (click)="openDialog(transaction)"
                          title="Create"
                        >
                          Create
                        </button>
                      </div>
                      <div class="col-6 p-1">
                        <button
                          class="btn btn-sm w-100 mini-transfer-button"
                          title="Transfer"
                          (click)="openBankTransferDialog(transaction)"
                        >
                          Transfer
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- left-side -->

        <!-- right-side -->
        <div class="col-5 d-grid">
          <div class="col-12">
            <!-- Invoice Table -->
            <div class="transaction-list g-0 row">
              <div
                class="col-12"
                *ngFor="
                  let paymentReceiptOrInvoice of filteredPaymentReceiptsAndInvoiceHeads
                "
              >
                <!-- Transaction Details (Right Side) -->
                <div
                  class="transaction-item g-0 bottom-space row border border-1 border-secondary-subtle g-0 rounded-3 p-3"
                  style="height: 80px"
                  *ngIf="
                    !paymentReceiptOrInvoice.matched &&
                    paymentReceiptOrInvoice.type === 'receipt'
                  "
                >
                  <ng-container
                    *ngTemplateOutlet="
                      paymentReceiptCardView;
                      context: { $implicit: paymentReceiptOrInvoice }
                    "
                  ></ng-container>
                </div>
                <div
                  class="transaction-item g-0 bottom-space row border border-1 border-secondary-subtle g-0 rounded-3 p-3"
                  style="height: 80px"
                  *ngIf="
                    !paymentReceiptOrInvoice.matched &&
                    paymentReceiptOrInvoice.type === 'invoice'
                  "
                >
                  <ng-container
                    *ngTemplateOutlet="
                      invoiceHeadCardView;
                      context: { $implicit: paymentReceiptOrInvoice }
                    "
                  ></ng-container>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- right-side -->
      </div>
    </div>
  </ng-template>

  <ng-template #debitTransactions>
    <div class="col-12">
      <div class="row g-0">
        <!-- full-debit-match -->
        <div class="col-12 d-grid">
          <div
            class="row g-0 bottom-space"
            *ngFor="let pair of matchedDebitPairs; let i = index"
          >
            <div class="col-7">
              <div class="col-12 g-0 d-flex">
                <div
                  class="col-9 border border-1 border-secondary-subtle rounded-3 p-3"
                >
                  <div class="row g-0">
                    <div
                      class="col-6 d-flex flex-column justify-content-center"
                    >
                      <!-- Transaction Info -->
                      <p class="text-black-50" style="font-size: 13px">
                        {{ pair.transaction.date | date : "dd MMM yyyy" }}
                      </p>
                      <p style="font-size: 15px; font-weight: 600">
                        {{ pair.transaction.description }}
                      </p>
                    </div>
                    <div class="col-3 text-end pe-3 middle-item">
                      <!-- Transaction Debit -->
                      <p class="text-black-50" style="font-size: 13px">
                        <i
                          class="fas fa-arrow-down my-auto"
                          style="color: red; font-weight: 500; font-size: 11px"
                        ></i>
                        Dr
                      </p>
                      <p style="font-size: 16px; font-weight: 600">
                        {{ pair.transaction.debit | currency }}
                      </p>
                    </div>
                    <div class="col-3 text-end">
                      <!-- Pending Balance -->
                      <p class="text-black-50" style="font-size: 13px">
                        Balance to Rec
                      </p>
                      <p style="font-size: 16px; font-weight: 600">
                        {{ pair.transaction.pendingBalance | currency }}
                      </p>
                    </div>
                  </div>
                </div>
                <div class="col-3 m-auto">
                  <div class="row g-0 px-3">
                    <div
                      class="col-6 p-1"
                      *ngIf="pair.transaction.matched === 'green'"
                    >
                      <button
                        class="btn btn-sm w-100 mini-match-button"
                        (click)="saveSuggestedTransaction(pair.transaction)"
                        title="Matched"
                      >
                        Confirm
                      </button>
                    </div>
                    <div class="col-6 p-1">
                      <button
                        class="btn w-100 btn-sm mini-search-button"
                        (click)="openFindPopup(pair.transaction)"
                        title="Find"
                      >
                        Find
                      </button>
                    </div>
                    <div class="col-6 p-1">
                      <button
                        class="btn w-100 btn-sm mini-create-button"
                        (click)="openDialog(pair.transaction)"
                        title="Create"
                      >
                        Create
                      </button>
                    </div>
                    <div class="col-6 p-1">
                      <button
                        class="btn w-100 btn-sm mini-transfer-button"
                        title="Transfer"
                        (click)="openBankTransferDialog(pair.transaction)"
                      >
                        Transfer
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-5">
              <div class="col-12 g-0" *ngIf="pair.type === 'VB'">
                <div
                  class="row g-0 border border-1 border-secondary-subtle rounded-3 p-3"
                  *ngIf="pair.voucherOrBill.type == 'bill'"
                  [ngClass]="{
                    'matched-green': pair.transaction.matched === 'green',
                    'matched-yellow': pair.transaction.matched === 'yellow',
                    'matched-red': pair.transaction.matched === 'orange',
                  }"
                >
                  <ng-container
                    *ngTemplateOutlet="
                      billHeadCardView;
                      context: { $implicit: pair.voucherOrBill }
                    "
                  ></ng-container>
                </div>
                <div
                  class="row g-0 border border-1 border-secondary-subtle rounded-3 p-3"
                  *ngIf="pair.voucherOrBill.type == 'voucher'"
                  [ngClass]="{
                    'matched-green': pair.transaction.matched === 'green',
                    'matched-yellow': pair.transaction.matched === 'yellow',
                    'matched-red': pair.transaction.matched === 'orange',
                  }"
                >
                  <ng-container
                    *ngTemplateOutlet="
                      paymentVoucherCardView;
                      context: { $implicit: pair.voucherOrBill }
                    "
                  ></ng-container>
                </div>
              </div>
              <div class="col-12 g-0" *ngIf="pair.type === 'RR'">
                <ng-container
                  *ngTemplateOutlet="
                    appliedRuleView;
                    context: {
                      $implicit: pair.assignedRuleGLDetails,
                      rowIndex: i,
                      transaction: pair.transaction
                    }
                  "
                ></ng-container>
              </div>
            </div>
          </div>
        </div>
        <!-- full-debit-match -->

        <!-- left-side -->
        <div class="col-7 d-grid">
          <div class="col-12 g-0 d-grid">
            <div class="col-12 g-0 transaction-list">
              <div
                class="col-12 g-0"
                *ngFor="let transaction of filteredTransactions"
              >
                <div
                  class="row transaction-item g-0 bottom-space"
                  *ngIf="!transaction.matched"
                  style="height: 80px"
                >
                  <div
                    class="col-9 border border-1 border-secondary-subtle rounded-3 p-3"
                    [ngClass]="{
                      'matched-yellow': transaction.matched === 'yellow',
                      'matched-green': transaction.matched === 'green'
                    }"
                  >
                    <div class="row g-0">
                      <div class="col-6">
                        <p class="text-black-50" style="font-size: 13px">
                          {{ transaction.date | date : "dd MMM yyyy" }}
                        </p>
                        <p style="font-size: 15px; font-weight: 600">
                          {{ transaction.description }}
                        </p>
                      </div>
                      <div class="col-3 text-end middle-item pe-3">
                        <p class="text-black-50" style="font-size: 13px">
                          <i
                            *ngIf="transaction.credit"
                            class="fas fa-arrow-up my-auto"
                            style="
                              color: limegreen;
                              font-weight: 500;
                              font-size: 11px;
                            "
                          ></i>
                          <i
                            *ngIf="transaction.debit"
                            class="fas fa-arrow-down my-auto"
                            style="
                              color: red;
                              font-weight: 500;
                              font-size: 11px;
                            "
                          ></i>
                          {{ transaction.credit ? "Cr" : "Dr" }}
                        </p>
                        <p style="font-size: 16px; font-weight: 600">
                          {{
                            transaction.credit
                              ? (transaction.credit | currency)
                              : (transaction.debit | currency)
                          }}
                        </p>
                      </div>
                      <div class="col-3 text-end">
                        <p class="text-black-50" style="font-size: 13px">
                          Balance toRec
                        </p>
                        <p style="font-size: 16px; font-weight: 600">
                          {{ transaction.pendingBalance | currency }}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="col-3 m-auto">
                    <div class="row g-0 px-3">
                      <div class="col-6 p-1">
                        <button
                          class="btn btn-sm w-100 mini-search-button"
                          (click)="openFindPopup(transaction)"
                          title="Find"
                        >
                          Find
                        </button>
                      </div>
                      <div class="col-6 p-1">
                        <button
                          class="btn mini-create-button w-100 btn-sm"
                          (click)="openDialog(transaction)"
                          title="Create"
                        >
                          Create
                        </button>
                      </div>
                      <div class="col-6 p-1">
                        <button
                          class="btn btn-sm w-100 mini-transfer-button"
                          title="Transfer"
                          (click)="openBankTransferDialog(transaction)"
                        >
                          Transfer
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- left-side -->

        <!-- right-side -->
        <div class="col-5 d-grid">
          <div class="col-12">
            <!-- Paymnet voucher Head Table -->
            <div class="transaction-list g-0 row">
              <div
                class="col-12"
                *ngFor="
                  let paymentVoucherOrBill of filteredPaymentVoucherHeadersAndBills
                "
              >
                <!-- Transaction Details (Right Side) -->
                <div
                  class="transaction-item g-0 bottom-space row border border-1 border-secondary-subtle g-0 rounded-3 p-3"
                  style="height: 80px"
                  *ngIf="
                    !paymentVoucherOrBill.matched &&
                    paymentVoucherOrBill.type == 'voucher'
                  "
                >
                  <ng-container
                    *ngTemplateOutlet="
                      paymentVoucherCardView;
                      context: { $implicit: paymentVoucherOrBill }
                    "
                  ></ng-container>
                </div>
                <div
                  class="transaction-item g-0 bottom-space row border border-1 border-secondary-subtle g-0 rounded-3 p-3"
                  style="height: 80px"
                  *ngIf="
                    !paymentVoucherOrBill.matched &&
                    paymentVoucherOrBill.type == 'bill'
                  "
                >
                  <ng-container
                    *ngTemplateOutlet="
                      billHeadCardView;
                      context: { $implicit: paymentVoucherOrBill }
                    "
                  ></ng-container>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- right-side -->
      </div>
    </div>
  </ng-template>

  <ng-template #paymentReceiptCardView let-paymentReceipt>
    <div class="col-12 g-0 d-flex align-items-center">
      <div class="col-5" style="height: fit-content !important">
        <p class="text-black-50" style="font-size: 13px">
          <span class="receipt-mini-label">{{
            paymentReceipt.paymentNumber
          }}</span>
          -
          {{ paymentReceipt.documentDate | date : "dd MMM yyyy" }}
        </p>
        <p style="font-size: 15px; font-weight: 600">
          {{ paymentReceipt.customerName }}
        </p>
      </div>
      <div class="col-4 text-end middle-item pe-3">
        <p class="text-black-50" style="font-size: 13px">Total</p>
        <p style="font-size: 16px; font-weight: 600">
          {{ paymentReceipt.totalPaidAmount | currency }}
        </p>
      </div>
      <div class="col-3 text-end">
        <p class="text-black-50" style="font-size: 13px">Balance to Rec</p>
        <p style="font-size: 16px; font-weight: 600">
          {{ getPaymentReceiptRecbalance(paymentReceipt) | currency }}
        </p>
      </div>
    </div>
  </ng-template>

  <ng-template #invoiceHeadCardView let-invoiceHead>
    <div class="col-12 g-0 d-flex align-items-center">
      <div class="col-5" style="height: fit-content !important">
        <p class="text-black-50" style="font-size: 13px">
          <span class="invoice-mini-label">{{
            invoiceHead.invoiceNumber
          }}</span>
          - {{ invoiceHead.postingDate | date : "dd MMM yyyy" }}
        </p>
        <p style="font-size: 15px; font-weight: 600">
          {{ invoiceHead.reference }}
        </p>
      </div>
      <div class="col-4 text-end middle-item pe-3">
        <p class="text-black-50" style="font-size: 13px">Total</p>
        <p style="font-size: 16px; font-weight: 600">
          {{ invoiceHead.grandTotal | currency }}
        </p>
      </div>
      <div class="col-3 text-end">
        <p class="text-black-50" style="font-size: 13px">Balance to Rec</p>
        <p style="font-size: 16px; font-weight: 600">
          {{ invoiceHead.balanceAmount | currency }}
        </p>
      </div>
    </div>
  </ng-template>

  <ng-template #paymentVoucherCardView let-paymentVoucher>
    <div class="col-12 g-0 d-flex align-items-center">
      <div class="col-5" style="height: fit-content !important">
        <p class="text-black-50" style="font-size: 13px">
          <span class="voucher-mini-label">{{
            paymentVoucher.voucherNumber
          }}</span>
          - {{ paymentVoucher.date | date : "dd MMM yyyy" }}
        </p>
        <p style="font-size: 15px; font-weight: 600">
          {{ paymentVoucher.payeeName }}
          <small> - {{ paymentVoucher.referenceNos }}</small>
        </p>
      </div>
      <div class="col-4 text-end pe-3 middle-item">
        <p class="text-black-50" style="font-size: 13px">Total</p>
        <p style="font-size: 16px; font-weight: 600">
          {{ paymentVoucher.totalPaidAmount | currency }}
        </p>
      </div>
      <div class="col-3 text-end">
        <p class="text-black-50" style="font-size: 13px">Balance to Rec</p>
        <p style="font-size: 16px; font-weight: 600">
          {{ getApRecbalance(paymentVoucher) | currency }}
        </p>
      </div>
    </div>
  </ng-template>

  <ng-template #billHeadCardView let-billHead>
    <div class="col-12 g-0 d-flex align-items-center">
      <div class="col-5" style="height: fit-content !important">
        <p class="text-black-50" style="font-size: 13px">
          <span class="bill-mini-label">{{ billHead.referenceNo }}</span>
          - {{ billHead.postingDate | date : "dd MMM yyyy" }}
        </p>
        <p style="font-size: 15px; font-weight: 600">
          {{ billHead.supplierName }}
        </p>
      </div>
      <div class="col-4 text-end pe-3 middle-item">
        <p class="text-black-50" style="font-size: 13px">Total</p>
        <p style="font-size: 16px; font-weight: 600">
          {{ billHead.netAmount | currency }}
        </p>
      </div>
      <div class="col-3 text-end">
        <p class="text-black-50" style="font-size: 13px">Balance to Rec</p>
        <p style="font-size: 16px; font-weight: 600">
          {{ billHead.dueAmount | currency }}
        </p>
      </div>
    </div>
  </ng-template>

  <ng-template
    #appliedRuleView
    let-ruleDetailsList
    let-rowIndex="rowIndex"
    let-transaction="transaction"
  >
    <ng-container
      *ngIf="
        ruleDetailsList.length &&
        ruleDetailsList[0].bankRuleHead.bankRuleType === 'SPEND'
      "
    >
      <div
        class="row g-0 border border-1 border-secondary bg-light rounded-3 p-3 position-relative"
      >
        <p
          class="text-black-50 fst-italic text-decoration-underline"
          *ngIf="expandedRowIndex !== rowIndex"
          style="
            font-size: 11px;
            position: absolute;
            left: 18px;
            bottom: 18px;
            cursor: pointer;
          "
        >
          Details View
        </p>
        <div class="col-12 g-0">
          <div
            class="col-12 g-0 p-0"
            style="transition: height 0.3s ease"
            [ngStyle]="{
              height: expandedRowIndex === rowIndex ? 'auto' : '42px',
              overflow: expandedRowIndex === rowIndex ? 'visible' : 'hidden'
            }"
          >
            <div class="col-12">
              <div class="float-start">
                <p style="font-size: 15px; font-weight: 600; padding-left: 1px">
                  {{ ruleDetailsList[0].bankRuleHead.bankRuleName }}
                </p>
              </div>
              <div class="float-end d-flex">
                <button
                  (click)="toggleRowExpand(rowIndex)"
                  class="btn btn-sm bg-transparent px-2 py-1"
                >
                  <i
                    class="fa"
                    [ngClass]="{
                      'fa-chevron-down': expandedRowIndex !== rowIndex,
                      'fa-chevron-up': expandedRowIndex === rowIndex
                    }"
                  ></i>
                </button>
              </div>
            </div>

            <br /><br />

            <form
              (ngSubmit)="submitBankRuleMatch(ruleDetailsList, transaction)"
            >
              <table class="rule-detail-table">
                <colgroup>
                  <col width="50%" />
                  <col width="15%" />
                  <col width="20%" />
                  <col width="5%" />
                </colgroup>
                <tr *ngFor="let ruleDetail of ruleDetailsList">
                  <td class="ps-0">
                    <select
                      class="custom-select p-0 w-100 bg-white px-2"
                      style="height: 32px; border-radius: 5px"
                      [(ngModel)]="ruleDetail.ruleDetailGlAccountId"
                      name="glAccountId_{{ ruleDetail.bankRuleDetailId }}"
                    >
                      <option
                        *ngFor="let expense of expenseLedgerAccountsList"
                        [value]="expense.coaLedgerAccountId"
                      >
                        {{ expense.ledgerAccountName }}
                      </option>
                    </select>
                  </td>
                  <td>
                    <input
                      class="custom-input p-0 w-100 bg-white px-2"
                      style="height: 31px !important; border-radius: 5px"
                      type="number"
                      min="0.01"
                      max="100"
                      [(ngModel)]="ruleDetail.ruleDetailGlAccountPercentage"
                      name="percentage_{{ ruleDetail.bankRuleDetailId }}"
                    />
                  </td>
                  <td class="pe-2">
                    <p
                      style="font-size: 13px; font-weight: 600; text-align: end"
                    >
                      {{
                        valueFromPercentage(
                          transaction,
                          ruleDetail.ruleDetailGlAccountPercentage
                        ) | currency
                      }}
                    </p>
                  </td>
                  <td class="pe-0">
                    <button
                      type="button"
                      [disabled]="ruleDetailsList.length === 1"
                      class="btn btn-sm bg-danger-subtle"
                      (click)="removeRuleDetail(ruleDetail)"
                    >
                      <i class="fa fa-trash"></i>
                    </button>
                  </td>
                </tr>
                <tfoot>
                  <tr>
                    <td>
                      <button
                        [disabled]="!isSpendRuleValid(ruleDetailsList)"
                        type="submit"
                        class="btn btn-sm mini-match-button px-3"
                      >
                        Assign
                      </button>
                    </td>
                    <td></td>
                    <td class="px-2">
                      <p class="px-2 py-2 total-cell">
                        {{ transaction.pendingBalance | currency }}
                      </p>
                    </td>
                    <td></td>
                  </tr>
                </tfoot>
              </table>
            </form>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-container
      *ngIf="
        ruleDetailsList.length &&
        ruleDetailsList[0].bankRuleHead.bankRuleType === 'RECEIVE'
      "
    >
      <div
        class="row g-0 border border-1 border-secondary bg-light rounded-3 p-3 position-relative"
      >
        <p
          class="text-black-50 text-decoration-underline fst-italic"
          *ngIf="expandedRowIndex !== rowIndex"
          style="
            font-size: 11px;
            position: absolute;
            left: 18px;
            bottom: 18px;
            cursor: pointer;
          "
        >
          Details View
        </p>
        <div class="col-12 g-0">
          <div
            class="col-12 g-0 p-0"
            style="transition: height 0.3s ease"
            [ngStyle]="{
              height: expandedRowIndex === rowIndex ? 'auto' : '42px',
              overflow: expandedRowIndex === rowIndex ? 'visible' : 'hidden'
            }"
          >
            <div class="col-12">
              <div class="float-start">
                <p style="font-size: 15px; font-weight: 600; padding-left: 1px">
                  {{ ruleDetailsList[0].bankRuleHead.bankRuleName }}
                </p>
              </div>
              <div class="float-end d-flex">
                <button
                  (click)="toggleRowExpand(rowIndex)"
                  class="btn btn-sm bg-transparent px-2 py-1"
                >
                  <i
                    class="fa"
                    [ngClass]="{
                      'fa-chevron-down': expandedRowIndex !== rowIndex,
                      'fa-chevron-up': expandedRowIndex === rowIndex
                    }"
                  ></i>
                </button>
              </div>
            </div>

            <br /><br />
            <form
              (ngSubmit)="submitBankRuleMatch(ruleDetailsList, transaction)"
            >
              <table class="rule-detail-table">
                <colgroup>
                  <col width="50%" />
                  <col width="15%" />
                  <col width="20%" />
                  <col width="5%" />
                </colgroup>
                <tr *ngFor="let ruleDetail of ruleDetailsList">
                  <td class="ps-0">
                    <select
                      class="custom-select p-0 w-100 bg-white px-2"
                      style="height: 32px; border-radius: 5px"
                      [(ngModel)]="ruleDetail.ruleDetailGlAccountId"
                      name="glAccountId_{{ ruleDetail.bankRuleDetailId }}"
                    >
                      <option
                        *ngFor="let income of incomeLedgerAccountsList"
                        [value]="income.coaLedgerAccountId"
                      >
                        {{ income.ledgerAccountName }}
                      </option>
                    </select>
                  </td>
                  <td>
                    <input
                      class="custom-input p-0 w-100 bg-white px-2"
                      style="height: 31px !important; border-radius: 5px"
                      type="number"
                      min="0.01"
                      max="100"
                      [(ngModel)]="ruleDetail.ruleDetailGlAccountPercentage"
                      name="percentage_{{ ruleDetail.bankRuleDetailId }}"
                    />
                  </td>
                  <td class="pe-2">
                    <p
                      style="font-size: 13px; font-weight: 600; text-align: end"
                    >
                      {{
                        valueFromPercentage(
                          transaction,
                          ruleDetail.ruleDetailGlAccountPercentage
                        ) | currency
                      }}
                    </p>
                  </td>
                  <td class="pe-0">
                    <button
                      [disabled]="ruleDetailsList.length === 1"
                      class="btn btn-sm bg-danger-subtle"
                    >
                      <i class="fa fa-trash"></i>
                    </button>
                  </td>
                </tr>
                <tfoot>
                  <tr>
                    <td>
                      <button class="btn btn-sm mini-match-button px-3">
                        Assign
                      </button>
                    </td>
                    <td></td>
                    <td class="px-2">
                      <p class="px-2 py-2 total-cell">
                        {{ transaction.pendingBalance | currency }}
                      </p>
                    </td>
                    <td></td>
                  </tr>
                </tfoot>
              </table>
            </form>
          </div>
        </div>
      </div>
    </ng-container>
  </ng-template>

  <!-- Expense, Income Popup -->
  <div class="custom-dialog p-5" style="width: 400px" *ngIf="isExpenseOpen">
    <form
      *ngIf="isDebitPopup"
      [formGroup]="expenseForm"
      (ngSubmit)="onExpenseSubmit()"
      class="dialog-content"
    >
      <div class="col-12" style="line-height: 20px">
        <p class="fs-5 fw-bold p-0 m-0">
          Create Expense
          <i
            class="fas fa-arrow-down my-auto"
            style="color: red; font-weight: 500; font-size: 14px"
          ></i>
        </p>
        <p class="text-black-50 p-0 m-0" style="font-size: 12px">
          Complete the form to create an expense
        </p>
      </div>
      <br />
      <br />
      <div class="col-12">
        <div class="row custom-form-label">
          <div class="col-12">
            <label for="payeeName">Supplier or Description</label>
            <input
              type="text"
              formControlName="payeeName"
              [matAutocomplete]="auto"
              class="custom-input text-start w-100 mt-2"
              style="height: 40px !important; font-weight: 500 !important"
            />
            <mat-autocomplete class="p-0" #auto="matAutocomplete">
              <mat-option
                style="
                  font-size: 12px !important;
                  border-bottom: 1px solid rgb(200, 200, 200);
                "
                *ngFor="let supplier of filteredSuppliers"
                [value]="supplier.bpName"
              >
                {{ supplier.bpName }}
              </mat-option>
            </mat-autocomplete>
          </div>
          <div class="col-12 my-3">
            <label for="amount">Amount</label>
            <input
              type="number"
              formControlName="amount"
              class="custom-input w-100 text-end mt-2"
              style="
                height: 40px !important;
                border-color: rgb(200, 200, 200) !important;
              "
            />
          </div>
          <div class="col-12">
            <label for="ledgerAccount">Ledger Account</label>
            <select
              formControlName="ledgerAccount"
              class="custom-select w-100 mt-2"
              style="
                height: 40px !important;
                border-color: rgb(200, 200, 200) !important;
              "
            >
              <option [ngValue]="null" disabled>Select account</option>
              <option
                *ngFor="let acc of expenseLedgerAccountsList"
                [value]="acc.coaLedgerAccountId"
              >
                {{ acc.ledgerAccountName }}
              </option>
            </select>
          </div>
          <div class="col-12 mt-3" *ngIf="isExpenseGstCheckboxVisible">
            <input
              type="checkbox"
              formControlName="gstCheck"
            />&nbsp;&nbsp;<span style="font-size: 13px">GST Applicable</span>
          </div>
        </div>
      </div>

      <div class="dialog-actions mt-5">
        <button
          type="button"
          class="btn-cancel"
          (click)="closeExpenseIncomeDialog()"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="btn-submit"
          [disabled]="expenseForm.invalid"
        >
          Submit
        </button>
      </div>
    </form>
    <form
      *ngIf="isCreditPopup"
      [formGroup]="incomeForm"
      (ngSubmit)="onIncomeSubmit()"
      class="dialog-content"
    >
      <div class="col-12" style="line-height: 20px">
        <p class="fs-5 fw-bold p-0 m-0">
          Create Income
          <i
            class="fas fa-arrow-up my-auto"
            style="color: limegreen; font-weight: 500; font-size: 14px"
          ></i>
        </p>
        <p class="text-black-50 p-0 m-0" style="font-size: 12px">
          Complete the form to create an income
        </p>
      </div>
      <br />
      <br />
      <div class="col-12">
        <div class="row custom-form-label">
          <div class="col-12">
            <label for="incomeDescription">Description</label>
            <input
              type="text"
              formControlName="incomeDescription"
              [matAutocomplete]="auto"
              class="custom-input text-start w-100 mt-2"
              style="height: 40px !important; font-weight: 500 !important"
            />
            <mat-autocomplete class="p-0" #auto="matAutocomplete">
              <mat-option
                style="
                  font-size: 12px !important;
                  border-bottom: 1px solid rgb(200, 200, 200);
                "
                *ngFor="let customer of filteredCustomers"
                [value]="customer.bpName"
              >
                {{ customer.bpName }}
              </mat-option>
            </mat-autocomplete>
          </div>
          <div class="col-12 my-3">
            <label for="incomeAmount">Amount</label>
            <input
              type="number"
              formControlName="incomeAmount"
              class="custom-input w-100 text-end mt-2"
              style="
                height: 40px !important;
                border-color: rgb(200, 200, 200) !important;
              "
            />
          </div>
          <div class="col-12">
            <label for="incomeLedgerAccount">Ledger Account</label>
            <select
              formControlName="incomeLedgerAccount"
              class="custom-select w-100 mt-2"
              style="
                height: 40px !important;
                border-color: rgb(200, 200, 200) !important;
              "
            >
              <option [ngValue]="null" disabled>Select account</option>
              <option
                *ngFor="let acc of incomeLedgerAccountsList"
                [value]="acc.coaLedgerAccountId"
              >
                {{ acc.ledgerAccountName }}
              </option>
            </select>
          </div>
          <div class="col-12 mt-3" *ngIf="isIncomeGstCheckboxVisible">
            <input
              type="checkbox"
              formControlName="incomeGstCheck"
            />&nbsp;&nbsp;<span style="font-size: 13px">GST Applicable</span>
          </div>
        </div>
      </div>

      <div class="dialog-actions mt-5">
        <button
          type="button"
          class="btn-cancel"
          (click)="closeExpenseIncomeDialog()"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="btn-submit"
          [disabled]="incomeForm.invalid"
        >
          Submit
        </button>
      </div>
    </form>
  </div>
  <!-- Expense, Income Popup -->

  <!-- View Details Popup -->
  <div class="custom-dialog2" *ngIf="isViewDetails">
    <h2 class="popup-title">{{ selectedRecordType | titlecase }} Details</h2>
    <button class="close-btn" (click)="closeViewDetails()">&times;</button>
    <table class="table1">
      <thead>
        <tr class="table-head1">
          <!-- Transaction table headers -->
          <th
            *ngIf="selectedRecordType === 'transaction'"
            scope="col"
            class="valuehead"
          >
            Date
          </th>
          <th
            *ngIf="selectedRecordType === 'transaction'"
            scope="col"
            class="valuehead"
          >
            Description
          </th>
          <th
            *ngIf="
              selectedRecordType === 'transaction' && selectedFilter !== 'debit'
            "
            scope="col"
            class="valuehead"
          >
            Credit
          </th>
          <th
            *ngIf="
              selectedRecordType === 'transaction' &&
              selectedFilter !== 'credit'
            "
            scope="col"
            class="valuehead"
          >
            Debit
          </th>
          <th
            *ngIf="selectedRecordType === 'transaction'"
            scope="col"
            class="valuehead"
          >
            Balance
          </th>
          <th
            *ngIf="selectedRecordType === 'transaction'"
            scope="col"
            class="valuehead"
          >
            Reference
          </th>

          <!-- Invoice table headers -->
          <th
            *ngIf="selectedRecordType === 'invoice'"
            scope="col"
            class="valuehead"
          >
            Invoice No
          </th>
          <th
            *ngIf="selectedRecordType === 'invoice'"
            scope="col"
            class="valuehead"
          >
            Customer
          </th>
          <th
            *ngIf="selectedRecordType === 'invoice'"
            scope="col"
            class="valuehead"
          >
            Invoice Date
          </th>
          <th
            *ngIf="selectedRecordType === 'invoice'"
            style="text-align: right"
            scope="col"
            class="valuehead"
          >
            Amount
          </th>

          <!-- Bill table headers -->
          <th
            *ngIf="selectedRecordType === 'bill'"
            scope="col"
            class="valuehead"
          >
            Reference No
          </th>
          <th
            *ngIf="selectedRecordType === 'bill'"
            scope="col"
            class="valuehead"
          >
            Supplier
          </th>
          <th
            *ngIf="selectedRecordType === 'bill'"
            scope="col"
            class="valuehead"
          >
            Bill Date
          </th>
          <th
            *ngIf="selectedRecordType === 'bill'"
            scope="col"
            class="valuehead"
            style="text-align: right"
          >
            Bill Amount
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngIf="selectedDetails != null">
          <!-- Transaction table details -->
          <td *ngIf="selectedRecordType === 'transaction'" class="value">
            {{ selectedDetails.date | date : "dd-MM-yyyy" }}
          </td>
          <td *ngIf="selectedRecordType === 'transaction'" class="value">
            {{ selectedDetails.description }}
          </td>
          <td
            *ngIf="
              selectedRecordType === 'transaction' && selectedFilter !== 'debit'
            "
            class="value"
          >
            {{ selectedDetails.credit | currency }}
          </td>
          <td
            *ngIf="
              selectedRecordType === 'transaction' &&
              selectedFilter !== 'credit'
            "
            class="value"
          >
            {{ selectedDetails.debit | currency }}
          </td>
          <td *ngIf="selectedRecordType === 'transaction'" class="value">
            {{ selectedDetails.balance | currency }}
          </td>
          <td *ngIf="selectedRecordType === 'transaction'" class="value">
            {{ selectedDetails.reference }}
          </td>

          <!-- Invoice table details -->
          <td *ngIf="selectedRecordType === 'invoice'" class="value">
            {{ selectedDetails.invoiceNumber }}
          </td>
          <td *ngIf="selectedRecordType === 'invoice'" class="value">
            {{ selectedDetails.reference }}
          </td>
          <td *ngIf="selectedRecordType === 'invoice'" class="value">
            {{ selectedDetails.postingDate | date : "dd-MM-yyyy" }}
          </td>
          <td
            *ngIf="selectedRecordType === 'invoice'"
            style="text-align: right"
            class="value"
          >
            {{ selectedDetails.grandTotal | currency }}
          </td>

          <!-- Bill table details -->
          <td *ngIf="selectedRecordType === 'bill'" class="value">
            {{ selectedDetails.referenceNo }}
          </td>
          <td *ngIf="selectedRecordType === 'bill'" class="value">
            {{ selectedDetails.supplierName }}
          </td>
          <td *ngIf="selectedRecordType === 'bill'" class="value">
            {{ selectedDetails.postingDate | date : "dd-MM-yyyy" }}
          </td>
          <td
            *ngIf="selectedRecordType === 'bill'"
            style="text-align: right"
            class="value"
          >
            {{ selectedDetails.netAmount | currency }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <!-- View Details Popup -->

  <!-- Find Popup -->
  <div class="find-popup-container" *ngIf="isFindPopup">
    <div class="find-popup col-12">
      <!-- Search -->
      <div class="find-popup-header col-12 px-4">
        <p class="text-white my-auto float-start" style="font-weight: bold">
          Find Transactions
        </p>
        <button
          class="find-popup-close-btn float-end"
          (click)="closeFindPopup()"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Transaction Amount -->
      <div class="find-popup-transaction col-12 p-4">
        <div class="float-start d-inline-flex">
          <p
            class="text-end text-black-50 my-auto position-relative"
            style="font-size: 13px; line-height: 12px"
          >
            Transaction <br />
            Amount
          </p>
          &nbsp;
          <p class="ps-2 my-auto" style="font-size: 24px">
            <strong>{{ selectedTransactionAmount | currency }}</strong>
          </p>
        </div>
        <div class="float-end">
          <input
            type="text"
            placeholder="Enter the keyword"
            style="max-width: 300px; width: 300px"
            class="find-popup-search-bar"
            [(ngModel)]="findrecordsFilter.searchTerm"
          />&nbsp;&nbsp;
          <input
            type="number"
            inputmode="decimal"
            min="0"
            placeholder="Enter the Amount"
            style="max-width: 200px; width: 200px"
            class="find-popup-search-bar"
            [(ngModel)]="findrecordsFilter.amountterm"
          />
        </div>
      </div>

      <!-- Table Container -->
      <div class="col-12 p-4 pb-0" style="height: calc(600px - 180px)">
        <div class="row" style="height: inherit; overflow-y: auto">
          <div class="col-12">
            <table class="find-transaction-tbl">
              <colgroup>
                <col width="35%" />
                <col width="15%" />
                <col width="20%" />
                <col width="20%" />
                <col width="10%" />
              </colgroup>
              <tr>
                <th>
                  {{
                    selectedTransactionType === "credit"
                      ? "Receipt Details"
                      : "Voucher details"
                  }}
                </th>
                <th>Total</th>
                <th>Balance To Rec</th>
                <th>Allocate Amount</th>
                <th>Action</th>
              </tr>

              <tr *ngFor="let selectedDetail of filteredFindRecords">
                <!-- Credit section -->
                <ng-container *ngIf="selectedTransactionType === 'credit'">
                  <ng-container *ngIf="selectedDetail.type === 'receipt'">
                    <td>
                      <small>
                        <span class="receipt-mini-label">{{
                          selectedDetail.paymentNumber
                        }}</span>
                        -
                        <span class="text-black-50">{{
                          selectedDetail.documentDate | date : "dd-MM-yyyy"
                        }}</span>
                      </small>
                      <p style="font-weight: 600">
                        {{ selectedDetail.customerName }}
                      </p>
                    </td>
                    <td>
                      {{ selectedDetail.totalPaidAmount | currency }}
                    </td>
                    <td>
                      {{
                        selectedDetail.totalPaidAmount -
                          selectedDetail.recAmount | currency
                      }}
                    </td>
                    <td>
                      <input
                        type="number"
                        class="custom-input w-100"
                        [ngClass]="{
                          'text-primary border-primary':
                            recAmountInput.value == recAmountInput.max
                        }"
                        #recAmountInput
                        [value]="
                          getAllocatbleAmount(
                            selectedTransactionAmount,
                            selectedDetail
                          )
                        "
                        [max]="
                          getAllocatbleAmount(
                            selectedTransactionAmount,
                            selectedDetail
                          )
                        "
                        [min]="1"
                      />
                    </td>
                    <td>
                      <button
                        class="find-popup-submit-btn w-100"
                        [disabled]="
                          recAmountInput.valueAsNumber <= 0 ||
                          recAmountInput.valueAsNumber >
                            getAllocatbleAmount(
                              selectedTransactionAmount,
                              selectedDetail
                            )
                        "
                        (click)="
                          saveFindTransaction(
                            selectedDetail,
                            recAmountInput.valueAsNumber
                          )
                        "
                      >
                        Allocate
                      </button>
                    </td>
                  </ng-container>
                  <ng-container *ngIf="selectedDetail.type === 'invoice'">
                    <td>
                      <small>
                        <span class="invoice-mini-label">{{
                          selectedDetail.invoiceNumber
                        }}</span>
                        -
                        <span class="text-black-50">{{
                          selectedDetail.postingDate | date : "dd-MM-yyyy"
                        }}</span>
                      </small>
                      <p style="font-weight: 600">
                        {{ selectedDetail.reference }}
                      </p>
                    </td>
                    <td>
                      {{ selectedDetail.grandTotal | currency }}
                    </td>
                    <td>
                      {{ selectedDetail.balanceAmount | currency }}
                    </td>
                    <td>
                      <input
                        type="number"
                        class="custom-input w-100"
                        [ngClass]="{
                          'text-primary border-primary':
                            recAmountInput.value == recAmountInput.max
                        }"
                        #recAmountInput
                        [value]="
                          getAllocatbleAmountForInvoice(
                            selectedTransactionAmount,
                            selectedDetail
                          )
                        "
                        [max]="
                          getAllocatbleAmountForInvoice(
                            selectedTransactionAmount,
                            selectedDetail
                          )
                        "
                        [min]="1"
                      />
                    </td>
                    <td>
                      <button
                        class="find-popup-submit-btn w-100"
                        [disabled]="
                          recAmountInput.valueAsNumber <= 0 ||
                          recAmountInput.valueAsNumber >
                            getAllocatbleAmountForInvoice(
                              selectedTransactionAmount,
                              selectedDetail
                            )
                        "
                        (click)="
                          saveFindTransaction(
                            selectedDetail,
                            recAmountInput.valueAsNumber
                          )
                        "
                      >
                        Allocate
                      </button>
                    </td>
                  </ng-container>
                </ng-container>

                <!-- Debit section -->
                <ng-container *ngIf="selectedTransactionType === 'debit'">
                  <ng-container *ngIf="selectedDetail.type === 'voucher'">
                    <td>
                      <small
                        ><span
                          class="voucher-mini-label"
                          style="font-size: 12px"
                          >{{ selectedDetail.voucherNumber }}</span
                        >
                        -
                        <span class="text-black-50">{{
                          selectedDetail.date | date : "dd-MM-yyyy"
                        }}</span></small
                      >
                      <p style="font-weight: 600">
                        {{ selectedDetail.payeeName }}
                      </p>
                    </td>
                    <td>{{ selectedDetail.totalPaidAmount | currency }}</td>
                    <td>
                      {{
                        selectedDetail.totalPaidAmount -
                          selectedDetail.recAmount | currency
                      }}
                    </td>
                    <td>
                      <input
                        type="number"
                        class="custom-input w-100"
                        [ngClass]="{
                          'text-primary border-primary':
                            recAmountInputDebit.value == recAmountInputDebit.max
                        }"
                        #recAmountInputDebit
                        [value]="
                          getAllocatbleAmount(
                            selectedTransactionAmount,
                            selectedDetail
                          )
                        "
                        [max]="
                          getAllocatbleAmount(
                            selectedTransactionAmount,
                            selectedDetail
                          )
                        "
                        [min]="1"
                      />
                    </td>
                    <td>
                      <button
                        class="find-popup-submit-btn w-100"
                        [disabled]="
                          recAmountInputDebit.valueAsNumber <= 0 ||
                          recAmountInputDebit.valueAsNumber >
                            getAllocatbleAmount(
                              selectedTransactionAmount,
                              selectedDetail
                            )
                        "
                        (click)="
                          saveFindTransaction(
                            selectedDetail,
                            recAmountInputDebit.valueAsNumber
                          )
                        "
                      >
                        Allocate
                      </button>
                    </td>
                  </ng-container>
                  <ng-container *ngIf="selectedDetail.type === 'bill'">
                    <td>
                      <small
                        ><span
                          class="bill-mini-label"
                          style="font-size: 12px"
                          >{{ selectedDetail.referenceNo }}</span
                        >
                        -
                        <span class="text-black-50">{{
                          selectedDetail.postingDate | date : "dd-MM-yyyy"
                        }}</span></small
                      >
                      <p style="font-weight: 600">
                        {{ selectedDetail.supplierName }}
                      </p>
                    </td>
                    <td>{{ selectedDetail.netAmount | currency }}</td>
                    <td>
                      {{ selectedDetail.dueAmount | currency }}
                    </td>
                    <td>
                      <input
                        type="number"
                        class="custom-input w-100"
                        [ngClass]="{
                          'text-primary border-primary':
                            recAmountInputDebit.value == recAmountInputDebit.max
                        }"
                        #recAmountInputDebit
                        [value]="
                          getAllocatbleAmountForBill(
                            selectedTransactionAmount,
                            selectedDetail
                          )
                        "
                        [max]="
                          getAllocatbleAmountForBill(
                            selectedTransactionAmount,
                            selectedDetail
                          )
                        "
                        [min]="1"
                      />
                    </td>
                    <td>
                      <button
                        class="find-popup-submit-btn w-100"
                        [disabled]="
                          recAmountInputDebit.valueAsNumber <= 0 ||
                          recAmountInputDebit.valueAsNumber >
                            getAllocatbleAmountForBill(
                              selectedTransactionAmount,
                              selectedDetail
                            )
                        "
                        (click)="
                          saveFindTransaction(
                            selectedDetail,
                            recAmountInputDebit.valueAsNumber
                          )
                        "
                      >
                        Allocate
                      </button>
                    </td>
                  </ng-container>
                </ng-container>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Find Popup -->

  <!-- Bank Tranasfer Popup -->
  <div
    class="custom-dialog p-5"
    style="width: 400px"
    *ngIf="isBankTransferOpen"
  >
    <form
      [formGroup]="bankTransferForm"
      (ngSubmit)="onBankTransferSubmit()"
      class="dialog-content"
    >
      <div class="col-12" style="line-height: 20px">
        <p class="fs-5 fw-bold p-0 m-0">
          Bank Transfer
          <i
            class="fas fa-random my-auto"
            style="color: rgb(0, 170, 248); font-weight: 500; font-size: 14px"
          ></i>
        </p>
        <p class="text-black-50 p-0 m-0" style="font-size: 12px">
          Complete the form to transasfer through banks
        </p>
      </div>
      <br />
      <br />
      <div class="col-12">
        <div class="row custom-form-label">
          <div class="col-12">
            <label for="tranasferBank">Bank Name</label>
            <select
              formControlName="transferBank"
              class="custom-select w-100 mt-2"
              style="
                height: 40px !important;
                border-color: rgb(200, 200, 200) !important;
              "
            >
              <option [ngValue]="null" disabled>Select account</option>
              <option
                *ngFor="let account of bankAccountsList"
                [value]="account.bankAccountId"
              >
                {{ account.accountName }}
              </option>
            </select>
          </div>
          <div class="col-12 my-3">
            <label for="transferDescription">Description</label>
            <input
              type="text"
              formControlName="transferDescription"
              class="custom-input w-100 text-start mt-2"
              style="
                height: 40px !important;
                border-color: rgb(200, 200, 200) !important;
              "
            />
          </div>
          <div class="col-12">
            <label for="transferAmount">Transfer Amount</label>
            <input
              readonly
              type="number"
              formControlName="transferAmount"
              class="custom-input w-100 text-end mt-2"
              style="
                height: 40px !important;
                border-color: rgb(200, 200, 200) !important;
              "
            />
          </div>
        </div>
      </div>
      <div class="dialog-actions mt-5">
        <button
          type="button"
          class="btn-cancel"
          (click)="closeBankTransferDialog()"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="btn-submit"
          [disabled]="bankTransferForm.invalid"
        >
          Transfer
        </button>
      </div>
    </form>
  </div>
  <!-- Bank Tranasfer Popup -->
</div>
