.container {
  font-family: "Inter";
  background-color: transparent;
  display: flex;
  justify-content: center;
  padding-block: 10px;
}

.invite-entity-form {
  background: #fff;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  min-width: 300px;
  height: 300px;
  text-align: center;
  margin-top: 20px;
}

.invite-entity-form h2 {
  margin-bottom: 30px;
  font-family: Inter;
  font-size: 30px;
  font-weight: 600;
  text-align: left;
  color: #535353;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
  font-family: Inter;
  font-size: 15px;
  font-weight: 600;
  line-height: 25.41px;
  color: #444343;
  position: relative;
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 2px solid #c7c7c7;
  border-radius: 8px;
  box-sizing: border-box;
  font-size: 1em;
  font-family: Inter;
}

.invite-entity-form .form-actions {
  display: flex;
  justify-content: right;
}

.invite-entity-form .form-actions button {
  padding: 10px 40px;
  font-size: 16px;
  cursor: pointer;
  border-radius: 12px;
  font-family: Inter;
  font-weight: 700;
  text-align: center;
}

.invite-entity-form .form-actions .cancel {
  background: transparent;
  color: #4262ff;
  border: #4262ff 2px solid;
  margin-left: 245px;
}

.invite-entity-form .form-actions .add-entity {
  background: linear-gradient(to right, #4262ff, #512ca2);
  border: none;
  color: #fff;
}

.invite-entity-form .form-actions .add-entity:hover {
  background: linear-gradient(to left, #4262ff, #512ca2);
}

.invite-entity-form .form-actions .cancel:hover {
  background: #4262ff;
  color: #fff;
}

@media (max-width: 599px) {
  .invite-entity-form .form-actions .cancel {
    background: transparent;
    color: #4262ff;
    border: #4262ff 2px solid;
    margin-left: 24px;
  }
}
