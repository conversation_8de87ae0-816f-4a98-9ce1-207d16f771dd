<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<br>
<div class="container">
    <h5><B>Profit and Loss Statement</B></h5>
    <hr>
    <div class="report-section">
        <div class="report-form-container">
            <div class="report-form">
               <!-- <div class="form-group">
                    <label for="fromDate">Date</label>
                    <div class="date-range">
                        <input type="date" id="fromDate" [(ngModel)]="fromDate">
                        <span>to</span>
                        <input type="date" id="toDate" [(ngModel)]="toDate">
                    </div>
                </div>-->

                <div class="form-group">
                    <label for="dateRange">Select Date Range</label>
                    <select id="dateRange" (change)="updateDateRange($event)">
                        <option value="">-- Select --</option>
                       <option value="endOfThisMonth">End of This Month</option>                       
                        <option value="endOfThisQuarter">End of This Quarter</option>
                        <option value="endOfThisYear">End of This Year</option>
                        <option value="endOfLastMonth">End of Last Month</option>
                        <option value="endOfLastQuarter">End of Last Quarter</option>
                        <option value="endOfLastYear">End of Last Year</option>
                        <option value="custom">Customized Date</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="fromDate">Date</label>
                    <div class="date-range">
                        <input type="date" id="fromDate" [(ngModel)]="fromDate" [disabled]="!isCustom">
                        <span>to</span>
                        <input type="date" id="toDate" [(ngModel)]="toDate" [disabled]="!isCustom">
                    </div>
                </div>
                
                <br>
                <div class="form-group">
                    <label>PNL Type:</label>
                    <div style="display: flex; gap: 20px; align-items: center;">
                        <input type="radio" id="accrual" name="pnlType" [(ngModel)]="pnlType" value="accrual">
                        <label for="accrual">Accrual</label>
                
                        <input type="radio" id="cash" name="pnlType" [(ngModel)]="pnlType" value="cash">
                        <label for="cash">Cash</label>
                    </div>
                </div>

                <div class="form-group" *ngIf="showComparisonDropdown">
                    <label for="comparison">Compared With:</label>
                    <select id="comparison" [(ngModel)]="selectedComparison" (change)="onComparisonChange()">
                        <option value="">-- Select --</option>
                        <option *ngFor="let option of comparisonOptions" [value]="option.value">
                            {{ option.label }}
                        </option>
                    </select>
                </div>
                
                <div class="form-group" *ngIf="showDivideByMonths">
                    <input type="checkbox" id="divideByMonths" [(ngModel)]="divideByMonths" 
                           [disabled]="selectedComparison !== ''">
                    <label for="divideByMonths">Divide Report into Months</label>
                </div>
                
                
                
                <br>
                <br>
                 <button class="generate-report-btn" (click) ="previewPnlReport(fromDate, toDate,selectedComparison, divideByMonths);">
                    Generate Report
                </button>

            </div>
        </div>


           <!-- Report Preview -->
           <div class="modal fade" id="simpleModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 740px;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="simpleModalLabel">Profit and Loss Statement</h5>
                        <button type="button" class="btn-close custom-close-btn" aria-label="Close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Loading Spinner -->
                        <div *ngIf="isLoading" class="spinner-container">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>

                        <!-- IFrame for Report Preview -->
                        <div style="margin-top: 20px;" [ngClass]="{'d-none': isLoading}">
                            <iframe #pnlreportPreviewFrame id="pnlreportPreviewFrame" width="700px" height="700px"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


