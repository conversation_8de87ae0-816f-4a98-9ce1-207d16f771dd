import { TestBed } from '@angular/core/testing';
import { CanActivateFn } from '@angular/router';

import { AuthGuard } from './auth.guard';

describe('AuthGuard', () => {
  let authGuard: AuthGuard;
  let authServiceSpy: jasmine.SpyObj<any>;
  let routerSpy: jasmine.SpyObj<any>;
  let subscriptionServiceSpy: jasmine.SpyObj<any>;

  beforeEach(() => {
    authServiceSpy = jasmine.createSpyObj('AuthService', ['isLoggedIn']);
    routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    subscriptionServiceSpy = jasmine.createSpyObj('SubscriptionService', ['someMethod']);
    TestBed.configureTestingModule({
      providers: [
        { provide: 'AuthService', useValue: authServiceSpy },
        { provide: 'Router', useValue: routerSpy },
        { provide: 'SubscriptionService', useValue: subscriptionServiceSpy },
      ]
    });
    authGuard = new AuthGuard(authServiceSpy, routerSpy, subscriptionServiceSpy);
  });

  it('should be created', () => {
    expect(authGuard).toBeTruthy();
  });
});
