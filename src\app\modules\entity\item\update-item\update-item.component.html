<app-header></app-header>
<app-navbar></app-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <div class="popup">
      <div class="popup-header">
          <h3>Update Sales Item</h3>
      </div>
      <form #f="ngForm" (ngSubmit)="f.form.valid && onSubmit(f)" class="row g-1" novalidate="feedback-form" (keydown)="preventSubmit($event)">
        <div>
          <div class="form-group">
            <label for="description">Item Name</label>
            <input 
              type="text" 
              id="itemName" 
              name="itemName" 
              [(ngModel)]="item.itemName"
              required 
            />
            <div *ngIf="f.submitted && f.controls['itemName'].invalid" class="text-danger">
              <div *ngIf="f.controls['itemName'].errors?.['required']">Item Name is required.</div>
            </div>
          </div>
          <div class="form-group">
            <label for="description">Description</label>
            <input 
              type="text" 
              id="description" 
              name="description" 
              [(ngModel)]="item.description"
              required 
            />
            <div *ngIf="f.submitted && f.controls['description'].invalid" class="text-danger">
              <div *ngIf="f.controls['description'].errors?.['required']">Description is required.</div>
            </div>
          </div>
          <div class="form-group">
            <label for="itemCode">Item Code</label>
            <input 
              type="text" 
              id="itemCode" 
              name="itemCode" 
              [(ngModel)]="item.itemCode" 
              required
              readonly
            />
            <div *ngIf="f.submitted && f.controls['itemCode'].invalid" class="text-danger">
              <div *ngIf="f.controls['itemCode'].errors?.['required']">Item Code is required.</div>
            </div>
          </div>              
            <div class="form-group">
                <label for="unitPrice">Unit Price (Excluding tax)</label>
                <input 
                  type="number" 
                  id="unitPrice" 
                  name="unitPrice" 
                  [(ngModel)]="item.unitPrice" 
                  required 
                  min="0" 
                  (input)="validateUnitPrice()" 
                  #unitPriceField="ngModel" 
                />
                <div *ngIf="f.submitted && unitPriceField.invalid" class="text-danger">
                  <div *ngIf="unitPriceField.errors?.['required']">Unit Price is required.</div>
                  <div *ngIf="unitPriceField.errors?.['min']">Unit price cannot be negative.</div>
                </div>
              </div>
            <div class="form-group" *ngIf="showTaxApplicabilityDropdown">
                <label for="taxApplicability">Tax Applicability</label>
                <select class="form-select" id="taxApplicability" [(ngModel)]="item.taxApplicability" name="taxApplicability" required>
                    <option value="" disabled selected>Select</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                </select>
                <div *ngIf="f.submitted && f.controls['taxApplicability'].invalid" class="text-danger">
                    <div *ngIf="f.controls['taxApplicability'].errors?.['required']">Tax Applicable is required.</div>
                </div>
            </div>
        </div>
          <div class="popup-footer">
              <button type="submit" class="add-btn" >Update</button>
          </div>
      </form>
  </div>
</div>

