.container {
  width: 90%;
  margin: 2% auto;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.subtitle {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 40px;
  /* text-align: center; */
}

.row1_col1 {
  width: 30%;
}

.row1_col1 input {
  width: 100%;
  height: 49px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
}

.input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.search-input {
  width: 100%;
  padding-right: 30px;
  padding-left: 10px;
  box-sizing: border-box;
}

.input-container i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  pointer-events: none;
}

.search-result-card {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-left {
  width: 23%;
  text-align: center;
  padding-right: 16px;
  border-right: 1px solid #eee;
}

.advisor-photo {
  width: 100%;
  height: auto;
  border-radius: 8px;
  object-fit: cover;
}

.photo-caption {
  margin-top: 8px;
  font-size: 0.9rem;
  color: #555;
}

.card-right {
  width: 75%;
  padding-left: 16px;
}

.advisor-name {
  margin: 0;
  font-size: 1.2rem;
  font-weight: bold;
}

.advisor-role {
  color: #007bff;
  font-weight: 500;
  margin: 4px 0;
}

.advisor-description {
  margin-top: 8px;
  color: #333;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
    text-align: justify;
  }

  .row1_col1 {
    width: 100%;
  }
}
