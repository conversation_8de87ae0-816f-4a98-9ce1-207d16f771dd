import { Component, OnInit, ViewChild } from '@angular/core';
import { OtherExpensesHead } from '../expence-claims';
import { ActivatedRoute, Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { fromEvent, Subscription } from 'rxjs';
import { ExpenceClaimsService } from '../expence-claims.service';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';
import { Entity, EntityTradingName } from 'src/app/modules/entity/entity';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import Swal from 'sweetalert2';
import { PeriodClosingService } from '../../period-closing/period-closing.service';
import { DateAdapter } from '@angular/material/core';

@Component({
  selector: 'app-expence-claims-list',
  templateUrl: './expence-claims-list.component.html',
  styleUrls: ['./expence-claims-list.component.css']
})
export class ExpenceClaimsListComponent implements OnInit {

  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  activeTab = 'all';
  currentPage = 1;
  pageSize = 10;
  expenseHeadList: OtherExpensesHead[] = [];
  selectedExpenses: Set<OtherExpensesHead> = new Set<OtherExpensesHead>();
  isAllSelected: boolean = false;
  searchTerm: string = ''; 
  filteredExpenses: OtherExpensesHead[] = []; 
  startDate: string | null = null; 
  endDate: string | null = null;  
  entity: Entity = new Entity();
  expenses: any[] = [];
  templateHtmlContent: string = '';
  recipientEmail: string = ''; 
  TempRecipientEmail: string =''; 
  expenseHead: OtherExpensesHead = new OtherExpensesHead();

  constructor(
    private expenseService: ExpenceClaimsService, 
    private entityService: EntityService, 
    private periodClosingService: PeriodClosingService,  
    private route: ActivatedRoute,  
    private router: Router, 
    public sanitizer: DomSanitizer,
    private dateAdapter: DateAdapter<Date>,
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }

  //Delete Button Function
    deleteExpences(otherExpensesId: number): void {

    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you really want to delete this Expense?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (result.isConfirmed) {
        
         
        this.expenseService.deleteExpences(otherExpensesId)
          .subscribe(
            () => {

              Swal.fire({
                title: 'Deleted!',
                text: 'Expense has been deleted.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
              }).then(() => {
                const entityId = +((localStorage.getItem('entityId')) + "");
                // Optionally update the UI or reload data
                this.expenseService.getAllPaymentExpensesHeadList(entityId);
              });
            },
            (error) => {
              console.error('Failed to delete Expense:', error);

              Swal.fire({
                title: 'Error!',
                text: 'Failed to delete Expense.',
                icon: 'error',
                confirmButtonText: 'OK',
                cancelButtonText: 'Ask Chimp',
                confirmButtonColor: '#be0032',
                cancelButtonColor: '#007bff',
                showCancelButton: true,
              }).then((result) => {
                if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                  if (this.chatBotComponent) {
                    Swal.fire({
                      title: 'Processing...',
                      text: 'Please wait while Chimp processes your request.',
                      allowOutsideClick: false,
                      didOpen: () => {
                        Swal.showLoading();
                        this.chatBotComponent.setInputData('Failed to delete Expense.');
                        this.chatBotComponent.responseReceived.subscribe(response => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound()
                        });
                      },
                    });
                  } else {
                    console.error('ChatBotComponent is not available.');
                  }
                }
              });
            }
          );
      }
    });
  }

  //End delete button

  ngOnInit(): void {
    this.getexpenseHeadListByEntity();
  }

  isDropdownOpen = false;

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }  

  addExpences() {
    this.router.navigate(['/ExpenceClaims']);
  }


  getexpenseHeadListByEntity() {
  const entityId = +((localStorage.getItem('entityId')) + '');
  this.expenseService.getAllOtherExpensesHeadList(entityId).subscribe(
    (data: OtherExpensesHead[]) => {
      this.expenseHeadList = data;
      
      // Initialize filtered list before applying filters
      this.filteredExpenses = [...this.expenseHeadList];

      // Check if each expense is locked based on spentOn date
      this.filteredExpenses.forEach((expense) => {
        const date = expense.spentOn;
        this.periodClosingService.isDateLocked(entityId, date).subscribe({
          next: (locked: boolean) => {
            expense.isLocked = locked;
          },
          error: (err) => {
            console.error('Error checking period lock for expense:', err);
            expense.isLocked = false; // default to editable on error
          }
        });
      });

    },
    (error) => {
      console.error('Error fetching expenses:', error);
    }
  );
}



  // Function to filter expenses based on the search term and active tab
  filterExpenses(): void {
    if (!this.searchTerm && !this.startDate && !this.endDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Search Criteria',
        text: 'Please provide at least one search criterion: Expense Number, Start Date, or End Date.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    let filtered = this.expenseHeadList;

    // Filter by active tab
    if (this.activeTab !== 'all') {
      filtered = filtered.filter(expense => expense.status.toLowerCase() === this.activeTab);
    }

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(expense =>
        expense.expensesNumber.toString().toLowerCase().includes(searchTermLower)
      );
    }

    // Filter by date range
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(expense => new Date(expense.spentOn) >= startDate);
    }

    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(expense => new Date(expense.spentOn) <= endDate);
    }

    this.filteredExpenses = filtered;
  }

  // Reset filters
  resetFilters() {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.activeTab = 'all'; // Reset the active tab to 'all'
    this.filteredExpenses = this.expenseHeadList;
  }

  // Function to handle changes in the search input
  onSearchChange() {
    this.filterExpenses(); // Call filter function on input change
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    if (this.searchTerm || this.startDate || this.endDate) {
      this.filterExpenses(); // Filter expenses only if search criteria are provided
    } else {
      this.filteredExpenses = this.activeTab === 'all' 
        ? this.expenseHeadList 
        : this.expenseHeadList.filter(expense => expense.status.toLowerCase() === this.activeTab);
    }
  }


  toggleSelection(expense: OtherExpensesHead, event: any): void {
    if (event.target.checked) {
      this.selectedExpenses.add(expense);
    } else {
      this.selectedExpenses.delete(expense);
    }
  }

  selectAll(event: any): void {
    this.isAllSelected = event.target.checked;

    if (this.isAllSelected) {
      this.expenseHeadList.forEach(expense => {
        this.selectedExpenses.add(expense);
      });
    } else {
      this.selectedExpenses.clear();
    }
  }

  clearSelectedCheckboxes() {
    // Deselect all checkboxes
    this.selectedExpenses.clear();
  }


  onPaymentreceiptMultiple(): void {
    if (this.selectedExpenses.size > 0) {
      const expensesToCredit = Array.from(this.selectedExpenses);

      // Filter valid expenses: balance > 0 and status is 'Pending', 'Sent', or 'Overdue'
      const validexpenses = expensesToCredit.filter(expense => {
        return expense.balanceAmount > 0 &&
          ['Pending', 'Sent', 'Overdue'].includes(expense.status);
      });

      // Filter invalid expenses: balance <= 0 or status is not 'Pending', 'Sent', or 'Overdue'
      const invalidexpenses = expensesToCredit.filter(expense => {
        return expense.balanceAmount <= 0 ||
          !['Pending', 'Sent', 'Overdue'].includes(expense.status);
      });

      if (validexpenses.length > 0) {
        // Extract only expenseHeadIds from valid expenses
        const expenseIds = validexpenses.map(expense => expense.otherExpensesId);

        // Pass the valid expense ids in query params
        this.router.navigate(['/payment-expenses'], {
          queryParams: { expenseIds: JSON.stringify(expenseIds) }
        });

        // If there are invalid expenses, show a warning message
        if (invalidexpenses.length > 0) {
          Swal.fire({
            title: 'Some expenses Were Skipped',
            text: `The following expenses were excluded because they either have a zero balance or are not marked with a status of 'Pending,' 'Sent,' or 'Overdue.': ${invalidexpenses.map(expense => expense.expensesNumber).join(', ')}`,
            icon: 'warning',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#ff7e5f',
            cancelButtonColor: '#be0032',
            showCancelButton: true,
          }).then((result) => {
            if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData('Some expenses Were Skipped');
                    this.chatBotComponent.responseReceived.subscribe(response => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound()
                    });
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
        }

      } else {
        // If no valid expenses are found, show a message
        Swal.fire({
          title: 'No Valid expenses',
          text: 'Only expenses with a status of Pending, Sent or Overdue and a balance greater than 0 are eligible for Paid.',
          icon: 'warning',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#ff7e5f',
          cancelButtonColor: '#be0032',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('No Valid expenses');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    } else {
      // No expenses selected, show a message
      Swal.fire({
        title: 'No expenses Selected',
        text: 'Please select an expense to create a Payment Receipt.',
        icon: 'warning',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
        showCancelButton: true,
      }).then((result) => {
        if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData('No expenses Selected');
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound()
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }

      });
      this.router.navigate(['/payment-expenses']);
    }

  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

  cancelSelectedExpenses(): void {
    if (this.selectedExpenses.size > 0) {
      const expensesToCancel = Array.from(this.selectedExpenses);


        const lockedBills = expensesToCancel.filter(expense => expense.isLocked);
      if (lockedBills.length > 0) {
        Swal.fire({
          title: 'Locked Period Detected',
          text: `Selected Expense(s) that belong to a closed accounting period and cannot be cancelled: ${lockedBills.map(b => b.expensesNumber).join(', ')}`,
          icon: 'warning',
          confirmButtonText: 'OK',
          confirmButtonColor: '#ff7e5f'
        });
        return;
      }
      
  
      // Filter valid expenses: Status is 'Pending'
      const validExpenses = expensesToCancel.filter(expense => expense.status === 'Pending');
  
      if (validExpenses.length > 0) {
        const expenseIds = validExpenses.map(expense => expense.otherExpensesId);
  
        Swal.fire({
          title: 'Confirm Cancellation',
          text: `Are you sure you want to cancel the selected expense(s)?`,
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: 'Yes, Cancel',
          cancelButtonText: 'No',
          confirmButtonColor: '#ff7e5f',
          cancelButtonColor: '#be0032',
        }).then(result => {
          if (result.isConfirmed) {
            this.expenseService.cancelExpenses(expenseIds).subscribe(
              () => {
                Swal.fire({
                  title: 'Success',
                  text: 'Selected expense(s) have been canceled successfully.',
                  icon: 'success',
                  confirmButtonText: 'OK',
                });
                this.getexpenseHeadListByEntity(); // Refresh the list
                this.selectedExpenses.clear(); // Clear selected expenses
              },
              error => {
                console.error('Error canceling expenses:', error);
                Swal.fire({
                  title: 'Error',
                  text: 'Failed to cancel the selected expense(s). Please try again.',
                  icon: 'error',
                  confirmButtonText: 'OK',
                });
              }
            );
          }
        });
      } else {
        Swal.fire({
          title: 'No Valid Expenses',
          text: 'Only expenses with a status of "Pending" can be canceled.',
          icon: 'warning',
          confirmButtonText: 'OK',
        });
      }
    } else {
      Swal.fire({
        title: 'No Expenses Selected',
        text: 'Please select at least one expense to cancel.',
        icon: 'warning',
        confirmButtonText: 'OK',
      });
    }
  }
  
  navigateToEditExpense(id: number) {
    const expense = this.filteredExpenses.find(expense => expense.otherExpensesId === id);
  
    if (expense && expense.status === 'Sent') {
      Swal.fire({
        title: 'Error!',
        text: 'Sent Expenses cannot be edited!',
        icon: 'error',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
      }).then((result) => {
        if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData('Sent Expenses cannot be edited!');
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });
    } else {
      this.router.navigate(['/edit-expense', id]);
    }
  }
  


}
