// import { Component, OnInit } from '@angular/core';
// import { ActivatedRoute, Router } from '@angular/router';
// import Swal from 'sweetalert2';
// import { PayRunDetailService } from '../../../services/pay-run-detail.service';

// @Component({
//   selector: 'app-email-payslip',
//   templateUrl: './email-payslip.component.html',
//   styleUrls: ['./email-payslip.component.css']
// })
// export class EmailPayslipComponent implements OnInit {
//   employees: any[] = [];  // Store received employees
//   selectedEmployees: any[] = [];  // Store selected employees
//   allSelected: boolean = true; // Track if all checkboxes are selected

//   constructor(
//     private route: ActivatedRoute,
//     private router: Router,
//     private payRunDetailService: PayRunDetailService
//   ) {}

//   ngOnInit(): void {
//     // Get employees from navigation state
//     if (history.state.employees) {
//       this.employees = history.state.employees;
//       // Auto-select all employees
//       this.selectedEmployees = [...this.employees];
//     }
//   }

//   // Toggle selection of an individual employee
//   toggleSelection(employee: any): void {
//     if (this.selectedEmployees.includes(employee)) {
//       this.selectedEmployees = this.selectedEmployees.filter(emp => emp !== employee);
//     } else {
//       this.selectedEmployees.push(employee);
//     }
//     // Update "Select All" checkbox state
//     this.allSelected = this.selectedEmployees.length === this.employees.length;
//   }

//   // Select/Deselect all employees
//   toggleSelectAll(): void {
//     if (this.allSelected) {
//       this.selectedEmployees = [];
//     } else {
//       this.selectedEmployees = [...this.employees];
//     }
//     this.allSelected = !this.allSelected;
//   }

//   sendEmailToEmployee(employee: any): void {
//     this.payRunDetailService.sendEmailToEmployee(employee).subscribe(
//       (_response: any) => {
//     Swal.fire({
//       title: `Send Payslip to ${employee.firstName}?`,
//       text: `An email will be sent to ${employee.email}.`,
//       icon: 'question',
//       showCancelButton: true,
//       confirmButtonText: 'Yes, Send!',
//       confirmButtonColor: '#007bff'
//     }).then((result) => {
//       if (result.isConfirmed) {
//         console.log(`Sending email to: ${employee.email}`);
//         Swal.fire('Sent!', `Payslip email sent to ${employee.email}.`, 'success');
//       }
//     });
//   }


//   // Send email to all selected employees
//   sendEmailToSelected(): void {
//     if (this.selectedEmployees.length === 0) {
//       Swal.fire('No Employees Selected', 'Please select at least one employee.', 'warning');
//       return;
//     }

//     Swal.fire({
//       title: 'Send Payslips to Selected Employees?',
//       text: `Emails will be sent to ${this.selectedEmployees.length} employees.`,
//       icon: 'question',
//       showCancelButton: true,
//       confirmButtonText: 'Yes, Send!',
//       confirmButtonColor: '#28a745'
//     }).then((result) => {
//       if (result.isConfirmed) {
//         console.log('Sending emails to selected employees:', this.selectedEmployees);
//         // Call the backend API here to send emails to multiple employees
//         Swal.fire('Sent!', `Payslips sent to selected employees.`, 'success');
//       }
//     });
//   }
// }
 