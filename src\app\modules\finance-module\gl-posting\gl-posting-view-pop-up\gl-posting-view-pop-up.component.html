<div class="popup-content">
  <h2>Document Details</h2>
  <p><strong>Transaction ID:</strong> {{ transactionId }}</p>
  <p><strong>Document Type:</strong> {{ documentType }}</p>

  <!-- Show details only if the document type is "Journal Voucher" -->
  <div>
      <p><span>Displaying Details:</span></p>
      <ul *ngIf="filteredJournalVoucherDetails">
          <li>
              Transaction ID: {{ filteredJournalVoucherDetails.glTransactionId?.glTransactionId }} <br>
              Document Type: {{ filteredJournalVoucherDetails.glTransactionId?.documentType }}<br>
              Document Number: {{ filteredJournalVoucherDetails.glTransactionId?.documentNumber }} <br>
              Date: {{ filteredJournalVoucherDetails.glTransactionId?.date }}<br>
              Description: {{ filteredJournalVoucherDetails.glTransactionId?.description }} <br>
              JV Number: {{ filteredJournalVoucherDetails.glTransactionId?.jvNumber }}<br>
              Status: {{ filteredJournalVoucherDetails.glTransactionId?.status }} <br>
              Dr: {{ filteredJournalVoucherDetails.glTransactionId?.totalDr }}<br>
              Cr: {{ filteredJournalVoucherDetails.glTransactionId?.totalCr }}<br>
          </li>
          <li>
              Ledger Account Id: {{ filteredJournalVoucherDetails.coaLedgerAccount?.coaLedgerAccountId }}<br>
              Ledger Account Code: {{ filteredJournalVoucherDetails.coaLedgerAccount?.ledgerAccountCode }}<br>
              Ledger Account Description: {{ filteredJournalVoucherDetails.coaLedgerAccount?.ledgerAccountDescription }}<br>
              Ledger Account Name: {{ filteredJournalVoucherDetails.coaLedgerAccount?.ledgerAccountName }}<br>
              Status: {{ filteredJournalVoucherDetails.coaLedgerAccount?.status }}<br>
              Tax Account: {{ filteredJournalVoucherDetails.coaLedgerAccount?.taxAccount }}<br>
              Account Type: {{ filteredJournalVoucherDetails.coaLedgerAccount?.coaHeaderId?.accountHeaderType }}<br>
          </li>
      </ul>
      <p *ngIf="!filteredJournalVoucherDetails">No Journal Voucher Details Available.</p>
  </div>