import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpService } from 'src/app/http.service';
import { environment } from 'src/environments/environment';
import { Earning, PayItemType, PayPeriod, PayProcessDTO, PayRunMaster } from '../payroll-setting';

@Injectable({
  providedIn: 'root',
})
export class PayProcessService {
  private readonly baseURL = environment.payrollApiUrl;

  constructor(private http: HttpClient, private httpService: HttpService) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  getPayprocessDetails(
    entityId: number,
    calendarId: number,
    peroidId: number
  ): Observable<PayProcessDTO[]> {
    console.log('Sending Request:', {
      method: 'GET',
      endpoint: '/payProcess/getPayProcessDataByCalendar',
      params: { entityId, calendarId, peroidId },
    });
    return this.request(
      'GET',
      `/payProcess/getPayProcessDataByCalendar`,
      null,
      {
        entityId: entityId.toString(),
        calendarId: calendarId.toString(),
        peroidId: peroidId.toString(),
      }
    );
  }
  
}
