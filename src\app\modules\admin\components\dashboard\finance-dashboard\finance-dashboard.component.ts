import { Component } from '@angular/core';
import { catchError, of } from 'rxjs';
import { BankAccount } from 'src/app/modules/finance-module/bank/bank';
import { BankService } from 'src/app/modules/finance-module/bank/bank.service';
import { BillService } from 'src/app/modules/finance-module/bill/bill.service';
import { ExpenceClaimsService } from 'src/app/modules/finance-module/expence-claims/expence-claims.service';
import { JournalVoucherService } from 'src/app/modules/finance-module/journal-voucher/journal-voucher.service';
import { InvoiceService } from 'src/app/modules/invoice/invoice.service';
import { QuotationService } from 'src/app/modules/quotation/quotation.service';
import Swal from 'sweetalert2';

interface Summary {
  count: number;
  amount: number;
}


interface InvoiceSummary {
  awaiting: Summary;
  overdue: Summary;
  paid: Summary;
  pending: Summary;
  revised: Summary;
  canceled: Summary;
  sent: Summary;
 
}

interface QuoteSummary {
  pending: Summary;
  sent: Summary;
  revised: Summary;
  accepted: Summary;
  expired: Summary;
  declined: Summary;
  toInvoice: Summary;
}


interface BillSummary {
  pending: Summary;
  overdue: Summary;
  paid: Summary;
  canceled: Summary;
  
}

interface ExpenceSummary {
  pending: Summary;
  overdue: Summary;
  canceled: Summary;
}

@Component({
  selector: 'app-finance-dashboard',
  templateUrl: './finance-dashboard.component.html',
  styleUrls: ['./finance-dashboard.component.css'],
})
export class FinanceDashboardComponent {


filteredBankAccounts: BankAccount[] = [];
bankAccounts: BankAccount[] = [];

    invoiceData: InvoiceSummary = {
    awaiting: { count: 0, amount: 0 },
    overdue: { count: 0, amount: 0 },
    paid: { count: 0, amount: 0 },
    pending: { count: 0, amount: 0 },
    revised: { count: 0, amount: 0 },
    canceled: { count: 0, amount: 0 },
    sent: { count: 0, amount: 0 },
    
  };

  quoteData: QuoteSummary = {
    pending: { count: 0, amount: 0 },
    sent: { count: 0, amount: 0 },
    revised: { count: 0, amount: 0 },
    accepted: { count: 0, amount: 0 },
    expired: { count: 0, amount: 0 },
    declined: { count: 0, amount: 0 },
    toInvoice: { count: 0, amount: 0 },
  };

  billData: BillSummary = {
    pending: { count: 0, amount: 0 },
    overdue: { count: 0, amount: 0 },
    paid: { count: 0, amount: 0 },
    canceled: { count: 0, amount: 0 },
  };

  ExpenceData: ExpenceSummary = {
    pending: { count: 0, amount: 0 },
    overdue: { count: 0, amount: 0 },
    canceled: { count: 0, amount: 0 },
  };

  gstCollected = 0;
  gstPaid = 0;
  gstPayable = 0;
 fromDate: string = '2025-01-01'; 
  toDate: string = ''; 


  constructor(
    private billService: BillService,
    private expenceClaimsService: ExpenceClaimsService,
    private bankService:BankService,
    private invoiceService: InvoiceService,
    private quotationService: QuotationService,
    private journalVoucherService: JournalVoucherService
  ) {}


  ngOnInit(): void {
    const entityId = Number(localStorage.getItem('entityId') || '0');
    this.fetchBillSummary(entityId);
    this.fetchexpenceSummary(entityId);
    this.fetchInvoiceSummary(entityId);
    this.fetchQuoteSummary(entityId);
    this.fetchBankAccounts();

  // const fromDate = '2025-01-01';
  const today = new Date();
  this.toDate = today.toISOString().split('T')[0];

  this.journalVoucherService.getGstSummary(entityId, this.fromDate, this.toDate).subscribe(data => {
    this.gstCollected = data.gstCollected;
    this.gstPaid = data.gstPaid;
    this.gstPayable = data.gstPayable;
  });
  }


  private fetchBillSummary(entityId: number): void {
    this.billService
      .getBillSummary(entityId)
      .pipe(
        catchError((error) => {
         // this.showError('Failed to load bill summary');
          console.error('Bill Summary Error:', error);
          return of(null);
        })
      )
      .subscribe((data) => {
        if (data) {
          this.billData = data;
        }
      });
  }

  private fetchexpenceSummary(entityId: number): void {
    this.expenceClaimsService
      .getExpenceSummary(entityId)
      .pipe(
        catchError((error) => {
         // this.showError('Failed to load expence summary');
          console.error('Expence Summary Error:', error);
          return of(null);
        })
      )
      .subscribe((data) => {
        if (data) {
          this.ExpenceData = data;
        }
      });
  }



  private fetchInvoiceSummary(entityId: number): void {
    this.invoiceService.getInvoiceSummary(entityId)
      .pipe(
        catchError(error => {
         // this.showError('Failed to load invoice summary');
          console.error('Invoice Summary Error:', error);
          return of(null);
        })
      )
      .subscribe(data => {
        if (data) {
          this.invoiceData = data;
        }
      });
  }

  private fetchQuoteSummary(entityId: number): void {
    this.quotationService.getQuoteSummary(entityId)
      .pipe(
        catchError(error => {
         // this.showError('Failed to load quote summary');
          console.error('Quote Summary Error:', error);
          return of(null);
        })
      )
      .subscribe(data => {
        if (data) {
          this.quoteData = data;
        }
      });
  }


fetchBankAccounts() {
    const entityId = parseInt(localStorage.getItem('entityId') || "0", 10);

    if (!entityId || isNaN(entityId)) {
      console.error("Invalid entityId. Cannot fetch BankAccounts.");
      return;
  }
  
    this.bankService.getBankAccountsByEntityId(entityId).subscribe(
      (data: BankAccount[]) => {
        this.bankAccounts = data;
        this.filteredBankAccounts = this.bankAccounts;
      },
      (error) => {
       // this.showError('Failed to load bankAccount summary');
        console.error('Error fetching bankAccount:', error);
        return of(null);
      }
    );
  }

  
  private showError(message: string): void {
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: message,
    });
  }
}
