/* General Styles */

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

h2 {
    font-size: 24px;
    font-weight: bold;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    /* Stack children vertically */
    gap: 20px;
    /* Adds 20px gap between rows */
}

.actions {
  display: flex;
  align-items: center;
}

/* Payroll Settings Heading */
.actions h2 {
  flex: 1;
  font-family: Inter, sans-serif;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262FF;
  margin: 0;
}

/* Sub-container Styles for Heading */
.sub-container {
  background-color: #ffffff;
  padding: 20px;
  border: 1px solid #4262FF;
  border-radius: 10px;
  margin-bottom: 20px;
}

.sub-container h2 {
  font-size: 24px;
  font-weight: bold;
}

/* Payroll Head Section */
.payroll-head {
    display: flex;
    align-items: center;
    gap: 20px;
}

.payroll-head-icon {
    width: 10%;
    display: flex;
    justify-content: center;
    font-size: 60px;
    color: #4262FF;
}

.payroll-head-icon .circle {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: #4262FF;
    color: white;
    font-weight: bold;
    font-size: 45px;
    text-transform: uppercase;
    line-height: 1;
}


.payroll-head-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-weight: bold;
}

.payroll-head-content strong {
    font-size: 16px;
    color: #333;
}

.payroll-head-content span {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.payroll-head-content a {
    color: #4262FF;
    text-decoration: underline;
}

.payroll-info {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    background-color: transparent;
    border-radius: 10px;
}

.nav-tabs {
    display: flex;
    flex-direction: row;
    justify-content: center;

}

.nav-tabs .nav-link {
    padding: 10px 39.5px;
    color: #4262FF;
    font-weight: 500;
    background-color: white;
    cursor: pointer;
    border-radius: 0 0 0 0;
}

.nav-tabs .nav-link.active {
    color: black;
    background-color: #f1eeee;
    border-radius: 0 0 0 0;
    border-bottom: none;
}

/* Pay Calendars Section */


.pay-calendar-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: white;
    margin-bottom: 5px;
    border-radius: 10px;
}

.pay-calendar-head strong {
    font-size: 18px;
    color: #333;
}

.btn-group .btn-primary {
    background: white;
    border: 1px solid #4262FF;
    color: #4262FF;
    font-weight: bold;
    border-radius: 10px;
    padding: 10px 20px;
}


.btn-group .dropdown-menu {
    width: 100%;
    padding: 10px;
    border-radius: 10px;
    font-weight: bold;
    overflow: hidden;
}

.dropdown-item {
    font-weight: bold;
    color: #333;
    padding: 8px 15px;
}

.dropdown-item:hover {
    background-color: #4262FF;
    color: white;
    font-weight: bold;
}

.pay-calendar-table {
    background-color: white;
    border-radius: 10px;
    padding: 20px 20px;
}

/* Table Styles */
.pay-calendar-table table {

    width: 100%;
    border-collapse: collapse;
    border-radius: 10px;
}

.pay-calendar-table th,
.pay-calendar-table td {
    text-align: left;
    padding: 20px 20px;
}

.pay-calendar-table th {
    background-color: white;
    font-weight: bold;
    color: #333;
}

.pay-calendar-table tbody tr {
    background-color: white;
    border-bottom: 1px solid #ddd;
}

.pay-calendar-table tbody tr:hover {
    background-color: #f1f1f1;
}

/* History Section */
.payroll-history {
    margin-top: 20px;
    background-color: white;
    border: 1px solid #4262FF;
    border-radius: 10px;
    padding: 20px 20px;
}

.payroll-history h3 {
    display: flex;
    align-items: center;
}

.payroll-history .btn-toggle {
    background: none;
    border: none;
    font-size: 18px;
    color: black;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.payroll-history .btn-toggle i {
    margin-left: 10px;
    font-weight: bold;
    transition: transform 0.3s;
}

.collapse:not(.show)+h3 .btn-toggle i {
    transform: rotate(0deg);
}

.collapse.show+h3 .btn-toggle i {
    transform: rotate(180deg);
}

.collapse {
    background-color: white;
    border: 1px solid white;
    border-radius: 5px;
}

.collapse p {
    font-size: 16px;
    color: #000000;
}

/* Pay-items */
.pay-items-section {
    display: flex;
    background-color: white;
    border-radius: 10px;
}

.pay-item-2 {
    display: flex;
    flex-direction: column;
    background-color: transparent;
    border-radius: 10px;
}

.side-bar {
    width: 20%;
    /* Set sidebar width */
    background-color: #ffffff;
    border-radius: 10px 0 0 10px;
    border-right: 1px solid #ddd;
    padding: 10 0px;
    /* Add some padding */
}

.nav_pay_item {
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;
}

.nav_pay_item li {
    margin-bottom: 10px;
}

.nav_pay_item a {
    text-decoration: none;
    width: 100%;
    color: #333;
    font-weight: 500;
    padding: 10px;
    display: block;
    border-radius: 10px;
}

.nav_pay_item a.active {
    color: black;
    font-weight: bold;
    border-left: 3px solid #4262FF;
    background-color: #dfe4ff;
}

.nav_pay_item a:hover {
    background-color: #d7dbf5;
}


.pay_item-content {
    border-radius: 0 10px 10px 0;
    width: 80%;
}

.Earnings,
.Deductions,
.Reimbursements,
.Leave {
    width: 100%;
    /* Adjust width to match the sidebar */
    padding: 20px;
    background-color: white;
}

.Leave-form-3,
.Leave-form-final {
    width: 100%;
    /* Adjust width to match the sidebar */
    padding: 20px;
    background-color: transparent;
}

.pay_item_head {
    display: flex;
    padding: 10px 0;
    border-radius: 0 10px 10px 0;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.pay_item_head h2 {
    font-size: 24px;
    font-weight: bold;
}

.table-responsive {
    margin-top: 10px;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table thead th {
    background-color: #ffffff;
    font-weight: bold;
    color: #333;
    padding: 10px;
    text-align: left;
}

.table tbody tr {
    background-color: white;
    border-bottom: 1px solid #ddd;
}

.table tbody tr:hover {
    background-color: #f1f1f1;
}

.table tbody td {
    padding: 10px;
    color: #555;
}

.btn-group .dropdown-menu {
    padding: 0 40px;
    border-radius: 10px;
    overflow: hidden;
    font-weight: bold;
}

.dropdown-item:hover {
    background-color: #4262FF;
    color: white;
}

.btn-secondary {
    background: none;
    border: none;
    color: #333;
    font-size: 18px;
    cursor: pointer;
}

.btn-secondary:hover {
    color: #4262FF;
}

.pay-item-one {
    display: flex;
    flex-direction: column;
    margin-bottom: 5px;
}

.pay-item-two {
    width: 100%;
    display: flex;
    margin-bottom: 20px;
}

.pay-item-col1 {
    display: flex;
    flex-direction: column;
    width: 30%;
    border-right: 1px solid #666666;
}

.pay-item-col2 {
    display: flex;
    flex-direction: column;
    padding-left: 10%;
    width: 70%;
}

.pay-item-col3 {
    display: flex;
    margin-right: 25px;
    width: 70%;
}

.custom-search {
    background-color: #c2c2c2;
    border-radius: 10px;
}

.filter-buttons {
    width: 30%;
    display: flex;
    align-items: center;
    gap: 25px;
}


.pay-item-four {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    border: 2px solid #c7c7c7;
    border-radius: 10px;
    margin-top: 20px;
    margin-bottom: 20px;
    background-color: white;
}

.pro-pic {
    width: 50px;
    height: 50px;
    background-color: #e0e0e0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    background-color: #4262FF;
}

.User-Details {
    padding-top: 15px;
    flex-grow: 1;
    margin-left: 15px;
}

.User-Details h5 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.User-Details small {
    size: 12px;
    color: gray;
}

.approve-btn button {
    color: #4262FF;
    font-weight: bold;
    border: 2px solid #4262FF;
    background: transparent;
    border-radius: 10px;
    padding: 5px 15px;
}

.app-icon {
    margin-left: 10px;
}

.app-icon i {
    font-size: 1.2rem;
    color: #555;
}

.leave-emp {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    margin: 0 auto;
    font-family: Arial, sans-serif;
}

.emp-name,
.salary-earnings,
.date-next-payment {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 5px 15px;
    border: 1px solid #ccc;
}

label {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
}

.employee-name {
    font-size: 18px;
    font-weight: bold;
    color: #4262FF;
    margin: 0;
}

.view-btn {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 16px;
    background-color: #f2f2f2;
    cursor: not-allowed;
}

.next-payment-date {
    font-size: 16px;
    font-weight: bold;
    width: 100%;
}



/* modal  */

.custom-modal {
  max-width: 1000px;
}
.modal-content {
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.modal-header {
  background-color: #007bff;
  color: black;
  font-size: 1.5rem;
  font-weight: bold;
  padding: 15px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.modal-head {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* Space between title and close button */
}

.ot-modal-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    /* Space between title and close button */
}

.modal-header .modal-title {
    font-family: 'Inter', sans-serif;
    font-weight: bold;
    font-size: 20px;
    color: black;
    /* Consistent title color */
}

.ot-modal-header .modal-header .modal-title {
    font-family: 'Inter', sans-serif;
    font-weight: bold;
    font-size: 20px;
    color: black;
    /* Consistent title color */
}

 .btn-close {
  background: none;
  border: none;
  font-size: 1.5rem; 
  color: black; 
  outline: none;
  display: flex;
  align-items: center;
  margin-right: -8px;
}

.btn-close:hover {
  color: #4262FF;
}

.modal-body {
  padding: 20px;
  text-align: center;
}

.modal-body .form-label2 {
  font-size: 20px;
  margin-bottom: 5px;
  display: block; 
  text-align: left; }


.modal-body .form-control,
.modal-body .form-select {
    border-radius: 8px;
    border: 1px solid #ccc;
    padding: 10px;
    font-size: 16px;
}

.modal-footer {
    border-top: none;
    /* Remove default border */
    display: flex;
    justify-content: flex-end;
    gap: 20px;
}

.modal-footer .btn-secondary,
.modal-footer .btn-primary,
.btn-primary {
    font-size: 16px;
    border-radius: 8px;
    font-weight: bold;
}

.modal-footer .btn-secondary {
    padding: 10px 20px;
    background-color: white;
    color: #4262FF;
    border: 1px solid #4262FF;
}

.modal-footer .btn-secondary:hover {
    background-color: #4262FF;
    color: white;
    border: 1px solid #4262FF;
}

.modal-footer .btn-primary,
.btn-primary {
    padding: 10px 40px;
    background: linear-gradient(to right, #4262FF, #512CA2);
    color: #fff;
    border: none;
}

.modal-footer .btn-primary:hover,
.btn-primary:hover {
    background: linear-gradient(to right, #512CA2, #4262FF);
}



.custom-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    /* Adjust size as needed */
    color: #666;
    /* Adjust color to match your theme */
    cursor: pointer;
    outline: none;
    display: flex;
    align-items: center;
    margin-right: -8px;
    /* Adjust to align with the edge if needed */
}

.custom-close-btn:hover {
    color: #4262FF;
    /* Optional hover color */
}

.add_calender {
    width: 30%;
    color: #4262FF;
    background-color: white;
    font-weight: bold;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 20px;
}

/* Employee  */
.earnings-container {
    background-color: #ffffff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 10px;
    width: 100%;
    border: 2px solid #c0c0c0;
}

.opening-balance {
    display: flex;
}

.opening-balance h2 {
    font-size: 18px;
    margin-right: 5px;
    font-weight: bold;
}

.earnings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.earnings-title {
    font-size: 16px;
    font-weight: bold;
    color: #000000;
    margin: 0;
}

.earning-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.earning-description {
    flex: 2;
    font-size: 20px;
    font-weight: bold;
    color: #020514;
}

.earning-amount-container {
    display: flex;
    align-items: center;
    flex: 1;
}

.earning-amount {
    flex: 1;
    padding: 5px;
    border: 2px solid #ccc;
    border-radius: 5px;
    text-align: right;
}

.btn-remove {
    background: none;
    border: none;
    color: #999;
    margin-left: 10px;
    cursor: pointer;
    font-size: 16px;
}

.btn-remove:hover {
    color: #ff4d4f;
}

.btn-add-earning {
    background: white;
    border: 1px solid #4262FF;
    color: #4262FF;
    font-weight: bold;
    border-radius: 10px;
    padding: 10px 20px;
}

.btn-add-earning:hover {
    background-color: #f0f4ff;
}

.earning-total {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    padding: 10px 0;
    margin-top: 10px;
}

.earning-total span {
    font-size: 14px;
    color: #333;
    margin-right: 40px;
}

.earning-total.value {
    font-size: 14px;
    color: #333;
    margin-left: 300px;
}


.emp-open-footer {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
    padding: 10px;
}

.emp-open-footer-wrapper {
    display: flex;
    justify-content: flex-end;
    /* Aligns content to the right */
    padding: 10px;
    /* Optional for spacing */
}

.right-buttons {
    display: flex;
    gap: 10px;
    /* Adds space between buttons */
}

button {
    padding: 10px 20px;
    /* Optional: Customize button size */
    font-size: 14px;
    /* Optional: Adjust font size */
}


.cancel-btn {
    background-color: #ffffff;
    color: #4262FF;
    border: 1px solid #4262FF;
    font-weight: bold;
    padding: 10px 25px;
    border-radius: 10px;
    margin-top: 20px;

}

.cancel-btn:hover {
    background-color: #4262FF;
    color: #ffffff;
}

.save-btn {
    background: linear-gradient(to right, #4262FF, #512CA2);
    color: #fff;
    border: 1px solid #4262FF;
    font-weight: bold;
    padding: 10px 30px;
    border-radius: 10px;
    margin-top: 20px;
}

.save-btn:hover {
    background: linear-gradient(to right, #512CA2, #4262FF);
}

.dropdown-menu {
    width: 100%;
    /* Make dropdown width the same as the input box */
    max-height: 200px;
    /* Limit the maximum height of the dropdown */
    overflow-y: auto;
    /* Make dropdown scrollable if there are more than 5 items */
}

.dropdown-item {
    padding: 8px 10px;
    /* Adjust the padding inside dropdown items */
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    /* Highlight item when hovering */
}

.search-input {
    background: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="gray" class="bi bi-search" viewBox="0 0 16 16"%3E%3Cpath d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001a1.007 1.007 0 0 0 .228.273l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.273-.228zm-5.442.656a5.5 5.5 0 1 1 0-11 5.5 5.5 0 0 1 0 11z"/%3E%3C/svg%3E') no-repeat left 10px center;
    background-size: 16px;
    padding-left: 35px;
    /* Space for the icon */
    height: 40px;
    /* Optional: Adjust input height */
    font-size: 14px;
    /* Optional: Adjust font size */
}

/* Cancel Button Styles */
.cancel-icon-button {
    all: unset;
    /* Reset all default styles */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    /* Icon size */
    cursor: pointer;
    padding: 5px;
    /* Spacing around the icon */
    border-radius: 50%;
    /* Circular button */
    transition: transform 0.2s, background-color 0.2s;
    /* Hover effect */
    color: red;
}

.cancel-icon-button i {
    color: red;
    /* Red icon color for cancel */
    background-color: transparent;
    /* Ensure no background color */
}

.cancel-icon-button:hover {
    background-color: rgba(255, 0, 0, 0.1);
    /* Light red hover */
    transform: scale(1.1);
    /* Slight zoom on hover */
}

/* Add Button Styles */
.add-icon-button {
    all: unset;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: transform 0.2s, background-color 0.2s;
    color: green
}

.add-icon-button i {
    color: green;
    /* Green icon color for add */
    background-color: transparent;
}

.add-icon-button:hover {
    background-color: rgba(0, 128, 0, 0.1);
    /* Light green hover */
    transform: scale(1.1);
}

/* Delete Button Styles */
.delete-icon-button {
    all: unset;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: transform 0.2s, background-color 0.2s;
    color: orange;
}

.delete-icon-button i {
    color: orange;
    /* Orange icon color for delete */
    background-color: transparent;
}

.delete-icon-button:hover {
    background-color: rgba(255, 165, 0, 0.1);
    /* Light orange hover */
    transform: scale(1.1);
}

.bank-account-container {
    margin: 20px;
}

.bank-account-row {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    /* Add space between input fields */
}

.form-group {
    flex: 1;
    /* Ensures equal width for all inputs */
}

.form-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    /* Align buttons to the right */
    padding: 5px 10px;
}

.form-actions button {
    margin-left: 10px;
    /* Add space between buttons */
}

/* earning-total {
  padding-left: 700px;

} */

button.btn-danger {
    background: red;
    color: white;
    border: none;
    padding: 5px 10px;
    margin-bottom: 10px;
    cursor: pointer;
}

button.btn-danger:hover {
    opacity: 0.8;
}

.form-actions-inline {
    position: absolute;
    /* Position buttons absolutely relative to the form-group */
    top: 35px;
    /* Adjust to align with the middle of the input */
    right: 10px;
    /* Position to the right edge of the input box */
    display: flex;
    gap: 10px;
    /* Space between buttons */
}

.no-borders {
    border-collapse: collapse;
    width: 100%;
}

.no-borders th {
    padding: 12px;
    /* Adds spacing */
    text-align: center;
    /* Centers the text horizontally */
    vertical-align: middle;
    /* Centers the text vertically */
}

.no-borders th {
    font-weight: bold;
    background-color: #f9f9f9;
    /* Optional: subtle background for the header */
}

.no-borders thead {
    border-bottom: 2px solid #000;
    /* Adds a bold line below the header */
}

.no-borders tbody tr {
    border-bottom: 1px solid #ddd;
    /* Adds a line below each row in the body */
}

.no-borders tbody tr:last-child {
    border-bottom: none;
    /* Removes the bottom border for the last row */
}

.salary-type-selector {
    display: flex;
    gap: 20px;
    align-items: center;
}

.radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.radio-input {
    margin-right: 8px;
    accent-color: #007bff;
    /* Modern browsers for styling */
    cursor: pointer;
}

.radio-label:hover {
    color: #0056b3;
}

/* General Styles */

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
  }

  h2{
    font-size: 24px;
    font-weight: bold;
  }
  
  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column; /* Stack children vertically */
    gap: 0px; /* Adds 0px gap between rows */
}
.actions {
    display: flex;
    align-items: center;
}
  
  /* Actions Header */
.actions h2 {
    flex: 1;
    font-family: Inter;
    font-size: 40px;
    font-weight: 700;
    text-align: left;
    color: #4262FF;
}
  
  /* Sub-container Styles */
.sub-container {
    background-color: #ffffff;
    padding: 20px;
    border: 1px solid #4262FF;
    border-radius: 10px;
}
  
  .sub-container h2 {
    font-size: 24px;
    font-weight: bold;
  }
  
  /* Payroll Head Section */
  .payroll-head {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .payroll-head-icon {
    width: 10%;
    display: flex;
    justify-content: center;
    font-size: 60px;
    color: #4262FF;
  }
  
  .payroll-head-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-weight: bold;
  }
  
  .payroll-head-content strong {
    font-size: 16px;
    color: #333;
  }
  
  .payroll-head-content span {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
  }
  
  .payroll-head-content a {
    color: #4262FF;
    text-decoration: underline;
  }

  .payroll-info{
    display: flex;
    justify-content: flex-start;
    width: 100%;
    background-color: transparent;
    border-radius: 10px;
  }

 .PayRun-Tabs{
  display: flex;
    justify-content: flex-start;
    width: 100%;
    background-color: transparent;
    border-radius: 10px;
 }
  
  .nav-tabs {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;

  }

  .nav-tabs .nav-link {
    padding: 10px 39.5px;
    color: #4262FF;
    font-weight: 500;
    background-color: white;
    cursor: pointer;
    border-radius: 0 0 0 0;
  }
  
  .nav-tabs .nav-link.active {
    color: black;
    background-color: #f1eeee;
    border-radius: 0 0 0 0; 
    border-bottom: none;
  }
  
  /* Pay Calendars Section */

  
  .pay-calendar-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: white;
    margin-bottom: 5px;
    border-radius: 10px;
  }
  
  .pay-calendar-head strong {
    font-size: 18px;
    color: #333;
  }
  
  .btn-group .btn-primary {
    background: white;
    border: 1px solid #4262FF;
    color: #4262FF;
    font-weight: bold;
    border-radius: 10px;
    padding: 10px 20px;
  }
  
  .btn-group .dropdown-menu {
    width: 100%;
    padding: 10px;
    border-radius: 10px;
    font-weight: bold;
    overflow: hidden;
  }
  
  .dropdown-item {
    font-weight: bold;
    color: #333;
    padding: 8px 15px;
  }
  
  .dropdown-item:hover {
    background-color: #4262FF;
    color: white;
    font-weight: bold;
  }

  .pay-calendar-table {
    background-color: white;
    border-radius: 10px;
    padding: 20px 20px;
  }
  
  /* Table Styles */
  .pay-calendar-table table {

    width: 100%;
    border-collapse: collapse;
    border-radius: 10px;
  }
  
  .pay-calendar-table th,
  .pay-calendar-table td {
    text-align: left;
    padding: 20px 20px;
  }
  
  .pay-calendar-table th {
    background-color: white;
    font-weight: bold;
    color: #333;
  }
  
  .pay-calendar-table tbody tr {
    background-color: white;
    border-bottom: 1px solid #ddd;
  }
  
  .pay-calendar-table tbody tr:hover {
    background-color: #f1f1f1;
  }
  
  /* History Section */
  .payroll-history {
    margin-top: 20px;
    background-color: white;
    border: 1px solid #4262FF;
    border-radius: 10px;
    padding: 20px 20px;
  }
  
  .payroll-history h3 {
    display: flex;
    align-items: center;
  }
  
  .payroll-history .btn-toggle {
    background: none;
    border: none;
    font-size: 18px;
    color: black;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  
  .payroll-history .btn-toggle i {
    margin-left: 10px;
    font-weight: bold;
    transition: transform 0.3s;
  }
  
  .collapse:not(.show) + h3 .btn-toggle i {
    transform: rotate(0deg);
  }
  
  .collapse.show + h3 .btn-toggle i {
    transform: rotate(180deg);
  }
  
  .collapse {
    background-color:white;
    border: 1px solid white;
    border-radius: 5px;
  }
  
  .collapse p {
    font-size: 16px;
    color: #000000;
  }

/* Pay-items */
.pay-items-section {
  display: flex; 
  background-color: white;
  border-radius: 10px;
}

.pay-items-sections {
  display: flex;
  background-color: white;
  border-radius: 10px;
  margin-inline: 12%;
}

.pay-items-sections-sub{
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 10px;
  margin-inline: 12%;
}

.pay-item-2{
  display: flex; 
  flex-direction: column;
  background-color: transparent;
  border-radius: 10px;
}

.side-bar {
  width: 20%; /* Set sidebar width */
  background-color: #ffffff;
  border-radius: 10px 0 0 10px;
  border-right: 1px solid #ddd;
  padding: 10 0px; /* Add some padding */
}

.nav_pay_item {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.nav_pay_item li {
  margin-bottom: 10px;
}

.nav_pay_item a {
  text-decoration: none;
  width: 100%;
  color: #333;
  font-weight: 500;
  padding: 10px;
  display: block;
  border-radius: 10px;
}

.nav_pay_item a.active {
  color: black;
  font-weight: bold;
  border-left: 3px solid #4262FF;
  background-color: #dfe4ff;
}

.nav_pay_item a:hover {
  background-color: #d7dbf5;
}


.pay_item-content{
  border-radius: 0 10px 10px 0;
  width: 80%;
}

.Earnings , .Deductions , .Reimbursements, .Leave {
  width: 100%; /* Adjust width to match the sidebar */
  padding: 20px;
  background-color: white;
}

.Leave-form-3 , .Leave-form-final{
  width: 100%; /* Adjust width to match the sidebar */
  padding: 20px;
  background-color: transparent;
}

.pay_item_head {
  display: flex;
  padding: 10px 0;
  border-radius: 0 10px 10px 0;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pay_item_head h2 {
  font-size: 24px;
  font-weight: bold;
}

.table-responsive {
  margin-top: 10px;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table thead th {
  background-color: #ffffff;
  font-weight: bold;
  color: #333;
  padding: 10px;
  text-align: left;
}

.table tbody tr {
  background-color: white;
  border-bottom: 1px solid #ddd;
}

.table tbody tr:hover {
  background-color: #f1f1f1;
}

.table tbody td {
  padding: 10px;
  color: #555;
}

.btn-group .dropdown-menu {
  padding: 0 40px;
  border-radius: 10px;
  overflow: hidden;
  font-weight: bold;
}

.dropdown-item:hover {
  background-color: #4262FF;
  color: white;
}

.btn-secondary {
  background: none;
  border: none;
  color: #333;
  font-size: 18px;
  cursor: pointer;
}

.btn-secondary:hover {
  color: #4262FF;
}

.pay-item-one{
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
}

.pay-item-two{
  width: 100%;
  display: flex;
  margin-bottom: 20px;
}

.pay-item-col1{
  display: flex;
  flex-direction: column;
  width: 30%;
  border-right: 1px solid #666666;
}

.pay-item-col2{
  display: flex;
  flex-direction: column;
  padding-left: 10%;
  width: 70%;
}
.pay-item-col3{
  display: flex;
  margin-right: 25px;
  width: 70%;
}

.custom-search {
  background-color: #c2c2c2;
  border-radius: 10px;
}
.filter-buttons {
  width: 30%;
  display: flex;
  align-items: center;
  gap: 25px;
}


.pay-item-four {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding:  0 10px;
  border: 2px solid #c7c7c7;
  border-radius: 10px;
  margin-top: 20px;
  margin-bottom: 20px;
  background-color: white;
}

.pro-pic {
  width: 50px;
  height: 50px;
  background-color: #e0e0e0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  background-color: #4262FF;
}

.User-Details {
  padding-top: 15px;
  flex-grow: 1;
  margin-left: 15px;
}

.User-Details h5 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.User-Details small {
  size: 12px;
  color: gray;
}

.approve-btn button {
  color: #4262FF;
  font-weight: bold;
  border: 2px solid #4262FF;
  background: transparent;
  border-radius: 10px;
  padding: 5px 15px;
}

.app-icon {
  margin-left: 10px;
}

.app-icon i {
  font-size: 1.2rem;
  color: #555;
}

.leave-emp {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.emp-name,
.salary-earnings,
.date-next-payment {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 5px 15px;
  border: 1px solid #ccc;
}

label {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
}

.employee-name {
  font-size: 18px;
  font-weight: bold;
  color: #4262FF;
  margin: 0;
}

.view-btn {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  background-color: #f2f2f2;
  cursor: not-allowed;
}

.next-payment-date {
  font-size: 16px;
  font-weight: bold;
  width: 100%;
}


.add_calender{
  width: 30%;
  color: #4262FF;
  background-color: white;
  font-weight: bold;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20px;
}

/* Employee  */
.earnings-container {
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
  width: 100%;
  border: 2px solid #c0c0c0;
}
.opening-balance{
  display: flex;
}

.opening-balance h2 {
  font-size: 18px;
  margin-right: 5px;
  font-weight: bold;
}

.earnings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.earnings-title {
  font-size: 16px;
  font-weight: bold;
  color: #000000;
  margin: 0;
}

.earning-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.earning-description {
  flex: 2;
  font-size: 14px;
  font-weight: bold;
  color: #4262FF;
}

.earning-amount-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.earning-amount {
  flex: 1;
  padding: 5px;
  border: 2px solid #ccc;
  border-radius: 5px;
  text-align: right;
}

.btn-remove {
  background: none;
  border: none;
  color: #999;
  margin-left: 10px;
  cursor: pointer;
  font-size: 16px;
}

.btn-remove:hover {
  color: #ff4d4f;
}

.btn-add-earning {
  background: white;
  border: 1px solid #4262FF;
  color: #4262FF;
  font-weight: bold;
  border-radius: 10px;
  padding: 10px 20px;
}

.btn-add-earning:hover {
  background-color: #f0f4ff;
}

.earning-total {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  padding: 10px 0;
  margin-top: 10px;
}

.earning-total span {
  font-size: 14px;
  color: #333;
}

.emp-open-footer {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  padding: 10px;
}

.cancel-btn{
  background-color: #ffffff;
  color: #4262FF;
  border: 1px solid #4262FF;
  font-weight: bold;
  padding: 10px 25px;
  border-radius: 10px;
  margin-top: 20px;

}

.cancel-btn:hover {
  background-color: #4262FF;
  color: #ffffff;
}

.save-btn{
  background: linear-gradient(to right, #4262FF, #512CA2);
  color: #fff;
  border: 1px solid #4262FF;
  font-weight: bold;
  padding: 10px 30px;
  border-radius: 10px;
  margin-top: 20px;
}

.save-btn:hover {
  background: linear-gradient(to right, #512CA2, #4262FF);
}

.menu-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 40px;
  margin-top: 20px;
  background-color: #dfe4ff;
  padding: 20px;
  border-radius: 20px;
}

.menu-sub-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 180px;
}

.menu-topic {
  font-size: 20px;
  font-weight: 600;
  color: #555;
}

.line {
  width: 2px;
  background: #555;
}

/* General styles for the form and modal */



.form-label {
    font-weight: 600;
    color: #333;
}

.date-picker-group {
  display: flex;
  gap: 20px; /* space between the two date inputs */
  flex-wrap: wrap; /* optional, allows wrapping on smaller screens */
}
.input-width {
  min-width: 200px;
  flex: 1;
}



/* Styles for the dropdown */
select.form-control {
    width: 30%;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

select.form-control:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Disabled option styling */
select.form-control option[disabled] {
    color: #6c757d;
}

/* Spinner container */
.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

/* IFrame container */
.iframe-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 10px;
}

/* IFrame styling */
iframe {
  width: 100%;
  height: 700px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-modal {
    max-width: 95%;
  }

  iframe {
    height: 500px;
  }
}

.input-width {
  width: 30%;
  min-width: 250px;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}


@media (max-width: 700px) {

select.form-control {
  width: 100%;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-secondary {
  background: none;
  border: none;
  color: #333;
  font-size: 18px;
  cursor: pointer;
  width: 100%;
}

.btn-primary {
  background: white;
  border: 1px solid #4262FF;
  color: #4262FF;
  font-weight: bold;
  border-radius: 10px;
  padding: 10px 20px;
  width: 100%;
}
}