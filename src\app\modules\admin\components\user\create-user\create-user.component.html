<app-custom-header></app-custom-header>

<body>
  <div class="form-container">
    <div *ngIf="showUserRegistration">
      <h2>
        {{ planName === 'Free' ? 'Create your Free account in seconds' : 'Create your account in seconds' }}
      </h2>
      <h3>Step 01</h3>
      <form
        [formGroup]="userForm"
        (ngSubmit)="navigateToEntityRegistration($event)"
        (keydown.enter)="$event.preventDefault()"
      >
        <div class="user-name">
          <div class="form-group">
            <label for="first-name">First Name <span style="color: red;">*</span></label>
            <input
              type="text"
              id="first-name"
              formControlName="firstName"
              [(ngModel)]="user.firstName"
              required
            />
            <div
              *ngIf="
                userForm.get('firstName')?.invalid &&
                (userForm.get('firstName')?.dirty ||
                  userForm.get('firstName')?.touched)
              "
            >
              <small
                class="text-danger"
                *ngIf="userForm.get('firstName')?.errors?.['required']"
                >First Name is required.</small
              >
              <small
                class="text-danger"
                *ngIf="userForm.get('firstName')?.errors?.['pattern']"
                >Name can only contain letters, numbers, and spaces.</small
              >
            </div>
          </div>
          <div class="form-group">
            <label for="last-name">Last Name <span style="color: red;">*</span></label>
            <input
              type="text"
              id="last-name"
              formControlName="lastName"
              [(ngModel)]="user.lastName"
              required
            />
            <div
              *ngIf="
                userForm.get('lastName')?.invalid &&
                (userForm.get('lastName')?.dirty ||
                  userForm.get('lastName')?.touched)
              "
            >
              <small
                class="text-danger"
                *ngIf="userForm.get('lastName')?.errors?.['required']"
                >Last Name is required.</small
              >
              <small
                class="text-danger"
                *ngIf="userForm.get('lastName')?.errors?.['pattern']"
                >Name can only contain letters, numbers, and spaces.</small
              >
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email <span style="color: red;">*</span></label>
          <input
            type="email"
            id="email"
            formControlName="email"
            [(ngModel)]="user.username"
            (keyup)="checkUser()"
            [readonly]="isEntityInvitation"
            required
          />
          <div
            *ngIf="
              userForm.get('email')?.invalid &&
              (userForm.get('email')?.dirty || userForm.get('email')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="userForm.get('email')?.errors?.['required']"
            >
              Email is required.
            </small>
            <small
              class="text-danger"
              *ngIf="userForm.get('email')?.errors?.['customEmail'] && userForm.get('email')?.value"
            >
              Invalid email format.
            </small>
          </div>
          <small *ngIf="isUsernameExits" class="text-danger">
            {{ usernameExistsMessage }}
          </small>
        </div>

        <div class="form-group">
          <label for="password">Password <span style="color: red;">*</span></label>
          <div class="input-group d-flex align-items-center">
            <input
              [type]="isPasswordVisible ? 'text' : 'password'"
              id="password"
              formControlName="password"
              [(ngModel)]="user.password"
              required
              class="form-control"
              style="flex: 1;"
            />
            <button
              type="button"
              class="btn btn-outline-secondary input-group-text d-flex align-items-center"
              (click)="isPasswordVisible = !isPasswordVisible"
              tabindex="-1"
              style="padding: 1; width: 40px; height: 47px;">
              <i [class]="isPasswordVisible ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
            </button>
          </div>
          <div
            *ngIf="
              userForm.get('password')?.invalid &&
              (userForm.get('password')?.dirty || userForm.get('password')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="userForm.get('password')?.errors?.['required']"
              >Password is required.</small
            >
            <small
              class="text-danger"
              *ngIf="userForm.get('password')?.errors?.['pattern']"
            >
              {{ getPasswordValidationMessage() }}
            </small>
          </div>
        </div>

        <div class="form-group">
          <label for="re-password">Re-enter Password</label>
          <div class="input-group d-flex align-items-center">
            <input
              [type]="isPasswordVisible ? 'text' : 'password'"
              id="re-password"
              formControlName="rePassword"
              required
              class="form-control"
              style="flex: 1;"
            />
            <button
              type="button"
              class="btn btn-outline-secondary input-group-text d-flex align-items-center"
              (click)="isPasswordVisible = !isPasswordVisible"
              tabindex="-1"
              style="padding: 1; width: 40px; height: 47px; display: flex;">
              <i [class]="isPasswordVisible ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
            </button>
          </div>
          <div
            *ngIf="
              userForm.hasError('mismatch') &&
              userForm.get('rePassword')?.touched
            "
          >
            <small class="text-danger">Passwords do not match.</small>
          </div>
        </div>

        <div class="form-actions actions">
          <button type="button" class="back-button" (click)="navigateToPreviousStep()">
            Back
          </button>
          <button type="submit" class="invite-button">
            Next
          </button>
        </div>
      </form>
    </div>

    <div *ngIf="!showUserRegistration">
      <h2>User Creation</h2>
      <h3>Step 02</h3>
      <h4>Business Entity Creation</h4>
      <form (ngSubmit)="onSubmitRegister()" #registerForm="ngForm" novalidate (keydown.enter)="$event.preventDefault()">
        <div class="form-group">
          <label for="abn-number">ABN <span style="color: red;">*</span></label>
          <div class="input-group-1">
            <input
              type="text"
              id="abn-number"
              name="abn-number"
              [(ngModel)]="entity.abn"
              (ngModelChange)="onAbnChange($event)"
              required
            />
            <button
              type="button"
              class="lookup-button"
              (click)="handleABNlookup()"
            >
              Lookup ABN
            </button>
          </div>
          <span
            *ngIf="abnValidationMessage"
            [ngClass]="{ invalid: !isAbnValid }"
          >
            {{ abnValidationMessage }}
          </span>
        </div>
        <div class="class-name">
          <div class="form-group">
            <label for="business-structure">Business Structure <span style="color: red;">*</span></label>
            <select
              id="business-structure"
              name="business-structure"
              [(ngModel)]="entity.businessStructure"
              required
            >
              <option value="" disabled selected>
                Select business structure
              </option>
              <option value="Sole trader">Sole trader</option>
              <option value="Company">Company</option>
              <option value="Partnership">Partnership</option>
              <option value="Trust">Trust</option>
              <option value="Co-operative">Co-operative</option>
              <option value="Indigenous corporation">
                Indigenous corporation
              </option>
              <option value="Joint venture">Joint venture</option>
            </select>
            <span
              *ngIf="structureValidationMessage"
              [ngClass]="{
                valid: isStructureValid,
                invalid: !isStructureValid
              }"
            >
              {{ structureValidationMessage }}
            </span>
          </div>
          <div class="form-group">
            <label for="entity-name">Entity Name <span style="color: red;">*</span></label>
            <input
              type="text"
              id="entity-name"
              name="entity-name"
              [value]="entityName"
              placeholder="Automatically populated"
              [(ngModel)]="entity.entityName"
              required
              readonly
            />
          </div>
        </div>
        <div class="form-group position-relative">
          <label for="business-address">Business Address <span style="color: red;">*</span></label>
          <div class="input-group">
            <input
              type="text"
              id="business-address"
              name="business-address"
              [(ngModel)]="entity.businessAddress"
              (keyup)="checkBusinessAddress()"
              required
              class="form-control"
            />
          </div>

          <ul
            *ngIf="suggestedAddresses.length > 0"
            class="dropdown-menu show"
            style="
              position: absolute;
              width: 100%;
              max-height: 200px;
              overflow-y: auto;
              z-index: 1000;
            "
          >
            <li
              *ngFor="let address of suggestedAddresses"
              class="dropdown-item"
              (click)="selectAddress(address)"
              style="cursor: pointer"
            >
              {{ address.full_address }}
            </li>
          </ul>

          <span
            *ngIf="addressValidationMessage"
            [ngClass]="{ valid: isAddressValid, invalid: !isAddressValid }"
          >
            {{ addressValidationMessage }}
          </span>
        </div>

          <div class="class-name">
          <div class="form-group">
            <label for="uploadLogo">Upload Logo</label>
            <input
              type="file"
              id="logo"
              name="logo"
              (change)="onFileSelected($event)"
              accept="image/*"
            />
            <span *ngIf="selectedFile">{{ selectedFile.name }}</span>
          </div>
          <div class="img-preview" *ngIf="selectedFile">
            <img [src]="url" height="100" width="100" />
          </div>
        </div>
        <div class="check-box">
          <input
            type="checkbox"
            name="gst-registration"
            [(ngModel)]="gstInformation"
            (click)="showGSTRegistrationInfo()"
          />
          <label>GST Applicable</label>
        </div>
        <div class="gst-registration-information" *ngIf="gstInformation">
          <div class="class-name">
            <div class="form-group">
              <label for="gst-return-frequency">GST Return Frequency</label>
              <select
                id="gst-return-frequency"
                name="gst-return-frequency"
                [(ngModel)]="entity.gstReturnFrequency"
                required
              >
                <option value="" disabled selected>
                  Select GST return frequency
                </option>
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="annually">Annually</option>
              </select>
            </div>
          </div>
        </div>
        <div class="form-actions">
          <button
            type="button"
            class="back-button"
            (click)="navigateToUserRegistration()"
          >
            Back
          </button>
          <button type="submit" class="create-button" [disabled]="isSubmitting">
            <span *ngIf="isSubmitting">Registering...</span>
            <span *ngIf="!isSubmitting">Register</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</body>
