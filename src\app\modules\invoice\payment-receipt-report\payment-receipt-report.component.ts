
import { Component, ElementRef, ViewChild } from '@angular/core';
import { InvoiceService } from '../invoice.service';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { BusinessPartner } from '../../business-partner/business-partner';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { InvoiceHead, PaymentReceiptsHead } from '../invoice';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-payment-receipt-report',
  templateUrl: './payment-receipt-report.component.html',
  styleUrls: ['./payment-receipt-report.component.css']
})
export class PaymentReceiptReportComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  
  fromDate: string = '';
  toDate: string = '';
  status: string = '%'; 
  isLoading = false;
  payment: BusinessPartner[] = [];
  paymentReceipts: PaymentReceiptsHead = new PaymentReceiptsHead();

  getAllCustomers = false; 

  @ViewChild('paymentReceiptPreviewFrame') paymentReceiptPreviewFrame!: ElementRef;


  constructor(
    private invoiceService: InvoiceService, 
    private router: Router, 
    public sanitizer: DomSanitizer, 
    private businessPartnerService: BusinessPartnerService,
    private swalAlerts: SwalAlertsService
  ) {}

   ngOnInit() {
    this.paymentReceipts.businessPartnerId = '0';
    this.loadCustomers();
  }


  previewPaymentReceipt(fromDate: string, toDate: string, status: string, businessPartnerId: any) {
  if (!fromDate || !toDate) {

    this.swalAlerts.showSwalWarning('Warning!', 'Please select Date Range', 'Missing date range for Payment Receipt report.');
    return;
  }
  this.isLoading = true;
  const entityId = +localStorage.getItem('entityId')!;
  const entityUUID = localStorage.getItem('entityUuid')!;
  const bpId = (businessPartnerId === 0 || businessPartnerId === '' || businessPartnerId == null) ? null : +businessPartnerId;

  const requestData = {
    fromDate,
    toDate,
    status,
    entityId,
    businessPartnerId: bpId,
    entityUUID
  };

  this.invoiceService.getPaymentListReport(requestData).subscribe(
    data => {
      const base64String = data.response;
      if (base64String) {
        this.loadPdfIntoIframe(base64String);
      } else {
        this.isLoading = false;
        alert('No Payment Receipt data for preview.');
      }
    },
    error => {
      this.isLoading = false;
      alert('Error loading Payment Receipt preview.');
    }
  );
}


 private loadPdfIntoIframe(base64String: string) {
      if (base64String && base64String.trim().length >= 50) {
        const pdfData = 'data:application/pdf;base64,' + base64String;
        const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
        const iframe = this.paymentReceiptPreviewFrame.nativeElement;
    
        iframe.onload = () => {
          this.isLoading = false;
        };
    
        iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
    
        // Open modal manually using Bootstrap JS
        const modalElement = document.getElementById('simpleModal');
        const modal = new bootstrap.Modal(modalElement!);
        modal.show();
      } else {
         this.isLoading = false;
         this.swalAlerts.showSwalWarning('No Data', 'No Payment Receipt data for preview.', 'No Payment Receipt data was returned for the selected range.');
    
      }
    }
    

    
 loadCustomers() {
  const entityId = +((localStorage.getItem('entityId')) + '');
  this.businessPartnerService.getCustomerListByEntity(entityId).subscribe(
    (customers: BusinessPartner[]) => {
      this.payment = customers;
    },
    (error: HttpErrorResponse) => {
      console.error('Error fetching customers', error);

      //  Use SwalAlertsService for error with Chimp support
      this.swalAlerts.showErrorWithChimpSupport(
        'Failed to load customers.',
        'Unable to fetch customer list for this entity. Please check if the customer service is responding.'
      );
    }
  );
}


  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.payment.find(cust => cust.businessPartnerId === +selectedCustomerId);

    // Set the reference field
    this.paymentReceipts.customerName = selectedCustomer?.bpName || '';
  }
  // Method to toggle all customers' data
  toggleAllCustomers() {
    this.getAllCustomers = !this.getAllCustomers;
    if (this.getAllCustomers) {
      this.paymentReceipts.businessPartnerId = '';  // Clear selected customer when showing all
    }
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }
}

