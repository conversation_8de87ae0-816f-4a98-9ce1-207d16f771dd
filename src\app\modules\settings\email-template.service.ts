import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export interface EmailTemplate {
    id: number | null;  
    templateName: string;
    subject: string;
    content: string;
  }

@Injectable({
  providedIn: 'root'
})
export class EmailTemplateService {

  private readonly baseURL = environment.apiUrl;

  constructor(private http: HttpClient) { }


  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    const authToken = this.getAuthToken();

  if (authToken) {
    headers = headers.set('Authorization', 'Bearer ' + authToken);
  } else {
    // Add secure API key for protected-but-public endpoints
    headers = headers.set('X-API-KEY', environment.secureApiKey);
  }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }
  addEmailTemplate(id: number, emailTemplate: any): Observable<any> {
    return this.request(
      'PUT',
      `/addEmailTemplate?entityId=${id}`,
      emailTemplate
    ); 
  }

  getEmailTemplateByEntityId(entityId: number): Observable<EmailTemplate[]> {
    return this.request('GET', `/emailTemplate/${entityId}`, null);
    
  }
  getAllTemplates(): Observable<EmailTemplate[]> {
    return this.request('GET', '/emailTemplateList', null);
  }

  // Handle HTTP errors
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed: ${error.message}`);
      return of(result as T);
    };
  }
}
