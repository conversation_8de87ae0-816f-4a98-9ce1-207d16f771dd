import { Component } from '@angular/core';

@Component({
  selector: 'app-partnership',
  templateUrl: './partnership.component.html',
  styleUrls: ['./partnership.component.css'],
})
export class PartnershipComponent {
  features = [
    {
      icon: '🤝',
      title: 'A Partnership That Works',
      description:
        'Accountants and bookkeepers who join forces with LEDGER CHIMP don’t just use our software—they become true partners OF LEDGER CHIMP.',
    },
    {
      icon: '🚀',
      title: 'Grow Your Practice & Enhance Client Services',
      description:
        'Our intuitive, professional-grade tools allowS you to support your clients more effectively and scale your business.',
    },
    {
      icon: '💡',
      title: 'Designed with Experts in Mind',
      description:
        'We know your needs, challenges, and ambitions—our platform is built to empower financial professionals with the flexibility and efficiency they deserve.',
    },
    {
      icon: '💰',
      title: '<PERSON>arn <PERSON>s for Growing with Us',
      description:
        'Partnering with LEDGER CHIMP comes with financial incentives when you sign up clients. Boost your earnings while providing your clients with a top-tier accounting solution.',
    },
  ];
}
