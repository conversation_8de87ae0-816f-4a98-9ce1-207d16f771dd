import { Component, OnInit, ViewChild } from '@angular/core';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { OtherExpensesHead, PaymentExpensesDetails, PaymentExpensesHead } from '../expence-claims';
import { Entity } from 'src/app/modules/entity/entity';
import { ActivatedRoute, Router } from '@angular/router';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { ExpenceClaimsService } from '../expence-claims.service';
import Swal from 'sweetalert2';
import { NgForm } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-payment-expenses',
  templateUrl: './payment-expenses.component.html',
  styleUrls: ['./payment-expenses.component.css']
})
export class PaymentExpensesComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  expenseHead: OtherExpensesHead = new OtherExpensesHead();
  paymentReceiptsHead: PaymentExpensesHead = new PaymentExpensesHead();
  details: PaymentExpensesDetails = new PaymentExpensesDetails();
  newPaymentNumber: string = '';
  businessEntityId: number = 0;
  lastPaymentNumber: string = '';
  businessEntity: Entity = new Entity();
  selectedExpenses: any[] = [];
  totalPaidAmount: number = 0;
  private debounceTimer: any = null;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private entityService: EntityService,
    private expenseService: ExpenceClaimsService
  ) { }


  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      const expenseIds = JSON.parse(params['expenseIds'] || '[]');
      if (expenseIds.length > 0) {
        this.loadExpenses(expenseIds);
      } else {
        Swal.fire({
          title: 'No expenses Found',
          text: 'No expense IDs were provided.',
          icon: 'warning',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#ff7e5f',
          cancelButtonColor: '#be0032',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('No expenses Found');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    });
    this.getBusinessEntityById();

    if (!this.paymentReceiptsHead.documentDate) {
      this.paymentReceiptsHead.documentDate = this.getTodayDate();
    }
  }

  // Utility function to get today's date in 'YYYY-MM-DD' format
  getTodayDate(): string {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const dd = String(today.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }


  loadExpenses(expenseIds: number[]): void {
    const expenseRequests = expenseIds.map(id => this.expenseService.getExpensesHeadById(id).toPromise());
    Promise.all(expenseRequests)
      .then(expenses => {
        this.selectedExpenses = expenses;
      })
      .catch(error => {
        Swal.fire({
          title: 'Error Loading expenses',
          text: 'There was an error fetching the expense details.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Error Loading expenses');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      });
  }

  

  getTotalPaidAmount(): number {
    return this.selectedExpenses.reduce((total, expense) => total + (expense.creditAmount || 0), 0);
  }


  getExpenseNewBalance(expense: any): number {
    const balanceAmount = expense.balanceAmount || 0;
    const creditAmount = expense.creditAmount || 0;
    return balanceAmount - creditAmount;
  }



  onCancel() {
    this.router.navigate(['payment-expenses']);
  }



  preventEnter(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault(); // Prevent form submission on "Enter"
    }
  }




  onSubmit(f: NgForm) {
    // Check if credit amount is provided for all expenses
    const invalidexpenses = this.selectedExpenses.filter(expense => !expense.creditAmount || expense.creditAmount <= 0);

    if (invalidexpenses.length > 0) {
      // Show validation error
      Swal.fire({
        title: 'Warning!',
        text: 'Paid Amount is required and must be greater than 0.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f'
      });
    } else {
      // Pass the expenses to the saveQuotation method
      this.paymentReceiptsHead.totalPaidAmount = this.getTotalPaidAmount();
      this.paymentReceiptsHead.details = this.selectedExpenses.map(expense => {
        let detail = new PaymentExpensesDetails();
        detail.otherExpensesId = expense.otherExpensesId;
        detail.expensesNumber = expense.expensesNumber;
        detail.netAmount = expense.netAmount;
        detail.balanceAmount = expense.balanceAmount;
        detail.paidAmount = expense.creditAmount;
        return detail;
      });

      this.saveCreditNote(this.selectedExpenses);
    }
  }

  saveCreditNote(expenses: any[]) {

    // Ensure document date is set, default to today if not provided
    if (!this.paymentReceiptsHead.documentDate) {
      this.paymentReceiptsHead.documentDate = this.getTodayDate();
    }
    // Iterate over each expense to validate its credit amount
    const invalidexpenses = expenses.filter(expense => expense.creditAmount > expense.balanceAmount);

    if (invalidexpenses.length > 0) {
      Swal.fire({
        title: 'Paid Amount Exceeds Balance!',
        text: 'One or more expenses have Paid amounts greater than their balances. Do you want to continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, continue',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
      }).then((result) => {
        if (result.isConfirmed) {
          // If user confirms, set flag and continue
          invalidexpenses.forEach(expense => {
            expense.isCreditExceeded = true;
          });
          this.proceedToSave();
        } else if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData('Paid Amount Exceeds Balance!');
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });

    } else {
      // If all credit amounts are valid, proceed to save
      this.proceedToSave();
    }
  }

  proceedToSave() {
    // Assuming the selectedExpenses array is already populated and validated
    if (this.selectedExpenses.length > 0) {
      
      this.paymentReceiptsHead.businessPartnerId = this.selectedExpenses[0].businessPartnerId;
      this.paymentReceiptsHead.supplierName = this.selectedExpenses[0].supplierName;
        this.paymentReceiptsHead.expensesNumbers = this.selectedExpenses.map(expense => expense.expensesNumber).join(',');

    }

  
    this.paymentReceiptsHead.documentStatus = 'Open';
    this.paymentReceiptsHead.userId = +(localStorage.getItem('userid') + "");
    this.paymentReceiptsHead.entityId = +(localStorage.getItem('entityId') + "");

    // Save the credit note
    this.expenseService.savePaymentExpensesHead(this.paymentReceiptsHead).subscribe(
      (response: any) => {
        this.updatePaymentExpensesNumber();

        // Update the balance amount for each selected expense
        this.selectedExpenses.forEach(expense => {
          // Assuming you have already calculated the credit note amount for each expense
          const newBalanceAmount = expense.balanceAmount - expense.creditAmount; // Calculate new balance
          this.updateexpenseBalance(expense.otherExpensesId, newBalanceAmount); // Call method to update the expense balance
        });

        Swal.fire({
          title: 'Success!',
          text: 'The Payment has been successfully saved.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/payment-expenses-list']);
          }
        });
      },
      (error: HttpErrorResponse) => {
        console.error('Error saving Payment', error);
        Swal.fire({
          title: 'Error!',
          text: 'Unable to save the Payment. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Unable to save the Payment. Please try again.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }

        });
      }
    );
  }

  // Method to update expense balance in the backend
  updateexpenseBalance(otherExpensesId: number, newBalanceAmount: number) {
    this.expenseService.updateExpenseBalance(otherExpensesId, newBalanceAmount).subscribe(
      (response: any) => {
      },
      (error: HttpErrorResponse) => {
        console.error(`Error updating balance for expense ${otherExpensesId}:`, error);
      }
    );
  }


  getBusinessEntityById() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '');
    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
      (data) => {
        this.businessEntity = data;
        this.lastPaymentNumber = this.incrementPaymentNumber(
          this.businessEntity.paymentExpensesNumber
        );
        this.paymentReceiptsHead.paymentExpensesNumber = this.lastPaymentNumber;
      },
      (error) => console.error(error)
    );
  }


    incrementPaymentNumber(paymentExpensesNumber: string): string {
      if (!paymentExpensesNumber) {
        return 'PE000001'; // Default value
      }
      // Ensure the numeric part exists and is valid
      const numericPart = paymentExpensesNumber.slice(2); // Remove the "PE" prefix
      if (isNaN(Number(numericPart))) {
        console.error(`Invalid Payment Expenses Number: ${paymentExpensesNumber}`);
        return 'PE000001';
      }
      const incrementedNumber = (Number(numericPart) + 1).toString().padStart(numericPart.length, '0');
      return 'PE' + incrementedNumber;
    }
    


  updatePaymentExpensesNumber() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '0');
    this.businessEntity.paymentExpensesNumber = this.paymentReceiptsHead.paymentExpensesNumber;
    this.entityService.updatePaymentExpensesNumber(this.businessEntity, this.businessEntityId).subscribe(
      (data) => {
      },
      (error) => {
        console.error(error);
      }
    );
  }
  
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}