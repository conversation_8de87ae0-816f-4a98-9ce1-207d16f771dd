.help-section {
  background: rgba(66, 98, 255, 0.05);
  /* padding: 0; */
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 100px;
  flex-wrap: wrap;
  padding: 10px 0;
}

.help-section .content {
  width: 40%;
}

.help-section h1 {
  font-family: Segoe UI;
  width: 70%; 
  font-size: 40px;
  font-weight: 700;
  line-height: 58.09px;
  text-align: left;
  color: #4262FF;
}

.help-section p {
  margin-bottom: 10px;
  font-family: Inter;
  font-size: 20px;
  font-weight: 600;
  line-height: 35px;
  text-align: left;
  color: #000;
}

.form-container {
  background: white;
  padding: 3% 10%;
  padding-bottom: 0%;
  display: flex;
  justify-content: center;
  width: 100%;
}

form {
  max-width: 500px; /* shrink width */
  width: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border: 3px solid #4262ff;
  border-radius: 20px;
  padding: 30px 30px;
}

form input,
form select,
form textarea {
  width: 100%;
  padding: 15px 20px;
  margin: 10px 0px;
  border-radius: 10px;
  border: 1px solid #ccc;
  align-items: center;
}

form textarea {
  padding: 20px 20px;
}

form label {
  height: 40px;
  margin: 20px 10px;
  font-family: Inter;
  font-size: 20px;
  font-weight: 700;
  line-height: 38.73px;
  text-align: left;
  color: #4262ff;
}

form ::placeholder {
  font-family: Inter;
  font-size: 15px;
  font-weight: 500;
  text-align: left;
  color: #565656;
}

form textarea {
  height: 200px;
  resize: none;
}

.captcha-container {
  width: 100%;
}

form input[type="submit"] {
  border: none;
  border-radius: 10px;
  background: linear-gradient(to right, #512ca2, #4262ff);
  color: white;
  cursor: pointer;
  font-family: Inter;
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  margin-top: 30px;
}

form input[type="submit"]:hover {
  background: linear-gradient(to left, #512ca2, #4262ff);
}

.try-now {
  background: rgb(255, 255, 255);
  padding: 10px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.try-now .feature-text {
  font-family: Inter;
  font-size: 50px;
  font-weight: 700;
  line-height: 118.6px;
  text-align: left;
  color: #000000;
}

.try-now button {
  padding: 10px 20px;
  display: inline-block;
  border: none;
  border-radius: 5px;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  cursor: pointer;
  font-family: Inter;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.cta-button {
  padding: 10px 20px;
  display: inline-block;
  border: none;
  border-radius: 5px;
  background: linear-gradient(to right, #512ca2, #4262ff);
  color: white;
  cursor: pointer;
  font-family: Inter;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.sub-button {
  display: inline-block;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background: linear-gradient(to right, #512ca2, #4262ff);
  border: none;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  white-space: nowrap;
  width: 100%;
}

.sub-button:hover {
  background: linear-gradient(to left, #512ca2, #4262ff);
}

@media (max-width: 599px) {
  .help-section .content {
      width: 90%;
    }

        .form-container {
          background: white;
          padding: 5% 2%;
          padding-bottom: 0%;
          display: inline-block;
          text-align: left;
          width: 100%;
        }

                .try-now h1 {
                  font-family: Inter;
                  font-size: 40px;
                  font-weight: 700;
                  line-height: 50.6px;
                  text-align: left;
                  color: #000000;
                }

               


          .try-now .feature-text {
          font-family: Inter;
          font-size: 15px;
          font-weight: 600;
          line-height: 118.6px;
          text-align: left;
          color: #000000;
        }

                                .try-now button {
                                  display: flex;
                                  padding: 10px 20px;
                                  border: none;
                                  border-radius: 5px;
                                  background: linear-gradient(to right, #4262ff, #512ca2);
                                  color: white;
                                  cursor: pointer;
                                  font-family: Inter;
                                  font-size: 16px;
                                  font-weight: 600;
                                  display: flex;
                                  text-align: center;
                                }

                                                                form input[type="submit"] {
                                                                  border: none;
                                                                  border-radius: 5px;
                                                                  background: linear-gradient(to right, #512ca2, #4262ff);
                                                                  color: white;
                                                                  cursor: pointer;
                                                                  font-family: Inter;
                                                                  font-size: 16px;
                                                                  font-weight: 700;
                                                                  text-align: center;
                                                                  margin-top: 30px;
                                                                  width: 100%;
                                                                  height: 48px;
                                                                }
}