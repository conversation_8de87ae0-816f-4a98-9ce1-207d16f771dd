
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, tap } from 'rxjs';
import { MultiplePayCalendar,PayCalendar,PayCycle, PayPeriod } from '../payroll-setting';
import { environment } from 'src/environments/environment';
import { BankAccount, BankAccountNew, EmployeeLeave, EmployeeTax, Employment, EmploymentData, LeaveType, NewDeduction, NewEarning, NewLeave, NewReimbursement, NewSuperannuation, NewTax, Personal } from '../empolyee/employee';

@Injectable({
  providedIn: 'root',
})
export class GlAccountService {
  private readonly baseURL = environment.financeApiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }
 
 getGlAccountList(entityId: number): Observable<any[]> {
     return this.request('GET', `/coa-ledger-accounts/list/${entityId}`, {});
   }
}
