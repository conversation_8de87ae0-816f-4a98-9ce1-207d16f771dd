import { Component, OnInit, ViewChild } from '@angular/core';
import Swal from 'sweetalert2';
import { BusinessPartner, BusinessPartnerType } from '../business-partner';
import { ActivatedRoute, Router } from '@angular/router';
import { BusinessPartnerService } from '../business-partner.service';
import { HttpErrorResponse } from '@angular/common/http';
import { InvoiceHead } from '../../invoice/invoice';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-create-business-partner',
  templateUrl: './create-business-partner.component.html',
  styleUrls: ['./create-business-partner.component.css'],
})
export class CreateBusinessPartnerComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent)
  chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  businessPartner: BusinessPartner = new BusinessPartner();
  customers: BusinessPartner[] = [];
  invoiceHead: InvoiceHead = new InvoiceHead();
  businessPartnerType: BusinessPartnerType[] = [];
  isFinance: boolean = false;
  suggestedAddresses: any[] = [];
  suggestedDeliveryAddresses: any[] = [];
  isDeliveryAddressSynced: boolean = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private businessPartnerService: BusinessPartnerService,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.isFinance = params.hasOwnProperty('isFinance');
    });
    this.loadBusinessPartnerTypes();
    this.getBusinessPartnersListByEntity();
  }

  navigateToBusinessPartnerCancel() {
    this.router.navigate(['/business-partner']);
  }

  confirmCancel(): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Unsaved changes will be lost if you proceed.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, cancel',
      cancelButtonText: 'No, stay',
      confirmButtonColor: '#be0032',
      cancelButtonColor: '#007bff',
    }).then((result) => {
      if (result.isConfirmed) {
        this.navigateToBusinessPartnerCancel();
      }
    });
  }

  private getBusinessPartnersListByEntity(): void {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.businessPartnerService
      .getBusinessPartnersListByEntity(entityId)
      .subscribe(
        (data: BusinessPartner[]) => {
          this.customers = data; // Store the list for email validation
        },
        (error: HttpErrorResponse) => {
          console.error('Error fetching business partners', error);
          Swal.fire({
            title: 'Error!',
            text: 'Failed to load business partners.',
            icon: 'error',
            confirmButtonText: 'OK',
          });
        }
      );
  }

  // isEmailDuplicate(email: string): boolean {
  //   if (!email || !this.customers.length) return false; // Skip validation if no email or no data
  //   return this.customers.some(
  //     (partner) => partner.email?.toLowerCase() === email.toLowerCase()
  //   );
  // }

  onSubmitCustomerForm(): void {
    const selectedType =
      this.businessPartner.businessPartnerTypeId?.businessPartnerTypeId;

    // Validate email before proceeding
    // if (this.isEmailDuplicate(this.businessPartner.email)) {
    //   Swal.fire({
    //     title: 'Duplicate Email',
    //     text: 'This email is already in use. Please enter a different email.',
    //     icon: 'warning',
    //     confirmButtonText: 'OK',
    //   });
    //   return; // Prevent further execution
    // }

    if (!selectedType) {
      Swal.fire({
        title: 'Default Type: Customer',
        text: 'The default partner type is set to "Customer". Do you want to proceed ?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Proceed',
        cancelButtonText: 'Cancel',
      }).then((result) => {
        if (result.isConfirmed) {
          const customerType = this.businessPartnerType.find(
            (type) => type.businessPartnerType === 'Customer'
          );
          if (customerType) {
            this.businessPartner.businessPartnerTypeId.businessPartnerTypeId =
              customerType.businessPartnerTypeId;
          }
          this.saveCustomerForm(this.businessPartner);
        }
      });
    } else {
      this.saveCustomerForm(this.businessPartner);
    }
  }

  saveCustomerForm(businessPartner: BusinessPartner): void {
    this.businessPartner.entityId.entityId = +(
      localStorage.getItem('entityId') + ''
    );

    this.businessPartnerService.saveBusinessPartner(businessPartner).subscribe(
      (response) => {
        Swal.fire({
          title: 'Success!',
          text: 'Business Partner added successfully.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        });
        this.router.navigate(['/business-partner']);
      },
      (error: HttpErrorResponse) => {
        console.error('Error adding customer', error);
        Swal.fire({
          title: 'Error!',
          text: 'Error adding customer.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Please wait while Chimp processes your request.'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
  }
  loadBusinessPartnerTypes(): void {
    this.businessPartnerService.getBusinessPartnerTypesList().subscribe(
      (data: BusinessPartnerType[]) => {
        this.businessPartnerType = data;
        if (this.isFinance) {
          const supplier = this.businessPartnerType.find(
            (type) => type.businessPartnerType === 'Supplier'
          );
          if (supplier) {
            this.businessPartner.businessPartnerTypeId.businessPartnerTypeId =
              supplier.businessPartnerTypeId;
          }
        }
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching Business Partner Type', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load Business Partner Type.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Failed to load Business Partner Type.'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
  }
  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

  checkBusinessAddress(): void {
    const query = this.businessPartner.businessAddress
      ? this.businessPartner.businessAddress.trim()
      : '';
    const apiUrl = `${environment.addressfinderBaseUrl}?key=${environment.addressFinderApiKey}&q=${encodeURIComponent(
      query
    )}&format=json&source=gnaf%2Cpaf`;

    if (!query) {
      this.suggestedAddresses = [];
      return;
    }

    this.http.get(apiUrl).subscribe(
      (response: any) => {
        this.suggestedAddresses = response?.completions || [];
      },
      (error) => {
        console.error('Error fetching business address suggestions:', error);
      }
    );
  }

  selectAddress(address: any): void {
    this.businessPartner.businessAddress = address.full_address;
    this.suggestedAddresses = [];
  }

  checkDeliveryAddress(): void {
    const query = this.businessPartner.deliveryAddress
      ? this.businessPartner.deliveryAddress.trim()
      : '';
    const apiUrl = `${environment.addressfinderBaseUrl}?key=${environment.addressFinderApiKey}&q=${encodeURIComponent(
      query
    )}&format=json&source=gnaf%2Cpaf`;

    if (!query) {
      this.suggestedDeliveryAddresses = [];
      return;
    }

    this.http.get(apiUrl).subscribe(
      (response: any) => {
        this.suggestedDeliveryAddresses = response?.completions || [];
      },
      (error) => {
        console.error('Error fetching delivery address suggestions:', error);
      }
    );
  }

  selectDeliveryAddress(address: any): void {
    this.businessPartner.deliveryAddress = address.full_address;
    this.suggestedDeliveryAddresses = [];
  }

  syncDeliveryAddress(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.isDeliveryAddressSynced = checkbox.checked;
    if (this.isDeliveryAddressSynced) {
      this.businessPartner.deliveryAddress = this.businessPartner.businessAddress;
    }
  }
}
