import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of, tap } from 'rxjs';
import { environment } from 'src/environments/environment';
import { AccountantDto, User, UserType } from './user';
import { Entity } from 'src/app/modules/entity/entity';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private readonly baseURL = environment.apiUrl;
  private userTypeCache: Map<number, UserType> = new Map();
  private userCache: Map<number, User> = new Map();

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  setAuthToken(token: string | null): void {
    if (token !== null) {
      window.sessionStorage.setItem('auth_token', token);
    } else {
      window.sessionStorage.removeItem('auth_token');
    }
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any,
    responseType: 'json' | 'text' = 'json'
  ): Observable<any> {
    let headers = new HttpHeaders();

   const authToken = this.getAuthToken();

  if (authToken) {
    headers = headers.set('Authorization', 'Bearer ' + authToken);
  } else {
    // Add secure API key for protected-but-public endpoints
    headers = headers.set('X-API-KEY', environment.secureApiKey);
  }

    const options: any = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
      responseType: responseType as 'json' | 'text',
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  register(user: User): Observable<any> {
    return this.request('POST', `/register`, user);
  }

  login(user: User): Observable<any> {
    return this.request('POST', `/login`, user);
  }

  inviteUser(payload: any): Observable<any> {
    return this.request('POST', `/invite-user`, payload, null, 'text');
  }

  inviteAccountant(payload: any): Observable<any> {
    return this.request('POST', `/invite-accountant`, payload, null, 'text');
  }

  checkUser(username: string): Observable<any> {
    const params = { username };
    return this.request('GET', `/check-username`, null, params);
  }

  getEntitiesForUser(userId: number): Observable<Entity[]> {
    return this.request('GET', `/${userId}/entities`, {});
  }

  getUserById(id: number): Observable<User> {
    return this.request('GET', `/user/${id}`, {});
  }

  getUserByIdCached(id: number): Observable<User> {
    const cachedUser = this.userCache.get(id);
    if (cachedUser) {
      return of(cachedUser);
    }

    return this.getUserById(id).pipe(
      tap((user) => this.userCache.set(id, user))
    );
  }

  clearUserCache(): void {
    this.userCache.clear();
  }

  clearUserFromCache(id: number): void {
    this.userCache.delete(id);
  }

  updateUser(id: number, user: User): Observable<User> {
    return this.request('PUT', `/user/${id}`, user);
  }

  updateUserPassword(id: number, password: string): Observable<User> {
    return this.request('PUT', `/user/password/${id}`, password);
  }

  checkAccountantExists(
    username: String,
    userTypeId: number,
    entityId: number
  ): Observable<boolean> {
    const params = { username, userTypeId, entityId };
    return this.request('GET', `/check-accountant-exists`, null, params);
  }
  

  addUserToEntity(userId: number, entityId: number): Observable<User> {
    return this.request(
      'POST',
      `/user/${userId}/add-entity/${entityId}`,
      null,
      null,
      'json'
    );
  }

  verifyUser(userEmail: string): Observable<any> {
  return this.request(
    'POST',
    '/user/verify-email',
    { email: userEmail }, // pass as body
    undefined,
    'text'
  );
}

  setUserVerified(token: string): Observable<any> {
    return this.request(
      'POST',
      `/user/set-verify`,
      null,
      { token: token },
      'text'
    );
  }

  isUserVerified(username: string): Observable<boolean> {
  return this.request('POST', `/user/isVerified`, { username });
}

  forgotPassword(email: string): Observable<any> {
    return this.request(
      'POST',
      `/forgot-password?email=${email}`,
      null,
      null,
      'text'
    );
  }

  resetPassword(password: string, token: string): Observable<any> {
    return this.request(
      'POST',
      `/reset-password`,
      { password, token },
      null,
      'text'
    );
  }

    getUsersListByEntity(entityId: any): Observable<User[]> {
      return this.request('GET', '/getUsersListByEntity', {}, { entityId: entityId });
    }

     deleteUser(id: number): Observable<void> {
    return this.request('DELETE', `/deleteUserById/${id}`, null);
    }


    
  getAccountantList(): Observable<AccountantDto[]> {
    return this.request('GET', `/getAccountantsList`, {});
  }

  //User Type

  getUserTypesList(): Observable<UserType[]> {
    return this.request('GET', `/userTypes`, {});
  }

  getUserTypeById(userTypeid: number): Observable<UserType> {
    return this.request('GET', `/userType/${userTypeid}`, {});
  }

  getUserTypeByIdCached(userTypeId: number): Observable<UserType> {
    const cached = this.userTypeCache.get(userTypeId);
    if (cached) {
      return of(cached);
    }

    return this.getUserTypeById(userTypeId).pipe(
      tap((userType) => this.userTypeCache.set(userTypeId, userType))
    );
  }

  clearUserTypeCache(): void {
    this.userTypeCache.clear();
  }

  clearUserTypeFromCache(userTypeId: number): void {
    this.userTypeCache.delete(userTypeId);
  }

  getUserTypeByUserType(userType: string): Observable<UserType> {
    return this.request(
      'GET',
      '/getUserTypeByUserType',
      null,
      { userType: userType },
      'json'
    );
  }
}
