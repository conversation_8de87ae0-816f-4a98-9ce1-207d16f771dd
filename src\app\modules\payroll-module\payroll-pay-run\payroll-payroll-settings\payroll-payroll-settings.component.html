<app-payroll-nevigation></app-payroll-nevigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <div class="actions sub-container">
    <h2>Payroll Settings</h2>
  </div>
  <form #payrollForm="ngForm">
    <div class="account-settings">
      <!-- Bank Account -->

      <div class="form-row">
      <div class="form-group">
        <label for="bank-account">Bank Account</label>
        <select
          id="bank-account"
          name="bankAccount"
          class="form-control"
          [(ngModel)]="payrollConfig.bankAccount"
          required
          #bankAccount="ngModel"
          (ngModelChange)="onGlAccountChange()"
        >
          <option value="" disabled selected>Select a Bank Account</option>
          <ng-container *ngFor="let account of glAccounts">
            <option [ngValue]="account.ledgerAccountCode">
              {{ account.ledgerAccountName }} : {{ account.ledgerAccountCode }}
            </option>
          </ng-container>
        </select>
        <div *ngIf="bankAccount.invalid && bankAccount.touched" class="text-danger">
          Bank Account is required.
        </div>
      </div>

      <!-- PAYG Liability -->
      <div class="form-group">
        <label for="payg-liability">PAYG Liability Account</label>
        <select
          id="payg-liability"
          name="paygLiabilityAccount"
          class="form-control"
          [(ngModel)]="payrollConfig.paygLiabilityAccount"
          required
          #paygLiability="ngModel"
          (ngModelChange)="onGlAccountChangePaygLiability()"
        >
          <option value="" disabled selected>Select a PAYG Liability Account</option>
          <ng-container *ngFor="let account of glAccounts">
            <option  [ngValue]="account.ledgerAccountCode">
              {{ account.ledgerAccountName }} : {{ account.ledgerAccountCode }}
            </option>
          </ng-container>
        </select>
        <div *ngIf="paygLiability.invalid && paygLiability.touched" class="text-danger">
          PAYG Liability Account is required.
        </div>
      </div>
    </div>

    
      <!-- Wages Expense -->
      <div class="form-row">
      <div class="form-group">
        <label for="wages-expense">Wages Expense Account</label>
        <select
          id="wages-expense"
          name="wagesExpenseAccount"
          class="form-control"
          [(ngModel)]="payrollConfig.wagesExpenseAccount"
          required
          #wagesExpense="ngModel"
          (ngModelChange)="onGlAccountChangeWagesExpens()"
        >
          <option value="" disabled selected>Select a Wages Expense Account</option>
          <ng-container *ngFor="let account of glAccounts">
            <option *ngIf="account.coaHeaderId.accountHeaderType === 'Expenses'" [ngValue]="account.ledgerAccountCode">
              {{ account.ledgerAccountName }} : {{ account.ledgerAccountCode }}
            </option>
          </ng-container>
        </select>
        <div *ngIf="wagesExpense.invalid && wagesExpense.touched" class="text-danger">
          Wages Expense Account is required.
        </div>
      </div>

      <!-- Wages Payable -->
      <div class="form-group">
        <label for="wages-payable">Wages Payable Account</label>
        <select
          id="wages-payable"
          name="wagesPayableAccount"
          class="form-control"
          [(ngModel)]="payrollConfig.wagesPayableAccount"
          required
          #wagesPayable="ngModel"
          (ngModelChange)="onGlAccountChangeWagesPayable()"
        >
          <option value="" disabled selected>Select a Wages Payable Account</option>
          <ng-container *ngFor="let account of glAccounts">
            <option [ngValue]="account.ledgerAccountCode">
              {{ account.ledgerAccountName }} : {{ account.ledgerAccountCode }}
            </option>
          </ng-container>
        </select>
        <div *ngIf="wagesPayable.invalid && wagesPayable.touched" class="text-danger">
          Wages Payable Account is required.
        </div>
      </div>

      </div>

      <!-- Superannuation Liability -->
      <div class="form-row">
      <div class="form-group">
        <label for="super-liability">Superannuation Liability Account</label>
        <select
          id="super-liability"
          name="superannuationLiabilityAccount"
          class="form-control"
          [(ngModel)]="payrollConfig.superannuationLiabilityAccount"
          required
          #superLiability="ngModel"
          (ngModelChange)="onGlAccountChangeSuperannuationLiability()"
        >
          <option value="" disabled selected>Select a Superannuation Liability Account</option>
          <ng-container *ngFor="let account of glAccounts">
            <option [ngValue]="account.ledgerAccountCode">
              {{ account.ledgerAccountName }} : {{ account.ledgerAccountCode }}
            </option>
          </ng-container>
        </select>
        <div *ngIf="superLiability.invalid && superLiability.touched" class="text-danger">
          Superannuation Liability Account is required.
        </div>
      </div>

      <!-- Superannuation Expense -->
      <div class="form-group">
        <label for="super-expense">Superannuation Expense Account</label>
        <select
          id="super-expense"
          name="superannuationExpenseAccount"
          class="form-control"
          [(ngModel)]="payrollConfig.superannuationExpenseAccount"
          required
          #superExpense="ngModel"
          (ngModelChange)="onGlAccountChangeSuperannuationExpense()"
        >
          <option value="" disabled selected>Select a Superannuation Expense Account</option>
          <ng-container *ngFor="let account of glAccounts">
            <option *ngIf="account.coaHeaderId.accountHeaderType === 'Expenses'" [ngValue]="account.ledgerAccountCode">
              {{ account.ledgerAccountName }} : {{ account.ledgerAccountCode }}
            </option>
          </ng-container>
        </select>
        <div *ngIf="superExpense.invalid && superExpense.touched" class="text-danger">
          Superannuation Expense Account is required.
        </div>
      </div>

      </div>
    </div>

    <!-- Footer Buttons -->
    <div class="emp-open-footer-wrapper">
      <div class="right-buttons">
        <!-- Add Button -->
        <button
          type="submit"
          class="btn btn-primary"
          *ngIf="!isEditing"
          [disabled]="payrollForm.invalid"
          (click)="onAddConfig()"
        >
          Add
        </button>

        <!-- Update Button -->
        <button
          type="submit"
          class="btn btn-primary"
          *ngIf="isEditing"
          [disabled]="payrollForm.pristine"
          (click)="updatePayrollConfig()"
        >
          Update
        </button>
      </div>
    </div>
  </form>
</div>
