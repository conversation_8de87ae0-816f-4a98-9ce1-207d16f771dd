import { Component, ViewChild } from '@angular/core';
import { InvoiceService } from '../invoice.service';
import { EntityService } from '../../entity/entity.service';
import { Entity } from '../../entity/entity';
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { InvoiceHead, PaymentReceiptsDetails, PaymentReceiptsHead } from '../invoice';
import { BusinessPartner } from '../../business-partner/business-partner';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
// Import Bootstrap's Modal class from the bootstrap package
import { Modal } from 'bootstrap';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';

@Component({
  selector: 'app-create-payment-receipt-user',
  templateUrl: './create-payment-receipt-user.component.html',
  styleUrls: ['./create-payment-receipt-user.component.css']
})
export class CreatePaymentReceiptUserComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  invoiceHead: InvoiceHead = new InvoiceHead();
  paymentReceiptsHead: PaymentReceiptsHead = new PaymentReceiptsHead();
  details: PaymentReceiptsDetails = new PaymentReceiptsDetails();
  newPaymentNumber: string = '';
  businessEntityId: number = 0;
  lastPaymentNumber: string = '';
  businessEntity: Entity = new Entity();
  selectedInvoices: any[] = [];
  totalPaidAmount: number = 0;
  private debounceTimer: any = null;
  customers: BusinessPartner[] = [];
  invoiceNumberInput: any;
  enteredInvoices: any[] = [];
  isCustomerSelected = false;
  activeRowIndex: number | null = null;
  searchCriteria = {
    fromDate: '',
    toDate: '',

  };

  filteredInvoices: any[] = [];
  isInvalidInvoice: boolean = true; // Initially disable the save payment receipt button

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private entityService: EntityService,
    private invoiceService: InvoiceService,
    private businessPartnerService: BusinessPartnerService,

  ) { }


  ngOnInit(): void {
    this.loadCustomers();
    this.initializeEmptyInvoiceRows();
    this.getBusinessEntityById();

    if (!this.paymentReceiptsHead.documentDate) {
      this.paymentReceiptsHead.documentDate = this.getTodayDate();
    }
  }

  initializeEmptyInvoiceRows(): void {
    this.enteredInvoices = [{ invoiceNumber: '', postingDate: '', grandTotal: 0, balanceAmount: 0, creditAmount: 0 }];
  }

  // Utility function to get today's date in 'YYYY-MM-DD' format
  getTodayDate(): string {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const dd = String(today.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  loadCustomers() {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.businessPartnerService.getCustomerListByEntity(entityId).subscribe(
      (customers: BusinessPartner[]) => {
        this.customers = customers;
      },
      (error: HttpErrorResponse) => {

      }
    );
  }

  onCustomerChange(event: any) {
    const selectedCustomerId = +event.target.value; // Convert to number using the unary + operator
    const selectedCustomer = this.customers.find(cust => cust.businessPartnerId === selectedCustomerId);

    if (selectedCustomer) {
      this.paymentReceiptsHead.customerName = selectedCustomer.bpName || '';
      this.paymentReceiptsHead.businessPartnerId = selectedCustomer.businessPartnerId; // Correctly set the businessPartnerId here
      this.isCustomerSelected = this.paymentReceiptsHead.businessPartnerId !== null;
    } else {
      // Handle case when no customer is found
      this.paymentReceiptsHead.customerName = '';
      this.paymentReceiptsHead.businessPartnerId = 0;
    }
  }

  getBusinessEntityById() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '');
    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
      (data) => {
        this.businessEntity = data;
        this.lastPaymentNumber = this.incrementPaymentNumber(
          this.businessEntity.paymentReceiptNumber
        );
        this.paymentReceiptsHead.paymentNumber = this.lastPaymentNumber;
      },
      (error) => console.error(error)
    );
  }

  incrementPaymentNumber(paymentNumber: string): string {
    if (!paymentNumber) {
      return 'P000001';
    }
    const prefix = paymentNumber.charAt(0);
    const numericPart = paymentNumber.slice(1);
    const incrementedNumber = (Number(numericPart) + 1).toString().padStart(numericPart.length, '0');
    return prefix + incrementedNumber;
  }

  getInvoiceNewBalance(invoice: any): number {
    const balanceAmount = invoice.balanceAmount || 0;
    const creditAmount = invoice.creditAmount || 0;
    return balanceAmount - creditAmount;
  }

  onCancel() {
    this.router.navigate(['/payment-receipt']);
  }

  preventEnter(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault(); // Prevent form submission on "Enter"
    }
  }

  validatePaidAmount(invoice: any) {
    if (invoice.creditAmount < 0) {
      Swal.fire({
        title: 'Invalid Input',
        text: 'Paid Amount cannot be negative. It has been reset to 0.',
        icon: 'warning',
        confirmButtonText: 'OK',
      });

      // Reset the credit amount to 0
      invoice.creditAmount = 0;
    }
  }

  onSubmit() {
     // Check if all required fields are filled
  if (!this.paymentReceiptsHead.documentDate ||
    !this.paymentReceiptsHead.customerName ||
    this.enteredInvoices.length === 0 ||
    this.enteredInvoices.some(invoice => !invoice.invoiceNumber)) {
  Swal.fire({
    title: 'Incomplete Form',
    text: 'Please fill in all required fields before submitting.',
    icon: 'warning',
    confirmButtonText: 'OK',
    confirmButtonColor: '#007bff'
  });
  return; // Stop the submission if validation fails
}
    // Pass the invoices to the saveQuotation method
    this.paymentReceiptsHead.totalPaidAmount = this.getTotalPaidAmountEntered();
    this.paymentReceiptsHead.details = this.enteredInvoices.map(invoice => {
      let detail = new PaymentReceiptsDetails();
      detail.invoiceHead = invoice.invoiceHeadId;
      detail.invoiceNumber = invoice.invoiceNumber;
      detail.grandTotal = invoice.grandTotal;
      detail.balanceAmount = invoice.balanceAmount;
      detail.paidAmount = invoice.creditAmount;
      detail.ledgerAccountName = "Income";
      detail.tax = invoice.grandTotal > 0 ? (invoice.totalGst / invoice.grandTotal * invoice.creditAmount) : 0;
      return detail;
    });

    this.savePaymentReciept(this.enteredInvoices);
  }


  savePaymentReciept(invoices: any[]) {

    // Ensure document date is set, default to today if not provided
    if (!this.paymentReceiptsHead.documentDate) {
      this.paymentReceiptsHead.documentDate = this.getTodayDate();
    }
    // Iterate over each invoice to validate its paid amount
    const invalidInvoices = invoices.filter(invoice => invoice.creditAmount > invoice.balanceAmount);

    if (invalidInvoices.length > 0) {
      Swal.fire({
        title: 'Paid Amount Exceeds Balance!',
        text: 'One or more invoices have Paid amounts greater than their balances. Do you want to continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, continue',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
      }).then((result) => {
        if (result.isConfirmed) {
          // If user confirms, set flag and continue
          invalidInvoices.forEach(invoice => {
            invoice.isCreditExceeded = true;
          });
          this.checkZeroPaidAmount(invoices);
        } else if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData('Paid Amount Exceeds Balance!');
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });

    } else {
      this.checkZeroPaidAmount(invoices);
    }
  }


  checkZeroPaidAmount(invoices: any[]) {
    // Check for invoices where creditAmount is 0
    const zeroCreditInvoices = invoices.filter(invoice => invoice.creditAmount === 0);

    if (zeroCreditInvoices.length > 0) {
      // Show SweetAlert for creditAmount === 0
      Swal.fire({
        title: 'Paid Amount is Zero',
        text: 'One or more invoices have a Paid Amount of 0. Do you want to proceed?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, proceed',
        cancelButtonText: 'No, edit',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
      }).then((result) => {
        if (result.isConfirmed) {
          this.proceedToSave(); // Proceed to save
        } else {
          // Allow user to edit credit amounts
          return;
        }
      });
    } else {
      // Proceed to save if no issues
      this.proceedToSave();
    }
  }

  proceedToSave() {
    // Assuming the selectedInvoices array is already populated and validated
    if (this.enteredInvoices.length > 0) {
      // Set the businessPartnerId from the first invoice, as all invoices have the same customer
      this.paymentReceiptsHead.invoiceNumbers = this.enteredInvoices.map(invoice => invoice.invoiceNumber);
    }

    this.paymentReceiptsHead.documentStatus = 'Open';
    this.paymentReceiptsHead.userId = +(localStorage.getItem('userid') + "");
    this.paymentReceiptsHead.entityId = +(localStorage.getItem('entityId') + "");

    // Save the Payment Receipt
    this.invoiceService.savePaymentReceiptsHead(this.paymentReceiptsHead).subscribe(
      (response: any) => {
        this.updatePaymentReceiptNumber();

        // Update the balance amount for each selected invoice
        this.enteredInvoices.forEach(invoice => {
          const updatePromises = this.enteredInvoices.map(invoice => {
            const newBalanceAmount = invoice.balanceAmount - invoice.creditAmount;
            return this.updateInvoiceBalancePromise(invoice.invoiceHeadId, newBalanceAmount);
          });

          Promise.all(updatePromises).then(() => {
            Swal.fire({
              title: 'Success!',
              text: 'The Payment Receipt has been successfully saved.',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            }).then(() => this.router.navigate(['/payment-receipt']));
          }).catch(error => {
            console.error('Error updating invoice balances', error);
            Swal.fire({
              title: 'Error!',
              text: 'Failed to update invoice balances.',
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#be0032',
              cancelButtonColor: '#007bff',
              showCancelButton: true,
              }).then((result) => {
                if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                  if (this.chatBotComponent) {
                    Swal.fire({
                      title: 'Processing...',
                      text: 'Please wait while Chimp processes your request.',
                      allowOutsideClick: false,
                      didOpen: () => {
                        Swal.showLoading();
                        this.chatBotComponent.setInputData('Failed to update invoice balances.');
                        this.chatBotComponent.responseReceived.subscribe(response => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound()
                        });
                      },
                    });
                  } else {
                    console.error('ChatBotComponent is not available.');
                  }
                }
            });
          });
        },
          (error: HttpErrorResponse) => {
            Swal.fire({
              title: 'Error!',
              text: 'Unable to save the Payment Receipt. Please try again.',
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#be0032',
              cancelButtonColor: '#007bff',
              showCancelButton: true,
              }).then((result) => {
                if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                  if (this.chatBotComponent) {
                    Swal.fire({
                      title: 'Processing...',
                      text: 'Please wait while Chimp processes your request.',
                      allowOutsideClick: false,
                      didOpen: () => {
                        Swal.showLoading();
                        this.chatBotComponent.setInputData('Unable to save the Payment Receipt. Please try again.');
                        this.chatBotComponent.responseReceived.subscribe(response => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound()
                        });
                      },
                    });
                  } else {
                    console.error('ChatBotComponent is not available.');
                  }
                }
            });
          }
        );
      }
    );
  }


  updateInvoiceBalancePromise(invoiceHeadId: number, newBalanceAmount: number): Promise<any> {
  return new Promise((resolve, reject) => {
    this.invoiceService.updateInvoiceBalance(invoiceHeadId, newBalanceAmount).subscribe(
      (response) => {
        resolve(response);
      },
      (error) => {
        // Check if it's a parse error but response status is 200
        if (error.status === 200 && error.message?.includes('Http failure during parsing')) {
          console.warn(`Parsing error ignored for invoice ${invoiceHeadId}:`, error);
          resolve({ message: 'Parsed manually, assuming success.' });
        } else {
          console.error(`Failed to update balance for invoice ${invoiceHeadId}`, error);
          reject(error);
        }
      }
    );
  });
}


  updatePaymentReceiptNumber() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '0');
    this.businessEntity.paymentReceiptNumber = this.paymentReceiptsHead.paymentNumber;
    this.entityService.updatePaymentReceiptNumber(this.businessEntity, this.businessEntityId).subscribe(
      (data) => {
      },
      (error) => {
        console.error(error);
      }
    );
  }


  onInvoiceNumberChange(invoiceNumber: string, index: number): void {
    // Clear any existing debounce timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // Set a new debounce timer to delay the API call
    this.debounceTimer = setTimeout(() => {
      // Call the method to fetch the invoice details
      this.fetchInvoiceDetailsOnEnter(invoiceNumber, index);
    }, 300); // 300ms delay
  }


  fetchInvoiceDetailsOnEnter(invoiceNumber: string, index: number): void {
    if (!invoiceNumber) return;
    if (!this.paymentReceiptsHead.customerName) {
      Swal.fire({
        title: 'Customer Not Selected',
        text: 'Please select a customer first.',
        icon: 'warning',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
        showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Customer Not Selected');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
      });
      return; // Exit the function if no customer is selected
    }

      // Check if the invoice number already exists in the enteredInvoices array
  const duplicateInvoice = this.enteredInvoices.some((invoice, i) => invoice.invoiceNumber === invoiceNumber && i !== index);

  if (duplicateInvoice) {
    Swal.fire({
      title: 'Duplicate Invoice',
      text: 'This invoice number has already been entered.',
      icon: 'warning',
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
      showCancelButton: true,
      }).then((result) => {
        if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData('Duplicate Invoice');
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound()
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
    });
    return; // Exit the function to prevent adding the duplicate invoice
  }
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.invoiceService.getInvoiceDetailsByNumberEn(invoiceNumber,entityId).subscribe(
      (invoiceData: any) => {
        if (invoiceData) {
          // Check if the customer name of the invoice matches the selected customer name
          if (invoiceData.reference === this.paymentReceiptsHead.customerName) {

            // Update the entered invoice data with the fetched details, including invoiceHeadId
            this.enteredInvoices[index] = {
              ...this.enteredInvoices[index],
              invoiceHeadId: invoiceData.invoiceHeadId, // Make sure to capture the invoiceHeadId
              postingDate: invoiceData.postingDate,
              grandTotal: invoiceData.grandTotal,
              balanceAmount: invoiceData.balanceAmount,
              creditAmount: 0  // Initialize with 0, can be changed later by user
            };
            this.isInvalidInvoice = false; // Enable button when invoice is valid
          } else {
            // Show a SweetAlert warning if customer names do not match
            Swal.fire({
              title: 'Customer Mismatch',
              text: 'This invoice does not belong to the selected customer.',
              icon: 'warning',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#ff7e5f',
              cancelButtonColor: '#be0032',
              showCancelButton: true,
              }).then((result) => {
                if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                  if (this.chatBotComponent) {
                    Swal.fire({
                      title: 'Processing...',
                      text: 'Please wait while Chimp processes your request.',
                      allowOutsideClick: false,
                      didOpen: () => {
                        Swal.showLoading();
                        this.chatBotComponent.setInputData('Customer Mismatch');
                        this.chatBotComponent.responseReceived.subscribe(response => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound()
                        });
                      },
                    });
                  } else {
                    console.error('ChatBotComponent is not available.');
                  }
                }
            });
            this.isInvalidInvoice = true; // Disable button if invoice doesn't belong to customer
          }
        } else {
          console.error('Invoice not found');
          this.isInvalidInvoice = true; // Disable button if invoice not found
        }
      },
      (error) => {
        console.error('Error fetching invoice details:', error);
        this.isInvalidInvoice = true; // Disable button on error
      }
    );
  }



  removeItem(index: number) {
    this.enteredInvoices.splice(index, 1); // Remove the item at the specified index

    this.getTotalPaidAmountEntered();
  }


  getTotalPaidAmountEntered(): number {
    return this.enteredInvoices.reduce((total, invoice) => total + (invoice.creditAmount || 0), 0);
  }


  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }


  addInvoiceRow(): void {
    this.enteredInvoices.push({
      invoiceNumber: '',
      postingDate: '',
      grandTotal: 0,
      balanceAmount: 0,
      creditAmount: 0
    });
  }


  searchInvoices() {
    if (!this.paymentReceiptsHead.customerName) {
      // this.closeSearchPopup();
      Swal.fire({
        title: 'Customer Not Selected',
        text: 'Please select a customer first.',
        icon: 'warning',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
        showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Customer Not Selected');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
      });
      this.isInvalidInvoice = true; // Disable button if no customer
      return; // Exit the function if no customer is selected
    }
    if (!this.searchCriteria.fromDate || !this.searchCriteria.toDate) {
      Swal.fire({
        title: 'Invalid Search',
        text: 'Please select a Date range.',
        icon: 'warning',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
        showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Invalid Search');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
      });
      this.isInvalidInvoice = true; // Disable button if invalid range
      return;
    }

    this.invoiceService.getInvoicesByDateRangeAndCustomer(
      this.searchCriteria.fromDate,
      this.searchCriteria.toDate,
      this.paymentReceiptsHead.businessPartnerId,
    ).subscribe(
      (invoices: any[]) => {
        this.filteredInvoices = invoices;
         // Check if there are no results and display a message
         if (this.filteredInvoices.length === 0) {
          Swal.fire({
            title: 'No Invoices Found',
            text: 'No invoices found within the selected date range.',
            icon: 'info',
            confirmButtonText: 'OK',
            confirmButtonColor: '#007bff'
          });
          this.isInvalidInvoice = true; // Disable button if invalid no invoices found
        }
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching invoices:', error);
        this.isInvalidInvoice = true; // Disable button on error
      }
    );
  }


  // This method is triggered when the search button is clicked
  onSearchInvoiceClick(index: number): void {
    this.activeRowIndex = index;  // Store the index of the row
  }



  selectInvoice(selectedInvoice: any): void {


      // Check if the invoice number already exists in enteredInvoices
  const duplicateInvoice = this.enteredInvoices.some(invoice => invoice.invoiceNumber === selectedInvoice.invoiceNumber);

  if (duplicateInvoice) {
    Swal.fire({
      title: 'Duplicate Invoice',
      text: 'This invoice number has already been entered.',
      icon: 'warning',
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
      showCancelButton: true,
      }).then((result) => {
        if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData('Duplicate Invoice');
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound()
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
    });
    this.isInvalidInvoice = true; // Disable button if duplicate
    return;  // Exit the function to prevent adding the duplicate invoice
  }

    if (this.activeRowIndex !== null) {
      this.enteredInvoices[this.activeRowIndex].invoiceNumber = selectedInvoice.invoiceNumber;
      this.enteredInvoices[this.activeRowIndex].grandTotal = selectedInvoice.grandTotal;
      this.enteredInvoices[this.activeRowIndex].balanceAmount = selectedInvoice.balanceAmount;
      this.enteredInvoices[this.activeRowIndex].invoiceHeadId = selectedInvoice.invoiceHeadId;  // Ensure this line is present
      this.enteredInvoices[this.activeRowIndex].creditAmount = 0;  // Reset the paid amount for the new entry

      this.activeRowIndex = null;

       // Clear the filteredInvoices list
      this.filteredInvoices = [];
      // this.closeSearchPopup();
      this.isInvalidInvoice = false; // Enable button when selection is valid
    }
  }

  isModalOpen = false;


  /*openModal(): void {
    this.isModalOpen = true; // Show the modal
  }

  closeSearchPopup(): void {
    this.isModalOpen = false; // Hide the modal
  }

}*/

openModal(): void {
  this.isModalOpen = true; // Show the modal
  setTimeout(() => {
    const modalElement = document.getElementById('invoiceSearchModal');
    if (modalElement) {
      const bootstrapModal = new window.bootstrap.Modal(modalElement);
      bootstrapModal.show();  // Bootstrap modal JavaScript API to open modal
    }
  }, 0);
}

closeSearchPopup(): void {
  this.isModalOpen = false; // Hide the modal via Angular

  // Also, close the modal using Bootstrap's JavaScript API
  const modalElement = document.getElementById('invoiceSearchModal');
  if (modalElement) {
    const bootstrapModal = window.bootstrap.Modal.getInstance(modalElement); // Get the existing modal instance
    if (bootstrapModal) {
      bootstrapModal.hide();  // Close the modal programmatically
    }
  }
}
playLoadingSound() {
  let audio = new Audio();
  audio.src = "../assets/google_chat.mp3";
  audio.load();
  audio.play();
}

stopLoadingSound(): void {
  if (this.audio) {
    this.audio.pause();
    this.audio.currentTime = 0;
  }
}
}
