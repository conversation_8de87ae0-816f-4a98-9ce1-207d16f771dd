import { SalesItem } from '../quotation/quotation';

export class InvoiceHead {
  invoiceHeadId: number = 0;
  businessPartnerId: any = 0;
  entityId: number = 0;
  userId: number = 0;
  invoiceNumber: string = '';
  date: string = '';
  postingDate: string = '';
  reference: string = '';
  paidDate: string = '';
  dueDate: string = '';
  paidInvoices: string = '';
  recipient: string = '';
  paidInvoiceDate: string = '';
  totalDiscAmount: number = 0;
  totalGst: number = 0.0;
  grandTotal: number = 0.0;
  paidTotal: number = 0.0;
  totalAmount: number = 0.0;
  balanceDue: number = 0.0;
  invoiceStatus: string = '';
  transactionDate: string = '';
  note: string = '';
  originalInvoiceNumber: string = '';
  balanceAmount: number = 0.0;
  overdueDate: string = '';
  overdueAmount: number = 0.0;
  entityTradingNameId: any = 0;
  invoiceDetails: InvoiceDetail[] = [];
  matched?: 'green' | 'yellow' |'orange'| false;
  partialMatched?: boolean;
  matchStatus: string = '';
  reconcileStatus: string = '';
  pendingBalance: number = 0.0;
  recAmount: number = 0.0;
  deletedItemIds: number[] = [];
  selected: boolean = false;
  fileBase64?: string;
  stripeInvoiceId: string = '';
  stripeAccountId: string = '';
  stripePaymentLink: string = '';
  paymentNo: string = '';
  createdBy: string= '';
  isLocked?: boolean;
}

export class InvoiceDetail {
  invoiceDetailId?: number = 0;
  salesItem: SalesItem = new SalesItem();
  invoiceHead?: InvoiceHead = new InvoiceHead();
  taxCategoryId: number = 0;
  invoiceNumber: string = '';
  quantity: number = 0;
  description: string = '';
  unitPrice: number = 0.0;
  discount: number = 0.0;
  tax: number = 0.0;
  amount: number = 0.0;
  notes: string = '';
  subTotal: number = 0.0;
  totalDiscountPercentage: number = 0.0;
  discountType?: string = '';
  taxApplicability?: boolean = false;
  balance?: number = 0.0;
  coaLedgerAccountId: number = 0;
  ledgerAccountName: string = '';
  ledgerAccountCode: string = '';
}

export class InvoiceLog {
  invoiceLogId: number = 0;
  entityId: number = 0;
  userId: number = 0;
  invoiceHead: InvoiceHead = new InvoiceHead();
  logDate: string = '';
  logTime: string = '';
  note: string = '';
}

export class PaymentReceiptsHead {
  paymentReceiptId: number = 0;
  details: PaymentReceiptsDetails[] = [];
  businessPartnerId: any = 0;
  entityId: number = 0;
  userId: number = 0;
  paymentNumber: string = '';
  documentDate: string = '';
  balanceDue: number = 0;
  documentStatus: string = '';
  remarks: string = '';
  totalPaidAmount: number = 0;
  customerName: string = '';
  invoiceNumbers: string[] = [];
  selected: boolean = false;
	recStatus:string='';
	matchStatus:string='Pending';
	recAmount:number = 0;
  matched:'green' | 'yellow' | 'orange' | false = false;
  isLocked?: boolean;
}

export class PaymentReceiptsDetails {
  paymentReceiptsDetailsId: number = 0;
  paymentReceiptsHead: PaymentReceiptsHead = new PaymentReceiptsHead();
  invoiceHead: number = 0;
  invoiceNumber: string = '';
  grandTotal: number = 0.0;
  balanceAmount: number = 0.0;
  paidAmount: number = 0.0;
  tax: number = 0.0;
  GlAccountIncome: number = 0.0;
  ledgerAccountName: string = '';
}

export interface SaveInvoiceHeadDTO {
  invoiceHead: InvoiceHead;
  autoPaymentReceipt: boolean;
}
