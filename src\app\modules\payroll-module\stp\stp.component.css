/* General Styles */

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

h2 {
  font-size: 24px;
  font-weight: bold;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column; /* Stack children vertically */
  gap: 20px; /* Adds 20px gap between rows */
}
.actions {
  display: flex;
  align-items: center;
}

/* Payroll Settings Heading */
.actions h2 {
  flex: 1;
  font-family: Inter, sans-serif;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
  margin: 0;
}

/* Sub-container Styles for Heading */
.sub-container {
  background-color: #ffffff;
  padding: 20px;
  border: 1px solid #4262ff;
  border-radius: 10px;
  /* margin-bottom: 20px; */
}

.sub-container h2 {
  font-size: 24px;
  font-weight: bold;
}

.disabled {
  pointer-events: none;
  opacity: 0.6;
  cursor: not-allowed;
}

/* Payroll Head Section */
.payroll-head {
  display: flex;
  align-items: center;
  gap: 20px;
}

.payroll-head-icon {
  width: 10%;
  display: flex;
  justify-content: center;
  font-size: 60px;
  color: #4262ff;
}

.payroll-head-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-weight: bold;
}

.payroll-head-content strong {
  font-size: 16px;
  color: #333;
}

.payroll-head-content span {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.payroll-head-content a {
  color: #4262ff;
  text-decoration: underline;
}

.payroll-info {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  background-color: transparent;
  border-radius: 10px;
}

.PayRun-Tabs {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  background-color: transparent;
  border-radius: 10px;
}

.nav-tabs {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.nav-tabs .nav-link {
  padding: 10px 39.5px;
  color: #4262ff;
  font-weight: 500;
  background-color: white;
  cursor: pointer;
  border-radius: 0 0 0 0;
}

.nav-tabs .nav-link.active {
  color: black;
  background-color: #f1eeee;
  border-radius: 0 0 0 0;
  border-bottom: none;
}

/* Pay Calendars Section */

.pay-calendar-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: white;
  margin-bottom: 5px;
  border-radius: 10px;
}

.pay-calendar-head strong {
  font-size: 18px;
  color: #333;
}

.btn-group .btn-primary {
  background: white;
  border: 1px solid #4262ff;
  color: #4262ff;
  font-weight: bold;
  border-radius: 10px;
  padding: 10px 20px;
}

.btn-group .dropdown-menu {
  width: 100%;
  padding: 10px;
  border-radius: 10px;
  font-weight: bold;
  overflow: hidden;
}

.dropdown-item {
  font-weight: bold;
  color: #333;
  padding: 8px 15px;
}

.dropdown-item:hover {
  background-color: #4262ff;
  color: white;
  font-weight: bold;
}

.pay-calendar-table {
  background-color: white;
  border-radius: 10px;
  padding: 20px 20px;
}

/* Table Styles */
.pay-calendar-table table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 10px;
}

.pay-calendar-table th,
.pay-calendar-table td {
  text-align: left;
  padding: 20px 20px;
}

.pay-calendar-table th {
  background-color: white;
  font-weight: bold;
  color: #333;
}

.pay-calendar-table tbody tr {
  background-color: white;
  border-bottom: 1px solid #ddd;
}

.pay-calendar-table tbody tr:hover {
  background-color: #f1f1f1;
}

/* History Section */
.payroll-history {
  margin-top: 20px;
  background-color: white;
  border: 1px solid #4262ff;
  border-radius: 10px;
  padding: 20px 20px;
}

.payroll-history h3 {
  display: flex;
  align-items: center;
}

.payroll-history .btn-toggle {
  background: none;
  border: none;
  font-size: 18px;
  color: black;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.payroll-history .btn-toggle i {
  margin-left: 10px;
  font-weight: bold;
  transition: transform 0.3s;
}

.collapse:not(.show) + h3 .btn-toggle i {
  transform: rotate(0deg);
}

.collapse.show + h3 .btn-toggle i {
  transform: rotate(180deg);
}

.collapse {
  background-color: white;
  border: 1px solid white;
  border-radius: 5px;
}

.collapse p {
  font-size: 16px;
  color: #000000;
}

/* Pay-items */
.pay-items-section {
  display: flex;
  background-color: white;
  border-radius: 10px;
}

.pay-items-sections {
  display: flex;
  background-color: white;
  border-radius: 10px;
  margin-inline: 12%;
}

.pay-items-sections-sub {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 10px;
  margin-inline: 12%;
}

.pay-item-2 {
  display: flex;
  flex-direction: column;
  background-color: transparent;
  border-radius: 10px;
}

.side-bar {
  width: 20%; /* Set sidebar width */
  background-color: #ffffff;
  border-radius: 10px 0 0 10px;
  border-right: 1px solid #ddd;
  padding: 10 0px; /* Add some padding */
}

.nav_pay_item {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.nav_pay_item li {
  margin-bottom: 10px;
}

.nav_pay_item a {
  text-decoration: none;
  width: 100%;
  color: #333;
  font-weight: 500;
  padding: 10px;
  display: block;
  border-radius: 10px;
}

.nav_pay_item a.active {
  color: black;
  font-weight: bold;
  border-left: 3px solid #4262ff;
  background-color: #dfe4ff;
}

.nav_pay_item a:hover {
  background-color: #d7dbf5;
}

.pay_item-content {
  border-radius: 0 10px 10px 0;
  width: 80%;
}

.Earnings,
.Deductions,
.Reimbursements,
.Leave {
  width: 100%; /* Adjust width to match the sidebar */
  padding: 20px;
  background-color: white;
}

.Leave-form-3,
.Leave-form-final {
  width: 100%; /* Adjust width to match the sidebar */
  padding: 20px;
  background-color: transparent;
}

.pay_item_head {
  display: flex;
  padding: 10px 0;
  border-radius: 0 10px 10px 0;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pay_item_head h2 {
  font-size: 24px;
  font-weight: bold;
}

.table-responsive {
  /* margin-top: 10px; */
  border-radius: none;
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table thead th {
  background-color: #ffffff;
  font-weight: bold;
  color: #333;
  padding: 10px;
  text-align: center;
}

.table tbody tr {
  background-color: white;
  border-bottom: 1px solid #ddd;
}

.table tbody tr:hover {
  background-color: #f1f1f1;
}

.table tbody td {
  padding: 10px;
  color: #555;
  text-align: center;
}

.btn-group .dropdown-menu {
  padding: 0 40px;
  border-radius: 10px;
  overflow: hidden;
  font-weight: bold;
}

.dropdown-item:hover {
  background-color: #4262ff;
  color: white;
}

.btn-secondary {
  background: transparent;
  color: #4262ff;
  border: 1px solid #4262ff;
  font-size: 18px;
  cursor: pointer;
  padding: 5px 40px;
  border-radius: 12px;
  font-weight: bold;
}

.btn-secondary:hover {
  background-color: #4262ff;
  color: white;
}

.btn-primary {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  border: 1px solid #4262ff;
  font-size: 18px;
  cursor: pointer;
  padding: 5px 40px;
  border-radius: 12px;
  font-weight: bold;
}

.btn-primary:hover {
  background-color: linear-gradient(to right, #512ca2, #4262ff);
}

.pay-item-one {
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
}

.pay-item-two {
  width: 100%;
  display: flex;
  margin-bottom: 20px;
}

.pay-item-col1 {
  display: flex;
  flex-direction: column;
  width: 30%;
  border-right: 1px solid #666666;
}

.pay-item-col2 {
  display: flex;
  flex-direction: column;
  padding-left: 10%;
  width: 70%;
}
.pay-item-col3 {
  display: flex;
  margin-right: 25px;
  width: 70%;
}

.custom-search {
  background-color: #c2c2c2;
  border-radius: 10px;
}
.filter-buttons {
  width: 30%;
  display: flex;
  align-items: center;
  gap: 25px;
}

.pay-item-four {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  border: 2px solid #c7c7c7;
  border-radius: 10px;
  margin-top: 20px;
  margin-bottom: 20px;
  background-color: white;
}

.pro-pic {
  width: 50px;
  height: 50px;
  background-color: #e0e0e0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  background-color: #4262ff;
}

.User-Details {
  padding-top: 15px;
  flex-grow: 1;
  margin-left: 15px;
}

.User-Details h5 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.User-Details small {
  size: 12px;
  color: gray;
}

.approve-btn button {
  color: #4262ff;
  font-weight: bold;
  border: 2px solid #4262ff;
  background: transparent;
  border-radius: 10px;
  padding: 5px 15px;
}

.app-icon {
  margin-left: 10px;
}

.app-icon i {
  font-size: 1.2rem;
  color: #555;
}

.leave-emp {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.emp-name,
.salary-earnings,
.date-next-payment {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 5px 15px;
  border: 1px solid #ccc;
}

.employee-name {
  font-size: 18px;
  font-weight: bold;
  color: #4262ff;
  margin: 0;
}

.view-btn {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  background-color: #f2f2f2;
  cursor: not-allowed;
}

.next-payment-date {
  font-size: 16px;
  font-weight: bold;
  width: 100%;
}

/* modal  */
.modal-content {
  width: 100%; /* Ensure content fills the dialog width */
  padding: 20px;
  border-radius: 10px; /* Rounded corners */
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
}

.modal-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between; /* Space between title and close button */
}

.modal-head {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between; /* Space between title and close button */
}

.ot-modal-header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between; /* Space between title and close button */
}

.modal-header .modal-title {
  font-family: "Inter", sans-serif;
  font-weight: bold;
  font-size: 20px;
  color: black; /* Consistent title color */
}

.ot-modal-header .modal-header .modal-title {
  font-family: "Inter", sans-serif;
  font-weight: bold;
  font-size: 20px;
  color: black; /* Consistent title color */
}

.modal-header .btn-close {
  outline: none;
  box-shadow: none;
}

.modal-body {
  font-family: "Arial", sans-serif;
  color: #333; /* Darker text color for readability */
}

.modal-body .form-label {
  font-weight: 500;
  color: #000000; /* Label color matches theme */
}

.modal-body .form-control,
.modal-body .form-select {
  border-radius: 8px;
  border: 1px solid #ccc;
  padding: 10px;
  font-size: 16px;
}

.modal-footer {
  border-top: none; /* Remove default border */
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 0%;
}

.modal-footer .btn-secondary,
.modal-footer .btn-primary {
  font-size: 16px;
  border-radius: 8px;
  font-weight: bold;
}

.modal-footer .btn-secondary {
  padding: 10px 20px;
  background-color: white;
  color: #4262ff;
  border: 1px solid #4262ff;
}

.modal-footer .btn-secondary:hover {
  background-color: #4262ff;
  color: white;
  border: 1px solid #4262ff;
}

.modal-footer .btn-primary {
  padding: 10px 20px;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: #fff;
  border: none;
  border-radius: 10px;
  width: auto;
}

.modal-footer .btn-primary:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

/* Center the modal in the middle of the page */
.modal-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh; /* Full height */
  max-width: 40%; /* Increase width to 80% of the viewport */
}

.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translateY(-50px); /* Starting animation position */
}

.modal.show .modal-dialog {
  transform: translateY(0); /* End animation position */
}

.custom-close-btn {
  background: none;
  border: none;
  font-size: 1.5rem; /* Adjust size as needed */
  color: #666; /* Adjust color to match your theme */
  cursor: pointer;
  outline: none;
  display: flex;
  align-items: center;
  margin-right: -8px; /* Adjust to align with the edge if needed */
}

.custom-close-btn:hover {
  color: #4262ff; /* Optional hover color */
}

.add_calender {
  width: 30%;
  color: #4262ff;
  background-color: white;
  font-weight: bold;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20px;
}

/* Employee  */
.earnings-container {
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
  width: 100%;
  border: 2px solid #c0c0c0;
}
.opening-balance {
  display: flex;
}

.opening-balance h2 {
  font-size: 18px;
  margin-right: 5px;
  font-weight: bold;
}

.earnings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.earnings-title {
  font-size: 16px;
  font-weight: bold;
  color: #000000;
  margin: 0;
}

.earning-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.earning-description {
  flex: 2;
  font-size: 14px;
  font-weight: bold;
  color: #4262ff;
}

.earning-amount-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.earning-amount {
  flex: 1;
  padding: 5px;
  border: 2px solid #ccc;
  border-radius: 5px;
  text-align: right;
}

.btn-remove {
  background: none;
  border: none;
  color: #999;
  margin-left: 10px;
  cursor: pointer;
  font-size: 16px;
}

.btn-remove:hover {
  color: #ff4d4f;
}

.btn-add-earning {
  background: white;
  border: 1px solid #4262ff;
  color: #4262ff;
  font-weight: bold;
  border-radius: 10px;
  padding: 10px 20px;
}

.btn-add-earning:hover {
  background-color: #f0f4ff;
}

.earning-total {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  padding: 10px 0;
  margin-top: 10px;
}

.earning-total span {
  font-size: 14px;
  color: #333;
}

.emp-open-footer {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  padding: 10px;
}

.cancel-btn {
  background-color: #ffffff;
  color: #4262ff;
  border: 1px solid #4262ff;
  font-weight: bold;
  padding: 10px 25px;
  border-radius: 10px;
  margin-top: 20px;
}

.cancel-btn:hover {
  background-color: #4262ff;
  color: #ffffff;
}

.save-btn {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: #fff;
  border: 1px solid #4262ff;
  font-weight: bold;
  padding: 10px 30px;
  border-radius: 10px;
  margin-top: 20px;
}

.save-btn:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

.menu-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 40px;
  margin-top: 20px;
  background-color: #dfe4ff;
  padding: 20px;
  border-radius: 20px;
}

.menu-sub-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 180px;
}

.menu-topic {
  font-size: 20px;
  font-weight: 600;
  color: #555;
}

.line {
  width: 2px;
  background: #555;
}

/* payRun section */

.custom-dropdown-item:hover {
  background-color: #007bff;
  color: white;
}

/* Skip button */
.custom-skip-button {
  background-color: #dc3545;
  color: white;
  font-weight: normal;
  border: none;
  padding: 0.2rem 0.5rem;
}

.custom-skip-button:hover {
  background-color: #a71d2a;
}

/* .post {
  padding: 5px 5px;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: #fff;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
} */

button.post {
  padding: 8px 16px;
  border: none;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-submit {
  background-color: #007bff;
  color: white;
}

.btn-submitted {
  background-color: #28a745;
  color: white;
}

.btn-submitting {
  background-color: #6c757d;
  color: white;
  cursor: wait;
}

/* Company Detail */

.company-detail {
  display: flex;
  align-items: center;
}

.company-detail img {
  height: 40px;
  margin-right: 10px;
}

.company-detail span {
  font-size: 1.2em;
  font-weight: bold;
}

.company-detail .navbar-brand h1 {
  font-size: 1.75em;
  color: #4e54c8;
  margin: 0;
  padding-left: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.navbar-brand {
  font-weight: bold;
  color: #4e54c8;
  display: flex;
  align-items: center;
}

/* Navbar Styles */

.navbar {
  display: flex;
  justify-content: center;
  padding: 10px 0;
  background: #f6f9ff;
}

.navbar ul {
  list-style: none;
  display: flex;
  margin: 0;
  padding: 0;
  gap: 10px;
  width: max-content;
}

.navbar ul li {
  width: 140px; /* Set a fixed width that accommodates longest text */
}

.navbar ul li a {
  text-decoration: none;
  color: #4262ff;
  /* padding: 10px 20px; */
  padding: 0;
  border-radius: 8px;
  background: #d9dfff;
  font-family: Arial, sans-serif;
  font-size: 15px;
  font-weight: 500;
  line-height: 45px;
  text-align: center;
  transition: background-color 0.3s ease, color 0.3s ease;
  display: block;
  width: 100%;
  box-sizing: border-box;
  white-space: nowrap; /* Prevent text wrapping */
}

.navbar ul li a:hover {
  background: #4262ff;
  color: #fff;
  cursor: pointer;
}

.navbar ul li a.active {
  background-color: #007bff;
  color: #fff;
}

/* Dropdown Styles */

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  list-style: none;
  margin: 0;
  padding: 0;
  flex-direction: column;
  align-items: center;
  width: 200px;
  z-index: 1;
  border-radius: 5px;
}

.dropdown-menu.show {
  display: flex;
}

.dropdown-menu li {
  margin: 0;
}

.dropdown-menu .dropdown-list {
  background-color: #fff;
  border-bottom: 1px solid #ddd;
}

.dropdown-menu .dropdown-list .dropdown-list-content {
  text-decoration: none;
  color: #333;
  border-radius: 8px;
  background: white;
  font-family: Arial, sans-serif;
  font-size: 15px;
  font-weight: 500;
  line-height: 22.5px;
  text-align: center;
  display: block;
  margin: 5px 0;
}

.dropdown-menu .dropdown-list .dropdown-list-content:hover {
  background: #4262ff;
  color: #fff;
  cursor: pointer;
}

/* Select Dropdown Styles */

select {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  /* margin: 5px 0; */
  color: black;
}

select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0px 0px 5px rgba(0, 123, 255, 0.5);
}

option {
  padding: 8px;
  background-color: #fff;
  font-size: 14px;
  color: black;
}

option:hover {
  background-color: #f1f1f1;
}

/* Toggle Link */

a {
  color: #333;
  text-decoration: none;
  padding: 8px 16px;
  font-size: 16px;
  transition: color 0.3s ease;
}

a:hover {
  color: #007bff;
}

/* General Dropdown List Hover Effect */

.dropdown-list-content:hover {
  background-color: #007bff;
  color: #fff;
}

.modern-navbar {
  box-shadow: 0 2px 16px rgba(66, 98, 255, 0.07);
  padding: 0 32px;
  min-height: 64px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.modern-company-detail {
  display: flex;
  align-items: center;
}

.modern-navbar-brand {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
}

.modern-company-name {
  font-family: Inter, sans-serif;
  font-size: 1.6rem;
  font-weight: 700;
  color: #4262ff;
  margin: 0;
  letter-spacing: 0.5px;
}

/* .modern-company-name {
  font-family: Inter, sans-serif;
  font-size: 1.6rem;
  font-weight: 700;
  color: #4262ff;
  margin: 0;
  letter-spacing: 0.5px;
  text-align: left;
  max-width: 240px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
} */

.modern-nav-items {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  flex: 1; /* Allow it to grow and push to the right */
}

.modern-nav-items ul {
  display: flex;
  flex-wrap: wrap;
  /* align-items: center; */
  gap: 10px;
  margin: 0;
  padding: 0;
  list-style: none;
}

.modern-nav-link {
  display: flex;
  align-items: center;
  gap: 7px;
  font-family: Inter, sans-serif;
  font-size: 1.08rem;
  font-weight: 500;
  color: #4262ff;
  background: none;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  transition: background 0.18s, color 0.18s;
  cursor: pointer;
  text-decoration: none;
}

.modern-nav-link:hover,
.modern-nav-link:focus {
  background: #f4f6fa;
  color: #512ca2;
  text-decoration: none;
}

.Card {
  width: 100%;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.row1 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 80px;
  flex-wrap: wrap;
}

.row1_col {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.row1_col label {
  font-size: 15px;
  font-weight: bold;
  /* margin-bottom: 5px; */
}

.row2 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  flex-wrap: wrap;
}

.row2_col {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.row2_col label {
  font-size: 15px;
  font-weight: bold;
  /* margin-bottom: 5px; */
}

input.year-input,
select.year-select {
  width: 150px;
  height: 35px;
  border-radius: 8px;
}

button {
  /* height: 35px; */
  margin-top: 20px; /* Align with inputs */
}

@media (max-width: 480px) {
  .navbar {
    padding: 5px;
  }

  .company-detail {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .company-detail .navbar-brand h1 {
    font-size: 1.1em;
  }

  .navbar ul li a {
    font-size: 12px;
    padding: 5px 10px;
    line-height: 30px;
  }

  .dropdown-menu {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 620px) {
  .nav-tabs {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  .payroll-tabs {
    width: 100%;
  }

  .modal-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    /* Full height */
    max-width: 100%;
    /* Increase width to 80% of the viewport */
  }
  .btn-secondary {
    background: none;
    border: none;
    color: #333;
    font-size: 18px;
    cursor: pointer;
    width: 100%;
  }
  .btn-primary {
    background: white;
    border: 1px solid #4262ff;
    color: #4262ff;
    font-weight: bold;
    border-radius: 10px;
    padding: 10px 20px;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .table-responsive {
    overflow-x: scroll;
  }

  .table th,
  .table td {
    white-space: nowrap;
  }

  .navbar {
    flex-direction: column;
    align-items: center;
    padding: 5px;
  }

  .company-detail {
    justify-content: center;
    text-align: center;
    margin-bottom: 10px;
  }

  .company-detail .navbar-brand h1 {
    font-size: 1.3em;
  }

  .navbar ul {
    flex-direction: column;
    text-align: center;
  }

  .navbar ul li {
    margin: 3px 0;
  }

  .navbar ul li a {
    font-size: 13px;
    padding: 6px 12px;
    line-height: 35px;
  }

  .dropdown-menu {
    position: static;
    width: 100%;
  }

  .dropdown-menu.show {
    display: block;
  }
}

@media (max-width: 991px) {
  .modern-navbar {
    flex-direction: column;
    align-items: flex-start;
    padding: 0 10px;
  }
  .modern-nav-items ul {
    gap: 10px;
    flex-wrap: wrap;
  }
  .modern-company-name {
    font-size: 1.2rem;
  }
}

@media (max-width: 1024px) {
  .navbar {
    flex-direction: column;
    align-items: center;
    padding: 10px;
  }

  .company-detail {
    justify-content: center;
    text-align: center;
    margin-bottom: 10px;
  }

  .company-detail .navbar-brand h1 {
    font-size: 1.5em;
  }

  .navbar ul {
    flex-wrap: wrap;
    justify-content: center;
  }

  .navbar ul li {
    margin: 5px 10px;
  }

  .navbar ul li a {
    font-size: 14px;
    padding: 8px 15px;
    line-height: 40px;
  }

  .dropdown-menu {
    width: 100%;
  }
}
