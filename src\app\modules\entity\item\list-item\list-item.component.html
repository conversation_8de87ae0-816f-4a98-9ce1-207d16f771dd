<app-admin-navigation></app-admin-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
    <div class="actions">
        <h1>Sales Items</h1>
    </div>
    <div class="search-create">
        <button type="button" (click)="createNewItem()" class="invoice-convert">Create New item</button>
    </div>
    <div class="table-responsive">
        <table>
            <thead>
                <tr class="table-head">
                    <th scope="col" style="width: 60px; text-align: left;" class="valuehead">Item Code</th>
                    <th scope="col" style="width: 100px; text-align: left;" class="valuehead">Item Name</th>
                    <th scope="col" style="width: 140px; text-align: left;" class="valuehead">Description</th>
                    <th scope="col" style="width: 110px; text-align: left;" class="valuehead">Unit Price (Excluding tax)</th>
                    <th scope="col" style="width: 70px; text-align: center;" class="valuehead">Tax Applicability</th>
                    <th scope="col" style="width: 40px; text-align: center;" class="valuehead">Actions</th>
                </tr>
            </thead>
            <tbody>
                <ng-container *ngFor="let item of items">
                    <tr *ngIf="item.itemCode !== 'SISSERVICE'">
                        <td style="width: 100px; text-align: left;" class="value">{{ item.itemCode }}</td>
                        <td style="width: 100px; text-align: left;" class="value">{{ item.itemName }}</td>
                        <td style="width: 100px; text-align: left;" class="value">{{ item.description }}</td>
                        <td style="width: 100px; text-align: right;" class="value">{{ item.unitPrice }}</td>
                        <td style="width: 70px; text-align: center;" class="value">{{ item.taxApplicability }}</td>
                        <td style="width: 40px; text-align: center;" class="value">
                            <button (click)="updateItem(item.salesItemId)" class="btn btn-orange btn-sm"
                                style="margin-right: 4px; border: none; background: none; padding: 2px; font-size: 1.2rem;"
                                title="Edit">
                                <i class="ri-edit-box-line" style="color: #4262FF;"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" (click)="deleteItem(item.salesItemId)"
                                style="margin-right: 2px; border: none; background: none; padding: 4px; font-size: 1.2rem;"
                                title="Delete">
                                <i class="ri-delete-bin-line" style="color: #FF0000;"></i>
                            </button>
                        </td>
                    </tr>
                </ng-container>
            </tbody>
        </table>
    </div>
</div>

