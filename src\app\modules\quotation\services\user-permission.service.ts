import { Injectable } from '@angular/core';
import { QuotationConstants, UserRole, QuoteStatus } from '../quotation.constants';
import { UserInfo } from '../quotation.interfaces';
import { QuoteHead } from '../quotation';

@Injectable({
  providedIn: 'root'
})
export class UserPermissionService {

  constructor() { }

  getCurrentUser(): UserInfo | null {
    const userString = localStorage.getItem(QuotationConstants.STORAGE_KEYS.USER);
    if (!userString) {
      return null;
    }
    return JSON.parse(userString) as UserInfo;
  }

  checkFreeUserSendingLimit(quotes: QuoteHead[]): { canSend: boolean; message?: string } {
    const currentUser = this.getCurrentUser();
    if (!currentUser) {
      return { canSend: false, message: 'User not found' };
    }

    if (currentUser.roleName !== UserRole.FREE) {
      return { canSend: true };
    }

    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Count quotes sent by this user this month
    const sentQuotesThisMonth = quotes.filter(quote => {
      const createdDate = new Date(quote.quoteDate);
      return (
        quote.userId === currentUser.id &&
        quote.status === QuoteStatus.SENT &&
        createdDate.getMonth() === currentMonth &&
        createdDate.getFullYear() === currentYear
      );
    }).length;

    if (sentQuotesThisMonth >= QuotationConstants.FREE_USER_MONTHLY_LIMIT) {
      return {
        canSend: false,
        message: QuotationConstants.MESSAGES.FREE_USER_LIMIT_REACHED
      };
    }

    return { canSend: true };
  }

  checkFreeUserCreationLimit(quotes: QuoteHead[]): { canCreate: boolean; message?: string } {
    const currentUser = this.getCurrentUser();
    if (!currentUser) {
      return { canCreate: false, message: 'User not found' };
    }

    if (currentUser.roleName !== UserRole.FREE) {
      return { canCreate: true };
    }

    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Count quotes created by this user this month
    const createdQuotesThisMonth = quotes.filter(quote => {
      const createdDate = new Date(quote.quoteDate);
      return (
        quote.userId === currentUser.id &&
        createdDate.getMonth() === currentMonth &&
        createdDate.getFullYear() === currentYear
      );
    }).length;

    if (createdQuotesThisMonth >= QuotationConstants.FREE_USER_MONTHLY_LIMIT) {
      return {
        canCreate: false,
        message: 'As a Free user, you can only create up to 5 quotes per month.'
      };
    }

    return { canCreate: true };
  }

  getUserRole(): string | null {
    const currentUser = this.getCurrentUser();
    return currentUser ? currentUser.roleName : null;
  }

  getUserId(): number | null {
    const currentUser = this.getCurrentUser();
    return currentUser ? currentUser.id : null;
  }

  isFreeUser(): boolean {
    const userRole = this.getUserRole();
    return userRole === UserRole.FREE;
  }

  isPremiumUser(): boolean {
    const userRole = this.getUserRole();
    return userRole === UserRole.PREMIUM;
  }

  isEnterpriseUser(): boolean {
    const userRole = this.getUserRole();
    return userRole === UserRole.ENTERPRISE;
  }
}
