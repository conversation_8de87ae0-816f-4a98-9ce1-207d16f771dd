* {
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

.main-container {
    background-color: rgb(241, 241, 241);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.actions {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.actions h1 {
    flex: 1;
    margin-bottom: 20px;
    font-family: Inter;
    font-size: 36px;
    font-weight: 700;
    text-align: left;
    color: #4262FF;
}

.actions .transparent-button {
    background: transparent;
    color: #4262FF;
    border: 1px solid #4262FF;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 29px;
    font-weight: bold;
    margin-bottom: 20px;
    margin-left: 15px;
}

.actions .transparent-button:hover {
    background-color: #4262FF;
    color: white;
}

.search-create {
    display: flex;
    justify-content: flex-end;
    /* align-items: center; */
    margin-bottom: 20px;
    gap: 10px;
}

.search-create .input-container {
    position: relative;
    width: 600px;
}

.search-create .input-container input.form-control {
    width: 100%;
    padding: 10px 30px 10px 10px;
    font-size: 16px;
    border-radius: 13px;
    border: 1px solid #ccc;
    box-sizing: border-box;
}

.search-create .input-container i.bi-search {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    pointer-events: none;
}

.search-create input {
    width: 510px;
    padding: 10px;
    font-size: 16px;
    border-radius: 13px;
    border: 1px solid #ccc;
    flex-grow: 1;
}

.search-create button {
    background: linear-gradient(to right, #4262FF, #512CA2);
    color: white;
    font-weight: bold;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 13px;
    border: none;
    margin-left: 10px;
    width: auto;
    top: 356px;
    left: 879px;
    border-radius: 13px;
}

.search-create button:hover {
    background: linear-gradient(to right, #512CA2, #4262FF);
}

.nav-tabs .nav-link.active {
    border-top: 3px solid blue;
}

.nav-item {
    margin-right: 10px;
    width: 13.4%;
    text-align: center;
    font-family: Inter;
    font-size: 16px;
    font-weight: 500;
}


/* .nav-link.active {
  background-color: #007bff;
  color: white;
} */

.InvoiceBox {
    width: 100%;
    margin: auto;
    padding: 20px;
    background-color: rgb(237, 237, 237);
}

.InvoiceSubBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-radius: 5px;
}

.InvoiceTopic p {
    font-size: 43px;
    font-weight: bold;
    margin: 0;
}

.InvoiceButtons {
    display: flex;
    flex-direction: row;
    gap: 25px;
}

.InvoiceButtons button {
    padding: 10px 15px;
    font-size: 14px;
    cursor: pointer;
    border: none;
    border-radius: 30px;
    width: 163px;
    background-color: #4262ff;
    color: white;
}

.InvoiceButtons button:hover {
    background-color: #07386e;
}

.Row1 {
    display: flex;
    flex-direction: column;
    padding-left: 80px;
    margin-top: 30px;
    padding-right: 80px;
    /* width: 100vw; */
    justify-content: space-between;
}

.SearchBar {
    display: flex;
    flex-direction: row;
    height: 50px;
    width: 500px;
    align-items: center;
    background-color: white;
    border-radius: 10px;
}

.input-group-text {
    cursor: pointer;
}

.SearchBar input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    font-size: 16px;
    border: 1px solid white;
    background-color: white;
    box-shadow: none;
    width: 400px;
}

.SearchBar img {
    width: 80px;
    height: 20px;
    background-color: white;
}

.SubRow1 {
    justify-content: space-between;
    width: 100%;
}

.SubRow1 button {
    border-color: #4262ff;
    height: 50px;
    border-radius: 10px;
    width: 300px;
    margin-left: 20px;
    color: #4262ff;
}

.SubRow1 button:hover {
    background-color: #4262ff;
    border: none;
    color: white;
}

.button-row {
    margin-top: 40px;
    display: flex;
    flex-direction: row;
    /* width: 100vw; */
    /* padding-left: 80px;
    padding-right: 80px; */
    justify-content: space-between;
    align-items: center;
    height: 50px;
    border: none;
    background-color: white;
}

.actions .transparent-button {
    background: transparent;
    color: #4262FF;
    border: 2px solid #4262FF;
    padding: 10px 20px;
    margin-right: 10px;
    cursor: pointer;
    border-radius: 25px;
    font-weight: bold;
}

.actions .transparent-button:hover {
    background-color: #4262FF;
    color: white;
}

.search-create button {
    background: linear-gradient(to right, #4262FF, #512CA2);
    color: white;
    font-weight: bold;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 10px;
    border: none;
    margin-left: 10px;
}

.search-create button:hover {
    background: linear-gradient(to right, #512CA2, #4262FF);
}


/* styles.css or app.component.css */

.flex-box {
    display: flex;
    margin-top: 5px;
    height: 50px;
    border: 2px solid;
    /* margin-right: 80px;
    margin-left: 80px; */
    flex-direction: row;
    /* padding-left: 100px;
    padding-right: 100px; */
    justify-content: space-between;
    align-items: center;
    border-top: none;
    background-color: white;
    border-left: none;
    border-right: none;
    border-color: #e6e6e6;
}

.flex-box-heading {
    font-weight: 600;
    font-size: 1rem;
    background-color: white;
}

.checkBox {
    display: flex;
    align-items: center;
    background-color: white;
}

.checkBox input {
    width: 20px;
    height: 20px;
    background-color: white;
}

.main-box {
    background-color: white;
    margin-left: 80px;
    margin-right: 80px;
}

.Box1 {
    background-color: white;
}

.flex-box-content {
    background-color: white;
    font-size: medium;
    color: #5e5e5e;
    display: flex;
}

.flex-box-content-button img {
    width: 80px;
    height: 20px;
    background-color: white;
}

.flex-box-content-button {
    display: flex;
    width: 110px;
    background-color: white;
    gap: 1;
}

.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600px;
    /* Same height as the iframe */
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

table {
    width: 100%;
    margin-bottom: 20px;
    border-radius: 10px;
    overflow: hidden;
}

.table-responsive {
    border-radius: none;
}

.table-head th {
    position: relative;
    padding: 10px;
    background-color: #d7dbf5;
    text-align: left;
    color: rgb(0, 0, 0);
    font-size: 14px;
    font-weight: bold;
}

.table-head th:first-child {
    border-top-left-radius: 10px;
}

.table-head th:last-child {
    border-top-right-radius: 10px;
}

tbody tr {
    background-color: white;
    border-bottom: rgb(171, 171, 171) 1px solid;
    font-size: small;
    color: rgb(102, 102, 102);
}

tbody tr:hover {
    background-color: #f1f1f1;
    /* Light grey color on hover */
}

td,
th {
    padding: 10px;
}

td.valueCheckbox,
th.valueCheckbox {
    width: 3%;
}

td.value,
th.valuehead {
    width: 12%;
}

th.valuehead {
    text-align: center;
}

.text-draft {
    color: #007bff;
    text-align: center;
    /* Primary (blue) for Draft */
}

.text-pending {
    color: #057c21;
    text-align: center;
    /* Success (green) for Pending */
}

.text-canceled {
    color: #dc3545;
    text-align: center;
    /* Danger (red) for Canceled */
}

.text-revised {
    color: #6c757d;
    text-align: center;
    /* Warning (yellow) for Revised */
}

.text-sent {
    color: #5bc0de;
    text-align: center;
    /* Info (cyan) for Sent */
}

.text-paid {
    color: #28a745;
    text-align: center;
    /* Success (green) for Paid */
}

.custom-close-btn {
    width: 50px;
    /* Adjust the width */
    height: 50px;
    /* Adjust the height */
}

span.lable {
    border: none;
    border-radius: 20px;
    padding: 5px 10px;
    font-weight: bold;
}

.border-draft {
    border-color: #007bff;
    background-color: #cce0f5;
    /* Primary (blue) for Draft */
}

.border-pending {
    border-color: #057c21;
    background-color: #d7ecdc;
    /* Success (green) for Pending */
}

.border-canceled {
    border-color: #dc3545;
    background-color: #eedbdd;
    /* Danger (red) for Canceled */
}

.border-revised {
    border-color: #6c757d;
    background-color: #eeebeb;
    /* Warning (yellow) for Revised */
}

.border-sent {
    border-color: #5bc0de;
    background-color: #ebf0f7;
    /* Info (cyan) for Sent */
}

.border-paid {
    border-color: #28a745;
    background-color: #d4edda;
    /* Success (green) for Paid */
}


/* Modal Styles */

.popup-container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Semi-transparent background */
    z-index: 1000;
    /* Ensure it sits above other elements */
}


/* Styles for the modal itself */

.customer-popup {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 500px;
    /* Adjust the width as per your design */
    width: 30%;
    /* Ensure it doesn’t go beyond screen size */
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.popup-header h2 {
    margin: 0;
    font-size: 1.5rem;
}


/* Close button */

.close-btn {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #333;
}

.close-btn:hover {
    color: #ff0000;
}


/* Form input styles */

.form-group {
    margin-bottom: 10px;
    text-align: left;
    font-family: Arial, sans-serif;
    font-size: 15px;
    font-weight: 600;
    line-height: 25.41px;
    color: #444343;
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 5px;
}

.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    resize: vertical;
    /* Allow resizing only vertically */
    min-height: 100px;
    /* Minimum height for better visibility */
    max-height: 300px;
    /* Restrict max height */
}

.form-group input[type="text"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 10px;
    font-size: 1rem;
}

.file-upload-group {
    display: flex;
    align-items: center;
    /* Align the label and input vertically in the middle */
    gap: 10px;
    /* Add space between the label and the input */
}

.file-upload-group input[type="file"] {
    border: 1px solid #ccc;
    padding: 5px;
    border-radius: 4px;
    font-size: 1rem;
}

.file-row {
    display: flex;
    align-items: center;
    gap: 20px;
    /* Space between label and attachment row */
    margin-top: 10px;
}


/* Align the file attachment components */

.file-attachment {
    display: flex;
    padding: 5px 5px;
    align-items: center;
    border-radius: 10px;
    border: 1px solid #1f1e1e;
    gap: 10px;
    /* Add space between file icon, name, and select */
}


/* Style for file icon */

.file-icon {
    font-size: 1.5rem;
    /* Make the file icon slightly larger */
}


/* Style for file name */

.file-name {
    font-size: 1rem;
    font-weight: bold;
    color: #333;
}


/* Style for select dropdown */

.file-attachment select {
    padding: 5px;
    font-size: 1rem;
    border-radius: 10px;
    border: 1px solid #1f1e1e;
    outline: none;
    background-color: #f9f9f9;
    cursor: pointer;
}


/* Checkbox style */

.form-group input[type="checkbox"] {
    margin-right: 5px;
}

.form-group label small {
    font-size: 0.8rem;
    color: #555;
}


/* Buttons */

.add-btn {
    background-color: #007bff;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.add-btn:hover {
    background-color: #0056b3;
}

.popup-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.popup-footer .add-btn {
    background: linear-gradient(to right, #4262FF, #512CA2);
    color: white;
    font-weight: bold;
    padding: 5px 40px;
    cursor: pointer;
    border-radius: 15px;
    border: none;
    margin-left: 10px;
    font-size: 17px;
}

.popup-footer .cancel-btn {
    background: transparent;
    color: #4262FF;
    border: 2px solid #4262FF;
    padding: 5px 40px;
    margin-right: 15px;
    cursor: pointer;
    border-radius: 12px;
    font-weight: bold;
    font-size: 17px;
}

.popup-footer .add-btn:hover {
    background-color: #218838;
}


/* General styles */

.Card {
    width: 100%;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}


/* Row layout for filters */

.row1 {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
}

.row1_col1 {
    width: 30%;
    /* Ensures enough space for input text */
}

.row1_col2,
.row1_col3,
.row1_col4,
.row1_col5 {
    width: 20%;
}

.row1_col1 input,
.row1_col2 select,
.row1_col3 input,
.row1_col4 input {
    width: 100%;
    height: 49px;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 14px;
}

label {
    display: block;
    font-weight: bold;
    font-family: Inter;
    font-size: 15px;
    color: #333;
}


/* Row layout for action buttons */

.row2 {
    display: flex;
    align-items: right;
    justify-content: flex-end;
    margin-top: 20px;
}

.primary-button {
    background: linear-gradient(to right, #4262FF, #512CA2);
    color: white;
    font-weight: bold;
    padding: 5px 30px;
    cursor: pointer;
    border-radius: 12px;
    border: none;
    margin-left: 10px;
    font-size: 17px;
}

.secondary-button {
    background: transparent;
    color: #4262FF;
    border: 1px solid #4262FF;
    padding: 5px 40px;
    margin-right: 5px;
    cursor: pointer;
    border-radius: 12px;
    font-weight: bold;
    font-size: 17px;
}

.primary-button:hover {
    background-color: linear-gradient(to right, #512CA2, #4262FF);
}

.secondary-button:hover {
    background-color: #4262FF;
    color: white;
}

.row2_col2 {
    padding: 8px 20px;
    margin: 0 10px;
    font-size: 14px;
    color: #555;
}


/* Miscellaneous Styles */

.tabs button {
    padding: 10px;
    border: none;
    background-color: lightgray;
    cursor: pointer;
}

.tabs button.active {
    background-color: darkblue;
    color: white;
}

.filter-section {
    margin: 10px 0;
}

.paid_footer {
    margin-bottom: 10px;
}

.action-bar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.action-buttons {
    display: flex;
    align-items: center;
}

.action-buttons button {
    margin-right: 10px;
}

.invoice-search {
    margin-left: auto;
}

.invoice-search input {
    width: 400px;
    height: 40px;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 10px;
    font-size: 14px;
}

.selected-item-text {
    margin-left: 20px;
    font-weight: bold;
    color: #4262FF;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.icon-group {
    display: flex;
    gap: 20px;
}

.close-icon,
.send-icon {
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
}

.close-icon:hover,
.send-icon:hover {
    color: #000;
}

.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 700px;
    /* Same height as the iframe */
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

.input-container {
    position: relative;
    display: inline-block;
    width: 100%;
  }
  
  .search-input {
    width: 100%;
    padding-right: 30px; /* Add space for the icon inside the input */
    padding-left: 10px;
    box-sizing: border-box; /* Ensures padding doesn't increase input size */
  }
  
  .input-container i {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none; /* Makes sure the icon doesn't block input clicks */
  }
  
  @media (max-width: 599px) {

    .actions h1 {
        font-size: 30px;
        margin-bottom: 15px;
    }

    .row1 {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
        }
    
        .row1_col1 {
            width: 100%;
            /* Ensures enough space for input text */
        }
    
        .row1_col2,
        .row1_col3,
        .row1_col4,
        .row1_col5 {
            width: 100%;
        }
    
        .row1_col1 input,
        .row1_col2 select,
        .row1_col3 input,
        .row1_col4 input {
            width: 100%;
            height: 49px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 8px;
            font-size: 14px;
        }

                  .row2 {
      display: flex;
      flex-direction: column-reverse;
      align-items: right;
      justify-content: flex-end;
      margin-top: 20px;
    }

        .primary-button {
          background: linear-gradient(to right, #4262ff, #512ca2);
          color: white;
          font-weight: bold;
          padding: 5px 30px;
          cursor: pointer;
          border-radius: 12px;
          border: 1px solid #4262ff;
          margin-left: 0px;
          font-size: 17px;
          width: 100%;
        }
    
        .secondary-button {
          background: transparent;
          color: #4262ff;
          border: 1px solid #4262ff;
          padding: 5px 40px;
          margin-right: 0px;
          cursor: pointer;
          border-radius: 12px;
          font-weight: bold;
          font-size: 17px;
          width: 100%;
          margin-top: 10px;
        }
  }