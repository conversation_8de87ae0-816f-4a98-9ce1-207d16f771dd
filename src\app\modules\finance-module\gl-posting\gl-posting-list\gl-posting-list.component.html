<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <!-- Heading And Button -->
  <div class="actions">
    <h1>Ledger Postings</h1>
    <button (click)="exportToExcel()" class="btn btn-primary">Export to Excel</button>
  </div>

  <div class="Card">
    <!-- Filter and Search Section -->
    <div class="row1">
      <!-- Input for number, reference -->
      <div class="row1_col1">
          <label for="document-type">Transaction ID</label>
          <div class="input-container" class="">
            <select id="document-type" class="search-input" [(ngModel)]="searchTerm">
              <option value="">All</option>
              <option *ngFor="let type of documentTypes" [value]="type">{{ type }}</option>
            </select>
          </div>
      </div>

      <div class="row1_col3">
        <label for="StartDate">Start Date</label>
        <input type="date" class="date-picker" id="StartDate" [(ngModel)]="startDate" />
      </div>

      <div class="row1_col4">
        <label for="EndDate">End Date</label>
        <input type="date" class="date-picker" id="EndDate" [(ngModel)]="endDate" />
      </div>
    </div>

    <div class="row2">
      <div class="row2_col3">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>
      <div class="row2_col1">
        <button type="button" class="primary-button" (click)="filterQuotes()">
          Search
        </button>
      </div>
    </div>
  </div>


  <div class="table-responsive">
    <table>
      <thead>
        <tr class="table-head">
          <th scope="col" class="valueCheckbox">
            <!-- <input type="checkbox" /> -->
          </th>
          <th scope="col" class="valuehead">Document No</th>
          <th scope="col" class="valuehead">Document Type</th>
          <th scope="col" class="valuehead">Business Partner</th>
          <th scope="col" class="valuehead">Posting Date</th>
          <th scope="col" class="valuehead">Remarks</th>
          <th scope="col" class="valuehead" style="text-align: right;">Dr</th>
          <th scope="col" class="valuehead" style="text-align: right;">Cr</th>
          <th scope="col" class="valuehead-action">Action</th>
        </tr>
      </thead>

      <tbody *ngFor="let quote of filteredApInvoices; let i = index">
        <tr [ngClass]="{
          'update-bg': quote.documentType === 'Bill Update',
          'cancel-bg': quote.documentType === 'Bill Cancellation'
        }" (click)="quote.glTransactionId !== null && toggleDetails(quote.glTransactionId)">
          <td class="valueCheckbox">
            <input type="checkbox" [(ngModel)]="quote.selected" />
          </td>
          <td class="value">{{ quote.documentType === 'Journal Voucher' ? quote.jvNumber : quote.documentNumber }}</td>
          <td class="value">{{ quote.documentType }}</td>
          <td class="value"> </td>
          <td class="value">{{ quote.date | date : "dd-MM-yyyy" }}</td>
          <td class="value">....</td>
          <td class="value" style="text-align: right;">
            {{ quote.totalDr | currency }}
          </td>
          <td class="value" style="text-align: right;">
            {{ quote.totalCr | currency }}
          </td>
          <td class="value" style="text-align: center;">
            <!-- <button class="btn btn-orange btn-sm"
              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" title="View">
              <i class="ri-edit-box-line" style="color: #4262FF;"></i>
            </button> -->

            <button class="btn btn-warning btn-sm" data-bs-target="#quotePreviewModal" data-bs-toggle="modal"
              (click)="openPopup(quote.glTransactionId!, quote.documentType!, quote.documentNumber!); $event.stopPropagation();"
              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" title="Preview">
              <i class="bi bi-eye" style="color: #DEBE15;"></i>
            </button>

          </td>
        </tr>
        <tr *ngIf="expandedRowId === quote.glTransactionId" class="expandedRow">
          <td colspan="10">
            <table border="1">
              <thead>
                <tr>
                  <th style="text-align: center">GL Account Code</th>
                  <th style="text-align: center">GL Account</th>
                  <th style="text-align: center">Description</th>
                  <th style="text-align: right">Dr</th>
                  <th style="text-align: right">Cr</th>
                  <th></th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let detail of getDetailsForPosting(quote.glTransactionId)">
                  <td style="text-align: center">{{ detail.coaLedgerAccount?.ledgerAccountCode }}</td>
                  <td style="text-align: center">{{ detail.coaLedgerAccount?.ledgerAccountName }}</td>
                  <td style="text-align: center">{{ detail.coaLedgerAccount?.ledgerAccountDescription }}</td>
                  <td class="value" style="text-align: right">
                    {{ detail.drAmount| currency }}
                  </td>
                  <td class="value" style="text-align: right">
                    {{ detail.crAmount| currency }}
                  </td>
                  <td></td>
                  <th></th>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>