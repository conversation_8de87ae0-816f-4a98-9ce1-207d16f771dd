.header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: linear-gradient(45deg, #455cff, #6822ff);
  padding: 10px 20px;
  border-bottom: 1px solid #ccc;
}

.nav ul {
  list-style: none;
  display: flex;
  margin: 0;
  padding: 0;
  margin-right: 50px;
}

.nav ul li {
  position: relative;
  margin: 0 20px;
}

.nav ul li a {
  text-decoration: none;
  font-family: Inter, sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 40px;
  text-align: center;
  color: #ffffff;
  cursor: pointer;
}

.nav ul li a:hover {
  color: #007bff;
}

.nav ul li .dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #ffffff;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
  list-style: none;
  z-index: 100;
  min-width: 50px;
}

.nav ul li .dropdown-menu li {
  margin: 0;
}

.nav ul li .dropdown-menu li a {
  color: #333;
  font-weight: 500;
  font-size: 14px;
  padding: 0px 10px;
  display: block;
  text-align: left;
  text-decoration: none;
  white-space: nowrap;
}
.nav ul li .dropdown-menu li a:hover {
  background-color: #f1f1f1;
  color: #007bff;
}

.nav ul li:hover .dropdown-menu {
  display: block;
}

@media (max-width: 601px) {
  .header {
    padding: 10px;
  }

  .nav ul {
    width: 100%;
  }

  .nav ul li {
    margin: 0 10px;
  }

  .nav ul li a {
    font-size: 10px;
    padding: 5px;
  }
}


@media (max-width: 440px) {
  .header {
    padding: 10px;
  }

  .nav ul {
    width: 100%;
  }

  .nav ul li {
    margin: 0 2px;
  }

  .nav ul li a {
    font-size: 10px;
    padding: 2px;
  }
}

