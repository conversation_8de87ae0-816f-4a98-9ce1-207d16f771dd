<app-admin-navigation></app-admin-navigation>
<app-logo-header></app-logo-header>

<div class="form-container">
  <div class="user-creation-form">
    <h2>Invite New User</h2>
    <form
      [formGroup]="inviteUserForm"
      (ngSubmit)="inviteUser()"
      (keydown)="preventSubmit($event)"
    >
      <div class="user-name">
        <div class="form-group">
          <label for="firstName">First Name</label>
          <input
            type="text"
            id="firstName"
            name="firstName"
            [(ngModel)]="firstName"
            required
            formControlName="firstName"
            class="form-control"
          />
          <div
            *ngIf="inviteUserForm.get('firstName')?.touched && inviteUserForm.get('firstName')?.invalid"
            class="text-danger"
          >
            <div *ngIf="inviteUserForm.get('firstName')?.errors?.['required']">
              First Name is required.
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="lastName">Last Name</label>
          <input
            type="text"
            id="lastName"
            name="lastName"
            [(ngModel)]="lastName"
            required
            formControlName="lastName"
            class="form-control"
          />
          <div
            *ngIf="inviteUserForm.get('lastName')?.touched && inviteUserForm.get('lastName')?.invalid"
            class="text-danger"
          >
            <div *ngIf="inviteUserForm.get('lastName')?.errors?.['required']">
              Last Name is required.
            </div>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          name="email"
          [(ngModel)]="email"
          (keyup)="checkEmailExistenceKeyup()"
          required
          formControlName="email"
          class="form-control"
        />
        <div *ngIf="isUsernameExists" class="text-danger">
          {{ usernameExistsMessage }}
        </div>
        <div
          *ngIf="inviteUserForm.get('email')?.touched && inviteUserForm.get('email')?.invalid"
          class="text-danger"
        >
          <div *ngIf="inviteUserForm.get('email')?.errors?.['required']">
            Email is required.
          </div>
          <div *ngIf="inviteUserForm.get('email')?.errors?.['customEmail']">
            Please enter a valid email.
          </div>
        </div>
      </div>

      <!-- User Type -->
      <div class="form-group" *ngIf="!isAccountant || !isSystemAdmin">
        <label for="userType">User Type</label>
        <select
          id="userType"
          name="userType"
          [(ngModel)]="selectedUserType.userTypeId"
          required
          formControlName="userType"
          class="form-select"
        >
          <option value="" disabled>Select User Type</option>
          <option
            *ngFor="let userType of userTypes"
            [value]="userType.userTypeId"
          >
            {{
              userType.userType === 'Primary user'
                ? 'Admin User'
                : userType.userType === 'General user'
                ? 'Basic User'
                : userType.userType === 'Accountant'
                ? 'Adviser – Accountant/Book keeper'
                : userType.userType
            }}
          </option>
        </select>
        <div
          *ngIf="inviteUserForm.get('userType')?.touched && inviteUserForm.get('userType')?.invalid"
          class="text-danger"
        >
          <div *ngIf="inviteUserForm.get('userType')?.errors?.['required']">
            User Type is required.
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="cancel" (click)="navigateToDashboard()">
          Cancel
        </button>
        <button 
        type="submit" 
        class="send-invite"
         [disabled]="isInviting"
         >
          <span *ngIf="!isInviting">Invite</span>
          <span *ngIf="isInviting">
              <i class="fa fa-spinner fa-spin"></i> Inviting...
            </span>
        </button>
      </div>
    </form>
  </div>
</div>
