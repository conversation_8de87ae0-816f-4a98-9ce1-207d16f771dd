<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
    <div class="header">
      <h3>Expenses Payment</h3>
    </div>
  
    <form #f="ngForm"   class="styled-form" novalidate>
  
      <div class="bd">
  
        <div class="form-section">
  
          <div class="form-row"
            style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
            <div class="form-group" style="display: flex; align-items: center; flex-grow: 1;">
              <label for="paymentExpensesNumber">Payment Number:</label>
              <input id="paymentExpensesNumber" type="text" required [(ngModel)]="paymentReceiptsHead.paymentExpensesNumber"
                name="paymentExpensesNumber"  #paymentExpensesNumber="ngModel" disabled />
            </div>
          </div>
  
  
          <div class="form-row"
            style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
            <div class="form-group" style="display: flex; align-items: center; flex-grow: 1;">
              <label for="documentDate">Document Date:</label>
              <input id="documentDate" type="date" required [(ngModel)]="paymentReceiptsHead.documentDate"
                name="documentDate" />
              <div *ngIf="f.controls['documentDate']?.touched && f.controls['documentDate']?.invalid" class="text-danger">
                Date is required.
              </div>
  
            </div>
          </div>
  
        </div>
  
        <div class="table-section">
          <table class="table-section">
            <thead>
              <tr>
                <th>#</th>
                <th>Expenses Number</th>
                <th>Date</th>
                <th>Amount</th>
                <th>Balance</th>
                <th>Paid Amount</th>
                <th>New Balance</th>
              </tr>
            </thead>
            <tbody>
              <!-- Loop through selected expenses -->
              <tr *ngFor="let expense of selectedExpenses; let i = index">
                <td>{{ i + 1 }}</td>
  
                <!-- expense Number -->
                <td>
                  <input type="text" class="form-control1" required [(ngModel)]="expense.expensesNumber"
                    name="expensesNumber{{i}}" disabled />
                </td>
  
                <!-- expense Date -->
                <td>
                  <input type="text" class="form-control" [ngModel]="expense.spentOn | date: 'dd-MM-yyyy'"
                    name="spentOn{{i}}" disabled />
                </td>
  
                <!-- Amount -->
                <td>
                  <input type="text" class="form-control" [value]="expense.netAmount | currency:'USD':'symbol':'1.2-2'"
                    disabled />
                  <input type="hidden" [(ngModel)]="expense.netAmount" name="netAmount{{i}}" />
                </td>
  
                <!-- Balance -->
                <td>
                  <input type="text" class="form-control"
                    [value]="expense.balanceAmount ? (expense.balanceAmount | currency:'USD':'symbol':'1.2-2') : '0.00'"
                    disabled />
                  <input type="hidden" [(ngModel)]="expense.balanceAmount" name="balanceAmount{{i}}" />
                </td>
  
                <!-- Credit Amount -->
                <td>
                  <input type="number" class="form-control" required [(ngModel)]="expense.creditAmount"
                    name="creditAmount{{i}}" (keydown)="preventEnter($event)" min="0"
                    [ngClass]="{ 'is-invalid': f.submitted && f.controls['creditAmount' + i].invalid }" />
                  <div *ngIf="f.controls['creditAmount' + i]?.touched && f.controls['creditAmount' + i]?.invalid"
                    class="text-danger">
                    Paid Amount is required.
                  </div>
                </td>
  
                <!-- New expense Balance -->
                <td>
                  <input id="ExpenseBalance" type="text" class="form-control"
                    [value]="getExpenseNewBalance(expense) | currency:'USD':'symbol':'1.2-2'" disabled />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
  
  
        <div class="form-section_2">
          <div class="form-row"
            style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px; ">
            <div class="form-group_2" style="display: flex; align-items: center; flex-grow: 1; margin-left: 40%;">
              <label for="totalCreditAmount">Total Paid Amount</label>
              <input id="totalCreditAmount" type="text" class="form-control"
                [value]="getTotalPaidAmount() | currency:'USD':'symbol':'1.2-2'" disabled />
            </div>
  
          </div>
  
          <div class="form-row"
            style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px; ">
            <div class="form-group_2" style="display: flex; align-items: center; flex-grow: 1; margin-left: 40%;">
              <label for="remarks">Remarks:</label>
              <textarea id="remarks" required [(ngModel)]="paymentReceiptsHead.remarks" name="remarks"
                rows="3"></textarea>
            </div>
          </div>
  
  
          <div class="d-flex justify-content-end mt-5 mb-4" style="margin-right: 20px; background-color: transparent;">
            <button type="button" class="btn btn-secondary me-2" (click)="onCancel()">Cancel</button>
            <button type="submit" class="btn btn-primary" (click)="onSubmit(f)">Save Payment Receipt</button>
          </div>
        </div>
      </div>
        
    </form>
  </div>