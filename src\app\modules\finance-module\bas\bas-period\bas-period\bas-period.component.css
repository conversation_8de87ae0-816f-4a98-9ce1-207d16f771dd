* {
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}


.heading-section {
  display: flex;
  margin-bottom: 20px;
}

.heading-section h1 {
  flex: 1;
  font-family: Inter;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

.button-section {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
  gap: 20px;
}

.primary-button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  border-radius: 13px;
  border: none;
  width: 350px;
}

.primary-button:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

.card {
  width: 100%;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-card {
  display: flex;
  gap: 20px;
  align-items: center;
}

.body-card {
  display: flex;
  justify-content: space-between;
}

.body-card h6 {
  font-family: Inter;
  font-weight: bold;
}

.prepare-button {
  background: #4262ff;
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  border-radius: 13px;
  border: none;
  /* width: 100px; */
}

.prepare-button:hover {
  background: white;
  color: #4262ff;
  border: 1px solid #4262ff;
}

.custom-filter-select {
  border: none;
  outline: none;
  color: #4262ff;
  font-weight: bold;
  cursor: pointer;
}

.group-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.list-item {
  display: flex;
  justify-content: space-between;
  padding: 16px 0; /* Add vertical spacing */
  border-bottom: 1px solid #ddd; /* Light gray line */
}

.list-item:last-child {
  border-bottom: none; /* Remove line after the last item */
}

.text-small {
  font-size: 14px;
  color: #6c757d;
  margin-top: 4px;
}

.text-review {
  color: #007bff;
}

.left-top-row,
.right-top-row {
  display: flex;
  align-items: center;
  gap: 12px; /* Adjust spacing between the 3 items */
}

a.text-review {
  cursor: pointer;
}
