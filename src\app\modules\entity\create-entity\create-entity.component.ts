import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Entity, EntityTradingName, Industry } from '../entity';
import { EntityService } from '../entity.service';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';

@Component({
  selector: 'app-create-entity',
  templateUrl: './create-entity.component.html',
  styleUrls: ['./create-entity.component.css'],
})
export class CreateEntityComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent)
  chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  tradingNameOption: string = 'single';
  tradingName: string = '';
  industryClassification: string = 'generalBusiness';
  plTemplate: string = 'standardPL';
  balanceSheetTemplate: string = 'standardBalanceSheet';
  newTradingName: string[] = [];
  additionalTradingName: boolean = false;
  selectedSubscriptionFeeId: number | null = null;
  entityTradingName: EntityTradingName = new EntityTradingName();

  industryList: Industry[] = [];
  url = '';
  entityId: number = 0;

  constructor(
    private router: Router,
    private entityService: EntityService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.entityId = Number(localStorage.getItem('entityId'));
    this.fetchIndstryList();
    this.selectedSubscriptionFeeId =
      this.route.snapshot.queryParams['subscriptionPlanId'];
  }

  multipleTradingName(): void {
    this.additionalTradingName = true;
  }

  singleTradingName(): void {
    this.additionalTradingName = false;
  }

  noTradingName(): void {
    this.tradingName = '';
    this.entityTradingName.tradingName = '';
    this.additionalTradingName = false;
  }

  addAdditionalTradingName(): void {
    this.additionalTradingName = true;
    this.newTradingName.push(''); // Push an empty string, which will bind correctly to each input
  }

  trackByIndex(index: number): number {
    return index;
  }

  // Method to remove a trading name by index
  removeTradingName(index: number): void {
    this.newTradingName.splice(index, 1); // Remove the element at the specified index
  }

  // private getBusinessEntity(): void {
  //   this.entityService.getBusinessEntityById(this.entityId).subscribe(
  //     (data) => {
  //       this.entity = data;
  //     },
  //     (error) => {
  //       console.error('Error fetching business entity:', error);
  //       Swal.fire({
  //         title: 'Error!',
  //         text: 'Failed to fetch the business entity details.',
  //         icon: 'error',
  //         confirmButtonText: 'OK',
  //         cancelButtonText: 'Ask Chimp',
  //         confirmButtonColor: '#be0032',
  //         cancelButtonColor: '#007bff',
  //         showCancelButton: true,
  //       }).then((result) => {
  //         if (
  //           result.isDismissed &&
  //           result.dismiss === Swal.DismissReason.cancel
  //         ) {
  //           if (this.chatBotComponent) {
  //             Swal.fire({
  //               title: 'Processing...',
  //               text: 'Please wait while Chimp processes your request.',
  //               allowOutsideClick: false,
  //               didOpen: () => {
  //                 Swal.showLoading();
  //                 this.chatBotComponent.setInputData(
  //                   'Failed to fetch the business entity details'
  //                 );
  //                 this.chatBotComponent.responseReceived.subscribe(
  //                   (response) => {
  //                     Swal.close();
  //                     this.chatResponseComponent.showPopup = true;
  //                     this.chatResponseComponent.responseData = response;
  //                     this.playLoadingSound();
  //                     this.stopLoadingSound();
  //                   }
  //                 );
  //               },
  //             });
  //           } else {
  //             console.error('ChatBotComponent is not available.');
  //           }
  //         }
  //       });
  //     }
  //   );
  // }

  fetchIndstryList(): void {
    this.entityService.getIndustryList().subscribe((response) => {
      this.industryList = response;
    });
  }

  onEntityRegister(input: any): void {
    const entityId = Number(localStorage.getItem('entityId'));
    if (!entityId || isNaN(entityId)) {
      console.error('Invalid entityId retrieved from local storage.');
      Swal.fire({
        title: 'Error!',
        text: 'Entity ID is missing or invalid. Please log in again.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      }).then(() => {
        this.router.navigate(['/user-login']);
      });
      return;
    }

    // Ensure entityId is assigned as an Entity object
    this.entityTradingName.entityId = { entityId } as Entity;

    const savePromises = [
      this.entityService
        .saveEntityTradingName(this.entityTradingName)
        .toPromise(),
    ];

    for (const additionalName of this.newTradingName) {
      const additionalEntityTradingName = new EntityTradingName();
      additionalEntityTradingName.entityId = { entityId } as Entity;
      additionalEntityTradingName.tradingName = additionalName;
      additionalEntityTradingName.industryId = this.entityTradingName.industryId;

      savePromises.push(
        this.entityService
          .saveEntityTradingName(additionalEntityTradingName)
          .toPromise()
      );
    }

    Promise.all(savePromises)
      .then(() => {
        this.selectedSubscriptionFeeId =
          this.route.snapshot.queryParams['subscriptionPlanId'];
        if (
          this.selectedSubscriptionFeeId == 1 ||
          this.selectedSubscriptionFeeId == null
        ) {
          Swal.fire({
            title: 'Success!',
            text: 'Registration successfully done. Please verify your email.',
            icon: 'success',
            iconColor: '#28a745',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then((result) => {
            if (result.isConfirmed) {
              this.router.navigate(['/user-login']);
            }
          });
        } else {
          const subscriptionPlanId = this.selectedSubscriptionFeeId;
          const onGrace = false;
          this.router.navigate(['/payment'], {
            queryParams: { subscriptionPlanId, onGrace},
          });
        }
      })
      .catch((error) => {
        console.error('Error saving trading names:', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to register business entity.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Failed to register business entity'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      });
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}
