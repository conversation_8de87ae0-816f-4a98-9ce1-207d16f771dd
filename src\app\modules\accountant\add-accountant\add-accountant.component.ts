import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { UserService } from '../../admin/components/user/user.service';
import { EntityService } from '../../entity/entity.service';
import { User } from '../../admin/components/user/user';
import { Entity } from '../../entity/entity';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-add-accountant',
  templateUrl: './add-accountant.component.html',
  styleUrls: ['./add-accountant.component.css'],
})
export class AddAccountantComponent implements OnInit {
  entityId: number = 0;
  user: User = new User();
  entity: Entity = new Entity();

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private router: Router,
    private entityService: EntityService,
    private route: ActivatedRoute
  ) {}

  addEntity(): void {
    this.userService
      .addUserToEntity(this.user.userId, this.entityId)
      .subscribe((response) => {
        Swal.fire({
          title: 'Success!',
          text: 'Registration successful. Redirecting to login...',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.router.navigate(['/user-login']);
        });
      });
  }

  cancel(): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to cancel?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes',
      cancelButtonText: 'No',
    }).then((result) => {
      if (result.isConfirmed) {
        this.router.navigate(['/user-login']);
      }
    });
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.entityId = params['entityId'];
      this.user.userId = params['userId'];
    });
    this.getEntity();
  }

  getEntity() {
    this.entityService.getBusinessEntityById(this.entityId).subscribe(
      (response) => {
        this.entity = response;
      },
      (error) => {
        console.error('Error fetching entity data:', error);
      }
    );
  }
}
