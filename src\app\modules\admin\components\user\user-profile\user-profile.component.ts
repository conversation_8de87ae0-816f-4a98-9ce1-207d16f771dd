import { Component, OnInit, ViewChild } from '@angular/core';
import { User } from '../user';
import { UserService } from '../user.service';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';

@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.css'],
})
export class UserProfileComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent)
  chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  userId: number = 0;
  user: User = new User();
  userPassword: string = '';

  showSuccessMessage: string = '';
  showErrorMessage: string = '';
  changePassword: boolean = false;
  isPasswordVisible: boolean = false;
  passwordError: string = '';
  passwordMismatchError: string = '';
  rePassword: string = '';


  constructor(private userService: UserService, private router: Router) {}

  ngOnInit(): void {
    const userIdString = localStorage.getItem('userid');
    this.userId = Number(userIdString);

    if (this.userId !== null) {
      this.userService.getUserById(this.userId).subscribe(
        (response: User) => {
          this.user = response;
        },
        (error) => {
          console.error('Error fetching user data', error);
        }
      );
    } else {
      console.error('User not found');
    }
  }

  validatePassword() {
    const password = this.userPassword;
    const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~])[A-Za-z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~]{8,}$/;

    if (!passwordPattern.test(password)) {
      this.passwordError = 'Password must be at least 8 characters long and include uppercase, lowercase, number, and one of the following special characters: !@#$%^&*()_+-=[]{};\':"\\|,.<>/?`~';
    } else {
      this.passwordError = '';
    }
  }
  
  checkPasswordsMatch() {
    if (this.userPassword !== this.rePassword) {
      this.passwordMismatchError = 'Passwords do not match.';
    } else {
      this.passwordMismatchError = '';
    }
  }

  onUserUpdate() {

    // If change password is checked, validate inputs
  if (this.changePassword) {
    this.validatePassword();
    this.checkPasswordsMatch();

    if (!this.userPassword || this.passwordError || this.passwordMismatchError) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Please enter a valid password and make sure both fields match.',
        icon: 'warning',
        confirmButtonColor: '#ffc107',
      });
      return; // Stop update if validation fails
    }}

    if (this.userPassword?.trim()?.length > 7) {
      this.userService
        .updateUserPassword(this.userId, this.userPassword)
        .subscribe(
          (response) => {
            this.showSuccessMessage = 'Password updated successfully.';
          },
          (error) => {
            console.error('Error updating password:', error);
            this.showErrorMessage =
              'Failed to update password. Please try again.';
          }
        );
    }
    this.userService.updateUser(this.userId, this.user).subscribe(
      (response: any) => {
        this.userService.setAuthToken(response.token);
        Swal.fire({
          title: 'Success!',
          text: 'User updated successfully.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
          timer: 3000,
          timerProgressBar: true,
          backdrop: true,
        });
        this.router.navigate(['/user-profile']);
      },
      (error) => {
        console.error('Error updating user', error);
        Swal.fire({
          title: 'Error!',
          text: 'Error updating user.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
          timer: 3000,
          timerProgressBar: true,
          backdrop: true,
          cancelButtonText: 'Ask Chimp',
          cancelButtonColor: '#007bff',
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Error updating user.');
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
  }

  enableChangePassword(): void {
    this.changePassword = true;
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}
