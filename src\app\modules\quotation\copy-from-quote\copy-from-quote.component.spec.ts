import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CopyFromQuoteComponent } from './copy-from-quote.component';

describe('CopyFromQuoteComponent', () => {
  let component: CopyFromQuoteComponent;
  let fixture: ComponentFixture<CopyFromQuoteComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [CopyFromQuoteComponent]
    });
    fixture = TestBed.createComponent(CopyFromQuoteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
