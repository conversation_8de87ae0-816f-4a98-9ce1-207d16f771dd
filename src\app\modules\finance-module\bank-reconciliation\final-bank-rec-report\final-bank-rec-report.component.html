<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container pt-5 px-5 position-relative">
  <div class="col-12 d-flex justify-content-between align-items-center py-2">
    <h1 class="h1 fw-bold">Bank Reconciliation Report</h1>
  </div>
  <div class="col-12 card">
    <div class="row">
      <div class="col-9">
        <label for="searchTerm">Statement Id</label>
        <input
          type="text"
          id="searchTerm"
          class="w-100 custom-input"
          [(ngModel)]="searchFilter.searchTerm"
        />
      </div>
      <div class="col-3 d-grid">
        <label for="bankType">Selected Bank</label>
        <select
          class="custom-select"
          id="bankType"
          [(ngModel)]="searchFilter.bankAccount"
        >
          <option value="All">All</option>
          <option
            *ngFor="let bankAccount of bankAccountList"
            [value]="bankAccount.bankName"
          >
            {{ bankAccount.bankName }}
          </option>
        </select>
      </div>
    </div>
  </div>
  <div class="col-12 pb-0 pt-3">
    <table>
      <colgroup>
        <col width="50%" />
        <col width="20%" />
        <col width="20%" />
        <col width="10%" />
      </colgroup>
      <tr class="tb-head">
        <th>Statement Details</th>
        <th class="text-center">Bank Account</th>
        <th>Statement Balance</th>
        <th>Action</th>
      </tr>
      <tr
        class="tb-detail"
        *ngFor="let bankStatementHeader of filteredCompletedBankStatements"
      >
        <td class="position-relative">
          <p *ngIf="bankStatementHeader.statementDate">
            {{ bankStatementHeader.statementDate }}
          </p>
          <p>{{ bankStatementHeader.bankStatementId }}</p>
          <p>
            {{ bankStatementHeader.bankAccount?.accountName }} -
            {{ bankStatementHeader.bankAccount?.accountNumber }}
          </p>
        </td>
        <td class="text-center">
          {{ bankStatementHeader.bankName }}
        </td>
        <td>{{ bankStatementHeader.statementTotal | currency }}</td>

        <td>
          <button
            class="btn btn-sm bg-primary text-white"
            (click)="
              openBankRecFinalReoport(
                bankStatementHeader.entityId,
                bankStatementHeader.bankStatementId
              )
            "
          >
            <i class="fa fa-eye"></i>
          </button>
        </td>
      </tr>
    </table>
  </div>
  <div style="margin-top: 20px" [ngClass]="{ 'd-none': isViewLoading }">
    <iframe
      #finalBankRecView
      id="#finalBankRecView"
      width="700px"
      height="700px"
    ></iframe>
  </div>
</div>
