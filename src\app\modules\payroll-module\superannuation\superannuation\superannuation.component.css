/* General Styles */

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
  }

  h2{
    font-size: 24px;
    font-weight: bold;
  }
  
  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column; /* Stack children vertically */
    gap: 20px; /* Adds 20px gap between rows */
}
.actions {
  display: flex;
  align-items: center;
}

/* Payroll Settings Heading */
.actions h2 {
  flex: 1;
  font-family: Inter, sans-serif;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262FF;
  margin: 0;
}

/* Sub-container Styles for Heading */
.sub-container {
  background-color: #ffffff;
  padding: 20px;
  border: 1px solid #4262FF;
  border-radius: 10px;
  margin-bottom: 20px;
}

.sub-container h2 {
  font-size: 24px;
  font-weight: bold;
}
  
.disabled {
  pointer-events: none;
  opacity: 0.6;
  cursor: not-allowed;
}
  
  /* Payroll Head Section */
  .payroll-head {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .payroll-head-icon {
    width: 10%;
    display: flex;
    justify-content: center;
    font-size: 60px;
    color: #4262FF;
  }
  
  .payroll-head-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-weight: bold;
  }
  
  .payroll-head-content strong {
    font-size: 16px;
    color: #333;
  }
  
  .payroll-head-content span {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
  }
  
  .payroll-head-content a {
    color: #4262FF;
    text-decoration: underline;
  }

  .payroll-info{
    display: flex;
    justify-content: flex-start;
    width: 100%;
    background-color: transparent;
    border-radius: 10px;
  }

 .PayRun-Tabs{
  display: flex;
    justify-content: flex-start;
    width: 100%;
    background-color: transparent;
    border-radius: 10px;
 }
  
  .nav-tabs {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;

  }

  .nav-tabs .nav-link {
    padding: 10px 39.5px;
    color: #4262FF;
    font-weight: 500;
    background-color: white;
    cursor: pointer;
    border-radius: 0 0 0 0;
  }
  
  .nav-tabs .nav-link.active {
    color: black;
    background-color: #f1eeee;
    border-radius: 0 0 0 0; 
    border-bottom: none;
  }
  
  /* Pay Calendars Section */

  
  .pay-calendar-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: white;
    margin-bottom: 5px;
    border-radius: 10px;
  }
  
  .pay-calendar-head strong {
    font-size: 18px;
    color: #333;
  }
  
  .btn-group .btn-primary {
    background: white;
    border: 1px solid #4262FF;
    color: #4262FF;
    font-weight: bold;
    border-radius: 10px;
    padding: 10px 20px;
  }
  
  .btn-group .dropdown-menu {
    width: 100%;
    padding: 10px;
    border-radius: 10px;
    font-weight: bold;
    overflow: hidden;
  }
  
  .dropdown-item {
    font-weight: bold;
    color: #333;
    padding: 8px 15px;
  }
  
  .dropdown-item:hover {
    background-color: #4262FF;
    color: white;
    font-weight: bold;
  }

  .pay-calendar-table {
    background-color: white;
    border-radius: 10px;
    padding: 20px 20px;
  }
  
  /* Table Styles */
  .pay-calendar-table table {

    width: 100%;
    border-collapse: collapse;
    border-radius: 10px;
  }
  
  .pay-calendar-table th,
  .pay-calendar-table td {
    text-align: left;
    padding: 20px 20px;
  }
  
  .pay-calendar-table th {
    background-color: white;
    font-weight: bold;
    color: #333;
  }
  
  .pay-calendar-table tbody tr {
    background-color: white;
    border-bottom: 1px solid #ddd;
  }
  
  .pay-calendar-table tbody tr:hover {
    background-color: #f1f1f1;
  }
  
  /* History Section */
  .payroll-history {
    margin-top: 20px;
    background-color: white;
    border: 1px solid #4262FF;
    border-radius: 10px;
    padding: 20px 20px;
  }
  
  .payroll-history h3 {
    display: flex;
    align-items: center;
  }
  
  .payroll-history .btn-toggle {
    background: none;
    border: none;
    font-size: 18px;
    color: black;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  
  .payroll-history .btn-toggle i {
    margin-left: 10px;
    font-weight: bold;
    transition: transform 0.3s;
  }
  
  .collapse:not(.show) + h3 .btn-toggle i {
    transform: rotate(0deg);
  }
  
  .collapse.show + h3 .btn-toggle i {
    transform: rotate(180deg);
  }
  
  .collapse {
    background-color:white;
    border: 1px solid white;
    border-radius: 5px;
  }
  
  .collapse p {
    font-size: 16px;
    color: #000000;
  }

/* Pay-items */
.pay-items-section {
  display: flex; 
  background-color: white;
  border-radius: 10px;
}

.pay-items-sections {
  display: flex;
  background-color: white;
  border-radius: 10px;
  margin-inline: 12%;
}

.pay-items-sections-sub{
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 10px;
  margin-inline: 12%;
}

.pay-item-2{
  display: flex; 
  flex-direction: column;
  background-color: transparent;
  border-radius: 10px;
}

.side-bar {
  width: 20%; /* Set sidebar width */
  background-color: #ffffff;
  border-radius: 10px 0 0 10px;
  border-right: 1px solid #ddd;
  padding: 10 0px; /* Add some padding */
}

.nav_pay_item {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.nav_pay_item li {
  margin-bottom: 10px;
}

.nav_pay_item a {
  text-decoration: none;
  width: 100%;
  color: #333;
  font-weight: 500;
  padding: 10px;
  display: block;
  border-radius: 10px;
}

.nav_pay_item a.active {
  color: black;
  font-weight: bold;
  border-left: 3px solid #4262FF;
  background-color: #dfe4ff;
}

.nav_pay_item a:hover {
  background-color: #d7dbf5;
}


.pay_item-content{
  border-radius: 0 10px 10px 0;
  width: 80%;
}

.Earnings , .Deductions , .Reimbursements, .Leave {
  width: 100%; /* Adjust width to match the sidebar */
  padding: 20px;
  background-color: white;
}

.Leave-form-3 , .Leave-form-final{
  width: 100%; /* Adjust width to match the sidebar */
  padding: 20px;
  background-color: transparent;
}

.pay_item_head {
  display: flex;
  padding: 10px 0;
  border-radius: 0 10px 10px 0;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pay_item_head h2 {
  font-size: 24px;
  font-weight: bold;
}

.table-responsive {
  margin-top: 10px;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table thead th {
  background-color: #ffffff;
  font-weight: bold;
  color: #333;
  padding: 10px;
  text-align: center;
}

.table tbody tr {
  background-color: white;
  border-bottom: 1px solid #ddd;
}

.table tbody tr:hover {
  background-color: #f1f1f1;
}

.table tbody td {
  padding: 10px;
  color: #555;
  text-align: center;
}

.btn-group .dropdown-menu {
  padding: 0 40px;
  border-radius: 10px;
  overflow: hidden;
  font-weight: bold;
}

.dropdown-item:hover {
  background-color: #4262FF;
  color: white;
}

.btn-secondary {
  background: none;
  border: none;
  color: #333;
  font-size: 18px;
  cursor: pointer;
}

.btn-secondary:hover {
  color: #4262FF;
}

.pay-item-one{
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
}

.pay-item-two{
  width: 100%;
  display: flex;
  margin-bottom: 20px;
}

.pay-item-col1{
  display: flex;
  flex-direction: column;
  width: 30%;
  border-right: 1px solid #666666;
}

.pay-item-col2{
  display: flex;
  flex-direction: column;
  padding-left: 10%;
  width: 70%;
}
.pay-item-col3{
  display: flex;
  margin-right: 25px;
  width: 70%;
}

.custom-search {
  background-color: #c2c2c2;
  border-radius: 10px;
}
.filter-buttons {
  width: 30%;
  display: flex;
  align-items: center;
  gap: 25px;
}


.pay-item-four {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding:  0 10px;
  border: 2px solid #c7c7c7;
  border-radius: 10px;
  margin-top: 20px;
  margin-bottom: 20px;
  background-color: white;
}

.pro-pic {
  width: 50px;
  height: 50px;
  background-color: #e0e0e0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  background-color: #4262FF;
}

.User-Details {
  padding-top: 15px;
  flex-grow: 1;
  margin-left: 15px;
}

.User-Details h5 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.User-Details small {
  size: 12px;
  color: gray;
}

.approve-btn button {
  color: #4262FF;
  font-weight: bold;
  border: 2px solid #4262FF;
  background: transparent;
  border-radius: 10px;
  padding: 5px 15px;
}

.app-icon {
  margin-left: 10px;
}

.app-icon i {
  font-size: 1.2rem;
  color: #555;
}

.leave-emp {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.emp-name,
.salary-earnings,
.date-next-payment {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 5px 15px;
  border: 1px solid #ccc;
}

label {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
}

.employee-name {
  font-size: 18px;
  font-weight: bold;
  color: #4262FF;
  margin: 0;
}

.view-btn {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  background-color: #f2f2f2;
  cursor: not-allowed;
}

.next-payment-date {
  font-size: 16px;
  font-weight: bold;
  width: 100%;
}



/* modal  */
  .modal-content {
    width: 100%; /* Ensure content fills the dialog width */
    padding: 20px;
    border-radius: 10px; /* Rounded corners */
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
}

.modal-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between; /* Space between title and close button */
}

.modal-head {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between; /* Space between title and close button */
}

.ot-modal-header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between; /* Space between title and close button */
} 

.modal-header .modal-title {
    font-family: 'Inter', sans-serif;
    font-weight: bold;
    font-size: 20px;
    color: black; /* Consistent title color */
}

.ot-modal-header .modal-header .modal-title {
  font-family: 'Inter', sans-serif;
  font-weight: bold;
  font-size: 20px;
  color: black; /* Consistent title color */
}

.modal-header .btn-close {
    outline: none;
    box-shadow: none;
}

.modal-body {
    font-family: 'Arial', sans-serif;
    color: #333; /* Darker text color for readability */
}

.modal-body .form-label {
    font-weight: 500;
    color: #000000; /* Label color matches theme */
}

.modal-body .form-control,
.modal-body .form-select {
    border-radius: 8px;
    border: 1px solid #ccc;
    padding: 10px;
    font-size: 16px;
}

.modal-footer {
    border-top: none; /* Remove default border */
    display: flex;
    justify-content: flex-end;
    gap: 20px;
}

.modal-footer .btn-secondary,
.modal-footer .btn-primary , .btn-primary{
    font-size: 16px;
    border-radius: 8px;
    font-weight: bold;
}

.modal-footer .btn-secondary {
    padding: 10px 20px;
    background-color: white;
    color: #4262FF;
    border: 1px solid #4262FF;
}

.modal-footer .btn-secondary:hover {
    background-color: #4262FF;
    color: white;
    border: 1px solid #4262FF;
}
.modal-footer .btn-primary , .btn-primary {
    padding: 10px 40px;
    background: linear-gradient(to right, #4262FF, #512CA2);
    color: #fff;
    border: none;
}

.modal-footer .btn-primary:hover ,
.btn-primary:hover {
    background: linear-gradient(to right, #512CA2, #4262FF);
}

/* Center the modal in the middle of the page */
.modal-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh; /* Full height */
    max-width: 40%; /* Increase width to 80% of the viewport */
}

.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translateY(-50px); /* Starting animation position */
}

.modal.show .modal-dialog {
    transform: translateY(0); /* End animation position */
}

.custom-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem; /* Adjust size as needed */
    color: #666; /* Adjust color to match your theme */
    cursor: pointer;
    outline: none;
    display: flex;
    align-items: center;
    margin-right: -8px; /* Adjust to align with the edge if needed */
}

.custom-close-btn:hover {
    color: #4262FF; /* Optional hover color */
}

.add_calender{
  width: 30%;
  color: #4262FF;
  background-color: white;
  font-weight: bold;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20px;
}

/* Employee  */
.earnings-container {
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
  width: 100%;
  border: 2px solid #c0c0c0;
}
.opening-balance{
  display: flex;
}

.opening-balance h2 {
  font-size: 18px;
  margin-right: 5px;
  font-weight: bold;
}

.earnings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.earnings-title {
  font-size: 16px;
  font-weight: bold;
  color: #000000;
  margin: 0;
}

.earning-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.earning-description {
  flex: 2;
  font-size: 14px;
  font-weight: bold;
  color: #4262FF;
}

.earning-amount-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.earning-amount {
  flex: 1;
  padding: 5px;
  border: 2px solid #ccc;
  border-radius: 5px;
  text-align: right;
}

.btn-remove {
  background: none;
  border: none;
  color: #999;
  margin-left: 10px;
  cursor: pointer;
  font-size: 16px;
}

.btn-remove:hover {
  color: #ff4d4f;
}

.btn-add-earning {
  background: white;
  border: 1px solid #4262FF;
  color: #4262FF;
  font-weight: bold;
  border-radius: 10px;
  padding: 10px 20px;
}

.btn-add-earning:hover {
  background-color: #f0f4ff;
}

.earning-total {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  padding: 10px 0;
  margin-top: 10px;
}

.earning-total span {
  font-size: 14px;
  color: #333;
}

.emp-open-footer {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  padding: 10px;
}

.cancel-btn{
  background-color: #ffffff;
  color: #4262FF;
  border: 1px solid #4262FF;
  font-weight: bold;
  padding: 10px 25px;
  border-radius: 10px;
  margin-top: 20px;

}

.cancel-btn:hover {
  background-color: #4262FF;
  color: #ffffff;
}

.save-btn{
  background: linear-gradient(to right, #4262FF, #512CA2);
  color: #fff;
  border: 1px solid #4262FF;
  font-weight: bold;
  padding: 10px 30px;
  border-radius: 10px;
  margin-top: 20px;
}

.save-btn:hover {
  background: linear-gradient(to right, #512CA2, #4262FF);
}

.menu-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 40px;
  margin-top: 20px;
  background-color: #dfe4ff;
  padding: 20px;
  border-radius: 20px;
}

.menu-sub-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 180px;
}

.menu-topic {
  font-size: 20px;
  font-weight: 600;
  color: #555;
}

.line {
  width: 2px;
  background: #555;
}

/* payRun section */

.custom-dropdown-item:hover {
  background-color: #007bff; 
  color: white; 
}

/* Skip button */
.custom-skip-button {
  background-color: #dc3545; 
  color: white; 
  font-weight: normal; 
  border: none; 
  padding: 0.2rem 0.5rem;
}

.custom-skip-button:hover {
  background-color: #a71d2a; 
}

.date-range,
.amount-range {
    display: flex;
    align-items: center;
}

.date-range input,
.amount-range input {
    flex: 1;
    margin-right: 5px;
    padding: 8px;
}

.date-range span,
.amount-range span {
    margin: 0 5px;
}

@media (max-width: 680px) {

  .date-range,
  .amount-range {
      display: flex;
      flex-direction: column;
      align-items: center;
  }
}

.Card {
  width: 100%;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* Row layout for filters */

.row1 {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.row1_col1 {
  width: 30%;
  /* Ensures enough space for input text */
}

.row1_col2,
.row1_col3,
.row1_col4,
.row1_col5 {
  width: 20%;
}

.row1_col1 input,
.row1_col2 select,
.row1_col3 input,
.row1_col4 input {
  width: 100%;
  height: 49px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
}

.row2 {
  display: flex;
  align-items: right;
  justify-content: flex-end;
  margin-top: 20px;
}



.validation-container {
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.validation-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
}

.validation-item {
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.error {
  background-color: #ffe5e5;
  border-left: 5px solid #ff4d4d;
}

.warning {
  background-color: #fff6e5;
  border-left: 5px solid #ffae42;
}

.error-code,
.error-severity {
  color: #d32f2f;
  font-weight: bold;
}

.warning-code,
.warning-severity {
  color: #ff9800;
  font-weight: bold;
}

p {
  margin: 5px 0;
}

code {
  background: #f4f4f4;
  padding: 2px 5px;
  border-radius: 4px;
  font-family: monospace;
}

.valueCheckbox {
  width: 5%;
}

@media (max-width: 700px) {
.row1 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 10px;
}

.row1_col1,
.row1_col2,
.row1_col3,
.row1_col4,
.row1_col5 {
  width: 100%;
}
}

.modal.show {
  display: block;
  background: rgba(0, 0, 0, 0.5);
}

.notification-item {
  margin-bottom: 15px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notification-item .card-body {
  padding: 15px;
}

.notification-item .card-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #007bff;
}

.notification-item .card-text {
  font-size: 0.9rem;
  color: #333;
}