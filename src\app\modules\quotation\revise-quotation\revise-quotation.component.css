/* Global styles */

body {
  font-family: Arial, sans-serif;
  background-color: transparent;
  margin: 0;
  padding: 0;
}

.container {
  width: 100%;
  max-width: 1200px; /* You can change this value to suit your desired width */
  margin: 0 auto;
  padding: 10px;
  background-color: transparent;
}

/* Heading styles */


.heading {
  display: flex;
  background-color: transparent;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: none;
  border: none;
  gap: 5px;
  /* Adds space between buttons */
}

.heading h3 {
  flex: 1;
  margin-bottom: 0;
  font-family: Inter;
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
  top: 264px;
}

.heading .transparent-button {
  top: 256px;
  border: 1px solid #4262ff;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  background-color: white;
  color: #4262ff;
  font-family: Inter;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

.heading .transparent-button:hover {
  background-color: #4262ff;
  color: white;
}

.bd {
  border: 2px solid #cec9c980;
  border-radius: 12px;
  margin:0;
  width: 100%;
}
/* Form styles */

.form-section {
  display: flex;
  font-family: Arial, sans-serif;
  flex-direction: column;
  padding: 20px;
  background-color: #f7f7f7; /* If you want a solid background color */
}

.form-row {
  display: flex;
  gap: 32px;
  padding-right: 20px;
  margin-bottom: 5px;
}

.form-group {
  flex: 1;
}

.input-style {
  height: 49px;
  top: 656px;
  left: 111px;
  padding: 10px;
  font-size: 14px;
}

.create-customer-container {
  display: flex;
  justify-content: flex-end;
}

label {
  display: block;
  font-weight: bold;
  color: #333;
}

.create-customer-btn {
  float: right;
  color: #4a4ae2;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-style: italic;
  font-weight: bold;
}


/* Customer and Search Item sections */

.customer-section,
.search-item-section {
  flex-basis: 50%;
}

/* Search bar styles */

.search-bar {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-left: 20px;
  margin-bottom: 20px;
  align-items: center;
  position: relative;
}

.search-bar input.input-style {
  flex-grow: 1;
  border: 1px solid #ccc;
  border-radius: 8px;
  padding-right: 40px;
  height: 49px;
  /* Add padding to make space for the button */
}

.search-bar button.btn.btn-link {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.search-bar button.btn.btn-link1 {
  background: linear-gradient(180deg, #4262ff 0%, #283b99 100%);
  color: white;
  border-radius: 8px;
  opacity: 0px;
}

/* Table styles */

.table-section {
  background-color: transparent;
  overflow: hidden;
  margin-bottom: 20px;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: -20px;
}

.table-responsive {
  overflow-x: auto;
  width: 100%;
}

.table-responsive table {
  width: 100%;
  min-width: 600px;
  white-space: nowrap;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: linear-gradient(
    90deg,
    rgba(66, 98, 255, 0.06) 0%,
    rgba(63, 20, 153, 0.06) 100%
  );
  color: black;
  text-align: left;
  padding: 12px;
  font-weight: normal;
}

td {
  padding: 12px 5px;
  border-bottom: 1px solid #ddd;
  background-color: white;
  vertical-align: middle;
  margin-left: auto;
}

.main-row-note{
  display: flex;
  flex-direction: row;
  width: 100%;
}

.notes-totals-section {
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  gap: 20px;
}

.notes-section,
.totals-section {
  flex: 1;
  width: 50%;
  background-color: transparent;
  border-radius: 8px;
  /* padding: 30px 10px; */
  box-shadow: none;
  border: none;
  margin-left: 14px;
  margin-right: 10px;
  margin-bottom: 20px;
  height: 300px;
  box-sizing: border-box;
}

.notes-section {
  flex: 1;
  padding-bottom: 20px;
}

#notes {
  border: 1px solid #e6e6e6;
  width: 100%;
  height: 300px;
  border-radius: 12px;
  opacity: 1;
  resize: none;
}

.totals-section {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
}

.totals-row {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  margin-left: auto;
}

.totals-row1 {
  flex-grow: 1;
  width: 60%;
  margin-right: 10px;
  text-align: right;
}

.totals-row2 {
  width: 40%;
  text-align: right;
  padding: 5px 10px;
  border: 1px solid #cec9c980;
  border-radius: 10px;
  background-color: #d9d9d94d;
  box-sizing: border-box;
}

/* Row styles in totals */
.totals-row {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 16px;
  color: #333333;
}

/* Special styling for the total amount */
.totals-row strong {
  font-size: 18px;
  font-weight: bold;
}

/* Styling for labels in totals rows */
.totals-row1 {
  font-weight: 500;
}

/* Styling for values in totals rows */
.totals-row2 {
  font-weight: 600;
  text-align: right;
}

.cancel-btn1 {
  padding: 10px 60px;
  border-radius: 17px;
  cursor: pointer;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  background: #ffffff;
  color: #6822ff;
  border: 2px solid #4262ff;
  margin-top: 15px;
  /* margin-left: 220px;
  margin-right: 10px; */
}

.cancel-btn1:hover {
  background: #4262ff;
  color: white;
}

.add-btn1 {
  padding: 10px 70px;
  border: none;
  border-radius: 17px;
  cursor: pointer;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  margin-top: 15px;
  /* margin-right: 32px; */
  
}

.add-btn1:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

input::file-selector-button {
  font-weight: bold;
  background: #4262ff;
  color: white;
  border: none;
  border-radius: 3px;
}

#checkBox {
  display: inline-block;
  vertical-align: middle;
  margin-left: 20px;
  /* Reduce the gap between checkbox and label */
}

#markas-1 {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0px;
  /* Reset any left margin/padding */
}


.update-link {
  font-size: 15px;
  font-family: Segoe UI, sans-serif;
  font-weight: 600;
  color:   #4a4ae2;
  margin-top: 20px;
  margin-left: 10px;
  display: inline-block; /* Allow custom width */
  white-space: nowrap; /* Prevent wrapping to the next line */
  text-align: left;
}


.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.form-group {
  margin-bottom: 10px;
  text-align: left;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 25.41px;
  color: #444343;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: normal;
}

.form-group input {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  font-size: 14px;
}

.popup-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.left-section {
  display: flex;
  justify-content: flex-start;
}

.right-section {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-btn {
  padding: 10px 60px;
  border-radius: 17px;
  cursor: pointer;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  background: #ffffff;
  color: #6822ff;
  border: 2px solid #4262ff;
  margin-top: 15px;
  /* margin-left: 250px; */
}

.cancel-btn:hover {
  background: #4262ff;
  color: white;
}

.add-btn {
  padding: 10px 70px;
  border: none;
  border-radius: 17px;
  cursor: pointer;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  margin-top: 15px;
}

.add-btn:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

.form-check {
  display: inline-flex;
  align-items: center;
}

.form-check-input {
  margin-right: 5px;
  margin-left: 5px;
}

.form-check-label {
  margin-right: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  text-transform: capitalize;
}

.form-check-tax {
  display: inline-flex;
  align-items: center;
}

.form-check-tax-input {
  margin-right: 5px;
}

.form-check-tax-label {
  margin-right: 2px;
}

.close-icon {
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.close-icon:hover {
  color: #000;
}

.table-responsive {
  overflow-x: auto;
}

.new-item {
  background-color: #4262ff;
  color: white;
}

.new-item:hover {
  background-color: #512ca2;
}

@media (max-width: 768px) {
  .heading {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .heading h3 {
    font-size: 26px;
    margin-bottom: 10px;
  }

  .heading .button-group {
    width: 100%;
  }

  .heading .transparent-button {
    width: 100%;
    text-align: center;
  }

  .form-row {
    flex-direction: column;
    gap: 20px;
    padding-right: 0;
  }

  .form-group {
    width: 100%;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
  }

  #tradingName{
    background-color: #fff;
  }

  .form-dataInput{
    display: flex;
    flex-direction: column;
  }

  .InputBoxes {
    flex-direction: column;
  }

  .search-bar {
    justify-content: flex-start;
    padding-left: 0;
    margin-bottom: 15px;
  }

  .search-bar input.input-style {
    width: 100%;
  }

  .table-section {
    padding-left: 0;
    padding-right: 0;
    overflow-x: auto;
  }

  table {
    min-width: 800px; /* Ensure table is scrollable on small screens */
  }

  .notes-totals-section {
    flex-direction: column;
    gap: 15px;
  }

  .notes-section,
  .totals-section {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    padding: 20px 15px;
    height: auto;
  }

  .totals-row {
    width: 100%;
    flex-direction: row;
    align-items: center;
    gap: 10px;
  }

  .totals-row1,
  .totals-row2 {
    width: auto;
    flex: 1;
    text-align: left;
  }

  .totals-row1 {
    text-align: left;
    margin-right: 10px;
  }

  .totals-row2 {
    text-align: right;
  }

  .popup-footer {
    flex-direction: column;
    gap: 10px;
  }

  .cancel-btn1,
  .add-btn1 {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .cancel-btn,
  .add-btn {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .update-link {
    text-align: center;
    margin-left: 0;
  }

  .form-check {
    flex-direction: column;
    align-items: flex-start;
  }

  .form-check-input {
    margin-right: 0;
    margin-bottom: 5px;
  }

  .form-check-label {
    margin-right: 0;
  }
}

@media (max-width: 480px) {
  .heading h3 {
    font-size: 22px;
  }

  .heading .transparent-button {
    font-size: 16px;
    padding: 8px 16px;
  }

  .form-group input,
  .input-style {
    font-size: 14px;
    padding: 8px;
  }

  .create-customer-btn {
    font-size: 14px;
  }

  .table th,
  .table td {
    padding: 8px;
    font-size: 14px;
  }

  .notes-section textarea {
    font-size: 14px;
  }

  .totals-row1,
  .totals-row2 {
    font-size: 14px;
  }

  .cancel-btn1,
  .add-btn1,
  .cancel-btn,
  .add-btn {
    font-size: 14px;
    padding: 8px 16px;
  }

  .update-link {
    font-size: 14px;
  }
}