<app-admin-navigation></app-admin-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <div class="actions">
    <h1>Entity Requests</h1>
  </div>
  <div class="table-responsive">
    <table>
      <thead>
        <tr class="table-head">
          <th
            scope="col"
            style="width: 100px; text-align: center"
            class="valuehead"
          >
            First Name
          </th>
          <th
            scope="col"
            style="width: 100px; text-align: center"
            class="valuehead"
          >
            Last Name
          </th>
          <th
            scope="col"
            style="width: 100px; text-align: center"
            class="valuehead"
          >
            User Type
          </th>
          <th
            scope="col"
            style="width: 100px; text-align: center"
            class="valuehead"
          ></th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let request of businessEntityRequestList">
          <tr>
            <td style="width: 100px; text-align: center" class="value">
              {{ request.userId.firstName }}
            </td>
            <td style="width: 100px; text-align: center" class="value">
              {{ request.userId.lastName }}
            </td>
            <td style="width: 100px; text-align: center" class="value">
              {{ request.userId.userTypeId.userType }}
            </td>
            <td style="width: 100px; text-align: center" class="value">
              <button
                class="btn btn-success btn-sm"
                (click)="acceptRequest(request)"
                style="
                  margin-right: 2px;
                  border: none;
                  padding: 4px;
                  font-size: 1.2rem;
                "
                title="Accept"
              >
                Accept
              </button>

              <button
                class="btn btn-danger btn-sm"
                (click)="declineRequest(request.businessEntityRequestId)"
                style="
                  margin-right: 2px;
                  border: none;
                  padding: 4px;
                  font-size: 1.2rem;
                "
                title="Decline"
              >
                Decline
              </button>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>
