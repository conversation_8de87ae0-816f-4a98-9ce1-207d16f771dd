import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Entity, EntityTradingName } from 'src/app/modules/entity/entity';
import { EntityService } from 'src/app/modules/entity/entity.service';

@Component({
  selector: 'app-logo-header',
  templateUrl: './logo-header.component.html',
  styleUrls: ['./logo-header.component.css']
})
export class LogoHeaderComponent implements OnInit {

    entity: Entity = new Entity();
    entityTradingName: EntityTradingName = new EntityTradingName();
    entityId: number = 0;
    logoUrl: string | null = null;
  
    constructor(private router: Router, private entityService: EntityService) {}
  
  ngOnInit(): void {
      this.entityId = JSON.parse(localStorage.getItem('entityId') || '[]');
      this.loadEntityData();
    }
  
    private loadEntityData(): void {
      this.getEntity();
    }
  
    private getEntity(): void {
      this.entityService.getBusinessEntityById(this.entityId).subscribe((data) => {
        this.entity = data;
        this.logoUrl = this.entity.logo ? `data:image/png;base64,${this.entity.logo}` : null;
      });
    }
  
}
