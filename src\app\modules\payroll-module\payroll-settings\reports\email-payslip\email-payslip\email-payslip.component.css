input[type="checkbox"] {
    width: 15px;
    height: 15px;
    cursor: pointer;
  }
  
  .btn-sm {
    padding: 5px 10px;
    font-size: 14px;
  }
  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    /* Stack children vertically */
    gap: 20px;
    /* Adds 20px gap between rows */
}  

.btn-success{
  background: white;
  border: 1px solid #4262FF;
  color: #4262FF;
  font-weight: bold;
  border-radius: 10px;
  padding: 10px 20px;
}