import { Component } from '@angular/core';

@Component({
  selector: 'app-bank-account',
  templateUrl: './bank-account.component.html',
  styleUrls: ['./bank-account.component.css']
})
export class BankAccountComponent {
  // Data for Assets
  assets = [
    { name: 'Cash', debit: '30,000.00', credit: '' },
    { name: 'Receivables', debit: '30,000.00', credit: '' },
  ];

  // Data for Liabilities
  liabilities = [
    { name: 'Loans', debit: '30,000.00', credit: '' },
    { name: 'Employees', debit: '30,000.00', credit: '' },
    { name: 'Payables', debit: '30,000.00', credit: '' },
  ];

  // Data for Equity
  equity = [
    { name: 'Equity', debit: '30,000.00', credit: '' },
    { name: 'Revenues', debit: '30,000.00', credit: '' },
    { name: 'Cost of Sales', debit: '30,000.00', credit: '' },
    { name: 'Expenses', debit: '30,000.00', credit: '' },
  ];

  // Total Values
  get totalDebit(): string {
    return '120,000.00';
  }

  get totalCredit(): string {
    return ''; // Adjust logic if credits exist
  }
}
