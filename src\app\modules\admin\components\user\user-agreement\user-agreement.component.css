.container {
  font-family: Inter;
  background: #ffffff;
  width: 50%;
  margin: 30px auto 30px auto;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  height: 100%;
}

h2 {
  text-align: center;
  color: #455cff;
  font-family: Segoe UI;
  font-size: 30px;
  font-weight: 600;
  line-height: 52.04px;
  text-align: center;
  margin: 5% 0;
}

.terms-box {
  background: #ffffff;
  border: 1px solid #ccc;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  margin-left: 8%;
  margin-right: 8%;
  height: 50%;
  overflow-y: scroll;
}

.terms-box p {
  font-family: Inter;
  font-family: Inter;
  font-size: 15px;
  font-weight: 400;
  line-height: 32px;
  text-align: justify;
  color: #000000;
}

.checkboxes {
  margin-top: 4%;
  margin-left: 30%;
  margin-bottom: 40px;
}

.checkboxes label {
  display: block;
  margin-bottom: 20px;
  font-size: 14px;
}

.checkboxes a {
  color: #0066cc;
  text-decoration: none;
}

.checkboxes a:hover {
  text-decoration: underline;
  cursor: pointer;
}

.proceed-btn-container {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

.proceed-btn {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  padding: 10px;
  width: 150px;
  height: 50px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-family: Inter;
  font-size: 17px;
  font-weight: 700;
  line-height: 29.05px;
  text-align: center;
}

.proceed-btn:hover {
  background: linear-gradient(to left, #4262ff, #512ca2);
}

.proceed-btn.disabled {
  cursor: default;
  background: #d3d3d3;
}

@media (max-width: 768px) {

  .container {
    width: 100%;
  }

  .checkboxes {
  margin-top: 4%;
  margin-left: 30px;
  margin-bottom: 40px;
}
}