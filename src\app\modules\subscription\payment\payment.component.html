<app-home-header></app-home-header>
<form (ngSubmit)="onSubmit()" [formGroup]="paymentForm">
  <div>
    <label for="amount">Amount</label>
    <input
      [value]="'$' + paymentForm.get('amount')?.value"
      id="amount"
      readonly
    />
    <div
      *ngIf="
        paymentForm.controls['amount'].invalid &&
        paymentForm.controls['amount'].touched
      "
    >
      Amount is required and must be greater than 0.
    </div>
  </div>
  <div id="card-element">
    <!-- Stripe Element will be inserted here -->
  </div>
  <button [disabled]="paymentProcessing" type="submit">
    <span *ngIf="paymentProcessing">Proceeding...</span>
    <span *ngIf="!paymentProcessing">Pay</span>
  </button>
</form>
