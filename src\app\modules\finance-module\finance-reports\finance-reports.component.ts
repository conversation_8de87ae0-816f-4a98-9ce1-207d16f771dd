import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-finance-reports',
  templateUrl: './finance-reports.component.html',
  styleUrls: ['./finance-reports.component.css']
})
export class FinanceReportsComponent {


    constructor(
      private router: Router
    ) {}
  
    navigateQuotationReport(): void {
      this.router.navigate(['/quotation-report']);
    }
  
    navigateInvoiceReport(): void {
      this.router.navigate(['/invoice-report']);
    }
  
    navigateCreditNoteReport(): void {
      this.router.navigate(['/credit-note-report']);
    }
  
    navigatePaymentReport(): void {
      this.router.navigate(['/payment-receipt-report']);
    }
  
    navigateInvoiceCashReport(): void {
      this.router.navigate(['/invoice-cash-report']);
    }
  
    navigateCustomerStatementReport(): void {
      this.router.navigate(['/customer-statement']);
    }
  
    navigateAccountsStatementReport(): void {
      this.router.navigate(['/account-statement']);
    }
  


    //finance 
    navigateGeneralPosting(): void {
     this.router.navigate(['/gl-posting-list']);
    }

    navigateGlReport(): void {
    this.router.navigate(['/gl-report']);
    }
    
    navigateBill(): void {
      this.router.navigate(['/payable-bill-list-report']);
     }

    navigateJVReport(): void {
      this.router.navigate(['/jv-list-report']);
    }

    navigateCreditNoteBill(): void {
        this.router.navigate(['/credit-note-bill-list-report']);
    }

    navigatePaymentBill(): void {
      this.router.navigate(['/record-batch-payments-list-report']);
     }

    navigatePnL(): void {
      this.router.navigate(['/pnl-report']);
    }

    navigateBalanceSheet(): void {
      this.router.navigate(['/bs-report']);
    }

    navigateBAS(): void {
       this.router.navigate(['/bas-period']);
    }

    navigateSupplier(): void {
        this.router.navigate(['/supplier-statement']);
    }
   
}
