<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <!-- Heading And Button -->
  <div class="actions">
    <h1>Invoices</h1>
    <div class="btn-group" #dropdownRef [class.show]="isDropdownOpen">
      <button
        type="button"
        class="btn btn-secondary dropdown-toggle gradient-btn"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        (click)="toggleDropdown()"
      >
        <i class="bi bi-three-dots-vertical"></i>
      </button>
      <ul class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen">
        <li>
          <a
            class="dropdown-item"
           (click)="handleSendInvoiceClick()"
          >Send</a>
        </li>
        <li>
          <a class="dropdown-item" (click)="cancelSelectedInvoices(); closeDropdown()">Cancel Invoice</a>
        </li>
        <!--<li>
          <a class="dropdown-item" (click)="markSelectedAsPaid()">Mark as Paid</a>
        </li>-->
        <li>
          <a class="dropdown-item" (click)="handleReviseInvoice(); closeDropdown()">Revise</a>
        </li>
        <li>
          <a class="dropdown-item" (click)="onCreateCreditNoteMultiple(); closeDropdown()">Credit Note</a>
        </li>
        <li *ngIf="userRole !== 'Free'">
          <a class="dropdown-item" (click)="onPaymentreceiptMultiple(); closeDropdown()">Payment</a>
        </li>
      </ul>
    </div>
  </div>

  <div class="search-create">
    <button type="button" (click)="createNewInvoice()" class="create-invoice">
      Create New Invoice
    </button>
    <button type="button" (click)="goToSavedQuote()" class="invoice-convert">
      Convert from Sales Quote
    </button>
    <button (click)="exportToExcel()" class="export-btn">Export to Excel</button>
  </div>

  <!-- Tabs -->
  <div>
    <ul class="nav nav-tabs mb-3 justify-content-start">
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'all'" (click)="setActiveTab('all')">All</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'pending'" (click)="setActiveTab('pending')">Pending</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'sent'" (click)="setActiveTab('sent')">Sent</a>
      </li>
      <li class="nav-item" *ngIf="userRole !== 'Free'">
        <a class="nav-link" [class.active]="activeTab === 'paid'" (click)="setActiveTab('paid')">Paid</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'overdue'" (click)="setActiveTab('overdue')">Overdue</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'canceled'" (click)="setActiveTab('canceled')">Canceled</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'revised'" (click)="setActiveTab('revised')">Revised</a>
      </li>
    </ul>
  </div>

  <div class="Card">
    <!-- Filter and Search Section -->
    <div class="row1">
      <!-- Input for number, reference -->
      <div class="row1_col1">
        <label for="search-input">Invoice Number or Customer</label>
        <div class="input-container">
          <input
            type="text"
            class="search-input"
            id="search-input"
            [(ngModel)]="searchTerm"
            (keydown.enter)="filterInvoices()"
          />
          <i class="bi bi-search"></i>
        </div>
      </div>

      <div class="row1_col3">
        <label for="StartDate">Start Date</label>
        <div style="position: relative; width: 100%;">
          <input
            matInput
            [matDatepicker]="startDatePicker"
            class="date-picker"
            id="StartDate"
            [(ngModel)]="startDate"
            placeholder="dd/mm/yyyy"
          />
          <mat-datepicker-toggle matSuffix [for]="startDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
        </div>
        <mat-datepicker #startDatePicker></mat-datepicker>
      </div>

      <div class="row1_col4">
        <label for="EndDate">End Date</label>
        <div style="position: relative; width: 100%;">
          <input
            matInput
            [matDatepicker]="endDatePicker"
            class="date-picker"
            id="EndDate"
            [(ngModel)]="endDate"
            placeholder="dd/mm/yyyy"
          />
          <mat-datepicker-toggle matSuffix [for]="endDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
        </div>
        <mat-datepicker #endDatePicker></mat-datepicker>
      </div>
    </div>

    <div class="row2">
      <div class="row2_col3">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>
      <div class="row2_col1">
        <button type="button" class="primary-button" (click)="filterInvoices()">
          Search
        </button>
      </div>
    </div>
  </div>

  <div class="table-responsive">
    <table>
      <thead>
        <tr class="table-head">
          <th scope="col" class="valueCheckbox">
            <input
              type="checkbox"
              [checked]="isAllSelected"
              (change)="selectAll($event)"
            />
          </th>
          <th scope="col" class="valuehead">Invoice No</th>
          <th scope="col" class="valuehead">Customer</th>
          <th scope="col" class="valuehead">Invoice Date</th>
          <th scope="col" class="valuehead" *ngIf="userRole !== 'Free'">Paid Date</th>
          <th scope="col" class="valuehead">Due Date</th>
          <th scope="col" class="valuehead" style="text-align: right">Amount</th>
          <th scope="col" class="valuehead" style="text-align: right">Balance Amount</th>
          <th scope="col" class="valuehead" style="text-align: center">Status</th>
          <th scope="col" class="valuehead">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let invoice of filteredInvoices"
          [ngStyle]="{
            color: invoice.invoiceStatus === 'Overdue' ? 'red' : 'inherit'
          }"
        >
          <td class="valueCheckbox">
            <input
              type="checkbox"
              [checked]="selectedInvoices.has(invoice)"
              (change)="toggleSelection(invoice, $event)"
            />
          </td>
          <td class="value">{{ invoice.invoiceNumber }}</td>
          <td class="value">{{ invoice.reference }}</td>
          <td class="value">{{ invoice.postingDate | date : "dd-MMM-yyyy" }}</td>
          <td *ngIf="userRole !== 'Free'" class="value">{{ invoice.paidDate | date : "dd-MMM-yyyy"}}</td>
          <td class="value">{{ invoice.dueDate | date : "dd-MMM-yyyy"}}</td>
          <td class="value" style="text-align: right">
            {{ invoice.grandTotal | currency }}
          </td>
          <td class="value" style="text-align: right">
            {{ invoice.balanceAmount | currency }}
          </td>
          <td class="value" style="text-align: center;"
            [ngClass]="{
              'text-draft': invoice.invoiceStatus === 'Draft',
              'text-pending': invoice.invoiceStatus === 'Pending',
              'text-canceled': invoice.invoiceStatus === 'Canceled',
              'text-revised': invoice.invoiceStatus === 'Revised',
              'text-sent': invoice.invoiceStatus === 'Sent',
              'text-paid': invoice.invoiceStatus === 'Paid',
              'text-overdue': invoice.invoiceStatus === 'Overdue',
              'text-awaiting-payment': invoice.invoiceStatus === 'Awaiting Payment'
            }"
          >
            <span
              class="lable"
              [ngClass]="{
                'border-draft': invoice.invoiceStatus === 'Draft',
                'border-pending': invoice.invoiceStatus === 'Pending',
                'border-canceled': invoice.invoiceStatus === 'Canceled',
                'border-revised': invoice.invoiceStatus === 'Revised',
                'border-sent': invoice.invoiceStatus === 'Sent',
                'border-paid': invoice.invoiceStatus === 'Paid',
                'border-overdue': invoice.invoiceStatus === 'Overdue',
                'border-awaiting-payment': invoice.invoiceStatus === 'Awaiting Payment'
              }"
            >{{ invoice.invoiceStatus }}</span>
          </td>

          <td class="value">
            <button
              (click)="viewInvoice(invoice.invoiceHeadId)"
              [disabled]="
                invoice.invoiceStatus === 'Canceled' || invoice.invoiceStatus === 'Revised' || invoice.isLocked
              "
              title="View"
              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
            >
              <i class="bi bi-arrows-fullscreen" style="color: #4262ff"
               [style.color]="

       invoice.invoiceStatus === 'Canceled' || invoice.invoiceStatus === 'Revised' || invoice.isLocked
        ? '#aaa'
        : '#4262ff'
    "></i>
            </button>

           <!--<button
             (click)="updateInvoice(invoice.invoiceHeadId)"
                 [disabled]="
                invoice.invoiceStatus === 'Sent' ||
                invoice.invoiceStatus === 'Revised'||
                invoice.invoiceStatus === 'Paid'||
                invoice.invoiceStatus === 'Canceled'||
                invoice.invoiceStatus === 'Awaiting Payment'
              "
              title="Edit"
              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
            >
              <i class="ri-edit-box-line" style="color: #4262ff"
                  [style.color]="
      invoice.invoiceStatus === 'Sent' ||
      invoice.invoiceStatus === 'Revised' ||
      invoice.invoiceStatus === 'Paid' ||
       invoice.invoiceStatus === 'Canceled'||
      invoice.invoiceStatus === 'Awaiting Payment'
        ? '#aaa'
        : '#4262ff'
    "></i>
            </button>-->

            <button
              (click)="previewInvoice(invoice.invoiceHeadId)"
              data-bs-target="#invoicePreviewModal"
              data-bs-toggle="modal"
                [disabled]="
                invoice.invoiceStatus === 'Revised'
              "
              title="Preview Invoice"
              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
            >
              <i class="bi bi-eye" style="color: #debe15"
               [style.color]="
      invoice.invoiceStatus === 'Revised'
        ? '#aaa'
        : '#debe15'
    "></i>
            </button>

            <button
              (click)="deleteInvoice(invoice.invoiceHeadId)"
              [disabled]="
                invoice.invoiceStatus === 'Sent' ||
                invoice.invoiceStatus === 'Paid'||
                invoice.invoiceStatus === 'Awaiting Payment'|| invoice.isLocked
              "
              title="Delete Invoice"
              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
            >
              <i class="ri-delete-bin-line" style="color: #ff0000"
               [style.color]="
      invoice.invoiceStatus === 'Sent' ||
      invoice.invoiceStatus === 'Paid' ||
      invoice.invoiceStatus === 'Awaiting Payment' || invoice.isLocked
        ? '#aaa'
        : '#ff0000'
    "></i>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
   <!-- <mat-paginator
  [length]="totalElements"
  [pageSize]="size"
  [pageSizeOptions]="[5, 10, 25, 50]"
  showFirstLastButtons
  (page)="onPageChange($event)">
</mat-paginator> -->

  </div>
</div>

<!-- Invoice Preview -->
<div
  class="modal fade"
  id="invoicePreviewModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="simpleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" style="max-width: 740px">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="simpleModalLabel">Invoice</h5>
        <div class="ml-auto icon-group">
          <i
            class="bi bi-x-circle close-icon"
            #closePreview
            data-bs-dismiss="modal"
            aria-label="Close"
            title="Close"
          ></i>
        </div>
      </div>

      <div class="modal-body">
        <!-- Loading Spinner -->
        <div *ngIf="isLoading" class="spinner-container">
          <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
          </div>
        </div>

        <!-- IFrame for Quotation Preview -->
        <div style="margin-top: 20px" [ngClass]="{ 'd-none': isLoading }">
          <iframe
            #invoicePreviewFrame
            id="invoicePreviewFrame"
            width="700px"
            height="700px"
          ></iframe>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Send Invoice -->
<div
  class="modal fade"
  id="sendInvoiceModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="simpleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" style="max-width: 740px; max-height: 1280px;">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="simpleModalLabel">Send Invoice</h5>
        <button
          type="button"
          class="btn-close custom-close-btn"
          aria-label="Close"
          data-bs-dismiss="modal"
        ></button>
      </div>

      <div class="modal-body">
        <form #sendInvoice="ngForm" name="sendInvoice" (ngSubmit)="sendInvoice.form.valid && sendSelectedInvoices()" novalidate="feedback-form">
          <div class="form-group">
            <label for="recipientEmail">Recipient Email</label>
            <input
              type="email"
              id="TempRecipientEmail"
              name="TempRecipientEmail"
              [(ngModel)]="TempRecipientEmail"
              placeholder="Enter a temporary email or  loaded email"
              class="form-control"
              required
               email
               #TempRecipientEmailRef="ngModel"
            />
            <p *ngIf="!TempRecipientEmail">Using stored email: {{ recipientEmail }}</p>
            <!--<div *ngIf="sendInvoice.submitted && sendInvoice.controls['TempRecipientEmail'].invalid" class="text-danger">
              <div *ngIf="sendInvoice.controls['TempRecipientEmail'].errors?.['required']">Email is required.</div>
              <div *ngIf="sendInvoice.controls['TempRecipientEmail'].errors?.['email']">Enter a valid email address.</div>
            </div>-->
             <div *ngIf="sendInvoice.submitted && TempRecipientEmailRef.invalid" class="text-danger">
              <div *ngIf="TempRecipientEmailRef.errors?.['required']">Email is required.</div>
              <div *ngIf="TempRecipientEmailRef.errors?.['email']">Enter a valid email address.</div>
             </div>
          </div>

          <div class="form-group">
            <label for="subject">Subject</label>
            <input
              type="text"
              id="subject"
              name="subject"
              [(ngModel)]="subject"
              required
            />
            <div *ngIf="sendInvoice.submitted && sendInvoice.controls['subject'].invalid" class="text-danger">
              <div *ngIf="sendInvoice.controls['subject'].errors?.['required']">Subject is required.</div>
            </div>
          </div>

          <div class="form-group">
            <label for="message">Message</label>
            <textarea
              id="message"
              name="message"
              rows="4"
              [(ngModel)]="content"
              required
            ></textarea>
            <div *ngIf="sendInvoice.submitted && sendInvoice.controls['message'].invalid" class="text-danger">
              <div *ngIf="sendInvoice.controls['message'].errors?.['required']">Message is required.</div>
            </div>
          </div>

          <div class="popup-footer">
            <button
              type="button"
              #closeSendInvoice
              class="cancel-btn"
              data-bs-dismiss="modal"
            >
              Close
            </button>
            <button
              type="submit"
              class="add-btn"
              [disabled]="isSending"
            >
              {{ isSending ? 'Sending...' : 'Send' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
