<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <div class="header">
    <h3>Create Payment Receipt</h3>
  </div>

  <form #f="ngForm" class="styled-form" novalidate>

  <div class="bd">
    <div class="form-section">
      <div class="form-row" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
          <div  class="form-group" style="display: flex; align-items: center; flex-grow: 1;">
            <label for="paymentNumber">Payment Receipt No:</label>
            <input id="paymentNumber" type="text" required [(ngModel)]="paymentReceiptsHead.paymentNumber"
              name="paymentNumber" disabled />
          </div>
      </div>


      <div class="form-row" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
          <div class="form-group" style="display: flex; align-items: center; flex-grow: 1;">
            <label for="documentDate">Payment Date:</label>
            <input id="documentDate" type="date" required [(ngModel)]="paymentReceiptsHead.documentDate"
              name="documentDate" />
            <div *ngIf="f.controls['documentDate']?.touched && f.controls['documentDate']?.invalid" class="text-danger">
              Date is required.
            </div>

          </div>
      </div>

      <div class="form-row" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">

          <div  class="form-group" style="display: flex; align-items: center; flex-grow: 1;">
            <label for="customerName">Customer Name:</label>
            <select
            class="form-select"
            id="customer" [(ngModel)]="paymentReceiptsHead.businessPartnerId"
            (change)="onCustomerChange($event)"
            name="customerName"
            [disabled]="isCustomerSelected"
            required>
              <option value="" selected disabled>Select Customer</option>
              <option *ngFor="let customer of customers" [value]="customer.businessPartnerId">
                {{ customer.bpName }}
              </option>
            </select>
          </div>
      </div>
   </div>

    <!-- Add Invoice Button -->
    <div class="form-row">
      <div class="search-bar d-flex justify-content-start">
<!--
        <button type="button" class="btn btn-primary" (click)="addInvoiceRow()">
          Add Invoice
        </button> -->

        <button type="button" class="btn btn-primary new-item" (click)="addInvoiceRow()">
          <i class="bi bi-plus-circle-fill"></i> Add New Row
        </button>


      </div>
    </div>

    <div class="table-section">
      <div class="table-responsive">
      <table class="table-section">
        <thead>
          <tr>
            <th>#</th>
            <th>Search</th>
            <th>Invoice No</th>
            <th style="text-align: right;">Amount</th>
            <th style="text-align: right;">Balance</th>
            <th style="text-align: right;">Amount Received</th>
            <th style="text-align: right;">Remaining Balance</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <!-- Loop through selected invoices -->
          <tr *ngFor="let invoice of enteredInvoices; let i = index">
            <td>{{ i + 1 }}</td>

            <td>
              <button type="button" class="btn btn-link" data-bs-toggle="modal" data-bs-target="#invoiceSearchModal"
                (click)="onSearchInvoiceClick(i)">
                <i class="fa fa-search"></i>
              </button>
            </td>
            <!-- Invoice Number -->
            <td>
              <input type="text" class="form-control1" required [(ngModel)]="enteredInvoices[i].invoiceNumber"
                (ngModelChange)="onInvoiceNumberChange($event, i)" name="invoiceNumber{{i}}"
                placeholder="Enter Invoice No" />
            </td>

            <!-- Amount -->
            <td>
              <input type="text" class="form-control" [value]="invoice.grandTotal | currency:'USD':'symbol':'1.2-2'"
                disabled />
              <input type="hidden" [(ngModel)]="invoice.grandTotal" name="grandTotal{{i}}" />
            </td>

            <!-- Balance -->
            <td>
              <input type="text" class="form-control"
                [value]="invoice.balanceAmount ? (invoice.balanceAmount | currency:'USD':'symbol':'1.2-2') : '0.00'"
                disabled />
              <input type="hidden" [(ngModel)]="invoice.balanceAmount" name="balanceAmount{{i}}" />
            </td>

            <!-- Paid Amount -->
            <td>
              <input type="number" class="form-control" required [(ngModel)]="invoice.creditAmount"
                name="creditAmount{{i}}"  (input)="validatePaidAmount(invoice)" (keydown)="preventEnter($event)" min="0"
                [ngClass]="{ 'is-invalid': f.submitted && f.controls['creditAmount' + i].invalid }" />
              <div *ngIf="f.controls['creditAmount' + i]?.touched && f.controls['creditAmount' + i]?.invalid"
                class="text-danger">
                Paid Amount is required.
              </div>
            </td>

            <!-- New Invoice Balance -->
            <td>
              <input id="InvoiceBalance" type="text" class="form-control"
                [value]="getInvoiceNewBalance(invoice) | currency:'USD':'symbol':'1.2-2'" disabled />
            </td>
            <td> <button type="button" class="btn btn-link" (click)="removeItem(i)">
                <i class="fa fa-trash" style="color: red;"></i>
              </button></td>
          </tr>
        </tbody>
      </table>

      </div>
    </div>


    <div class="form-section_2">
      <div class="form-row" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px; ">

          <div class="form-group_2" style="display: flex; align-items: center; flex-grow: 1;">
            <label for="totalCreditAmount">Total Paid Amount</label>
            <input id="totalCreditAmount" type="text" class="form-control"
              [value]="getTotalPaidAmountEntered() | currency:'USD':'symbol':'1.2-2'" disabled />
          </div>

      </div>

      <div class="form-row" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px; ">
          <div class="form-group_2" style="display: flex; align-items: center; flex-grow: 1; ">
            <label for="remarks">Remarks:</label>
            <textarea id="remarks" placeholder="Enter any notes or reference (optional)" required [(ngModel)]="paymentReceiptsHead.remarks" name="remarks"
              rows="3"></textarea>
          </div>
      </div>



    <div class="d-flex justify-content-end mt-5 mb-4 btnRow">
      <button type="button" class="btn btn-secondary me-2 paybtn" (click)="onCancel()">Cancel</button>
      <button type="submit" class="btn btn-primary paybtn" (click)="onSubmit()" [disabled]="isInvalidInvoice">Save Payment Receipt</button>
    </div>
  </div>
    </div> 
  </form>
</div>

<!-- Modal for Searching Invoices -->
<div class="modal fade" id="invoiceSearchModal" tabindex="-1" aria-labelledby="invoiceSearchModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="invoiceSearchModalLabel">Search Invoice</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form>
          <div class="mb-3">
            <label for="fromDate" class="form-label">From Date</label>
            <input type="date" class="form-control" id="fromDate" [(ngModel)]="searchCriteria.fromDate" name="fromDate">
          </div>
          <div class="mb-3">
            <label for="toDate" class="form-label">To Date</label>
            <input type="date" class="form-control" id="toDate" [(ngModel)]="searchCriteria.toDate" name="toDate">
          </div>
        </form>

        <!-- List of filtered invoices in table view -->
        <div *ngIf="filteredInvoices && filteredInvoices.length > 0">
          <h6>Invoices</h6>
          <table class="table table-striped">
            <thead>
              <tr>
                <th scope="col">Invoice No.</th>
                <th scope="col">Date</th>
                <th scope="col" class="text-end">Balance</th>
                <th></th>

              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let invoice of filteredInvoices">
                <td>{{ invoice.invoiceNumber }}</td>
                <td >{{ invoice.postingDate | date: 'dd-MM-yyyy' }}</td>
                <td class="text-end">{{ invoice.balanceAmount | currency }}</td>
                <td class="text-end">
                  <button class="btn btn-sm btn-primary" (click)="selectInvoice(invoice)">Select</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div *ngIf="filteredInvoices?.length === 0">
         <!--<p>No invoices found for the selected criteria.</p>-->
        </div>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" (click)="searchInvoices()">Search</button>
      </div>
    </div>
  </div>
</div>
