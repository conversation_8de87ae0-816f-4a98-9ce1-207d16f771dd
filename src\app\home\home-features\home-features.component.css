.how-it-works {
  background: #4262FF0D;
  padding: 10px 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 100px;
  flex-wrap: wrap;
}

.how-it-works h1 {
  font-family: Segoe UI;
  font-size: 40px;
  font-weight: 700;
  line-height: 58.09px;
  text-align: left;
  color: #4262FF;
}

.how-it-works p {
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 20px;
  font-weight: 500;
  line-height: 35px;
  text-align: left;
  color: #000;
  padding-right: 150px;
}

.how-it-works .info {
  text-align: left;
}

.feature-1 {
  background: white;
  padding: 20px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 100px;
  flex-wrap: wrap;
}

.feature-2 {
  background: #4262FF0D;
  padding: 10px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 100px;
  flex-wrap: wrap;
}

.feature-1 .content{
  max-width: 800px;
  text-align: left;
}
.feature-2 .content {
  max-width: 800px;
  text-align: left;
}

.feature-2 h3{
  color: #4262FF;
  font-family: Segoe UI;
  font-weight: 700;
  font-size: 35px;
}

.feature-1 h2,
.feature-2 h2 {
  font-family: Inter;
  font-size: 35px;
  font-weight: 700;
  text-align: left;
  color: #000;
}

.feature-1 p,
.feature-2 p {
  font-family: Segoe UI;
  font-size: 20px;
  font-weight: normal;
  line-height: 35px;
  text-align: left;
  color: #000;
}

.feature-1 li,
.feature-2 li {
  font-family: Segoe UI;
  font-size: 20px;
  font-weight: normal;
  text-align: left;
  color: #000;
}

.get-started {
  background: white;
  padding: 10px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.get-started .feature-text {
  font-family: Inter;
  font-size: 50px;
  font-weight: 700;
  line-height: 118.6px;
  text-align: left;
  color: #000000;
}

.get-started button {
  padding: 10px 20px;
  display: inline-block;
  border: none;
  border-radius: 5px;
  background: linear-gradient(to right, #4262FF, #512CA2);
  color: white;
  cursor: pointer;
  font-family: Inter;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

 @media (max-width: 599px) {
  .get-started h1 {
      font-family: Inter;
      font-size: 40px;
      font-weight: 700;
      line-height: 50.6px;
      text-align: left;
      color: #000000;
    }

        .get-started .feature-text {
          font-family: Inter;
          font-size: 20px;
          font-weight: 700;
          line-height: 118.6px;
          text-align: left;
          color: #000000;
        }

                .get-started button {
                  padding: 10px 20px;
                  border: none;
                  border-radius: 5px;
                  background: linear-gradient(to right, #4262FF, #512CA2);
                  color: white;
                  cursor: pointer;
                  font-family: Inter;
                  font-size: 16px;
                  font-weight: 600;
                  display: flex;
                  text-align: center;
                }


                
                 .how-it-works {
                  padding: 20px 20px;}
                
                  .how-it-works p {
                  padding-right: 20px;
}
}
 

