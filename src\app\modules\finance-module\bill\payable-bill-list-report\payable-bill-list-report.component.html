<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<br>
<div class="container">
    <h5><B>Payable Bill List</B></h5>
    <hr>
    <div class="report-section">
        <div class="report-form-container">
            <div class="report-form">
                <div class="form-group">
                    <label for="fromDate">Date</label>
                    <div class="date-range">
                        <input type="date" id="fromDate" [(ngModel)]="fromDate">
                        <span>to</span>
                        <input type="date" id="toDate" [(ngModel)]="toDate">
                    </div>
                </div>
                <br>
                <div class="form-row">
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-select" id="status" [(ngModel)]="status">
                            <option value="%">All</option>
                            <option value="Pending">Pending</option>
                            <option value="Paid">Paid</option>
                            <option value="Awaiting Payment">Awaiting Payment</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="supplier">Supplier</label>
                        <select class="form-select" id="supplier" [(ngModel)]="billData.businessPartnerId" (change)="onSupplierChange($event)" name="supplierName" required>
                            <option value="0">All Suppliers</option>
                            <option *ngFor="let supplier of suppliers" [value]="supplier.businessPartnerId">
                                {{ supplier.bpName }}
                            </option>
                        </select>
                    </div>
                </div>
                <br>
                 <button class="generate-report-btn" (click)="previewBills(fromDate, toDate, status,billData.businessPartnerId);" >
                    Generate Report
                </button>
            </div>
        </div>

        <!-- Report Preview -->
        <div class="modal fade" id="simpleModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 740px;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="simpleModalLabel">Payable Bills</h5>
                        <button type="button" class="btn-close custom-close-btn" aria-label="Close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Loading Spinner -->
                        <div *ngIf="isLoading" class="spinner-container">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>

                        <!-- IFrame for Report Preview -->
                        <div style="margin-top: 20px;" [ngClass]="{'d-none': isLoading}">
                            <iframe #BillreportPreviewFrame id="BillreportPreviewFrame" width="700px" height="700px"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


