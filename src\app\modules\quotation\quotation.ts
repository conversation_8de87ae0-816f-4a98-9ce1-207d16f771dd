export class Quote {
  quoteId: number = 0;
  quoteNumber: string = '';
  customerName: string = '';
  quoteDate: string = '';
  validUntilDate: string = '';
  amount: string = '';
  status: string = '';
}


export class QuoteHead {
  quoteId: number = 0;
  businessPartnerId: any = 0;
  entityId: number = 0;
  entityUuid: string = '';
  userId: number = 0;
  quoteNumber: string = '';
  quoteDate: string = '';
  validUntilDate: string = '';
  customerName: string = '';
  amount: number = 0;
  status: string = '';
  abn: string = '';
  address: string = '';
  city: string = '';
  state: string = '';
  postalCode: string = '';
  note: string = '';
  subTotal: number = 0;
  totalDiscAmount: number = 0;
  totalGst: number = 0;
  grandTotal: number = 0;
  transactionDate: string = '';
  details: QuotationDetail[] = [];
  selected: boolean = false; 
  originalQuoteNumber: string = '';
  quoteDetails: QuotationDetail[] = [];
  businessPartnerType: string='';
  recipient: any;
  entityTradingNameId : any = 0;
  deletedItemIds: number[] = [];
 
}


export class QuoteLog {
  quoteLogId: number = 0;
  entityId: number = 0;
  userId: number = 0;
  quoteHead: QuoteHead = new QuoteHead();
  logDate: string = '';
  logTime: string = '';
  note: string = '';
}

export class SalesItem {
  salesItemId: number = 0;
  entityId: number = 0;
  userId: number = 0;
  itemTypeId: number = 0;
  itemCode: string = '';
  itemName: string = '';
  sellingPrice: number = 0.0;
  standardDiscount: number = 0.0;
  salesAccount: string = '';
  taxApplicability: string = '';
  itemStatus: string = '';
  description: string = '';
  unitPrice: number = 0.0;
  amount: number = 0.0;
  transactionDate: string = '';
}

export class QuotationDetail {
  
  salesQuotesDetailId?: number = 0;
  salesItemId: SalesItem = new SalesItem();
  taxCategoryId: number = 0;
  itemName: string = '';
  quoteNumber: string = '';
  description: string = '';
  quantity: number = 0;
  unitPrice: number = 0.0;
  discount: number = 0.0;
  tax: number = 0.0;
  amount: number = 0.0; 
  discountType: string = '';
  taxApplicability?: boolean = false;
}


export class QuoteHeadRevise {
  quoteHeadReviseId: number = 0;
  quoteId: QuoteHead= new QuoteHead();
  businessPartnerId: number = 0;
  entityId: number = 0;
  userId: number = 0;
  quoteNumber: string = '';
  quoteDate: string = '';
  validUntilDate: string = '';
  customerName: string = '';
  amount: number = 0; 
  status: string = '';
  abn: string = '';
  address: string = '';
  city: string = '';
  state: string = '';
  postalCode: string = '';
  note: string = '';
  subTotal: number = 0; 
  totalDiscAmount: number = 0; 
  totalGst: number = 0; 
  grandTotal: number = 0; 
  transactionDate: string = '';
  expireDate: string = '';
  detailsrevise: QuotesDetailRevise[] = [];
}

export class QuotesDetailRevise {
  quotesDetailReviseId: number = 0;
  salesQuotesDetailId: SalesItem = new SalesItem();
  quoteHeadReviseId: QuoteHeadRevise = new QuoteHeadRevise();
  taxCategoryId: number = 0;
  itemName: string = '';
  quoteNumber: string = '';
  description: string = '';
  quantity: string = '';
  unitPrice: string = '';
  discount: string = '';
  tax: string = '';
  amount: string = '';
}
