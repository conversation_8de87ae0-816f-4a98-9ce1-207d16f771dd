export class Bas {
  date: string = '';
  id: string = '';
  type: string = '';
  amount: number = 0;
  taxType: string = '';
  taxAmount: number = 0;
  description: string = '';
  customer: string = '';
  refNo: string = '';
  status: string = '';
}

export class BasReport {
  basReportId: number = 0;
  entityId : number  = 0;
  userId : number  = 0;;
  periodStart: string = '';
  periodEnd: string= '';
  gstOnSales: number  = 0.0;
  gstOnPurchases : number = 0.0;
  capitalPurchase: number = 0.0;
  totalPurchase: number  = 0.0;
  gstPaid: number  = 0.0;
  gstCollected: number  = 0.0;
  basVersion: string = '';
  status: string = '';
  basLoadgeDate: string ='';
  userName:string= '';
}
