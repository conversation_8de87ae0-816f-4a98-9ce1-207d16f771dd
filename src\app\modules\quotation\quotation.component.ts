import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>mponent, <PERSON><PERSON><PERSON><PERSON>, HostL<PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { QuoteHead } from './quotation';
import { QuotationService } from './quotation.service';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { DomSanitizer } from '@angular/platform-browser';
import { fromEvent, Subscription } from 'rxjs';
import { BusinessPartnerService } from '../business-partner/business-partner.service';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import {  EntityService } from '../entity/entity.service';
import { Entity, EntityTradingName } from '../entity/entity';
import { EmailTemplateService } from '../settings/email-template.service';
import { EmailTemplate } from '../settings/email-template';
import { NgForm } from '@angular/forms';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { InvoiceHead } from '../invoice/invoice';
import { Modal } from 'bootstrap';
import { SwalAlertsService } from '../swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';
import { QuotationConstants, QuoteStatus, UserRole, EmailType, QuoteStatusUtils } from './quotation.constants';
import { EmailData, FilterCriteria, UserInfo, ValidationResult } from './quotation.interfaces';
import { QuotationValidationService } from './services/quotation-validation.service';
import { UserPermissionService } from './services/user-permission.service';
import { QuotationEmailService } from './services/quotation-email.service';
import { QuotationBusinessService } from './services/quotation-business.service';
@Component({
  selector: 'app-quotation',
  templateUrl: './quotation.component.html',
  styleUrls: ['./quotation.component.css'],
})
export class QuotationComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  @ViewChild('sendQuote') sendQuote!: NgForm;

  // Expose utility functions to template
  QuoteStatusUtils = QuoteStatusUtils;
  
  entity: Entity = new Entity();
  [x: string]: any;
  quotes: QuoteHead[] = []; 
  invoices: InvoiceHead[] = [];
  searchTerm: string = ''; 
  filteredQuotes: QuoteHead[] = [];
  selectedQuotes: Set<QuoteHead> = new Set<QuoteHead>();
  entityTradingName: EntityTradingName = new EntityTradingName();
  quoteHeadList: QuoteHead[] = [];
  isAllSelected: boolean = false;
  quotationData: QuoteHead = new QuoteHead();
  activeTab = 'all';
  private audio!: HTMLAudioElement;
  emailTemplates: any[] = [];
  quoteTemplate: any = null; 
  subject: string = '';
  content: string = '';
  templateHtmlContent: string = '';
  recipientEmail: string = ''; // Loaded from the database
  TempRecipientEmail: string ='';

  startDate: string | null = null; // Bind to the start date input
  endDate: string | null = null;   // Bind to the end date input

  constructor(
    private quotationService: QuotationService,
    private emailTemplateService: EmailTemplateService,
    private entityService: EntityService,
    private businessPartnerService: BusinessPartnerService,
    private router: Router,
    private swalAlertsService: SwalAlertsService,
    public sanitizer: DomSanitizer,
    private validationService: QuotationValidationService,
    private userPermissionService: UserPermissionService,
    private emailService: QuotationEmailService,
    private businessService: QuotationBusinessService) { }

  ngOnInit() {
    this.fetchQuotations();
  }

  modalHideSubscription?: Subscription;

  //dropdown
  isDropdownOpen = false;

  @ViewChild('dropdownRef') dropdownRef!: ElementRef;

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown() {
    this.isDropdownOpen = false;
  }

  // Listen for clicks on the whole document
  @HostListener('document:click', ['$event'])
  handleClickOutside(event: MouseEvent) {
    if (
      this.dropdownRef && !this.dropdownRef.nativeElement.contains(event.target) 
    ) {
      this.closeDropdown();
    }
  }

  ngAfterViewInit() {
    const modalElement = document.getElementById('quotePreviewModal');
    if (modalElement) {
      this.modalHideSubscription = fromEvent(modalElement, 'hide.bs.modal').subscribe(() => {
        this.clearSelectedCheckboxes();
      });
    }
  }

 loadRecipientEmail(quoteHead: QuoteHead): void {
    if (quoteHead.businessPartnerId) {
      this.businessPartnerService.getBusinessPartnerById(quoteHead.businessPartnerId).subscribe(
        (businessPartner) => {
          if (businessPartner && businessPartner.email) {
            this.recipientEmail = businessPartner.email;
            this.TempRecipientEmail = businessPartner.email;
          } else {
            this.recipientEmail = ''; // Clear if no email found
            this.TempRecipientEmail = '';
            console.warn('No email found for the selected business partner.');
          }
        },
        (error) => {
          console.error('Error fetching recipient email:', error);
          this.recipientEmail = '';
          this.TempRecipientEmail = '';
        }
      );
    }
  }


loadEmailTemplate(): void {
  const entityId = +((localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_ID)) + "");

  this.emailTemplateService.getEmailTemplateByEntityId(entityId).subscribe(
    (data: any[]) => {
      if (data && data.length > 0) {
        this.emailTemplates = data;
        this.quoteTemplate = this.emailTemplates.find(template => template.emailType === EmailType.QUOTE);

        if (this.quoteTemplate) {
          const selectedQuotes = this.quotes.filter(quote => quote.selected);

          const parser = new DOMParser();
         // const year = new Date().getFullYear();
          const today = new Date();
          const currentYear = today.getFullYear().toString();
                 
          if (selectedQuotes.length === 1) {
            const selectedQuote = selectedQuotes[0];
            this.subject = this.quoteTemplate.subject.replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.QUOTE_NUMBER, selectedQuote.quoteNumber);
            this.loadRecipientEmail(selectedQuote);

            this.businessPartnerService.getBusinessPartnerById(selectedQuote.businessPartnerId).subscribe(
              (bp) => {
                const bpName = bp?.bpName || QuotationConstants.DEFAULTS.CUSTOMER_NAME;
                const quoteNumber = selectedQuote.quoteNumber || QuotationConstants.DEFAULTS.QUOTE_NUMBER;


                this.templateHtmlContent = this.quoteTemplate.content
                  .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.BUSINESS_PARTNER_NAME, bpName)
                  .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.QUOTE_NUMBER, quoteNumber)
                  .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.CURRENT_YEAR, currentYear);


                const parsed = parser.parseFromString(this.templateHtmlContent, 'text/html');
                this.content = parsed.body.textContent || "";
              },
              (error) => {
                console.error('Failed to load business partner info for quote', error);
              }
            );
          } else if (selectedQuotes.length > 1) {
            this.subject = QuotationConstants.DEFAULTS.MULTIPLE_QUOTES_SUBJECT;
            this.templateHtmlContent = this.quoteTemplate.content
              .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.BUSINESS_PARTNER_NAME, QuotationConstants.DEFAULTS.CUSTOMER_NAME)
              .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.QUOTE_NUMBER, QuotationConstants.DEFAULTS.MULTIPLE_QUOTES_QUOTE_NUMBER)
              .replace(QuotationConstants.TEMPLATE_PLACEHOLDERS.CURRENT_YEAR, currentYear);

            const parsed = parser.parseFromString(this.templateHtmlContent, 'text/html');
            this.content = parsed.body.textContent || "";
          } else {
            this.subject = '';
            this.templateHtmlContent = '';
            this.content = '';
          }
        }
      }
    }
  );
}


handleSendQuoteClick(): void {
  const selectedQuotes = this.businessService.getSelectedQuotes(this.filteredQuotes);

  const validation = this.validationService.validateQuotesForSending(selectedQuotes);
  if (!validation.isValid) {
    this.swalAlertsService.showWarning(validation.message!, () => {});
    return;
  }

  const validStatusQuotes = this.businessService.getValidQuotesForSending(selectedQuotes);
  const invalidStatusQuotes = selectedQuotes.filter(q =>
    !QuoteStatusUtils.isValidForSending(q.status)
  );

  if (invalidStatusQuotes.length > 0) {
    const invalidQuotes = invalidStatusQuotes.map(q => q.quoteNumber).join(', ');
    Swal.fire({
      title: 'Some quotes skipped',
      html: `The following quotes have invalid status and will be skipped:<br><strong>${invalidQuotes}</strong>`,
      icon: 'warning',
      confirmButtonText: 'Continue',
      confirmButtonColor: '#007bff'
    }).then(() => {
      //  Deselect invalid quotes
      invalidStatusQuotes.forEach((quote) => {
        quote.selected = false; // Untick the checkbox in the UI
        this.selectedQuotes.delete(quote); // Remove from selectedQuotes Set if using it
      });

      this.loadEmailTemplate();

    

        const modalElement = document.getElementById(QuotationConstants.MODAL_IDS.SEND_QUOTE);
        if (modalElement) {
          const sendModal = new Modal(modalElement);
          sendModal.show();
        } else {
          console.error(`${QuotationConstants.MODAL_IDS.SEND_QUOTE} element not found.`);
        }
  
    });

  } else {
    // No invalid quotes — continue as usual
    
    this.loadEmailTemplate();

   
      const modalElement = document.getElementById(QuotationConstants.MODAL_IDS.SEND_QUOTE);
      if (modalElement) {
        const sendModal = new Modal(modalElement);
        sendModal.show();
      } else {
        console.error(`${QuotationConstants.MODAL_IDS.SEND_QUOTE} element not found.`);
      }

  }
}



@ViewChild('closePreview') closePreview: any;
@ViewChild('closeSendQuote') closeSendQuote: any;
isSending: boolean = false;
sendSelectedQuotes(): void {
  // Check user permissions
  const permissionCheck = this.userPermissionService.checkFreeUserSendingLimit(this.quotes);
  if (!permissionCheck.canSend) {
    if (permissionCheck.message === QuotationConstants.MESSAGES.FREE_USER_LIMIT_REACHED) {
      Swal.fire({
        title: 'Limit Reached!',
        text: permissionCheck.message,
        icon: 'info',
        confirmButtonText: 'Upgrade Plan',
        confirmButtonColor: '#007bff',
      }).then((result) => {
        if (result.isConfirmed) {
          this.businessService.redirectToSubscriptionPage();
        }
      });
    }
    return;
  }
  
  const selectedQuotes = this.businessService.getSelectedQuotes(this.filteredQuotes);
  if (selectedQuotes.length > 0) {
    const pendingQuotes = this.businessService.getValidQuotesForSending(selectedQuotes);

    if (pendingQuotes.length === 0) {
      this.handleNoValidQuotesError();
      return;
    }

    const emailValidation = this.validationService.validateEmailAddress(this.TempRecipientEmail);
    if (!emailValidation.isValid) {
      Swal.fire({
        title: 'Missing Email',
        text: emailValidation.message!,
        icon: 'error',
        confirmButtonText: 'OK'
      });
      return;
    }

    this.confirmAndSendQuotes(pendingQuotes);
  } else {
    this.swalAlertsService.showErrorWithChimpSupport(
      'Please select quotes to send.',
      'No quotes Selected'
    );
  }
}

ngOnDestroy() {
  if (this.modalHideSubscription) {
    this.modalHideSubscription.unsubscribe();
  }
}

clearSelectedCheckboxes() {
  // Deselect all checkboxes
  this.selectedQuotes.clear();
}

private handleNoValidQuotesError(): void {
  this.swalAlertsService.showErrorWithChimpSupport(
    QuotationConstants.MESSAGES.NO_VALID_QUOTES,
    'No Valid quotes'
  ).then(() => {
    this.clearSelectedCheckboxes();
    this.isAllSelected = false;
  });
}

private confirmAndSendQuotes(pendingQuotes: QuoteHead[]): void {
  Swal.fire({
    title: 'Send quotes',
    text: `Do you want to send the selected invoices to ${this.TempRecipientEmail}?`,
    icon: 'question',
    showCancelButton: true,
    confirmButtonText: 'Send',
    cancelButtonText: 'Cancel',
    confirmButtonColor: '#007bff',
    cancelButtonColor: '#6c757d'
  }).then((result) => {
    if (result.dismiss === Swal.DismissReason.cancel || result.dismiss === Swal.DismissReason.backdrop) {
      this.clearSelectedCheckboxes();
      this.isAllSelected = false;
    }

    if (result.isConfirmed) {
      this.processSendQuotes(pendingQuotes);
    }
  });
}

private async processSendQuotes(pendingQuotes: QuoteHead[]): Promise<void> {
  this.isSending = true;

  try {
    // Fetch BusinessPartner details for each quote
    const businessPartnerFetchPromises = pendingQuotes.map(quote =>
      this.businessPartnerService.getBusinessPartnerById(quote.businessPartnerId).toPromise()
    );

    const businessPartners = await Promise.all(businessPartnerFetchPromises);
    this.businessService.attachBusinessPartnerEmails(pendingQuotes, businessPartners);

    const emailData = this.emailService.prepareEmailData(
      this.TempRecipientEmail,
      this.subject,
      this.content,
      this.templateHtmlContent,
      pendingQuotes
    );

    const entityUuid = this.emailService.getEntityUuid();
    if (!entityUuid) {
      this.isSending = false;
      alert(QuotationConstants.MESSAGES.MISSING_ENTITY_UUID);
      return;
    }

    this.sendQuotesViaEmail(emailData, entityUuid);
  } catch (error) {
    this.handleSendQuotesError(error, QuotationConstants.MESSAGES.FETCH_BP_ERROR, 'Failed to fetch business partner details.');
  }
}

private sendQuotesViaEmail(emailData: any, entityUuid: string): void {
  this.quotationService.sendQuoteWithEmail(emailData, entityUuid).subscribe(
    (response) => {
      this.handleSendQuotesSuccess();
    },
    (error) => {
      this.handleSendQuotesError(error, QuotationConstants.MESSAGES.SEND_ERROR, 'Failed to send quotes.');
    }
  );
}

private handleSendQuotesSuccess(): void {
  Swal.fire({
    title: 'Success!',
    text: QuotationConstants.MESSAGES.SEND_SUCCESS,
    icon: 'success',
    confirmButtonText: 'OK',
    confirmButtonColor: '#28a745',
  }).then(() => {
    this.isSending = false;
    this.closeSendQuote.nativeElement.click();
    this.closePreview.nativeElement.click();
    this.businessService.clearQuoteSelections(this.filteredQuotes);
    this.selectedQuotes.clear();
    this.isAllSelected = false;
    this.fetchQuotations();
    this.businessService.navigateToRoute(this.router, '/quotation');
  });
}

private handleSendQuotesError(error: any, message: string, chatbotPrompt: string): void {
  console.error('Send quotes error:', error);
  this.isSending = false;
  this.swalAlertsService.showErrorWithChimpSupport(message, chatbotPrompt);
}


  toggleSelection(quotation: QuoteHead, event: any): void {
    if (event.target.checked) {
      this.selectedQuotes.add(quotation);
    } else {
      this.selectedQuotes.delete(quotation);
    }
  }
  fetchQuotations() {

    const entityId = +((localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_ID)) + "");

    this.quotationService.getAllSalesQuotesHeadList(entityId).subscribe(
      (data: QuoteHead[]) => {
        this.quotes = data;
        this.filteredQuotes = this.quotes;
      },
      (error) => {

      }
    );
  }

  onSearchChange() {
    this.filterQuotes(); // Call filter function on input change
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    if (this.searchTerm || this.startDate || this.endDate) {
      this.filterQuotes(); // Filter quotes only if search criteria are provided
    } else {
      this.filteredQuotes = this.activeTab === 'all' 
        ? this.quotes 
        : this.quotes.filter(quote => quote.status.toLowerCase() === this.activeTab);
    }
  }

  filterQuotes(): void {
  
  if (!this.searchTerm && !this.startDate && !this.endDate) {
        Swal.fire({
          icon: 'warning',
          title: 'Missing Search Criteria',
          text: 'Please provide at least one search criterion: Quote Number or Customer, Start Date, or End Date.',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff',
        });
        return;
      }
  
      const searchTermLower = this.searchTerm.toLowerCase().trim();
      let filtered = this.quotes;
  
      // Filter by active tab
      if (this.activeTab !== 'all') {
        filtered = filtered.filter(invoice => invoice.status.toLowerCase() === this.activeTab);
      }
  
      // Filter by search term
      if (searchTermLower) {
        filtered = filtered.filter(invoice =>
          invoice.quoteNumber.toString().toLowerCase().includes(searchTermLower) ||
          invoice.customerName.toLowerCase().includes(searchTermLower)
        );
      }
  
      // Filter by date range
      if (this.startDate) {
        const startDate = new Date(this.startDate);
        filtered = filtered.filter(invoice => new Date(invoice.quoteDate) >= startDate);
      }
  
      if (this.endDate) {
        const endDate = new Date(this.endDate);
        filtered = filtered.filter(invoice => new Date(invoice.quoteDate) <= endDate);
      }
  
      this.filteredQuotes = filtered;
    }
  

  // Reset filters
  resetFilters() {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.activeTab = 'all'; // Reset the active tab to 'all'
    this.filteredQuotes = this.quotes;
  }


  deleteQuotation(id: number, index: number) {
    Swal.fire({
      title: 'Delete Quotation',
      text: QuotationConstants.MESSAGES.DELETE_CONFIRMATION,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
    }).then((result) => {
      if (result.isConfirmed) {
        this.quotationService.deleteSalesQuotesHead(id).subscribe(
          () => {
            Swal.fire({
              title: 'Deleted!',
              text: QuotationConstants.MESSAGES.DELETE_SUCCESS,
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#3085d6',
            });
            this.quotes.splice(index, 1); // Remove the quotation from the array
          },
          (error) => {
            console.error('Error deleting quotation:', error);
            Swal.fire({
              title: 'Error!',
              text: QuotationConstants.MESSAGES.DELETE_ERROR,
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#be0032',
              cancelButtonColor: '#007bff',
              showCancelButton: true,
            }).then((result) => {
              if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                if (this.chatBotComponent) {
                  Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while Chimp processes your request.',
                    allowOutsideClick: false,
                    didOpen: () => {
                      Swal.showLoading();
                      this.chatBotComponent.setInputData('Failed to delete quotation.');
                      this.chatBotComponent.responseReceived.subscribe(response => {
                        Swal.close();
                        this.chatResponseComponent.showPopup = true;
                        this.chatResponseComponent.responseData = response;
                        this.playLoadingSound();
                        this.stopLoadingSound() 
                      });
                    },
                  });
                } else {
                  console.error('ChatBotComponent is not available.');
                }
              }
            });
          }
        );
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        Swal.fire({
          title: 'Cancelled',
          text: QuotationConstants.MESSAGES.DELETE_CANCELLED,
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#3085d6',
        });
      }
    });
  }

  editQuotation(id: number) {
    this.router.navigate(['/edit-quotation', id]);
  }

  viewQuotation(id: number) {
    this.router.navigate(['/view-quotation', id]);
  }

  createInvoice(id: number) {
   /** const userString = localStorage.getItem('user');
    if (!userString) {
      // Handle case where user is not found
      return;
    }
    
    const currentUser = JSON.parse(userString); // Now it's an object
    
    const userRole = currentUser.roleName;
    const userId = currentUser.id;
    
    if (userRole === 'Free') {
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
  
      // Count user's invoice created this month
      const freeUserInvoicesCount = this.invoices.filter(invoice => {
        const createdDate = new Date(invoice.postingDate);
        return invoice.userId === userId &&
               createdDate.getMonth() === currentMonth &&
               createdDate.getFullYear() === currentYear;
      }).length;
  
      if (freeUserInvoicesCount >= 5) {
        Swal.fire({
          title: 'Limit Reached!',
          text: 'As a Free user, you can only create up to 5 Invoices per month.',
          icon: 'info',
          confirmButtonText: 'Upgrade Plan',
          confirmButtonColor: '#007bff',
        });
        return;
      }
    }**/
  
    this.router.navigate(['/copy-from-quotation', id]);
  }

  createQuote() {
    
   /** const userString = localStorage.getItem('user');
    if (!userString) {
      // Handle case where user is not found
      return;
    }
    
    const currentUser = JSON.parse(userString); // Now it's an object
    
    const userRole = currentUser.roleName;
    const userId = currentUser.id;
    
    if (userRole === 'Free') {
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
  
      // Count user's quotes created this month
      const freeUserQuotesCount = this.quotes.filter(quote => {
        const createdDate = new Date(quote.quoteDate);
        return quote.userId === userId &&
               createdDate.getMonth() === currentMonth &&
               createdDate.getFullYear() === currentYear;
      }).length;
  
      if (freeUserQuotesCount >= 5) {
        Swal.fire({
          title: 'Limit Reached!',
          text: 'As a Free user, you can only create up to 5 quotes per month.',
          icon: 'info',
          confirmButtonText: 'Upgrade Plan',
          confirmButtonColor: '#007bff',
        });
        return;
      }
    }**/
    this.router.navigate(['/create-quotation']);
  }

    handleReviseQuotation() {
      const selectedQuotes = this.businessService.getSentQuotes(
        this.businessService.getSelectedQuotes(this.quotes)
      );

      const validation = this.validationService.validateQuoteForRevision(selectedQuotes);
      if (!validation.isValid) {
        Swal.fire({
          title: 'Warning!',
          text: validation.message!,
          icon: 'warning',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff',
        });
        return;
      }

      const quoteId = selectedQuotes[0].quoteId;
      this.reviseQuotation(quoteId);
    }
  reviseQuotation(id: number) {
    this.router.navigate(['/revise-quote', id]);
  }

  selectAll(event: any) {
    const isChecked = event.target.checked;
    this.businessService.selectAllQuotes(this.quotes, isChecked);
  }

  archive() {

  }
  markas() {

  }
    handleCopyFromQuote() {
      const selectedQuotes = this.businessService.getSelectedQuotes(this.quotes);

      const validation = this.validationService.validateQuoteForCopy(selectedQuotes);
      if (!validation.isValid) {
        this.swalAlertsService.showErrorWithChimpSupport(
          validation.message!,
          'Please select a Quote to Copy.'
        );
        return;
      }

      const quoteId = selectedQuotes[0].quoteId;
      this.copyQuotation(quoteId);
    }
    

  copyQuotation(id: number) {
    this.router.navigate(['/copy-quote', id]);
  }
  @ViewChild('quotationPreviewFrame') quotationPreviewFrame!: ElementRef;
  isLoading = false;
  private quotationCache = new Map<number, string>();


  handlePreviewQuotation() {
    const selectedQuotes = this.businessService.getSelectedQuotes(this.quotes);

    const validation = this.validationService.validateQuoteForPreview(selectedQuotes);
    if (!validation.isValid) {
      Swal.fire({
        title: 'Warning!',
        text: validation.message!,
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const quoteId = selectedQuotes[0].quoteId;
    this.previewQuotation(quoteId);
  }

  previewQuotation(quoteId: number) {
    this.isLoading = true;

    const quote = this.filteredQuotes.find(inv => inv.quoteId === quoteId);

    if (quote) {
      const fakeEvent = { target: { checked: true } };
      this.toggleSelection(quote, fakeEvent);
    }

    const cachedBase64String = this.quotationCache.get(quoteId);
    if (cachedBase64String) {
      this.loadPdfIntoIframe(cachedBase64String);
      return;
    }
    const entityId = +((localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_ID)) + "");

    const entityUuid = localStorage.getItem(QuotationConstants.STORAGE_KEYS.ENTITY_UUID);

          if (!entityUuid) {
            this.isLoading = false;
            alert(QuotationConstants.MESSAGES.MISSING_ENTITY_UUID);
            return;
          }

    this.quotationService.getQuoteReport(quoteId,entityId, entityUuid).subscribe(
      data => {
        const base64String = data.response;

        if (base64String) {
          this.quotationCache.set(quoteId, base64String);
          this.loadPdfIntoIframe(base64String);
        } else {
          this.isLoading = false;
          alert(QuotationConstants.MESSAGES.NO_QUOTATION_DATA);
        }
      },
      error => {
        this.isLoading = false;
        alert(QuotationConstants.MESSAGES.QUOTATION_PREVIEW_ERROR);
      }
    );
  }
  private loadPdfIntoIframe(base64String: string) {
    const pdfData = 'data:application/pdf;base64,' + base64String;
    const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
    const iframe = this.quotationPreviewFrame.nativeElement;
    iframe.onload = () => {
      this.isLoading = false;
    };
    iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
  }
  
  handleCreateInvoice() {
    const selectedQuotes = this.businessService.getValidQuotesForInvoiceCreation(
      this.businessService.getSelectedQuotes(this.quotes)
    );

    const validation = this.validationService.validateQuoteForInvoiceCreation(selectedQuotes);
    if (!validation.isValid) {
      Swal.fire({
        title: 'Warning!',
        text: validation.message!,
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const quoteId = selectedQuotes[0].quoteId;
    this.createInvoice(quoteId);
  }

  playLoadingSound() {
    this.businessService.playAudioNotification();
  }

  stopLoadingSound(): void {
    this.businessService.stopAudioNotification(this.audio);
  }
  exportToExcel(): void {
    const validation = this.validationService.validateExportData(this.filteredQuotes);
    if (!validation.isValid) {
      alert(validation.message!);
      return;
    }

    const exportData = this.businessService.prepareExportData(this.filteredQuotes);
    this.businessService.exportToExcel(exportData);
  }


}
