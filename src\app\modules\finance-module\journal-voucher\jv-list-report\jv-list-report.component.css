.report-form-container {
    position: relative;
    /* Needed for the ::before to be positioned correctly */
    max-width: 600px;
    margin-left: 0;
    margin-right: auto;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.report-form-container::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 10px;
    background: linear-gradient(90deg, #4262FF 0%, #512CA2 100%);
    z-index: -1;
    /* Ensures the outline stays behind the form container */
}

.generate-report-btn {
    display: block;
    margin-left: auto;
    /* Pushes the button to the right */
    /* Other button styles */
}


/* Styling individual form elements */

.report-form .form-group {
    margin-bottom: 15px;
}

.date-range,
.amount-range {
    display: flex;
    align-items: center;
}

.date-range input,
.amount-range input {
    flex: 1;
    margin-right: 5px;
    padding: 8px;
}

.date-range span,
.amount-range span {
    margin: 0 5px;
}

select,
input[type="date"],
input[type="number"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
}


/* Style for the button */

button.generate-report-btn {
    margin-top: 20px;
    padding: 8px 22px;
    background: linear-gradient(90deg, #4262FF 0%, #512CA2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 18px;
    transition: background 0.3s ease;
}

button.generate-report-btn:hover {
    background: linear-gradient(90deg, #512CA2 0%, #4262FF 100%);
}


/* Optional: Centering the modal content for the preview */

.modal-content {
    max-width: 740px;
    margin: 0 auto;
}


/* Spinner alignment */

.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}


/* Hide the iframe while loading */

.d-none {
    display: none;
}

@media (max-width: 680px) {
    .date-range,
        .amount-range {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
}