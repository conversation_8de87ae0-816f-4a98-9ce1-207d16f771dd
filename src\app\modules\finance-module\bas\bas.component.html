<!--Dont remove 
<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
    <div class="actions">
        <h1>Business Activity Statement</h1>
        <div class="btn-group">
            
            <button
            type="button"
            class="btn btn-secondary dropdown-toggle gradient-btn"
            data-bs-toggle="dropdown"
            aria-expanded="false"
            >Export
            
          </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li>
                    <a
                      class="dropdown-item" 
                      >1</a
                    >
                  </li>
                <li><a class="dropdown-item">2</a></li>
                <li><a class="dropdown-item">3</a></li>
            </ul>
        </div>
    </div>
  
    <ul class="nav nav-tabs mb-2  justify-content-start">
        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'Summary'" (click)="setActiveTab('Summary')">Summary</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'Rate'" (click)="setActiveTab('Rate')">Transaction By Rate</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'Field'" (click)="setActiveTab('Field')">Transaction By BAS Field</a>
        </li>
    </ul>
    <div *ngIf="activeTab === 'Summary'">
    <div class="container mt-4">
        <div class="alert alert-danger" role="alert">
          <strong>Overdue by 2 days</strong>
        </div>
        
        <div class="d-flex justify-content-between align-items-center mb-3">
         
          <div class="w-75">
            <div class="row">
              <div class="col-md-4">
                <p>21 April</p>
              </div>
              <div class="col-md-4">
                <p>Document ID: 0124578 33251</p>
              </div>
              <div class="col-md-4">
                <p>Form Type: G</p>
              </div>
            </div>

            <div class="row mt-2">
              <div class="col-md-4">
                <p>ABN: *************</p>
              </div>
              <div class="col-md-4">
                <p>GST Account Method: Cash</p>
              </div>
              <div class="col-md-4">
              </div>
            </div>
          </div>
          <div class="text-right w-35">
            <h1>599</h1>
            <p>Amount Refundable</p>
          </div>
        </div>
      </div>
      
     
      <table class="table table-borderless">
        <tbody>
          <tr>
            <td colspan="3">
              <h5>Goods and Services Tax <small>Mar</small></h5>
            </td>
          </tr>
          <tr>
            <td><span class="badge badge-secondary">G1</span> Total Sales</td>
            <td class="text-center">GST Inclusive</td>
            <td class="text-right">1195</td>
          </tr>
    
          <tr>
            <td colspan="3">
              <h5>Amount you owe the Tax Office</h5>
            </td>
          </tr>
          <tr>
            <td><span class="badge badge-secondary">1A</span> GST On Sales</td>
            <td class="text-center"><a href="#">Adjust</a></td>
            <td class="text-right">745</td>
          </tr>
          <tr>
            <td><span class="badge badge-secondary">1A</span> Total owed to the ATO</td>
            <td></td>
            <td class="text-right font-weight-bold">745</td>
          </tr>
    
    
          <tr>
            <td colspan="3">
              <h5>Amount the Tax Office owes you</h5>
            </td>
          </tr>
          <tr>
            <td><span class="badge badge-secondary">1A</span> GST on purchases</td>
            <td class="text-center"><a href="#">Adjust</a></td>
            <td class="text-right">745</td>
          </tr>
          <tr>
            <td><span class="badge badge-secondary">1A</span> Total owed to the ATO</td>
            <td></td>
            <td class="text-right font-weight-bold">745</td>
        
          <tr>
            <td colspan="3">
              <h5>Refund</h5>
            </td>
          </tr>
          <tr>
            <td><span class="badge badge-secondary">9</span> Your Refund Amount</td>
            <td></td>
            <td class="text-right font-weight-bold">745</td>
          </tr>
        </tbody>
      </table>
    
        <div class="d-flex justify-content-between mt-3">
          <button class="secondary-button">Export</button>
          <div class="">
            <button class="secondary-button me-2">Save As Draft</button>
            <button class="primary-button">Lodge To ATO</button>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="activeTab === 'Rate'">
    <div class="container mt-3">
        <div class="mb-3 ">
          <input
            type="text"
            class="form-control"
            placeholder="Search"
            [(ngModel)]="searchText"
            (input)="filterTransactions()"
          />
        </div>
      
        <table class="table table-bordered table-hover">
          <thead class="table-light">
            <tr>
              <th>Transaction Date</th>
              <th>Transaction ID</th>
              <th>Transaction Type</th>
              <th>Transaction Amount</th>
              <th>Tax Type</th>
              <th>Tax Amount</th>
              <th>Description</th>
              <th>Customer</th>
              <th>Ref No</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let transaction of filteredTransactions">
              <td>{{ transaction.date }}</td>
              <td>{{ transaction.id }}</td>
              <td>{{ transaction.type }}</td>
              <td>{{ transaction.amount | number: '1.2-2' }}</td>
              <td>{{ transaction.taxType }}</td>
              <td>{{ transaction.taxAmount | number: '1.2-2' }}</td>
              <td>{{ transaction.description }}</td>
              <td>{{ transaction.customer }}</td>
              <td>{{ transaction.refNo }}</td>
              <td>{{ transaction.status }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      </div>

      <div *ngIf="activeTab === 'Field'">
        <div class="container">
              <h1>June 2024</h1>         
            <div class="d-flex justify-content-between align-items-center mb-6"> 
              <div class="w-75 ">
                <div class="row">
                 <h5>GST Accounting Method: Accurals</h5>
                </div>
                </div> 
            </div>
            <div class="form-check form-switch mb-4 ">
                <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault">
            </div>

            <table class="table table-borderless">
                <tbody>
                <tr>
                    <td colspan="3">
                    <h5>Goods and Services Tax <small>Mar</small></h5>
                    </td>
                </tr>
                <tr>
                    <td><span class="badge badge-secondary">G1</span> Total Sales</td>
                    <td class="text-center">GST Inclusive</td>
                    <td class="text-right">1195</td>
                </tr>        
                <tr>
                    <td colspan="3">
                    <h5>Amount you owe the Tax Office</h5>
                    </td>
                </tr>
                <tr>
                    <td><span class="badge badge-secondary">1A</span> GST On Sales</td>
                    <td class="text-center"><a href="#">Adjust</a></td>
                    <td class="text-right">745</td>
                </tr>
                <tr>
                    <td><span class="badge badge-secondary">1A</span> Total owed to the ATO</td>
                    <td></td>
                    <td class="text-right font-weight-bold">745</td>
                </tr>        
                <tr>
                    <td colspan="3">
                    <h5>Amount the Tax Office owes you</h5>
                    </td>
                </tr>
                <tr>
                    <td><span class="badge badge-secondary">1A</span> GST on purchases</td>
                    <td class="text-center"><a href="#">Adjust</a></td>
                    <td class="text-right">745</td>
                </tr>
                <tr>
                    <td><span class="badge badge-secondary">1A</span> Total owed to the ATO</td>
                    <td></td>
                    <td class="text-right font-weight-bold">745</td>
                <tr>
                    <td colspan="3">
                    <h5>Refund</h5>
                    </td>
                </tr>
                <tr>
                    <td><span class="badge badge-secondary">9</span> Your Refund Amount</td>
                    <td></td>
                    <td class="text-right font-weight-bold">745</td>
                </tr>
                </tbody>
            </table>
        
            <div class="d-flex justify-content-between mt-3">
              <button class="secondary-button">Export</button>
              <div class="">
                <button class="primary-button">Lodge To ATO</button>
              </div>
            </div>
          </div>
        </div>
      -->

<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <div class="heading-section">
    <h1>Business Activity Statement</h1>
  </div>

  <div class="row my-3">
    <div class="col-md-3">
      <label for="fromDate" class="form-label">From</label>
      <input
        id="fromDate"
        class="form-control"
        [(ngModel)]="fromDate"
        readonly
      />
    </div>

    <div class="col-md-3">
      <label for="toDate" class="form-label">To</label>
      <input
        id="toDate"
        class="form-control"
        [(ngModel)]="toDate"
        readonly
      />
    </div>
  </div>

  <div class="card">
    <div class="upper-section">
      <div>
        <div class="left-first-row">{{ toDate | date : "MMM YYY" }}</div>
        <!--<div class="left-second-row text-gray">ABN: *********** &bull; GST accounting method: Cash</div>-->
        <div class="left-third-row text-small">Unfiled amounts is on for this statement <i class="bi bi-question-circle"></i></div>
      </div>
      <div>
        <!--<div class="right-first-row">
          Amount payable 
          <span class="right-price">564</span>
        </div>-->
      </div>
    </div>
    <hr>
    <div class="tables-section">
      <table>
        <thead>
          <tr><th colspan="5">Goods and services tax</th></tr>
          <tr><td colspan="5" class="text-gray">{{ fromDate | date : "MMM" }} - {{ toDate | date : "MMM YYY" }}</td></tr>
        </thead>
        <tbody>
          <tr>
            <td>G1</td>
            <td>Total sales</td>
            <td>GST inclusive</td>
            <td>Adjust G1</td>
            <td  style= "text-align: right">{{ basSummary.G1 | number : "1.2-2" }}</td>
          </tr>

          <tr>
            <td>G10</td>
            <td>GST on Expenses</td>
            <td>GST inclusive</td>
            <td>Adjust G10</td>
            <td  style= "text-align: right">{{ basSummary.G10 | number : "1.2-2" }}</td>
          </tr>

          <tr>
            <td></td>
            <td>GST Free Income</td>
            <td></td>
            <td></td>
            <td  style= "text-align: right">{{ basSummary.g1FreeIncome | number : "1.2-2" }}</td>
          </tr>

          <tr>
            <td></td>
            <td> GST Free Expenses</td>
            <td></td>
            <td></td>
            <td  style= "text-align: right">{{ basSummary.G11 | number : "1.2-2" }}</td>
          </tr>
        </tbody>
      </table>

      <!--<table>
        <thead>
          <tr><th colspan="4">PAYG tax withheld</th></tr>
          <tr><td colspan="4" class="text-gray">Apr-Jun 2025</td></tr>
        </thead>
        <tbody>
          <tr>
            <td>W1</td>
            <td>Total salary, wages and other payments <i class="bi bi-info-circle"></i></td>
            <td>Change amount</td>
            <td>36,184</td>
          </tr>
          <tr>
            <td>W2</td>
            <td>Amounts withheld from payments at W1</td>
            <td>Change amount</td>
            <td>3,541</td>
          </tr>
          <tr>
            <td>W4</td>
            <td>Amounts withheld where no ABN is quoted</td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>W3</td>
            <td>Other amounts withheld (excluding shown at W2 or W4)</td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>WS</td>
            <td>Total withheld W2 + W3 + W4</td>
            <td></td>
            <td>3,541</td>
          </tr>
        </tbody>
      </table> -->     

      <table>
        <thead>
          <tr>
            <th colspan="4">Amounts you owe the Tax Office</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1A</td>
            <td>GST on sales (Income)</td>
            <td></td>
             <td></td>
            <td  style= "text-align: right">{{basSummary["1A"] | number : "1.2-2"}}</td>
          </tr>
          <!--<tr>
            <td>4</td>
            <td>PAYG tax withheld</td>
            <td></td>
            <td>3,541</td>
          </tr>
          <tr>
            <td>8A</td>
            <td>Total owed to the ATO</td>
            <td></td>
            <td>5,027</td>
          </tr>-->
        </tbody>
      </table>      

      <table>
        <thead>
          <tr>
            <th colspan="4">Amounts the Tax Office owes you</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1B</td>
            <td>GST on purchases</td>
            <td></td>
            <td  style= "text-align: right">{{basSummary["1B"] | number : "1.2-2"}}</td>
          </tr>
          <!--<tr>
            <td>8B</td>
            <td>Total owed by the ATO</td>
            <td></td>
            <td>4,463</td>
          </tr>-->
        </tbody>
      </table>      

      <table>
        <thead>
          <tr>
            <th colspan="4">Payment</th>
          </tr>
        </thead>
        <tbody>
         <tr>
            <td>G12</td>
            <td>Total Purchases</td>
            <td></td>
            <td></td>
            <td   class="font-weight-bold"  style= "text-align: right">{{ basSummary.G12 | number : "1.2-2" }}</td>
          </tr>
        </tbody>
      </table>      
    </div>
    <!-- <hr> 
    <div class="button-section">
      <div class="left-button">
        <button class="export-button">Export</button>
      </div>
      <div class="right-buttons">
        <button class="save-button">Save as draft</button>
        <button class="finalise-button">Finalise & create draft bill</button> 
      </div>
    </div>-->
     <div>
        <button class="primary-button" (click)="markAsLodged()">
          Mark as Lodged
        </button>
      </div>
  </div>

  <!-- BAS Period Picker -->
  <!-- <div class="row my-3">
    <div class="col-md-3">
      <label for="fromDate" class="form-label">From</label>
      <input
        id="fromDate"
        class="form-control"
        [(ngModel)]="fromDate"
        readonly
      />
    </div>

    <div class="col-md-3">
      <label for="toDate" class="form-label">To</label>
      <input
        id="toDate"
        class="form-control"
        [(ngModel)]="toDate"
        readonly
      />
    </div>
  </div> -->

  <!-- <div class="table-container">
    <h1>{{ toDate | date : "MMMM yyyy" }}</h1>

    <table class="table table-borderless">
      <tbody>
        <tr>
          <td colspan="3">
            <h5>
              Goods and Services Tax <small>{{ toDate | date : "MMM" }}</small>
            </h5>
          </td>
        </tr>
        <tr>
          <td>
            <span class="badge badge-secondary">G1</span> GST on Income (GST Inclusive)
          </td>
          <td  style= "text-align: right" >{{ basSummary.G1 | number : "1.2-2" }}</td>
          <td class="text-right"></td>
        </tr>
        <tr>
          <td>
            <span class="badge badge-secondary">G10</span> GST on Expenses
          </td>
          <td style= "text-align: right">{{ basSummary.G10 | number : "1.2-2" }}</td>
          <td class="text-right"></td>
        </tr>

       <tr>
         <td>
            <span class="badge badge-secondary">G10</span> GST Free Income
          </td>
          <td style= "text-align: right">{{ basSummary.g1FreeIncome | number : "1.2-2" }}</td>
          <td class="text-right"></td>
        </tr>

       <tr>
        <td>
            <span class="badge badge-secondary">G10</span> GST Free Expenses
          </td>
          <td style= "text-align: right">{{ basSummary.G11 | number : "1.2-2" }}</td>
          <td class="text-right"></td>
        </tr>
      -- commented block
       <tr>
          <td>
            <span class="badge badge-secondary">G11</span> Capital Purchases
          </td>
          <td style= "text-align: right">{{ basSummary.G11 | number : "1.2-2" }}</td>
          <td class="text-right"></td>
        </tr>
       <tr>
          <td>
            <span class="badge badge-secondary">G12</span> Total Purchases
          </td>
          
          <td class="font-weight-bold"  style= "text-align: right">{{ basSummary.G12 | number : "1.2-2" }}</td>
          <td class="text-right"></td>
        </tr>

       <tr>
          <td colspan="3"><h5>Amount you owe the Tax Office</h5></td>
        </tr>
      --
        <tr>
          <td><span class="badge badge-secondary">1A</span> GST Collected</td>
          
          <td  class="font-weight-bold"  style= "text-align: right">{{ basSummary["1A"] | number : "1.2-2" }}</td>
          <td class="text-right"></td>
        </tr>

      -- commented block
        <tr>
          <td colspan="3"><h5>Amount the Tax Office owes you</h5></td>
        </tr>
      --
        <tr>
          <td><span class="badge badge-secondary">1B</span>GST Paid</td>
          
          <td  class="font-weight-bold"  style= "text-align: right">
            {{ basSummary["1B"] | number : "1.2-2" }}
          </td>
          <td class="text-right"></td>
        </tr>
      </tbody>
    </table>

    <div class="d-flex justify-content-between mt-3">
    -- commented block
      <button class="secondary-button">Export</button>
    --
      <div>
        <button class="primary-button" (click)="markAsLodged()">
          Mark as Lodged
        </button>
      </div>
    </div>
  </div> -->
</div>
