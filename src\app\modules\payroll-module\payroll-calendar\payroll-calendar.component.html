<app-payroll-nevigation></app-payroll-nevigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <div class="actions sub-container">
    <h2>Payroll Calendar</h2>
  </div>
  

  <!-- Conditional Display Based on Active Tab -->
  <div class="pay-calendars-section" >
    <div class="pay-calendars">
      <div class="pay-calendar-head">
        <strong>Pay Calendars</strong>
        <div class="btn-group">
          <button
            type="button"
            class="btn btn-primary "
            href="#"
            data-bs-toggle="modal"
            data-bs-target="#setup_multiple_pay_calenders"
          >
            + Add Pay Calendar
          </button>
        </div>
      </div>
      <!-- Update your existing template -->
      <div class="pay-calendar-table">
        <!-- No changes needed to the table structure itself, 
             the CSS will handle the scrolling behavior -->
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Name</th>
              <th>Pay Cycle </th>
              <th>Pay Start Date</th>
              <th>Next Pay Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let calendar of payCalendars | paginate: { itemsPerPage: 5, currentPage: page }">
              <td>{{ calendar.calendarName }}</td>
              <td>{{ calendar.payCycleName }}</td>
              <td>{{ calendar.payStartDate }}</td>
              <td>{{ calendar.nextPayDate }}</td>
              <td class="value">
                <button (click)="updateCalendar(calendar.payCalendarId)" class="btn btn-orange btn-sm" style="
                    margin-right: 2px;
                    border: none;
                    background: none;
                    padding: 2px;
                    font-size: 1rem;
                  " title="Edit">
                  <i class="ri-edit-box-line" style="color: #4262ff"></i>
                </button>
                <ng-container *ngIf="!getButtonState(calendar.payCalendarId); else disabledButton">
                  <button class="btn btn-danger btn-sm" (click)="deleteCalendar(calendar.payCalendarId)"
                    style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" title="Delete">
                    <i class="ri-delete-bin-line" style="color: #ff0000"></i>
                  </button>
                </ng-container>
                <ng-template #disabledButton>
                  <button class="btn btn-secondary btn-sm"
                    style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
                    title="Disabled">
                    <i class="ri-lock-line" style="color: #808080"></i>
                  </button>
                </ng-template>
      
                <button class="btn btn-warning btn-sm" (click)="previewEmployees(calendar.payCalendarId)"
                  data-bs-toggle="modal" style="
                    margin-right: 2px;
                    border: none;
                    background: none;
                    padding: 2px;
                    font-size: 1rem;
                  " title="Preview">
                  <i class="bi bi-eye" style="color: #debe15"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      
        <pagination-controls class="d-flex justify-content-end" (pageChange)="page = $event">
        </pagination-controls>
      </div>
    </div>

    <!-- <div class="payroll-history">
      <h3>
        <button
          class="btn btn-toggle"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#historyContent"
          aria-expanded="false"
          aria-controls="historyContent"
        >
          <i class="bi bi-chevron-down" style="margin-right: 50px"></i>History
        </button>
      </h3>
      <div class="collapse" id="historyContent">
        
        <p>No history available yet.</p>
      </div>
    </div> -->
  </div>


<!--setup_multiple_pay_calenders-->
<div
  class="modal fade"
  id="setup_multiple_pay_calenders"
  tabindex="-1"
  aria-labelledby="setup_multiple_pay_calenders"
  aria-hidden="true"
>
<div class="modal-dialog">
  <div class="modal-content">
    <div class="modal-header">
      <h5 class="modal-title" id="setup_multiple_pay_calenders">Setup Pay Calendars</h5>
      <button
        type="button"
        class="custom-close-btn"
        data-bs-dismiss="modal"
        aria-label="Close"
        (click)="navigateToPayrollSettings()"
      >
        <i class="bi bi-x-circle"></i>
      </button>
    </div>
    <div class="modal-body">
      <form #payMultipleCalendarForm="ngForm" (ngSubmit)="addMultiplePayCalendar()">
        <!-- Calendar Name -->
        <div class="mb-3">
          <label for="Calendar" class="form-label">Calendar Name</label>
          <input
            type="text"
            class="form-control"
            id="name"
            [(ngModel)]="multiplePayCalendarData.calendarName"
            name="name"
            required
          />
          <div
            *ngIf="payMultipleCalendarForm.controls['name']?.invalid && payMultipleCalendarForm.controls['name']?.touched"
            class="text-danger"
          >
            Calendar Name is required.
          </div>
        </div>

        <!-- Pay Cycle -->
        <div class="mb-3">
          <label for="payCycle" class="form-label">Pay Cycle</label>
          <select
            class="form-control"
            id="payCycle"
            [(ngModel)]="multiplePayCalendarData.payCycle"
            name="payCycle"
            required
            (change)="onPayCycleChange()"
          >
            <option value="">Select Pay Cycle</option>
            <option *ngFor="let cycle of payCycles" [ngValue]="cycle">
              {{ cycle.cycleName }}
            </option>
          </select>
          <div
            *ngIf="payMultipleCalendarForm.controls['payCycle']?.invalid && payMultipleCalendarForm.controls['payCycle']?.touched"
            class="text-danger"
          >
            Pay cycle is required.
          </div>
        </div>

        <!-- Monthly Pay Cycle Options -->
        <div *ngIf="multiplePayCalendarData.payCycle?.cycleName === 'Monthly'">
          <!-- Radio Buttons -->
          <div class="mb-3">
            <label class="form-label">Select Option:</label>
            <div class="radio-group">
              <div class="radio-item">
                <input
                  type="radio"
                  id="paidFromTo"
                  name="dateOption"
                  [(ngModel)]="selectedOption"
                  value="fromTo"
                  (change)="clearDateInputs()"
                />
                <label for="paidFromTo">Paid (from - to)</label>
              </div>
              <div class="radio-item">
                <input
                  type="radio"
                  id="calendarMonth"
                  name="dateOption"
                  [(ngModel)]="selectedOption"
                  value="calendarMonth"
                  (change)="clearDateInputs()"
                />
                <label for="calendarMonth">Paid on calendar month</label>
              </div>
            </div>
          </div>

          <!-- From and To Dates -->
          <div *ngIf="selectedOption === 'fromTo'" class="mb-3">
            <div class="date-group">
              <div class="date-item">
                <label for="fromDate" class="form-label">From Day</label>
                <select
                  class="form-control"
                  id="fromDate"
                  [(ngModel)]="multiplePayCalendarData.fromDay"
                  name="fromDate"
                 
                >
                  <option *ngFor="let day of days" [value]="day">{{ day }}</option>
                </select>
              </div>
              <div class="date-item">
                <label for="toDate" class="form-label">To Day</label>
                <select
                  class="form-control"
                  id="toDate"
                  [(ngModel)]="multiplePayCalendarData.payDay"
                  name="toDate"
                  (change)="validateDateRange()"
                 
                >
                  <option *ngFor="let day of days" [value]="day">{{ day }}</option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- Calendar Month Pay Date -->
          <div *ngIf="selectedOption === 'calendarMonth'" class="mb-3">
            <label for="payDate" class="form-label">Pay Day</label>
            <select
              class="form-control"
              id="payDate"
              [(ngModel)]="multiplePayCalendarData.payDay"
              name="payDate"
             
            >
              <option *ngFor="let day of days" [value]="day">{{ day }}</option>
            </select>
          </div>
        </div>

    
        <div class="mb-3">
          <label for="payStartDate" class="form-label">Pay Start Date</label>
          <input
            type="date"
            class="form-control"
            id="payStartDate"
            [(ngModel)]="multiplePayCalendarData.payStartDate"
            name="payStartDate"
            required
            (change)="onPayStartDateChange()"
          />
        </div>

        <div  class="mb-3">
          <label for="nextPayDate" class="form-label">Next Pay Date</label>
          <input
            type="date"
            class="form-control"
            id="nextPayDate"
            [(ngModel)]="multiplePayCalendarData.nextPayDate"
            name="nextPayDate"
            
          />
        </div>

        <!-- Footer Buttons -->
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
            (click)="navigateToPayrollSettings()"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="payMultipleCalendarForm.invalid"
          >
            Add
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

</div>

