import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { OtherExpensesDetail, OtherExpensesHead, PaymentExpensesHead , CoaLedgerAccount} from './expence-claims';
import { Entity } from '../../entity/entity';
// import { ExpenseDetails, ExpenseHead } from './expence-claims';

@Injectable({
  providedIn: 'root'
})
export class ExpenceClaimsService {

  private readonly baseURL = environment.financeApiUrl;
      
        constructor(private http: HttpClient) {}
      
        getAuthToken(): string | null {
          return window.sessionStorage.getItem('auth_token');
        }
      
        request(
          method: string,
          url: string,
          data: any,
          params?: any
        ): Observable<any> {
          let headers = new HttpHeaders();
      
          if (this.getAuthToken() !== null) {
            headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
          }
      
          const options = {
            headers: headers,
            params: new HttpParams({ fromObject: params }),
          };
      
          switch (method.toUpperCase()) {
            case 'GET':
              return this.http.get(this.baseURL + url, options);
            case 'POST':
              return this.http.post(this.baseURL + url, data, options);
            case 'PUT':
              return this.http.put(this.baseURL + url, data, options);
            case 'DELETE':
              return this.http.delete(this.baseURL + url, options);
            default:
              throw new Error('Unsupported HTTP method');
          }
        }

        saveExpenses(payload: any): Observable<OtherExpensesHead> {
          return this.request('POST', '/other-expenses-head/saveExpensesHead', payload);
        }

        getAllCoaLedgerAccounts(): Observable<any> {
          return this.request('GET', '/coa-ledger-accounts/list', null);
        }

        getAllOtherExpensesHeadList(entityId: number): Observable<OtherExpensesHead[]> {
            return this.request("GET", `/other-expenses-head/otherExpensesHeadList/${entityId}`, {}, {});
          }

        
        getExpensesHeadById(id: number): Observable<OtherExpensesHead> {
            return this.request("GET", `/other-expenses-head/getOtherExpensesHeadById/${id}`, {});
          }


        savePaymentExpensesHead(paymentReceiptsHead: PaymentExpensesHead): Observable<any> {
            return this.request('POST', `/savePaymentExpensesHead`, paymentReceiptsHead);
          }

        deleteExpences(otherExpensesId: number): Observable<any> {
            return this.request(
              'DELETE',
              `/deleteExpences/${otherExpensesId}`,
              {}
            );
          }

        getAllPaymentExpensesHeadList(entityId: number): Observable<PaymentExpensesHead[]> {
          return this.request("GET", `/paymentExpensesHeadList/${entityId}`, {}, {});
        }

        updateExpenseBalance(otherExpensesId: number, newBalanceAmount: number): Observable<any> {
            const requestBody = {balanceAmount: newBalanceAmount};
            return this.request('PUT', `/other-expenses-head/updateExpenseBalance/${otherExpensesId}`, requestBody);
          }

          
          // Cancel a single expense
          cancelExpense(id: number): Observable<void> {
            return this.request('PUT', `/other-expenses-head/cancel/${id}`, null);
          }

          // Cancel multiple expenses
          cancelExpenses(ids: number[]): Observable<void> {
            return this.request('PUT', `/other-expenses-head/cancel`, ids);
          }

          updateOtherExpensesHead(id: number, expense: OtherExpensesHead): Observable<OtherExpensesHead> {
            return this.request('PUT', `/other-expenses-head/update/${id}`, expense);
          }  

          getBusinessEntityById(businessEntityId: number): Observable<Entity> {
              return this.request(
                'GET',
                `/getBusinessEntityById/${businessEntityId}`,
                {}
              );
          }

          getOtherExpensesDetailByHeadId(id: number): Observable<OtherExpensesDetail[]> {
            return this.request("GET", `/other-expenses-detail/OtherExpensesDetailByHeadId/${id}`, {});
          }

          getActiveCoaLedgerAccountListByEntity(entityId: number): Observable<CoaLedgerAccount[]> {
            return this.request('GET', '/coa-ledger-accounts/getActiveCoaLedgerAccountListByEntity', {}, { entityId: entityId });
          }

          
          getExpenceSummary(entityId: number): Observable<any> {
          return this.request('GET', `/dashboard/expence-summary`, null, { entityId });
        }

}
