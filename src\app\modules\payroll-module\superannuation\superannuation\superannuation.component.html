<app-payroll-nevigation></app-payroll-nevigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">


<div class="container">
  <div class="actions sub-container">
    <h2>Superannuations</h2>
    <button type="button" class="btn btn-primary" (click)="fetchNotifications()" data-bs-toggle="modal" data-bs-target="#notificationsModal">
  View Notifications
</button>
  </div>

  <div class="Card">
    <!-- Filter and Search Section -->
    <div class="row1">
      <div class="row1_col1">
        <label for="dateRange">Quick Select</label>
        <select id="dateRange" class="form-control" (change)="setDateRange($event)">
          <option value="">Select Range</option>
          <option value="1">Last 1 Month</option>
          <option value="2">Last 2 Months</option>
          <option value="3">Last 3 Months</option>
          <option value="6">Last 6 Months</option>
        </select>
      </div>

      <div class="row1_col3">
        <label for="fromDate">Start Date</label>
        <input type="date" id="fromDate" [(ngModel)]="fromDate" class="date-picker">
      </div>

      <div class="row1_col4">
        <label for="toDate">End Date</label>
        <input type="date" id="toDate" [(ngModel)]="toDate" class="date-picker">
      </div>
    </div>

    <div class="row2">
      <div class="row2_col1">
        <button type="button" class="btn btn-primary" (click)="search(fromDate, toDate)">Search</button>
      </div>
    </div>
  </div>

  <!-- Error and Warning Buttons -->
  <div class="button-container">
    <button class="btn btn-danger" *ngIf="validationErrors.length > 0" data-bs-toggle="modal" data-bs-target="#errorModal">Errors ({{ validationErrors.length }})</button>
    &nbsp;
    <button class="btn btn-warning" *ngIf="validationWarnings.length > 0" data-bs-toggle="modal" data-bs-target="#warningModal">Warnings ({{ validationWarnings.length }})</button>
  </div>

  <!-- Error Modal -->
<div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="errorModalLabel">Validation Errors</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="validationErrors.length > 0">
          <div *ngFor="let error of validationErrors">
            <p><strong>Context:</strong> {{ error.context }}</p>
            <p><strong>Error Code:</strong> <span class="error-code">{{ error.errorCode }}</span></p>
            <p><strong>Message:</strong> {{ error.message }}</p>
            <p><strong>Path:</strong> <code>{{ error.path }}</code></p>
            <p><strong>Severity:</strong> <span class="error-severity">{{ error.severity }}</span></p>
            <hr />
          </div>
        </div>
        <div *ngIf="validationErrors.length === 0">
          <p>No errors found.</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Warning Modal -->
<div class="modal fade" id="warningModal" tabindex="-1" aria-labelledby="warningModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="warningModalLabel">Validation Warnings</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="validationWarnings.length > 0">
          <div *ngFor="let warning of validationWarnings">
            <p><strong>Context:</strong> {{ warning.context }}</p>
            <p><strong>Error Code:</strong> <span class="warning-code">{{ warning.errorCode }}</span></p>
            <p><strong>Message:</strong> {{ warning.message }}</p>
            <p><strong>Path:</strong> <code>{{ warning.path }}</code></p>
            <p><strong>Severity:</strong> <span class="warning-severity">{{ warning.severity }}</span></p>
            <hr />
          </div>
        </div>
        <div *ngIf="validationWarnings.length === 0">
          <p>No warnings found.</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Notifications Modal -->
<div class="modal fade" id="notificationsModal" tabindex="-1" aria-labelledby="notificationsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="notificationsModalLabel">Notifications</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">

        <!-- Loading Spinner -->
        <div *ngIf="isLoadingNotifications" class="d-flex justify-content-center align-items-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- No Notifications -->
        <div *ngIf="!isLoadingNotifications && notifications.length === 0">
          <p>No notifications available at this time.</p>
        </div>

        <!-- Notifications List -->
        <div *ngIf="!isLoadingNotifications && notifications.length > 0">
          <div class="notification-list">
            <div *ngFor="let notification of notifications" class="notification-item card mb-3">
              <div class="card-body">
                <h5 class="card-title">Status: {{ notification.parsedPayloadMessage }}</h5>
                <p class="card-text">
                  <strong>Date:</strong> {{ notification.create_date | date:'medium' }}<br>
                  <strong>Process ID:</strong> {{ notification.processId }}
                </p>
              </div>
            </div>
          </div>
        </div>

      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>


  <!-- Table -->
  <div class="table-responsive">
    <!-- Spinner -->
    <div *ngIf="isLoadingTable" class="d-flex justify-content-center align-items-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- Table -->
    <table *ngIf="!isLoadingTable" class="table table-hover">
      <thead>
        <tr>
          <th scope="col" class="valueCheckbox">
            <input type="checkbox" [checked]="isAllSelected" (change)="selectAll($event)" />
          </th>
          <th>Name</th>
          <th>Type</th>
          <th>Code Type</th>
          <th>Amount</th>
          <th>Status</th>
          <th style="padding-right: 16px; padding-left: 24px;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>Beam Status</span>
            <button 
              type="button" 
              class="btn btn-sm btn-link p-0" 
              title="Refresh" 
              (click)="refreshStatus()"
            >
              🔄
            </button>
          </div>
        </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let payRunDetail of pagedPayRunDetails">
          <td class="valueCheckbox">
            <ng-container *ngIf="payRunDetail.status === 'PENDING' || (payRunDetail.status === 'SUBMITTED' && payRunDetail.beamStatus === 'REFUNDED')">
              <input
                type="checkbox"
                [checked]="selectedPayRunDetail.has(payRunDetail)"
                (change)="toggleSelection(payRunDetail, $event)" />
            </ng-container>
          </td>
          <td>{{ payRunDetail.employee?.firstName }} {{ payRunDetail.employee?.lastName }}</td>
          <td>{{ payRunDetail.type }}</td>
          <td>{{ payRunDetail.codeType }}</td>
          <td>{{ payRunDetail.amount | currency }}</td>
          <td>
            <span [ngStyle]="{
              'background-color': payRunDetail.status === 'PENDING' ? 'yellow' :
                                  payRunDetail.status === 'SUBMITTED' ? 'lightgreen' :
                                  payRunDetail.status === 'DELIVERED' ? 'lightblue' : 'lightgray',
              'color': '#000',
              'padding': '4px 10px',
              'border-radius': '15px',
              'display': 'inline-block',
              'width': '130px',
              'text-align': 'center',
              'font-weight': '500'
            }">
              {{ payRunDetail.status }}
            </span>
          </td>
          <td style="padding-right: 16px;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            
            <!-- Pill status -->
            <div [ngStyle]="{
              'background-color': payRunDetail.beamStatus === 'AWAITING_PAYMENT' ? 'yellow' :
                                  payRunDetail.beamStatus === 'AWAITING_CLEARANCE' ? 'orange' :
                                  payRunDetail.beamStatus === 'RECONCILED' ? 'lightgreen' :
                                  payRunDetail.beamStatus === 'SENT_TO_FUND' ? 'lightblue' :
                                  payRunDetail.beamStatus === 'SENT_TO_FUND_WITH_RESPONSE' ? 'lightcoral' :
                                  payRunDetail.beamStatus === 'SENT_TO_FUND_WITH_REFUND' ? 'lightpink' :
                                  payRunDetail.beamStatus === 'CANCELLED' ? 'gray' :
                                  payRunDetail.beamStatus === 'REFUNDED' ? 'red' :
                                  payRunDetail.beamStatus === 'PROCESSED' ? 'lightgoldenrodyellow' : 'inherit',
              'padding': '4px 10px',
              'color': '#000',
              'border-radius': '15px',
              'font-weight': '500',
              'min-width': '130px',
              'text-align': 'center'
            }">
             {{ payRunDetail.beamStatus || '-' }}
            </div>

            <!-- Add cancelProcessed for AWAITING_PAYMENT -->
            <span *ngIf="payRunDetail.beamStatus?.trim() === 'AWAITING_PAYMENT'"
                  style="cursor: pointer;"
                  title="Cancel"
                  (click)="cancelProcessed(payRunDetail.beamProcessId)">
              <i class="bi bi-x-circle-fill" style="color: rgb(244, 36, 36); font-size: 1.3rem;"></i>
            </span>

            <span *ngIf="payRunDetail.beamStatus?.trim() === 'PROCESSED'"
                  style="cursor: pointer;"
                  title="Cancel"
                  (click)="cancelProcessed(payRunDetail.beamProcessId)">
              <i class="bi bi-x-circle-fill" style="color: rgb(244, 36, 36); font-size: 1.3rem;"></i>
            </span>
          </div>
        </td>
        </tr>
      </tbody>
    </table>
    <div class="text-end">
      <button
        class="btn btn-primary"
        type="button"
        [disabled]="isSubmitting"
        (click)="submitSuperannuations()"
      >
        {{ isSubmitting ? 'Submitting...' : 'Submit Payment' }}
      </button>
    </div>
    <div class="d-flex justify-content-between align-items-center mt-3">
      <div>
        Page {{ currentPage + 1 }} of {{ totalPages }}
      </div>
      <div>
        <nav>
          <ul class="pagination pagination-sm mb-0">
            <li class="page-item" [class.disabled]="currentPage === 0">
              <a class="page-link" (click)="prevPage()">Previous</a>
            </li>

            <li
              class="page-item"
              *ngFor="let page of visiblePages"
              [class.active]="page === currentPage"
            >
              <a class="page-link" (click)="goToPage(page)">{{ page + 1 }}</a>
            </li>

            <li class="page-item" [class.disabled]="currentPage + 1 >= totalPages">
              <a class="page-link" (click)="nextPage()">Next</a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</div>
