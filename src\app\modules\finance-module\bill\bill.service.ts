import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import {
  ApInvoiceDetail,
  ApInvoiceHead,
  CoaLedgerAccount,
  CreditNoteBillHead,
  PaymentVoucherHeader,
  SaveApInvoiceHeadDTO,
} from './bill';
import { BankStatementDetail } from '../bank-reconciliation/bank-reconciliation';
import { BasReport } from '../bas/bas';

@Injectable({
  providedIn: 'root',
})
export class BillService {
 
  private readonly baseURL = environment.financeApiUrl;
  private baseUrl = 'http://localhost:8080/apInvoice';

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  saveApInvoiceHeadData(
    saveApInvoiceHeadDTO: SaveApInvoiceHeadDTO
  ): Observable<ApInvoiceHead> {
    return this.request(
      'POST',
      '/apInvoice/saveApInvoiceHead',
      saveApInvoiceHeadDTO
    );
  }

  saveApiInvoiceHeadFile(
    apiInvoiceHeadId: number,
    file: File
  ): Observable<ApInvoiceHead> {
    const formData: FormData = new FormData();
    formData.append('apInvoiceHeadId', apiInvoiceHeadId.toString());
    formData.append('file', file);
    return this.request('PUT', `/apInvoice/save-file`, formData, null);
  }

  savePaymentVoucher(
    paymentVoucherHeader: PaymentVoucherHeader
  ): Observable<PaymentVoucherHeader> {
    return this.request(
      'POST',
      '/payment-voucher-header/savePaymentVoucherHeader',
      paymentVoucherHeader
    );
  }

  saveCreditNoteBillHead(
    paymentReceiptsHead: CreditNoteBillHead
  ): Observable<any> {
    return this.request('POST', `/saveCreditNoteBillHead`, paymentReceiptsHead);
  }

  uploadImage(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    return this.request('POST', '/api/ocr/extract-text', formData);
  }

  getAllApInvoiceHeadList(entityId: number): Observable<ApInvoiceHead[]> {
    return this.request(
      'GET',
      `/apInvoice/apInvoiceHeadList/${entityId}`,
      {},
      {}
    );
  }

  getAllCoaLedgerAccounts(entityId: number): Observable<any> {
    return this.request('GET', `/coa-ledger-accounts/list/${entityId}`, null);
  }

  getActiveCoaLedgerAccountListByEntity(
    entityId: number
  ): Observable<CoaLedgerAccount[]> {
    return this.request(
      'GET',
      '/coa-ledger-accounts/getActiveCoaLedgerAccountListByEntity',
      {},
      { entityId: entityId }
    );
  }

  getActiveCoaLedgerAccountListByEntityBill(
    entityId: number
  ): Observable<CoaLedgerAccount[]> {
    return this.request(
      'GET',
      '/coa-ledger-accounts/getActiveCoaLedgerAccountListByEntityBill',
      {},
      { entityId: entityId }
    );
  }


  getActiveCoaLedgerAccountListByEntitySales(
    entityId: number
  ): Observable<CoaLedgerAccount[]> {
    return this.request(
      'GET',
      '/coa-ledger-accounts/getActiveCoaLedgerAccountListByEntitySales',
      {},
      { entityId: entityId }
    );
  }

  getAllPaymentVoucherHeaderList(
    entityId: number
  ): Observable<PaymentVoucherHeader[]> {
    return this.request(
      'GET',
      `/payment-voucher-header/paymentVoucherHeaderList/${entityId}`,
      {},
      {}
    );
  }

  getAllCreditNoteBillHeadList(
    entityId: number
  ): Observable<CreditNoteBillHead[]> {
    return this.request('GET', `/creditNoteBillHeadList/${entityId}`, {}, {});
  }

  exportToExcel() {
    const url = `${this.baseUrl}/exportToExcel`;
    return this.http.get(url, { responseType: 'blob' });
  }

  getApInvoiceHeadById(id: number): Observable<ApInvoiceHead> {
    return this.request('GET', `/apInvoice/get/${id}`, {});
  }

  getApInvoiceDetailsByApInvoiceHeadId(
    id: number
  ): Observable<ApInvoiceDetail[]> {
    return this.request(
      'GET',
      `/ap-invoice-detail/getApInvoiceDetailsByHeadById/${id}`,
      {}
    );
  }

  //Update
  updateAPInvoiceHead(
    id: number,
    apInvoiceHead: ApInvoiceHead
  ): Observable<ApInvoiceHead> {
    return this.request(
      'PUT',
      `/apInvoice/updateAPInvoiceHead/${id}`,
      apInvoiceHead
    );
  }

  updateBillBalance(
    apInvoiceHeadId: number,
    newBalanceAmount: number
  ): Observable<any> {
    const requestBody = { dueAmount: newBalanceAmount };
    return this.request(
      'PUT',
      `/apInvoice/updateBillBalance/${apInvoiceHeadId}`,
      requestBody
    );
  }

  updateDueAmount(
    billId: number,
    paymentAmount: number
  ): Observable<ApInvoiceHead> {
    return this.request('PUT', `/apInvoice/updateDueAmount/${billId}`, null, {
      paymentAmount: paymentAmount.toString(),
    });
  }

  //delete
  deletePaymentVoucherHeaders(paymentVoucherHeaderId: number): Observable<any> {
    return this.request(
      'DELETE',
      `/deletePaymentVoucherHeaders/${paymentVoucherHeaderId}`,
      {}
    );
  }

   deleteBillHeaders(apInvoiceHeadId: number): Observable<any> {
    return this.request(
      'DELETE',
      `/deleteApInvoice/${apInvoiceHeadId}`,
      {}
    );
  }

  //cancel
  cancelPaymentVoucher(id: number): Observable<void> {
    return this.request(
      'PUT',
      `/payment-voucher-header/cancel/${id}`,
      null,
      {}
    );
  }

  cancelPaymentVouchers(ids: number[]): Observable<void> {
    return this.request('PUT', `/payment-voucher-header/cancel`, ids, {});
  }

  cancelBill(id: number): Observable<void> {
    return this.request('PUT', `/apInvoice/cancel/${id}`, null, {});
  }

  cancelBills(ids: number[]): Observable<void> {
    return this.request('PUT', `/apInvoice/cancel`, ids, {});
  }

  cancelCreditNote(id: number): Observable<void> {
    return this.request('PUT', `/creditNoteBillHead/cancel/${id}`, null, {});
  }

  cancelCreditNotes(ids: number[]): Observable<void> {
    return this.request('PUT', `/creditNoteBillHead/cancel`, ids, {});
  }

  //reports

  getRecordBatchPaymentsListReport(requestData: {
  fromDate: string,
  toDate: string,
  status: string,
  entityId: number,
  businessPartnerId: number | null,
  entityUUID: string
}): Observable<any> {
   return this.request('POST',`/payment-voucher-header/getRecordBatchPaymentsListReport`, requestData);
}



    
  getBillListReport(requestData: {
  fromDate: string,
  toDate: string,
  status: string,
  entityId: number,
  businessPartnerId: number | null,
  entityUUID: string
}): Observable<any> {
   return this.request('POST',`/apInvoice/getBillListReport`, requestData);
}


  getCreditNoteBillListReport(requestData: {
  fromDate: string,
  toDate: string,
  status: string,
  entityId: number,
  businessPartnerId: number | null,
  entityUUID: string
}): Observable<any> {
   return this.request('POST',`/getCreditNoteBillListReport`, requestData);
}

 /**getPnlListReportWithComparison(
    fromDate: string,
    toDate: string,
    entityId: number,
    selectedComparison: string
  ): Observable<any> {
    return this.request(
      'GET',
      `/pnl/generatePnlListReportWithComparison`,
      {},
      {
        from: fromDate,
        to: toDate,
        entityId: entityId,
        selectedComparison: selectedComparison,
      }
    );
  }

  getPnlListReport(
    fromDate: string,
    toDate: string,
    entityId: number
  ): Observable<any> {
    return this.request(
      'GET',
      `/pnl/generateProfitLossReport`,
      {},
      { from: fromDate, to: toDate, entityId: entityId }
    );
  }

  getPnlListReportDivideByMonths(
    fromDate: string,
    toDate: string,
    entityId: number
  ): Observable<any> {
    return this.request(
      'GET',
      `/pnl/generateProfitLossReportDivideByMonths`,
      {},
      { from: fromDate, to: toDate, entityId: entityId }
    );
  }



  getCashPnlListReport(
    fromDate: string,
    toDate: string,
    entityId: number
  ): Observable<any> {
    return this.request(
      'GET',
      `/pnl/generateCashBasedProfitLossReport`,
      {},
      { from: fromDate, to: toDate, entityId: entityId }
    );
  }**/


       
  getPnlListReportWithComparison(requestData: {
  fromDate: string,
  toDate: string,
  entityId: number,
  selectedComparison: string,
  entityUUID: string
}): Observable<any> {
   return this.request('POST',`/pnl/generatePnlListReportWithComparison`, requestData);
}


        
  getPnlListReport(requestData: {
  fromDate: string,
  toDate: string,
  entityId: number,
  entityUUID: string
}): Observable<any> {
   return this.request('POST',`/pnl/generateProfitLossReport`, requestData);
}


        
  getPnlListReportDivideByMonths(requestData: {
  fromDate: string,
  toDate: string,
  entityId: number,
  entityUUID: string
}): Observable<any> {
   return this.request('POST',`/pnl/generateProfitLossReportDivideByMonths`, requestData);
}


        
  getCashPnlListReport(requestData: {
  fromDate: string,
  toDate: string,
  entityId: number,
  entityUUID: string
}): Observable<any> {
   return this.request('POST',`/pnl/generateCashBasedProfitLossReport`, requestData);
}


  getBalanceSheetListReport(
    fromDate: string,
    toDate: string,
    entityId: number
  ): Observable<any> {
    return this.request(
      'GET',
      `/bs/getBalanceSheetListReport`,
      {},
      { from: fromDate, to: toDate, entityId: entityId }
    );
  }


  getBalanceSheetListReportWithComparison(
    fromDate: string,
    toDate: string,
    entityId: number,
    selectedComparison: string
  ): Observable<any> {
    return this.request(
      'GET',
      `/bs/getBalanceSheetListReportWithComparison`,
      {},
      {
        from: fromDate,
        to: toDate,
        entityId: entityId,
        selectedComparison: selectedComparison,
      }
    );
  }

  getAllApInvoiceHeadListByStatus(entityId: number): Observable<ApInvoiceHead[]> {
    return this.request('GET', `/apInvoice/apInvoiceHeadListByStatus/${entityId}`, {}, {});
  }

  getBillSummary(entityId: number): Observable<any> {
  return this.request('GET', `/dashboard/bill-summary`, null, { entityId });
  }

  getAgedDebtorsSummary(entityId: number): Observable<any> {
  return this.request('GET', `/dashboard/aged-debtors-summary`, null, { entityId });
  }

  saveBasReportForLoadge(
    basReport: BasReport
  ): Observable<any> {
    return this.request(
      'POST',
      `/apInvoice/saveBasReportForLoadge`,
      basReport
    );
  }

  getBasReportsByEntityId(entityId: number): Observable<BasReport[]> {
    return this.request('GET', `/apInvoice/getBasReportsByEntityId/${entityId}`, {});
  }

   getBasReportById(basId: number): Observable<BasReport> {
     return this.request('GET', `/apInvoice/getBasReportById/${basId}`, {});
  }

  getBalanceSheet(fromDate: string, toDate: string, entityId: number): Observable<any> {
    return this.request(
      'GET',
      `/bs/generateBalanceSheet`,
      {},
      { from: fromDate, to: toDate, entityId: entityId }
    );
  }
}
