import { Component, OnInit, ViewChild } from '@angular/core';
import { CoaHeaders, CoaLedgerAccount } from '../gl-account';
import { Router } from '@angular/router';
import { GlAccountService } from '../gl-account.service';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { NgForm } from '@angular/forms';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

@Component({
  selector: 'app-gl-account-list',
  templateUrl: './gl-account-list.component.html',
  styleUrls: ['./gl-account-list.component.css'],
})
export class GlAccountListComponent implements OnInit {
  coaLedgerAccountList: CoaLedgerAccount[] = [];
  coaLedgerAccount: CoaLedgerAccount = new CoaLedgerAccount();
  updateCoaLedgerAccount: CoaLedgerAccount = new CoaLedgerAccount();
  coaHeaders: CoaHeaders[] = [];

  constructor(
    private glAccountService: GlAccountService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getCoaHeaders();
    this.getCoaLedgerAccount();
  }

  getCoaHeaders(): void {
    const entityId = +(localStorage.getItem('entityId') + '');

    this.glAccountService.getCoaHeaderListByEntity(entityId).subscribe(
      (data) => {
        this.coaHeaders = data;
      },
      (error) => {
        console.error('Error fetching coa headers:', error);
      }
    );
  }

  getCoaLedgerAccount(): void {
    const entityId = +(localStorage.getItem('entityId') + '');

    this.glAccountService.getCoaLedgerAccountListByEntity(entityId).subscribe(
      (data) => {
        this.coaLedgerAccountList = data;
      },
      (error) => {
        console.error('Error fetching coa accounts:', error);
      }
    );

    this.coaLedgerAccount.coaHeaderId.coaHeaderId = '';
  }

  setLedgerAccountCode(event: Event): void {
    const selectedHeaderId = (event.target as HTMLSelectElement).value;

    const selectedHeader = this.coaHeaders.find(
      (header) => header.coaHeaderId == selectedHeaderId
    );

    if (selectedHeader) {
      let maxCode = parseInt(selectedHeader.series, 10);

      const accountsForSelectedHeader = this.coaLedgerAccountList.filter(
        (account) => account.coaHeaderId.coaHeaderId == selectedHeaderId
      );

      accountsForSelectedHeader.forEach((account) => {
        const accountCode = parseInt(account.ledgerAccountCode, 10);
        if (!isNaN(accountCode) && accountCode > maxCode) {
          maxCode = accountCode;
        }
      });

      this.coaLedgerAccount.ledgerAccountCode = (maxCode + 1).toString();
    }
  }

  @ViewChild('coaLedgerAccountForm') coaLedgerAccountForm!: NgForm;
  @ViewChild('closeaddGLAccountPopUp') closeaddGLAccountPopUp: any;

  addCoaLedgerAccount(): void {
    this.coaLedgerAccount.entityId = +(localStorage.getItem('entityId') + '');
    this.coaLedgerAccount.status = 'Active';

    this.glAccountService.addCoaLedgerAccount(this.coaLedgerAccount).subscribe(
      (response) => {
        Swal.fire({
          title: 'Success!',
          text: 'GL Account added successfully.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getCoaLedgerAccount();
          this.coaLedgerAccountForm.resetForm();
          this.closeaddGLAccountPopUp.nativeElement.click();
        });
      },
      (error: HttpErrorResponse) => {
        console.error('Error adding gl account', error);
        Swal.fire({
          title: 'Error!',
          text: 'Error adding GL account.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  editCoaLedgerAccount(account: any): void {
    this.updateCoaLedgerAccount = { ...account };
  }

  @ViewChild('updateCoaLedgerAccountForm') updateCoaLedgerAccountForm!: NgForm;
  @ViewChild('closeUpdateGLAccountPopUp') closeUpdateGLAccountPopUp: any;
  updateGlAccount(): void {
    this.glAccountService
      .updateGlAccount(
        this.updateCoaLedgerAccount.coaLedgerAccountId,
        this.updateCoaLedgerAccount
      )
      .subscribe(
        (response) => {
          Swal.fire({
            title: 'Success!',
            text: 'GL Account updated successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {
            this.getCoaLedgerAccount();
            this.updateCoaLedgerAccountForm.resetForm();
            this.closeUpdateGLAccountPopUp.nativeElement.click();
          });
        },
        (error: HttpErrorResponse) => {
          console.error('Error updating GL Account:', error);
          Swal.fire({
            title: 'Error!',
            text:
              error.status === 400
                ? 'Invalid input. Please check the details and try again.'
                : error.status === 404
                ? 'GL Account not found.'
                : error.status === 500
                ? 'An internal server error occurred. Please try again later.'
                : 'An unexpected error occurred. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
  }

  deleteGlAccount(id: number): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to delete this GL Account?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        this.glAccountService.deleteGlAccount(id).subscribe(
          () => {
            this.getCoaLedgerAccount();
            Swal.fire({
              icon: 'success',
              title: 'Deleted!',
              text: 'GL Account has been deleted.',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            });
          },
          (error: HttpErrorResponse) => {
            console.error('Error deleting GL account:', error);
            Swal.fire({
              title: 'Error!',
              text:
                error.status === 404
                  ? 'GL Account not found.'
                  : 'An error occurred while deleting the GL Account. Please try again.',
              icon: 'error',
              confirmButtonText: 'OK',
              confirmButtonColor: '#be0032',
            });
          }
        );
      }
    });
  }

  searchTerm: string = '';
  exportToExcel(): void {
    const exportData = this.filteredAccounts.map((account) => ({
      'Ledger Account ID': account.coaLedgerAccountId,
      'Header ID': account.coaHeaderId.coaHeaderId,
      'Ledger Account Name': account.ledgerAccountName,
      'Ledger Account Code': account.ledgerAccountCode,
      'Ledger Account Description': account.ledgerAccountDescription,
      'Tax Account': account.taxAccount,
      Status: account.status,
      'Default Tax Code': account.defaultTaxCode,
      'Entity ID': account.entityId,
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'GL Accounts');

    const excelBuffer: any = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
    });

    this.saveAsExcelFile(excelBuffer, 'GL_Accounts');
  }

  private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
    });
    const file = new File([data], `${fileName}.xlsx`, { type: data.type });
    saveAs(file);
  }

  get filteredAccounts() {
    return this.coaLedgerAccountList.filter(
      (account) =>
        account.coaHeaderId.accountHeaderType
          .toLowerCase()
          .includes(this.searchTerm.toLowerCase()) ||
        account.ledgerAccountName
          .toLowerCase()
          .includes(this.searchTerm.toLowerCase()) ||
        account.ledgerAccountCode
          .toLowerCase()
          .includes(this.searchTerm.toLowerCase()) ||
        account.status.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }
}
