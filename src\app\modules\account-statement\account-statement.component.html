<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response> 

<div class="container">
    <br>
    <h4>Account Statement</h4>
    <hr>
    <div class="report-section">
        <div class="report-form-container">
            <div class="report-form">

                <div class="form-group">
                    <label for="toDate">Date </label>
                    <div class="date-range">
                        <input type="date" id="toDate" [(ngModel)]="toDate">

                    </div>
                </div>
                <br>
                <div class="form-row">
                    <div class="form-group">
                        <label for="customer">Customer</label>
                        <select class="form-select" id="customer" [(ngModel)]="invoiceHead.businessPartnerId" (change)="onCustomerChange($event)" name="customerName" required>
                        <option value="0">All Customer</option>
                        <option *ngFor="let customer of customers" [value]="customer.businessPartnerId">
                            {{ customer.bpName }}
                        </option>
                        </select>
                    </div>

                    <div class="form-group">
                    </div>
                </div>
                <button class="generate-report-btn" (click)="previewAccountStatemnet(fromDate,toDate, invoiceHead.businessPartnerId);">
                    Generate Report
                </button>
            </div>
        </div>

        <!-- Invoice Preview -->
        <div class="modal fade" id="simpleModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 740px;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="simpleModalLabel">Account Statement </h5>
                        <button type="button" class="btn-close custom-close-btn" aria-label="Close" data-bs-dismiss="modal">
                            <span aria-hidden="true"></span>
                        </button>

                    </div>
                    <div class="modal-body">
                        <!-- Loading Spinner -->
                        <div *ngIf="isLoading" class="spinner-container">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>

                        <!-- IFrame for Invoice Preview -->
                        <div style="margin-top: 20px;" [ngClass]="{'d-none': isLoading}">

                            <iframe #accountStatemnetPreviewFrame id="#accountStatemnetPreviewFrame" width="700px" height="700px"></iframe>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
