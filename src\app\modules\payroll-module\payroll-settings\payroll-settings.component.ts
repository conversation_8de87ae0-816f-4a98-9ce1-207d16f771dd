import { Component, OnInit, ViewChild } from '@angular/core';
import { Earning, PayCalendar} from './payroll-setting'; 
import { Deduction, leaveCategory, PayItemType, Reimbursement} from './payroll-setting'; 
import { NgForm } from '@angular/forms';
import { PayCalendarService } from './services/pay-calendar.service';
import { EarningService } from './services/earning.service';
import { ReadPropExpr } from '@angular/compiler';
import { start } from '@popperjs/core';
import { MultiplePayCalendar, PayCycle, PayPeriod } from './payroll-setting';   
import Swal from 'sweetalert2';
import { enableDebugTools } from '@angular/platform-browser';
import { DeductionService } from './services/deduction.service';
import { Router } from '@angular/router';
import { ReimbursementsService } from './services/reimbursements.service';
import * as bootstrap from 'bootstrap';
import { LeaveCategory } from './empolyee/employee';
import { LeaveService } from './services/leave.service';

@Component({
  selector: 'app-payroll-settings',
  templateUrl: './payroll-settings.component.html',
  styleUrls: ['./payroll-settings.component.css']
})
export class PayrollSettingsComponent implements OnInit {


  @ViewChild('payCalendarForm') payCalendarForm: NgForm | undefined; 
  @ViewChild('payMultipleCalendarForm') payMultipleCalendarForm: NgForm | undefined;
  earnings: Earning[] = []
  PayitemTypes: PayItemType[] = []
  earning:Earning = new Earning();
  leaveCategories: leaveCategory[] = []
  deductions:Deduction[] =[]
  deduction:Deduction = new Deduction();
  reimbursements: Reimbursement[]=[]
  reimbursement: Reimbursement = new Reimbursement();
  isPopupVisible: boolean = false;
  payCycles: PayCycle[] = []; 
  payPeriods: PayPeriod[] = []; 
  selectedPayPeriod: number | null = null;
  payCalendars: PayCalendar[] = []; 
  page: number = 1;
  activeTab: string = 'calendars';  
  multiplePayCalendarData: MultiplePayCalendar = new MultiplePayCalendar();
  
  deductionCategory: string[] = ['twise monthly','onece a month'];
  payRunMasters: any;

  constructor(private payCalendarService: PayCalendarService,private earningService: EarningService, private router: Router, private deductionService: DeductionService,private reimbursementsService: ReimbursementsService, private leaveService: LeaveService) {}

  ngOnInit(): void {
    // this.getAllPayCycles();
    // this.getEarnings();
    // this.getDeductions();
    // this.getReimbursement();
    this.getPayitemType();
    // this.getLeaveCategories();
  }
  navigateToPayrollSettings(): void {
    window.location.assign("/payroll-settings");
  }

  // private getLeaveCategories() {
  //   this.leaveService.getLeaveCategoryList().subscribe(data => {
  //     this.leaveCategories = data;
  //   });
  // }

  // private getEarnings() {
  //   this.earningService.getEarningList().subscribe(data => {
  //     this.earnings = data;
  //   });
  // }
  private getPayitemType() {
    this.earningService.getPayitemType().subscribe(data => {
      this.PayitemTypes = data;
    });
  }

  // private getReimbursement() {
  //   this.reimbursementsService.getReimbursementsList().subscribe(data => {
  //     this.reimbursements = data;
  //   });
  // }
 
  
  // private getDeductions(){
  //   this.deductionService.getDeductionList().subscribe(
  //     data => {
  //       this.deductions =data;
  //       console.log(data);
  //       console.log(start);
  //     }
  //   )
  // }
  glAccounts: string[] = ['Account A', 'Account B', 'Account C', 'Account D']; 
  
  paymentTypes: string[] = ['Per Unit', 'Amount']; 

  get rateLabel(): string {
    if (!this.earning.typeOfUnits) {
      return '';
    }
    return this.earning.typeOfUnits === 'Per Unit' ? 'Rate' : 'Fixed Amount';
  }

  ReimbursementsType: string[] = ['Type A','Type B','Type C','Type D'];

  onEarningAdd():void{
    const entityId = +((localStorage.getItem('entityId')) + "");
    const userId = +((localStorage.getItem('userid')) + "");
    const date = new Date().toISOString(); 
  
    const payload = {
      ...this.earning,
      payItemType:this.PayitemTypes[0],
      entityId,
      userId,
      date,
    };
  
    this.earningService.saveEarning(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Ordinary Time Earnings added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745'
        }).then(() => {
          this.earning = new Earning();
          
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Ordinary Time Earnings. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032'
        });
      }
    );
  }

  // Add Reimbursement
  onReimbursementAdd(): void {
    const entityId = +((localStorage.getItem('entityId')) + "");
    const userId = +((localStorage.getItem('userid')) + "");
    const date = new Date().toISOString();

    const payload = {
      ...this.reimbursement,
      payItemType:this.PayitemTypes[0],
      entityId,
      userId,
      date,
    };

    this.reimbursementsService.saveReimbursement(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Reimbursement added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745'
        }).then(() => {
          this.reimbursement = new Reimbursement();

        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Reimbursement. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032'
        });
      }
    );
  }



  deleteReimbursement(id: number) {
    this.reimbursementsService.deleteReimbursement(id).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Reimbursement delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          window.location.assign("/payroll-settings");
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Reimbursement. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  // getAllPayCycles(): void {
  //   this.payCalendarService.getAllPayCycles().subscribe({
  //     next: (data: PayCycle[]) => {
  //       this.payCycles = data;
  //     },
  //     error: (err) => {
  //       console.error('Error fetching PayCycles:', err);
  //     }
  //   });
  // }

  addDeduction():void {
    const entityId = +((localStorage.getItem('entityId')) + "");
    const userId = +((localStorage.getItem('userid')) + "");
    const date = new Date().toISOString();


    const payload = {
      ...this.deduction,
      payItemType:this.PayitemTypes[0],
      entityId,
      userId,
      date,
    };

    this.deductionService.saveDeduction(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Deduction added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.deduction = new Deduction();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Deduction. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  // getAllPayPeriods(): void {
  //   this.payCalendarService.getAllPayPeriods().subscribe({
  //     next: (data: PayPeriod[]) => {
  //       this.payPeriods = data;
  //     },
  //     error: (err) => {
  //       console.error('Error fetching payPeriods:', err);
  //     }
  //   });
  // }
  
  //PayCalendarData
  
  onPayCycleChange(): void {
    const selectedCycle = this.payCycles.find(
      (cycle) => cycle.cycleName === this.multiplePayCalendarData.payCycle?.cycleName
    );

    if (selectedCycle) {
      this.multiplePayCalendarData.payCycle = selectedCycle;
    }

    this.updateNextPayDate();
  }

  onPayStartDateChange(): void {
    this.updateNextPayDate();
  }

  private updateNextPayDate(): void {
    const selectedCycle = this.multiplePayCalendarData.payCycle;
    if (selectedCycle && this.multiplePayCalendarData.payStartDate) {
      const payStartDate = new Date(this.multiplePayCalendarData.payStartDate);
      const nextPayDate = new Date(
        payStartDate.setDate(payStartDate.getDate() + selectedCycle.numberOfDate)
      );
      this.multiplePayCalendarData.nextPayDate = nextPayDate.toISOString().split('T')[0]; 
    }
  }
 
  addMultiplePayCalendar(): void {
    const entityId = +((localStorage.getItem('entityId')) + "");
    const userId = +((localStorage.getItem('userid')) + "");
    const date = new Date().toISOString(); 
    
    
    const payload = {
      ...this.multiplePayCalendarData,
      entityId,
      userId,
      date,
    };
  
    this.payCalendarService.saveMultiplePayCalendar(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Pay calendar Data added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
           window.location.assign("/payroll-settings");
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Pay Calendar Data. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

    deleteCalendar(id: number) {
      this.payCalendarService.deletePayCalendar(id).subscribe(
        (_response: any) => {
          Swal.fire({
            title: 'Success!',
            text: 'Pay calendar delete successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {
            window.location.assign("/payroll-settings");
          });
        },
        (_error: any) => {
          Swal.fire({
            title: 'Error!',
            text: 'Failed to delete Pay Calendar. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
    }

  viewDetails(): void {
    console.log('Row clicked! Navigating to details...');
    this.router.navigate(['/payRun-user-details']);
  }
  
  deleteEarning(id: number) {
    this.earningService.deleteEarning(id).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Earning delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          window.location.assign("/payroll-settings");
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Earning. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }


  deleteDeduction(id: number) {
    this.deductionService.deleteDeduction(id).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Deduction delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          window.location.assign("/payroll-settings");
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Deduction. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }
    updateCalendar(id: number): void {
    this.router.navigate(['/update-calendar', id]);
  }

  updateEarnings(id: number): void {
    this.router.navigate(['/update-earning', id]);
  }


  updateDeductions(id: number): void {
    this.router.navigate(['/update-deduction', id]);
  }
  
  
   
  setActiveTab(tab: string) {
    this.activeTab = tab;
    if (tab === 'employee') {
      this.router.navigate(['/payroll-settings-employee']);
      
  }
  }

  isActiveTab(tab: string): boolean {
    return this.activeTab === tab;
  }

  activeSide: string = 'Earnings';

  isActiveSide(side: string): boolean {
    return this.activeSide === side;
  }

  setActiveSide(side: string): void {
    this.activeSide = side;
  }

  activeInnerTab: string = 'DraftPayRun';

  isActiveInnerTab(innerTab: string): boolean {
    return this.activeInnerTab === innerTab;
  }

  setActiveInnerTab(innerTab: string): void {
    this.activeInnerTab = innerTab;
  }

  activeSides: string = 'DraftPayRun';

  isActiveSides(side: string): boolean {
    return this.activeSides === side;
  }

  setActiveSides(side: string): void {
    this.activeSides = side;
  }


  // payCalendars = [
  //   { name: 'Fortnightly Calendar', payCycle: 'Fortnightly', nextPayPeriod: '12 Jan 2024 - 12 Jan 2024', nextPaymentDate: '12 Jan 2024' },
  //   { name: 'Weekly Calendar', payCycle: 'Weekly', nextPayPeriod: '12 Jan 2024 - 12 Jan 2024', nextPaymentDate: '12 Jan 2024' },
  //   { name: 'Monthly Calendar', payCycle: 'Monthly', nextPayPeriod: '12 Jan 2024 - 12 Jan 2024', nextPaymentDate: '12 Jan 2024' },
  //   { name: 'Bi-Monthly Calendar', payCycle: 'Bi-Monthly', nextPayPeriod: '12 Jan 2024 - 12 Jan 2024', nextPaymentDate: '12 Jan 2024' },
  //   { name: 'Quarterly Calendar', payCycle: 'Quarterly', nextPayPeriod: '12 Jan 2024 - 12 Jan 2024', nextPaymentDate: '12 Jan 2024' },
  //   { name: 'Yearly Calendar', payCycle: 'Yearly', nextPayPeriod: '12 Jan 2024 - 12 Jan 2024', nextPaymentDate: '12 Jan 2024' }
  // ];
  // page = 1;

  // earnings = [
  //   { name: 'Casual Worker', category: 'Allowance', rate: 'Rate Per Unit', unitType: 'Hour', glAccount: '477 Wages', reportable: 'No' },
  //   { name: 'Casual Worker', category: 'Allowance', rate: 'Rate Per Unit', unitType: 'Hour', glAccount: '477 Wages', reportable: 'No' },
  //   { name: 'Casual Worker', category: 'Allowance', rate: 'Rate Per Unit', unitType: 'Hour', glAccount: '477 Wages', reportable: 'No' },
  //   { name: 'Casual Worker', category: 'Allowance', rate: 'Rate Per Unit', unitType: 'Hour', glAccount: '477 Wages', reportable: 'No' },
  //   { name: 'Casual Worker', category: 'Allowance', rate: 'Rate Per Unit', unitType: 'Hour', glAccount: '477 Wages', reportable: 'No' },
  //   { name: 'Casual Worker', category: 'Allowance', rate: 'Rate Per Unit', unitType: 'Hour', glAccount: '477 Wages', reportable: 'No' }
  // ];
  earningsPage = 1;

  // deductions = [
  //   { name: 'FBT', category: 'Other/None', account: '850-Suspense', reducesPAYG: 'Yes', reducesSGC: 'No', excludedFromW1: 'No' },
  //   { name: 'FBT', category: 'Other/None', account: '850-Suspense', reducesPAYG: 'Yes', reducesSGC: 'No', excludedFromW1: 'No' },
  //   { name: 'FBT', category: 'Other/None', account: '850-Suspense', reducesPAYG: 'Yes', reducesSGC: 'No', excludedFromW1: 'No' },
  //   { name: 'FBT', category: 'Other/None', account: '850-Suspense', reducesPAYG: 'Yes', reducesSGC: 'No', excludedFromW1: 'No' },
  //   { name: 'FBT', category: 'Other/None', account: '850-Suspense', reducesPAYG: 'Yes', reducesSGC: 'No', excludedFromW1: 'No' },
  //   { name: 'FBT', category: 'Other/None', account: '850-Suspense', reducesPAYG: 'Yes', reducesSGC: 'No', excludedFromW1: 'No' }
  // ];
  deductionsPage = 1;

  // reimbursements = [
  //   { type: 'Other Reimbursable Cost', description: '850-Suspense', controlAccount: '850-Suspense' },
  //   { type: 'Other Reimbursable Cost', description: '850-Suspense', controlAccount: '850-Suspense' },
  //   { type: 'Other Reimbursable Cost', description: '850-Suspense', controlAccount: '850-Suspense' },
  //   { type: 'Other Reimbursable Cost', description: '850-Suspense', controlAccount: '850-Suspense' },
  //   { type: 'Other Reimbursable Cost', description: '850-Suspense', controlAccount: '850-Suspense' },
  //   { type: 'Other Reimbursable Cost', description: '850-Suspense', controlAccount: '850-Suspense' }
  // ];
  reimbursementsPage = 1;

  leaves = [
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No' },
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No' },
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No' },
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No' },
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No' },
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No' }
  ];
  leavesPage = 1;

  leaveTwo = [
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No', exemptSuperannuation: 'No', showBalance: 'No' },
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No', exemptSuperannuation: 'No', showBalance: 'No' },
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No', exemptSuperannuation: 'No', showBalance: 'No' },
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No', exemptSuperannuation: 'No', showBalance: 'No' },
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No', exemptSuperannuation: 'No', showBalance: 'No' },
    { name: 'Annual Leave', category: 'Annual Leave', units: 'Hours', entitlement: '152.00', loadingRate: 'No', showOnPayslip: 'No', exemptSuperannuation: 'No', showBalance: 'No' }
  ];
  leaveTwoPage = 1

  emp_earnings: { description: string; amount: number }[] = [];
  totalEarningAmount = 0;
  selectedEarningType: string | null = null;

  emp_leave: { description: string; amount: number }[] = [];
  totalLeaveAmount = 0;
  selectedLeaveType: string | null = null;

  emp_tax: { description: string; amount: number }[] = [];
  totalTaxAmount = 0;
  selectedTaxType: string | null = null;

  emp_reimbursement: { description: string; amount: number }[] = [];
  totalReimbursemenAmount = 0;
  selectedReimbursemenType: string | null = null;

  emp_deduction: { description: string; amount: number }[] = [
    { description: 'Ordinary Hours', amount: 40000 },
  ];
  totalDeduction = 40000;

  emp_superannuation: { description: string; amount: number }[] = [
    { description: 'HESTA Super HCG', amount: 40000 },
  ];
  totalSuperannuation = 40000;

  earningTypes = ['Ordinary Hours', 'Overtime', 'Allowance', 'Bonus'];
  leaveTypes = ['Annual Leave', 'Sick Leave', 'Unpaid Leave', 'Long Service Leave'];
  TaxTypes = ['PAYG', 'Medicare Levy', 'HELP', 'SFSS'];
  reimbursementTypes = ['Other Reimbursable Cost'];

  openEarningModal() {
    this.selectedEarningType = null;
  }

  addEarningLine() {
    if (this.selectedEarningType) {
      this.emp_earnings.push({ description: this.selectedEarningType, amount: 0 });
      this.updateEarning();
    }
  }

  removeEarning(index: number) {
    this.emp_earnings.splice(index, 1);
    this.updateEarning();
  }

  updateEarning() {
    this.totalEarningAmount = this.emp_earnings.reduce((sum, earning) => sum + +earning.amount, 0);
  }

  openLeaveModal() {
    this.selectedLeaveType = null;
  }

  addLeaveLine() {
    if (this.selectedLeaveType) {
      this.emp_leave.push({ description: this.selectedLeaveType, amount: 0 });
      this.updateLeave();
    }
  }

  removeLeave(index: number) {
    this.emp_leave.splice(index, 1);
    this.updateLeave();
  }

  updateLeave() {
    this.totalLeaveAmount = this.emp_leave.reduce((sum, leaves) => sum + +leaves.amount, 0);
  }

  openTaxModal() {
    this.selectedTaxType = null;
  }

  addTaxLine() {
    if (this.selectedTaxType) {
      this.emp_tax.push({ description: this.selectedTaxType, amount: 0 });
      this.updateTax();
    }
  }

  removeTax(index: number) {
    this.emp_tax.splice(index, 1);
    this.updateTax();
  }

  updateTax() {
    this.totalTaxAmount = this.emp_tax.reduce((sum, tax) => sum + +tax.amount, 0);
  }

  openReimbursementModal() {
    this.selectedReimbursemenType = null;
  }
  addReimbursementLine() {
    if (this.selectedReimbursemenType) {
      this.emp_reimbursement.push({ description: this.selectedReimbursemenType, amount: 0 });
      this.updateReimbursement();
    }
  }
  removeReimbursement(index: number) {
    this.emp_reimbursement.splice(index, 1);
    this.updateReimbursement();
  }
  updateReimbursement() {
    this.totalReimbursemenAmount = this.emp_reimbursement.reduce((sum, reimbursement) => sum + +reimbursement.amount, 0);
  }

  removeDeduction(index: number) {
    if (this.emp_deduction.length === 1) {
      this.emp_deduction.splice(index, 1);
      this.updateDeduction();
    }
  }
  updateDeduction() {
    this.totalDeduction = this.emp_deduction.reduce((sum, deduction) => sum + +deduction.amount, 0);
  }

  removeSuperannuation(index: number) {
    if (this.emp_superannuation.length === 1) {
      this.emp_superannuation.splice(index, 1);
      this.updateSuperannuation();
    }
  }

  updateSuperannuation() {
    this.totalSuperannuation = this.emp_superannuation.reduce((sum, superannuation) => sum + +superannuation.amount, 0);
  }
  openPopup(): void {
    this.isPopupVisible = true; // Opens the popup
  }
  
  closePopup(): void {
    this.isPopupVisible = false; // Closes the popup
  }

}
