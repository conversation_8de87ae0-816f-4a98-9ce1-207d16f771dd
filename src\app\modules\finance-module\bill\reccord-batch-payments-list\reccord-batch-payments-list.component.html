<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <!-- Heading And Button -->
  <div class="actions">
    <h1>Record Payments</h1>
    <!--<div class="btn-group" [class.show]="isDropdownOpen">
      
      <button
        type="button"
        class="btn btn-secondary dropdown-toggle gradient-btn"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        (click)="toggleDropdown()"
      >
        <i class="bi bi-three-dots-vertical"></i>
      
      </button>
      <ul class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen">
         <li>
          <a class="dropdown-item">Preview & Print</a>
        </li> 
        <li>
          <a class="dropdown-item" (click)="cancelSelectedPaymentVouchers()">Cancel</a>
        </li>        
      </ul>
    </div>-->
  </div>

  <div class="search-create">
    <button type="btn btn-outline-primary" (click)="createPaymentVoucherHeader()" class="invoice-convert">
        Record Payment
    </button>
    <button (click)="exportToExcel()" class="export-btn">Export to Excel</button>

  </div>

  <div class="Card">
    <!-- Filter and Search Section -->
    <div class="row1">
      <!-- Input for number, reference -->
      <div class="row1_col1">
        <label for="search-input">Reference No or Supplier</label>
        <div class="input-container">
          <input
            type="text"
            class="search-input"
            id="search-input"
            [(ngModel)]="searchTerm"
          />
          <i class="bi bi-search"></i>
        </div>
      </div>

      <div class="row1_col3">
        <label for="StartDate">Start Date</label>
        <div style="position: relative; width: 100%;">
          <input
            matInput
            [matDatepicker]="startDatePicker"
            class="date-picker"
            id="StartDate"
            [(ngModel)]="startDate"
            placeholder="dd/mm/yyyy"
          />
          <mat-datepicker-toggle matSuffix [for]="startDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
        </div>
        <mat-datepicker #startDatePicker></mat-datepicker>
      </div>

      <div class="row1_col4">
        <label for="EndDate">End Date</label>
        <div style="position: relative; width: 100%;">
          <input
            matInput
            [matDatepicker]="endDatePicker"
            class="date-picker"
            id="EndDate"
            [(ngModel)]="endDate"
            placeholder="dd/mm/yyyy"
          />
          <mat-datepicker-toggle matSuffix [for]="endDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
        </div>
        <mat-datepicker #endDatePicker></mat-datepicker>
      </div>
    </div>

    <div class="row2">
      <div class="row2_col3">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>     
      <div class="row2_col1">
        <div class="row2_col1">
            <button type="button" class="primary-button" (click)="filterPaymentVoucherHeaders()">Search</button>
        </div>
      </div>
    </div>
  </div>

  <div class="table-responsive">
    <table>
        <thead>
            <tr class="table-head">
                <th scope="col" class="valueCheckbox">
                    <input type="checkbox" [checked]="isAllSelected" (change)="selectAll($event)" />
                </th>                      
                <th scope="col" class="valuehead">Payment Number.</th>
                <th scope="col" class="valuehead">Bill Number(s)</th>
                <th scope="col" class="valuehead">Supplier Name</th>
                <th scope="col" class="valuehead">Document Date</th>
                <th scope="col" class="valuehead">Bank Name</th>
                <th scope="col" class="valuehead">Account Name</th>
                <th style="text-align: right;" scope="col" class="valuehead">Total Credit</th>
                <th scope="col" class="valuehead" style="text-align: center">Status</th>
                <!--<th style="text-align: center;" scope="col" class="valuehead">Actions</th>-->
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let bill of filteredPaymentVoucherHeaders; let i = index">
                <td class="valueCheckbox">
                    <input
                      type="checkbox"
                      [(ngModel)]="bill.selected"
                      (change)="toggleSelection(bill, $event)"
                    />
                </td>
                <td class="value">{{ bill.voucherNumber }}</td>
                <td class="value">{{ bill.referenceNos }}</td>
                <td class="value">{{ bill.payeeName }}</td>
                <td class="value">{{ bill.date | date:'dd-MM-yyyy' }}</td>
                 <td class="value">{{ bill.bankName }}</td>
                  <td class="value">{{ bill.ledgerAccountName }}</td>
                <td style="text-align: right;" class="value">{{ bill.totalPaidAmount | currency}}</td>
                <td
                style="padding-left: 15px"
                class="value"
                  [ngClass]="{
                      'text-pending': bill.status === 'Pending',
                      'text-open': bill.status === 'Open',
                      'text-overdue': bill.status === 'Overdue',
                      'text-paid': bill.status === 'Paid',
                      'text-Closed': bill.status === 'Closed',
                      'text-canceled': bill.status === 'Canceled',
    
                      
                  }"
              >
                <span
                  class="lable"
                  [ngClass]="{
                                'border-pending': bill.status === 'Pending',
                                'border-open': bill.status === 'Open',
                                'border-overdue': bill.status === 'Overdue',
                                'border-paid': bill.status === 'Paid',
                                'border-Closed': bill.status === 'Closed',
                                'border-canceled': bill.status === 'Canceled',
                            }"
                  >{{ bill.status }}</span
                >
              </td>
                <!--<td style="text-align: center;" class="value">
                  <button
                  class="btn btn-orange btn-sm"
                  style="
                    margin-right: 2px;
                    border: none;
                    background: none;
                    padding: 2px;
                    font-size: 1rem;
                  "
                  title="Edit"
                >
                  <i class="ri-edit-box-line" style="color: #4262ff"></i>
                </button>
                    <button (click)="deletePaymentVoucherHeaders(bill.paymentVoucherHeaderId)" class="btn btn-danger btn-sm" style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" data-bs-toggle="tooltip" title="Delete">
                   <i class="ri-delete-bin-line" style="color: #FF0000;"></i>
                  </button>
                </td>-->
            </tr>
        </tbody>
    </table>
</div>
</div>


