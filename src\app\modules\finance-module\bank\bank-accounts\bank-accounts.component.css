.form-container {
  max-width: 800px;
  margin: auto;
  padding: 1rem;
}

.text-header {
  font-family: Inter;
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 1.8rem;
}

.primary-acc-text {
  font-weight: 600;
  margin: 1rem 0;
}

.box-div {
  background-color: #fff;
  padding: 1rem;
  border-radius: 8px;
}

.form-group {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.full-width {
  width: 100%;
  margin-bottom: 1rem;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 1rem;
}

.form-input {
  padding: 0.5rem;
  font-size: 1rem;
  border-radius: 6px;
  border: 1px solid #ccc;
}

.input-labels {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.hr-line {
  margin: 1rem 0;
  border: 0;
  height: 1px;
  background-color: #e0e0e0;
}

.button-div {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.cancel-btn,
.approve-btn {
  padding: 10px 20px;
  width: 192px;
  border-radius: 17px;
  font-size: 1rem;
  cursor: pointer;
}

.cancel-btn {
  border: 2px solid #4262ff;
  background: #ffffff;
  color: #4262ff;
}

.cancel-btn:hover {
  background-color: #4262ff;
  color: white;
}

.approve-btn {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  border: none;
}

/* Responsive behavior */
@media (max-width: 600px) {

  .text-header {
  font-size: 28px;
  font-weight: 700;;
}

  .form-row {
    flex-direction: column;
  }

  .form-group {
    padding: 0;
  }

  .button-div {
    display: flex;
    flex-direction: column-reverse;
    align-items: stretch;
  }

  .cancel-btn,
  .approve-btn {
    width: 100%;
  }
}

.error-message {
  color: red;
  font-size: 0.85rem;
  margin-top: 4px;
}
input.ng-invalid.ng-touched,
select.ng-invalid.ng-touched {
  border-color: red;
}
