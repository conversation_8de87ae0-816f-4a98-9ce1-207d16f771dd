// export class expenceclaims{

// }
// export class ExpenseHead {
//     otherExpensesId : number = 0;
//     entityId: number = 0;
//     userId : number = 0;
//     expensesNumber : number = 0;
//     spentAt: string | undefined;   // Use 'string' for dates in string format
//     spentOn: Date | undefined;                 // Keep Date if you're working with Date objects
//     grossAmount: number = 0;           // Use 'number' for any numeric type
//     tax: number = 0;                   // Use 'number' instead of 'double'
//     netAmount: number = 0;             // Use 'number' instead of 'Double'
// }

// export class ExpenseDetails {
//     // otherExpensesDetailId : number = 0;
//     other_expenses_id : ExpenseHead = new ExpenseHead;
//     // expenseType : string | undefined;
//     description: string = '';      // Initialize to empty string
//     amount: number = 0;   
//     // expensesNumber : string | undefined;        
//     tax: boolean = false;                   // Use 'number' instead of 'Double'
//     gl_account: number = 0;            // Use 'number' instead of 'Double'
//     total_amount: number = 0;          // Use 'number' instead of 'Double'
// }




// export class OtherExpensesHead {
//     otherExpensesId : number = 0;
//     businessPartnerId: any = 0;
//     entityId: number = 0;
//     userId : number = 0;
//     description: string = '';
//     expensesNumber: string = '';
//     spentAt: string = ''; 
//     spentOn: string = '';             
//     supplierName: string = '';
//     status: string = '';
//     totalGst: number = 0.0;
//     grossAmount: number = 0.0;
//     netAmount: number = 0.0;
//     otherExpensesDetails: OtherExpensesDetail[] = [];  
// }

// export class OtherExpensesDetail {
//   otherExpensesDetailId?: number = 0;
//   otherExpensesHead?: OtherExpensesHead = new OtherExpensesHead();
//   expenseType: string = '';
//   expensesNumber: string = '';
//   description: string = '';
//   amount: number = 0.0;
//   totalAmount: number = 0.0;
//   tax: number = 0.0;
//   coaLedgerAccountId: number = 0;
//   ledgerAccountName: string = '';
//   taxApplicability?: boolean = false;    
// }

export class expenceclaims{

}
export class OtherExpensesHead {
    otherExpensesId : number = 0;
    entityId: number = 0;
    businessPartnerId : number = 0;
    userId : number = 0;
    supplierName : string = '';
    description: string = '';
    expensesNumber: string ='';
    spentAt: string = ''; 
    spentOn: string = '';             
    grossAmount: number = 0.0;     
    netAmount: number = 0.0; 
    status : string = '';
    totalGst : number = 0.0;
    otherExpensesDetails: OtherExpensesDetail[] = [];  
    selected: boolean = false; 
    balanceAmount: number = 0.0;
    creditAmount: number = 0.0;
    remarks: string = '';
    bankRecStatus: string = '';
    isLocked?: boolean;
}

export class OtherExpensesDetail {
  otherExpensesDetailId?: number = 0;
  otherExpensesHead?: OtherExpensesHead = new OtherExpensesHead();
  expenseType: string = '';
  expensesNumber: string = '';
  description: string = '';
  amount: number = 0.0;
  total_amount: number = 0.0;
  tax: number = 0.0;
  gl_account : string = '';
  coaLedgerAccountId: number = 0;
  ledgerAccountName: string = '';
  taxApplicability?: boolean = false; 
}

export class CoaLedgerAccount{
  coaLedgerAccountId: number = 0;
  ledgerAccountCode: string = '';
  ledgerAccountName : string = '';
}


export class PaymentExpensesHead{

  paymentExpensesHeadId: number = 0;
    details: PaymentExpensesDetails[] = [];
    businessPartnerId: any = 0;
    entityId: number = 0;
    userId: number = 0;
    paymentExpensesNumber: string = '';
    documentDate: string = '';
    balanceDue: number = 0;
    documentStatus: string = '';
    remarks: string = '';
    totalPaidAmount: number = 0;
    supplierName: string = '';
   expensesNumbers: string = '';
    selected: boolean = false;
}


export class PaymentExpensesDetails {
  paymentExpensesDetailsId: number = 0;
  paymentExpensesHead: PaymentExpensesHead = new PaymentExpensesHead();
  otherExpensesId: number = 0;
  expensesNumber: string = '';
  netAmount: number = 0.0;
  balanceAmount: number = 0.0;
  paidAmount: number = 0.0;
}
