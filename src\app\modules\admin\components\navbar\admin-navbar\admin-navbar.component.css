.company-detail {
  display: flex;
  align-items: center;
}

.company-detail img {
  height: 40px;
  margin-right: 10px;
}

.company-detail span {
  font-size: 1.2em;
  font-weight: bold;
}

.Ledger_Chimp {
  margin: -1px 0;
}

.navbar-brand {
  font-weight: bold;
  color: #4e54c8;
  display: flex;
  align-items: center;
}

.navbar {
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  padding: 10px 0;
}

.navbar ul {
  list-style: none;
  display: flex;
  margin: 0;
  padding: 0;
}

.navbar ul li {
  margin: 0 15px;
}

.navbar ul li a {
  text-decoration: none;
  color: #4262ff;
  padding: 10px 20px;
  border-radius: 8px;
  background: #d9dfff;
  font-family: Arial, sans-serif;
  font-size: 15px;
  font-weight: 500;
  line-height: 45px;
  text-align: center;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.navbar ul li a:hover {
  background: #4262ff;
  color: #fff;
  cursor: pointer;
}

.navbar ul li a.active {
  background-color: #007bff;
  color: #fff;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  list-style: none;
  margin: 0;
  padding: 0;
  flex-direction: column;
  align-items: center;
  width: 200px;
  z-index: 1;
  border-radius: 5px;
}

.dropdown-menu.show {
  display: flex;
}

.dropdown-menu li {
  margin: 0;
}

.dropdown-menu .dropdown-list {
  background-color: #fff;
  border-bottom: 1px solid #ddd;
}

.dropdown-menu .dropdown-list .dropdown-list-content {
  text-decoration: none;
  color: #333;
  border-radius: 8px;
  background: white;
  font-family: Arial, sans-serif;
  font-size: 15px;
  font-weight: 500;
  line-height: 22.5px;
  text-align: center;
  display: block;
  margin: 5px 0;
}

.dropdown-menu .dropdown-list .dropdown-list-content:hover {
  background: #4262ff;
  color: #fff;
  cursor: pointer;
}

select {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  margin: 5px 0;
  color: black;
}

select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0px 0px 5px rgba(0, 123, 255, 0.5);
}

option {
  padding: 8px;
  background-color: #fff;
  font-size: 14px;
  color: black;
}

option:hover {
  background-color: #f1f1f1;
}

a {
  color: #333;
  text-decoration: none;
  padding: 8px 16px;
  font-size: 16px;
  transition: color 0.3s ease;
}

a:hover {
  color: #007bff;
}

.dropdown-list-content:hover {
  background-color: #007bff;
  color: #fff;
}
