import { Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { GlPostingHead } from '../journal-voucher';
import { JournalVoucherService } from '../journal-voucher.service';
import Swal from 'sweetalert2';
import { DateAdapter } from '@angular/material/core';

@Component({
  selector: 'app-jv-list',
  templateUrl: './jv-list.component.html',
  styleUrls: ['./jv-list.component.css'],
})
export class JvListComponent {
  searchQuery: string = '';
  glPostingHeadList: GlPostingHead[] = [];
  filteredGlPostingHeadList: GlPostingHead[] = [];
  updatedGlpostingHead: GlPostingHead = new GlPostingHead();
  date: string | null = null;
  startDate: string | null = null; 
  endDate: string | null = null;  
  searchTerm: string = ''; 

  constructor(
    private router: Router,
    private journalVoucherService: JournalVoucherService,
    private dateAdapter: DateAdapter<Date>,
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }

  ngOnInit(): void {
    this.fetchGlPostingHeadList(+(localStorage.getItem('entityId') + ''));
  }

  fetchGlPostingHeadList(entityId: number): void {
    this.journalVoucherService.getGlPostingHeadList(entityId).subscribe(
      (data) => {
       // Filter the list for "Journal Voucher"
       this.glPostingHeadList = data.filter(
        (glPostingHead) => glPostingHead.documentType === 'Journal Voucher'
      );

          // Initialize filtered list before applying filters
      this.filteredGlPostingHeadList = [...this.glPostingHeadList];
      },
      (error) => {
        console.error('Error fetching journal vouchers:', error);
      }
    );
  }

   //dropdown
   isDropdownOpen = false;

   toggleDropdown(): void {
     this.isDropdownOpen = !this.isDropdownOpen;
   }

  onSearch(): void {
    const query = this.searchQuery.toLowerCase().trim();
    if (query.length == 0) {
      window.location.reload();
    }
    this.glPostingHeadList = this.glPostingHeadList.filter((glPostingHead) =>
      Object.values(glPostingHead).some(
        (value) => value && value.toString().toLowerCase().includes(query)
      )
    );
  }

  filterJournalVouchers(): void {
    if (!this.searchTerm && !this.startDate && !this.endDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Search Criteria',
        text: 'Please provide at least one search criterion: Voucher Number, Start Date, or End Date.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    let filtered = this.glPostingHeadList;

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(journal =>
        journal.jvNumber != null && journal.jvNumber.toString().toLowerCase().includes(searchTermLower)
      );
    }

    // Filter by date range
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(journal => journal.date && new Date(journal.date) >= startDate);
    }

    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(journal => journal.date && new Date(journal.date) <= endDate);
    }

    this.filteredGlPostingHeadList = filtered;
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.filteredGlPostingHeadList = this.glPostingHeadList;
  }

  navigateJVCreationPage(): void {
    this.router.navigate(['/create-journal-voucher']);
  }

  editJV(id: number): void {
    this.router.navigate(['/update-journal-voucher', id]);
  }
}
