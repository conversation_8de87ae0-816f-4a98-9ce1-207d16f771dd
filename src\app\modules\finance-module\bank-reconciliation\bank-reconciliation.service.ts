import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpService } from 'src/app/http.service';
import { environment } from 'src/environments/environment';
import { InvoiceService } from '../../invoice/invoice.service';
import { InvoiceHead, PaymentReceiptsHead } from '../../invoice/invoice';
import { BillService } from '../bill/bill.service';
import { ApInvoiceHead } from '../bill/bill';
import { BankRuleDTO, BankRuleTransactionMatchDTO, BankStatementHeader, BankStatementMatchedDetails, TransactionRequestBankTransfer, TransactionRequestIncomeOrExpense } from './bank-reconciliation';

@Injectable({
  providedIn: 'root'
})
export class BankReconciliationService {



  private readonly baseURL = environment.financeApiUrl;

    constructor(private http: HttpClient, private httpService: HttpService, private invoiceService: InvoiceService, private paymentRece: InvoiceService, private billService: BillService) {}

    getAuthToken(): string | null {
      return window.sessionStorage.getItem('auth_token');
    }

    request(
      method: string,
      url: string,
      data: any,
      params?: any
    ): Observable<any> {
      let headers = new HttpHeaders();

      if (this.getAuthToken() !== null) {
        headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
      }

      const options = {
        headers: headers,
        params: new HttpParams({ fromObject: params }),
      };

      switch (method.toUpperCase()) {
        case 'GET':
          return this.http.get(this.baseURL + url, options);
        case 'POST':
          return this.http.post(this.baseURL + url, data, options);
        case 'PUT':
          return this.http.put(this.baseURL + url, data, options);
        case 'DELETE':
          return this.http.delete(this.baseURL + url, options);
        // Add more HTTP methods as needed
        default:
          throw new Error('Unsupported HTTP method');
      }
    }

    // setter and getters for excel file
    fileData: File | null = null;
    transactionData: any[] = [];

    setFile(file: File | null){
      this.fileData = file;
    }

    getFile(){
      return this.fileData;
    }

    setTransactions(transactions: any){
      this.transactionData = transactions;
    }

    getTransactions(){
      return this.transactionData;
    }

    uploadBankStatement(id: Number,bankAccountId: Number, formData: FormData): Observable<any> {
      return this.request('POST', `/bank-statements/upload/${id}/${bankAccountId}`, formData, {});
    }

    getInvoiceHeadListByEntity(entityId: number): Observable<InvoiceHead[]> {
      return this.invoiceService.getInvoiceHeadListByStatus(entityId);
    }

    getPaymnetReceiptsListByEntity(entityId: number): Observable<PaymentReceiptsHead[]> {
      return this.invoiceService.getAllPaymentReceiptsHeadList(entityId);
    }
    getPaymentReceiptByInvoiceId(invoiceNumber: string,entityId: number) :Observable<PaymentReceiptsHead>{
      return this.invoiceService.getPaymentReceiptsHeadByInvoiceIdEntityId(invoiceNumber,entityId);
    }

    getAllApInvoiceHeadList(entityId: number): Observable<ApInvoiceHead[]> {
      return this.billService.getAllApInvoiceHeadListByStatus(entityId);
    }

    getAllTransactionsByHeader(bankStatementId: string | undefined) : Observable<any>{
      if (bankStatementId) {
        return this.request('GET', `/bank-statements/getDetailsByHeaderId/${bankStatementId}`,{}, {});
      }
      throw new Error('Bank Statement ID is required');
    }

    getAllTransactionsByBankAccountFeed(bankAccountId:number,entityId:number,requestDate:string){
      return this.request('GET', `/bank-statements/getAllTransactionsByBankAccountFeed/${bankAccountId}/${entityId}/${requestDate}`,{}, {});
    }

    getBankStatementHeaderByBankAccountId(bankAccountId:number){
      return this.request('GET', `/bank-statements/getStatementHeaderByBankId/${bankAccountId}`,{}, {});
    }

    saveSuggestTransaction(transactionData: BankStatementMatchedDetails): Observable<any> {
      return this.request('POST', '/bank-statement-matched-details/saveSuggestTransaction', transactionData,{});
    }

    saveFindDebitTransaction(requestData: any): Observable<any> {
      return this.request('POST', '/bank-statement-matched-details/saveDebitFindTransaction', requestData,{});
    }

    saveFindCreditTransaction(requestData: any): Observable<any> {
      return this.request('POST', '/bank-statement-matched-details/saveCreditFindTransaction', requestData,{});
    }

    saveSuggestAndDebitDebitTransaction(requestData: any): Observable<any> {
      return this.request('POST', '/bank-statement-matched-details/saveDebitSuggestAndCreateTransaction', requestData,{});
    }

    saveSuggestCreditTransaction(requestData: any): Observable<any> {
      return this.request('POST', '/bank-statement-matched-details/saveCreditFindTransaction', requestData,{});
    }

    saveCreateDebitTransaction(requestData:any):Observable<any>{
       return this.request('POST', '/bank-statement-matched-details/saveDebitCreateTransaction', requestData,{});
    }

    saveCreateIncomeOrExpense(requestData:TransactionRequestIncomeOrExpense):Observable<any>{
       return this.request('POST', '/bank-statement-matched-details/saveCreateIncomeOrExpense', requestData,{});
    }

    createTransactionBankTransfer(requestData:TransactionRequestBankTransfer):Observable<any>{
       return this.request('POST', '/bank-statement-matched-details/createTransactionBankTransfer', requestData,{});
    }

    getMatchedDetails(entityId: number, bankStatementId: String): Observable<BankStatementMatchedDetails[]> {
      return this.request('GET', `/bank-statement-matched-details/getMatchedDetails/${entityId}/${bankStatementId}`, {}, {});
    }

    getMatchedTransactionsByHeader(bankStatementHeader: BankStatementHeader): Observable<BankStatementMatchedDetails[]>{
      return this.request('GET', '/bank-statement-matched-details/getMatchedTransactionsByHeader', bankStatementHeader,{});
    }

    deleteDebitMatchedTransaction(bankStatementMatchedDetail: BankStatementMatchedDetails): Observable<any>{
      return this.request('POST', '/bank-statement-matched-details/deleteDebitBankStatementMatchedDetail', bankStatementMatchedDetail,{});
    }

    deleteCreditMatchedTransaction(bankStatementMatchedDetail: BankStatementMatchedDetails): Observable<any>{
      return this.request('POST', '/bank-statement-matched-details/deleteCreditBankStatementMatchedDetail', bankStatementMatchedDetail,{});
    }

    saveBankRule(bankRuleHead:BankRuleDTO):Observable<any>{
      return this.request('POST', '/bank-rule/saveBankRule', bankRuleHead,{});
    }

    loadBankRulesByBankAccountIdAndEntityId(entityId: number,BankAccountId:number):Observable<any> {
      return this.request('GET',`/bank-rule/getBankRulesByBankAccountIdAndEntityId/${entityId}/${BankAccountId}`,{},{});
    }

    submitBankRuleMatch(bankRuleTransactionMatchDTO: BankRuleTransactionMatchDTO):Observable<any> {
      return this.request('POST','/bank-statement-matched-details/createTransactionBankRule', bankRuleTransactionMatchDTO,{})
    }

    undoTransactionMatching(entityId: number, groupId: string):Observable<any> {
      return this.request('POST',`/bank-statement-matched-details/undoMatching/${entityId}/${groupId}`,{},{})
    }

    reconciliationFinalize(entityId: number, bankStatementId: string):Observable<any> {
      return this.request('POST',`/bank-statement-matched-details/reconciliationFinalize/${entityId}/${bankStatementId}`,{},{})
    }

    getPendingTransactionCount(entityId: number, bankStatementId: string):Observable<any> {
      return this.request('GET',`/bank-statement-matched-details/getPendingTransactionCount/${entityId}/${bankStatementId}`,{},{})
    }

    loadCompletedStatementHeaders(entityId: number):Observable<any>  {
      return this.request('GET',`/bank-statement-matched-details/loadCompletedStatementHeaders/${entityId}`,{},{})
    }

    bankRecFinalReportView(entityId: number,bankStatementId:string):Observable<any>  {
      return this.request('GET',`/bank-statement-matched-details/bankRecFinalReportView/${entityId}/${bankStatementId}`,{},{})
    }
}

