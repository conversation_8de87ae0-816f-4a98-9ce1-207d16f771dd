import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { QuotationDetail, QuoteHead,QuoteLog,SalesItem } from './quotation';

@Injectable({
  providedIn: 'root'
})
export class QuotationService {


  getQuoteHeadLists() {
    throw new Error('Method not implemented.');
  }

  private readonly baseURL = environment.salesApiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  getQuoteDetailsByQuoteHeadId(id: number): Observable<QuotationDetail[]> {

    return this.request("GET", `/getQuoteDetailsByQuoteHeadId/${id}`, {});
  }

  getSalesQuotesHeadById(id: number): Observable<QuoteHead> {

    return this.request("GET", `/api/getSalesQuotesHeadById/${id}`, {});
  }

  getAllSalesQuotesHead(): Observable<QuoteHead[]> {
    return this.request('GET', '/api/salesQuotesHeadList', {}, {});
  }

  getAllSalesQuotesHeadList(entityId: number): Observable<QuoteHead[]> {
    return this.request("GET", `/api/salesQuotesHeadList/${entityId}`, {}, {});
  }

  deleteSalesQuotesHead(id: number): Observable<void> {
    return this.request("DELETE", `/api/deleteSalesQuotesHead/${id}`, {});
  }

  updateSalesQuotesHead(id: number, salesQuotesHead: QuoteHead): Observable<QuoteHead> {
    return this.request('PUT', `/api/updateSalesQuotesHead/${id}`, salesQuotesHead);
  }
updateQuoteStatus(quoteId: number, status: string): Observable<any> {
  return this.request('PUT', `/api/updateQuoteStatus/${quoteId}/status?status=${status}`, {});
}


  saveSalesItem(salesItem: SalesItem): Observable<SalesItem> {
    return this.request('POST', '/saveSalesItem', salesItem);
  }


  getSalesItemByCode(itemCode: string): Observable<SalesItem> {
    return this.request('GET', `/getSalesItemByCode/${itemCode}`, {});
  }

  getSalesItemByCodeAndEntityId(itemCode: string, entityId: any): Observable<SalesItem> {
    return this.request('GET', `/getSalesItemByCodeAndEntityId`, {}, { itemCode: itemCode, entityId: entityId });
  }
  
  saveSalesQuotesHead(quotationData: any): Observable<any> {
    return this.request('POST', '/api/saveSalesQuotesHead', quotationData);
  }

  saveQuoteHeadRevise(quotationData: any): Observable<any> {
    return this.request('POST', '/api/saveReviseQuote', quotationData);
  }


  reviseQuote(id: number): Observable<any> {
      return this.request("PUT", `/api/reviseQuote/${id}`, {});
  }

  deleteQuoteItem(salesQuotesDetailId: number): Observable<any> {
    return this.request('DELETE', `/api/deleteQuoteItem/${salesQuotesDetailId}`, {});
  }

  getQuotationListReport(requestData: {
  fromDate: string,
  toDate: string,
  status: string,
  entityId: number,
  businessPartnerId: number | null,
  entityUUID: string
}): Observable<any> {
   return this.request('POST',`/api/getQuotationListReport`, requestData);
}


  getQuoteReport(quoteId: number, entityId: any, entityUuid: string): Observable<any> {
    return this.request('GET', `/api/getQuoteReport/${quoteId}/${entityId}/${entityUuid}`, {});
  }
  getQuoteReport1(quoteId: number, entityId: any): Observable<any> {
    alert(entityId);
    return this.request('GET', `/api/getQuoteReport/${quoteId}`, { entityId });
  }
  
  
  getAllSalesItems(): Observable<SalesItem[]> {
    return this.request('GET', '/salesItemList', {}, {});
  }

  getAllSalesItemsByEntity(entityId: any): Observable<SalesItem[]> {
    return this.request('GET', '/getAllSalesItemsByEntity', {}, { entityId: entityId });
  }

   sendQuotes(quotes: QuoteHead[]): Observable<any> {
    return this.request('POST', '/api/sendQuotes', quotes);
  } 

  getQuoteHeadList(): Observable<QuoteHead[]> {
    return this.request('GET', '/api/salesQuotesHeadList', {});
  }
sendQuoteWithEmail(
  emailData: { subject: string; content: string; quotes: QuoteHead[] },
  entityUuid: string
): Observable<any> {
  return this.request('POST', `/api/sendQuoteWithEmail/${entityUuid}`, emailData);
}

  getQuoteHeadListByEntity(entityId: any): Observable<QuoteHead[]> {
    return this.request('GET', '/api/salesQuotesHeadList', {}, { entityId: entityId });
  }


  getAllQuoteLogsByQuoteHeadId(id: number): Observable<QuoteLog[]> {
    return this.request('GET', `/getAllQuoteLogsByQuoteHeadId/${id}`, {});
  }

   getQuoteSummary(entityId: number): Observable<any> {
  return this.request('GET', `/dashboard/quote-summary`, null, { entityId });
}

}
