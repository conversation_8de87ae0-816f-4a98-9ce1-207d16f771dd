import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { GlPostingHead, GlPostingDetails } from './journal-voucher';

@Injectable({
  providedIn: 'root',
})
export class JournalVoucherService {
  private readonly baseURL = environment.financeApiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  //Gl posting Head

  getGlPostingHead(): Observable<GlPostingHead[]> {
    return this.request('GET', '/gl-posting-head/list', null, null);
  }

  getGlPostingHeadById(id: number): Observable<GlPostingHead> {
    return this.request('GET', `/gl-posting-head/get/${id}`, null);
  }

  getGlPostingHeadList(entityId: number): Observable<GlPostingHead[]> {
    return this.request('GET', `/gl-posting-head/by-entityId/${entityId}`, null);
  }

  addGlPostingHead(data: GlPostingHead): Observable<GlPostingHead> {
    return this.request('POST', '/gl-posting-head/save', data, null);
  }

  updateGlPostingHead(
    id: number,
    data: GlPostingHead
  ): Observable<GlPostingHead> {
    return this.request('PUT', `/gl-posting-head/update/${id}`, data);
  }

  deleteGlPostingHead(id: number): Observable<void> {
    return this.request('DELETE', `/gl-posting-head/delete/${id}`, null);
  }

  getJournalVoucherListReport(fromDate: string, toDate: string, status: string, entityId: number,businessPartnerId:any): Observable<any> {
    return this.request('GET', `/api/getJournalVoucherListReport`, {},
      { from: fromDate, to: toDate , status: status,entityId: entityId,businessPartnerId:businessPartnerId});
}

  //Gl posting details

  getGlPostingDetails(): Observable<GlPostingDetails[]> {
    return this.request('GET', '/gl-posting-details/list', null, null);
  }

  getGlPostingDetailsList(entityId: number): Observable<GlPostingDetails[]> {
    return this.request('GET', `/gl-posting-details/by-entityId/${entityId}`, null);
  }

  addGlPostingDetails(data: GlPostingDetails): Observable<GlPostingDetails> {
    return this.request('POST', '/gl-posting-details/save', data, null);
  }



   getGstSummary(entityId: number, fromDate: string, toDate: string): Observable<any> {
   return this.request('GET', `/gl-posting-details/gst-summary`, {},
     { entityId:entityId, fromDate:fromDate, toDate:toDate });
  
}

  addGlPostingDetailsList(
    data: GlPostingDetails[]
  ): Observable<GlPostingDetails[]> {
    return this.request('POST', '/gl-posting-details/save-list', data, null);
  }

  updateGlPostingDetails(
    id: number,
    data: GlPostingDetails
  ): Observable<GlPostingDetails> {
    return this.request('PUT', `/gl-posting-details/update/${id}`, data, null);
  }

  deleteGlPostingDetails(id: number): Observable<void> {
    return this.request(
      'DELETE',
      `/gl-posting-details/delete/${id}`,
      null,
      null
    );
  }

  
}
