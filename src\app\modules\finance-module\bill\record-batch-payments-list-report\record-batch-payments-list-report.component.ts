import { Component, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { HttpErrorResponse } from '@angular/common/http';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import Swal from 'sweetalert2';
import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { BillService } from '../bill.service';
import { ApInvoiceHead, PaymentVoucherHeader } from '../bill';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';
@Component({
  selector: 'app-record-batch-payments-list-report',
  templateUrl: './record-batch-payments-list-report.component.html',
  styleUrls: ['./record-batch-payments-list-report.component.css']
})
export class RecordBatchPaymentsListReportComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  @ViewChild('recordBatchPaymentsPreviewFrame') recordBatchPaymentsPreviewFrame!: ElementRef;

   private audio!: HTMLAudioElement;
  

   fromDate: string = '';
   toDate: string = '';
   status: string = '%';  
   suppliers: BusinessPartner[] = [];
   recordBatchPaymentsData: PaymentVoucherHeader = new PaymentVoucherHeader();
   isLoading = false;
   getAllSuppliers = false;  



constructor(
    private billService: BillService,
    public sanitizer: DomSanitizer,
    private swalAlerts: SwalAlertsService,
    private businessPartnerService: BusinessPartnerService
  ) { }

  ngOnInit() {
    this.recordBatchPaymentsData.businessPartnerId = '0';
    this.loadSuppliers();
  }

      previewRecordBatchPayments(fromDate: string, toDate: string, status: string, businessPartnerId: any) {
      if (!fromDate || !toDate) {
    
        this.swalAlerts.showSwalWarning('Warning!', 'Please select Date Range', 'Missing date range for Payment report.');
        return;
      }
      this.isLoading = true;
      const entityId = +localStorage.getItem('entityId')!;
      const entityUUID = localStorage.getItem('entityUuid')!;
      const bpId = (businessPartnerId === 0 || businessPartnerId === '' || businessPartnerId == null) ? null : +businessPartnerId;
    
      const requestData = {
        fromDate,
        toDate,
        status,
        entityId,
        businessPartnerId: bpId,
        entityUUID
      };
    
      this.billService.getRecordBatchPaymentsListReport(requestData).subscribe(
        data => {
          const base64String = data.response;
          if (base64String) {
            this.loadPdfIntoIframe(base64String);
          } else {
            this.isLoading = false;
            alert('No Payment data for preview.');
          }
        },
        error => {
          this.isLoading = false;
          alert('Error loading Payment preview.');
        }
      );
    }
    
        
          private loadPdfIntoIframe(base64String: string) {
            if (base64String && base64String.trim().length >= 50) {
              const pdfData = 'data:application/pdf;base64,' + base64String;
              const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
              const iframe = this.recordBatchPaymentsPreviewFrame.nativeElement;
          
              iframe.onload = () => {
                this.isLoading = false;
              };
          
              iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
          
              // Open modal manually using Bootstrap JS
              const modalElement = document.getElementById('simpleModal');
              const modal = new bootstrap.Modal(modalElement!);
              modal.show();
            } else {
               this.isLoading = false;
               this.swalAlerts.showSwalWarning('No Data', 'No Payment data for preview.', 'No Payment data was returned for the selected range.');
          
            }
          }
          
          
      loadSuppliers() {
      const entityId = +((localStorage.getItem('entityId')) + '');
      this.businessPartnerService.getSupplierListByEntity(entityId).subscribe(
        (customers: BusinessPartner[]) => {
            this.suppliers = customers.filter(
                (supplier) =>
                  supplier.businessPartnerTypeId?.businessPartnerType === 'Supplier'
              );
        },
        (error: HttpErrorResponse) => {
          console.error('Error fetching Suppliers', error);
    
          //  Use SwalAlertsService for error with Chimp support
          this.swalAlerts.showErrorWithChimpSupport(
            'Failed to load Suppliers.',
            'Unable to fetch Suppliers list for this entity. Please check if the Suppliers service is responding.'
          );
        }
      );
    }
    

  onSupplierChange(event: any) {
    const selectedSupplierId = event.target.value;
    const selectedSupplier = this.suppliers.find(cust => cust.businessPartnerId === +selectedSupplierId);

    // Set the reference field
    this.recordBatchPaymentsData.payeeName = selectedSupplier?.bpName || '';
  }


 // Method to toggle all Suppliers' data
 toggleAllSuppliers() {
  this.getAllSuppliers = !this.getAllSuppliers;
  if (this.getAllSuppliers) {
    this.recordBatchPaymentsData.businessPartnerId = '';  // Clear selected customer when showing all
  }
}
  
    
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }







}
