        /* Global styles */
        h3 {
            flex: 1;
            margin-bottom: 0;
            font-family: Inter;
            font-size: 36px;
            font-weight: 700;
            text-align: left;
            color: #4262FF;
            top: 264px;
    }
  

    body {
          font-family: Arial, sans-serif;
          background-color: transparent;
          margin: 0;
          padding: 0;
      }
      

      .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          background-color: transparent;
      }
      /* Header styles */

      .header {
          display: flex;
          background-color: transparent;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          box-shadow: none;
          border: none;
          gap: 5px;
          /* Adds space between buttons */
      }

      .popup-header h3 {
          flex: 1;
          margin-bottom: 0;
          font-family: Inter;
          font-size: 36px;
          font-weight: 700;
          text-align: left;
          color: #4262FF;
          top: 264px;
      }

      .header .transparent-button {
          top: 256px;
          border: 1px solid #4262FF;
          padding: 10px 20px;
          cursor: pointer;
          border-radius: 12px;
          font-weight: bold;
          background-color: white;
          color: #4262FF;
          font-family: Inter;
          font-size: 18px;
          font-weight: 600;
          text-align: center;
      }

      .header .transparent-button:hover {
          background-color: #4262FF;
          color: white;
      }
      .bd{
        border: 2px solid #CEC9C980; /* Sets the border width, style, and color */
        border-radius: 12px; /* Rounds the corners */
      }
      /* Form styles */

      .form-section {
        display: flex;
        font-family: Arial, sans-serif;
        flex-direction: column;
        padding: 20px;
        background-color: #f7f7f7; /* If you want a solid background color */

      }


      .form-row {
          display: flex;
          gap: 32px;
          padding-right: 20px;
          margin-bottom: 5px;
      }

      .form-group {
          flex: 1;
      }

      .input-style {
          height: 49px;
          top: 656px;
          left: 111px;
          padding: 10px;
          font-size: 14px;
      }

      .create-customer-container {
          display: flex;
          justify-content: flex-end;
      }

      label {
          display: block;
          /* margin-bottom: 5px; */
          font-weight: bold;
          color: #333;
      }

      .create-customer-btn {
          float: right;
          color: #4a4ae2;
          background: none;
          border: none;
          cursor: pointer;
          font-size: 14px;
          font-style: italic;
          font-weight: bold;
      }

      #quotationNo  {
          background-color: white;
          width: 100%;
        height: 40px;
        border-radius: 5px; /* Rounds the corners */
        border: 0.1px solid #C7C7C7;
      }
      #validityUntil{
        background-color: white;
        width: 100%;
        height: 95%;
        border-radius: 5px; /* Rounds the corners */
        border: 0.1px solid #C7C7C7;
      }
      #quotationDate{
        background-color: white;
        width: 100%;
        height: 95%;
        border-radius: 5px; /* Rounds the corners */
        border: 0.1px solid #C7C7C7;
      }
      /* #quotation,
      #quotationNo {
          margin-left: 25px;
      } */

      #customer-add {
          width: 100%;
        height: 40px;
        border-radius: 5px; /* Rounds the corners */
        border: 0.1px solid #C7C7C7;
      }
      /* Customer and Search Item sections */

      .customer-section,
      .search-item-section {
          flex-basis: 50%;
      }
      /* Search bar styles */

      .search-bar {
          display: flex;
          justify-content: flex-end;
          width: 100%;
          margin-bottom: 20px;
          align-items: center;
          position: relative;
      }

      .search-bar input.input-style {
          flex-grow: 1;
          border: 1px solid #ccc;
          border-radius: 8px;
          /* Add padding to make space for the button */
      }

      /*.search-bar button.btn.btn-link {*/
      /*    position: absolute;*/
      /*    right: 10px;*/
      /*    top: 50%;*/
      /*    transform: translateY(-50%);*/
      /*    background: none;*/
      /*    border: none;*/
      /*    cursor: pointer;*/
      /*    padding: 0;*/
      /*}*/


      .search-bar button.btn.btn-link1 {
        background: linear-gradient(180deg, #4262FF 0%, #283B99 100%);
        color: white;
        border-radius: 8px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .search-bar p {
        margin-left: 5px; /* Adjust margin as needed */
        font-size: 14px; /* Adjust font size as needed */
        color: #000; /* Adjust text color as needed */
      }

      /* Table styles */

      .table-section {
          background-color: transparent;
          overflow: hidden;
          margin-bottom: 20px;
          padding-left: 20px;
          padding-right: 20px;
          padding-top: -20px;
      }

      table {
          width: 100%;
          border-collapse: collapse;
      }

      th {
        background: linear-gradient(90deg, rgba(66, 98, 255, 0.06) 0%, rgba(63, 20, 153, 0.06) 100%);
        color: black;
          text-align: left;
          padding: 12px;
          font-weight: normal;
      }

      td {
          padding: 12px;
          border-bottom: 1px solid #ddd;
          background-color: white;
      }
      /* Notes and Totals section styles */

      .notes-totals-container {
          display: flex;
          gap: 20px;
          margin-top: 20px;
        padding-bottom: 20%;
      }

      .notes-section,
      .totals-section {
          flex: 1;
          background-color: transparent;
          border-radius: 8px;
          padding: 30px;
          box-shadow: none;
          border: none;
          margin-left: 14px;
          margin-right: 70px;
          height: 260px;
          box-sizing: border-box;
      }

      #notes {
          border: 1px solid #E6E6E6;
          width: 100%;
          height: 100%;
          /* Make the textarea fill the available height */
          border-radius: 12px;
          opacity: 1;
          resize: none;
          /* Prevent resizing */
      }

      .totals-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #eee;
      }

      .totals-row span,
      .totals-row strong {
          flex: 1;
          text-align: right;
      }

      #totals-row1 {
          flex-grow: 1;
          margin-right: 10px;
          text-align: right;
      }

      #totals-row2 {
          min-width: 2%;
          text-align: right;
          padding: 5px 10px;
          border: 1px solid #CEC9C980;
          border-radius: 10px;
          background-color: #D9D9D94D;
          box-sizing: border-box;
      }
      /* Utility classes */

      .text-right {
          text-align: right;
      }

      .text-center {
          text-align: center;
      }
      /* Icon button */

      .icon-button {
          background: none;
          border: none;
          cursor: pointer;
          font-size: 24px;
          color: #4a4ae2;
      }
      /* Customer Popup */

      .customer-popup .popup-container {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: flex-start;
          background: rgba(0, 0, 0, 0.5);
          z-index: 1000;
          overflow-y: auto;
          padding-top: 20px;
      }

      .customer-popup .popup {
          background: white;
          width: 100%;
          max-height: 100%;
          border-radius: 10px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          padding: 40px;
          position: relative;
          overflow-y: auto;
      }

    .class-name {
          display: flex;
          gap: 20px;
      }

      .img-preview {
          margin-left: 20px;
          margin-bottom: 10px;
      }

      .customer-popup h3 {
          text-align: left;
          color: #000000;
          font-family: Segoe UI, sans-serif;
          font-size: 20px;
          font-weight: 600;
          text-align: left;
          margin-bottom: 20px;
      }

      .customer-popup small {
          font-family: Segoe UI, sans-serif;
          font-size: 12px;
          font-weight: 300;
          text-align: left;
          color: #444343;
      }

      .container select {
          width: 100%;
          padding: 10px;
          border: 2px solid #c7c7c7;
          border-radius: 8px;
          box-sizing: border-box;
          font-size: 1em;
          font-family: Arial, sans-serif;
      }

       .cancel-btn {
          padding: 10px 70px;
          border-radius: 25px;
          cursor: pointer;
          font-family: Arial, sans-serif;
          font-size: 15px;
          font-weight: 700;
          text-align: center;
          background: transparent;
          color: #4262FF;
          border: 2px solid #4262FF;
          margin-top: 15px;
          margin-left: 250px;
      }

      .cancel-btn:hover {
          background: #4262FF;
          color: white;
      }

      input::file-selector-button {
          font-weight: bold;
          background: #4262FF;
          color: white;
          border: none;
          border-radius: 3px;
      }

      #checkBox {
          display: inline-block;
          vertical-align: middle;
          margin-left: 20px;
          /* Reduce the gap between checkbox and label */
      }

      #markas-1 {
          display: inline-block;
          vertical-align: middle;
          margin-left: 0px;
          /* Reset any left margin/padding */
      }
      /* Item Popup*/

      .popup-container {
          position: fixed;
          /* padding-top: 10%; */
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          background: rgba(0, 0, 0, 0.5);
          z-index: 1000;
      }

      .popup {
          background: white;
          width: 100%;
          max-height: 50%;
          border-radius: 10px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          padding: 30px;
          position: relative;
          overflow-y: auto;
      }

      .popup::-webkit-scrollbar {
          width: 0px;
          background: transparent;
      }

      .popup {
          -ms-overflow-style: none;
          scrollbar-width: none;
      }

      .popup-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
      }

      .container h2 {
          margin-bottom: 5px;
          font-family: Segoe UI, sans-serif;
          font-size: 20px;
          font-weight: 600;
          font-weight: bold;
          text-align: left;
          color: #535353;
      }

      .close-btn {
          background: none;
          border: none;
          font-size: 24px;
          cursor: pointer;
      }

      .form-group {
          margin-bottom: 10px;
          text-align: left;
          font-family: Segoe UI, sans-serif;
          font-size: 15px;
          font-weight: 600;
          line-height: 25.41px;
          color: #444343;
      }

      .form-group label {
          display: block;
          margin-bottom: 5px;
          font-weight: normal;
      }

      .form-group input {
          width: 100%;
          padding: 8px 10px;
          border: 1px solid #E6E6E6;
          border-radius: 8px;
          font-size: 14px;
      }

      .popup-footer {
          display: flex;
          justify-content: flex-end;
          justify-content: space-between;
          margin-top: 20px;
      }

      .cancel-btn {
          padding: 10px 70px;
          border-radius: 25px;
          cursor: pointer;
          font-family: Segoe UI, sans-serif;
          font-size: 15px;
          font-weight: 700;
          text-align: center;
          background: #ffffff;
          color: #6822FF;
          border: 2px solid #4262FF;
          margin-top: 15px;
          margin-left: 62%;
      }

      .cancel-btn:hover {
          background: #4262FF;
          color: white;
      }

      .add-btn {
          padding: 10px 80px;
          border: none;
          border-radius: 25px;
          cursor: pointer;
          font-family: Segoe UI, sans-serif;
          font-size: 15px;
          font-weight: 700;
          text-align: center;
          background: linear-gradient(to right, #4262FF, #512CA2);
          color: white;
          margin-top: 15px;
      }

      .add-btn:hover {
          background: linear-gradient(to right, #512CA2, #4262FF);
      }

      .form-check {
          display: inline-block;
          margin-right: 10px;
      }
