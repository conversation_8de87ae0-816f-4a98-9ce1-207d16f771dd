<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
    <!-- Heading And Button -->
    <div class="actions">
        <h1>GL Entry for Creditor</h1>
    </div>

    <div class="Card">
    <div class="actions-2">
        <h4><b>Account Code:</b> {{accountCode}}</h4>
        <h4><b>Business Partner:</b> {{businessPartnerDetails?.bpFirstName + " " + businessPartnerDetails?.bpName}}</h4>
    </div>

    <div class="row1">
        <!-- Input for number, reference -->
        <div class="row1_col1">
          <label for="search-input">GL Account Id</label>
          <div class="input-container">
            <input type="text" class="search-input" id="search-input" [(ngModel)]="searchTerm" />
            <i class="bi bi-search"></i>
          </div>
        </div>

        <div class="row1_col3">
          <label for="StartDate">Start Date</label>
          <input type="date" class="date-picker" id="StartDate" [(ngModel)]="startDate" />
        </div>
  
        <div class="row1_col4">
          <label for="EndDate">End Date</label>
          <input type="date" class="date-picker" id="EndDate" [(ngModel)]="endDate" />
        </div>
      </div>

      <div class="row2">
        <div class="row2_col3">
          <button type="button" class="secondary-button" (click)="resetFilters()">
            Reset
          </button>
        </div>
        <div class="row2_col1">
          <button type="button" class="primary-button" (click)="filterQuotes()">
            Search
          </button>
        </div>
      </div>
      </div>

    <div class="table-responsive">
        <table>
            <thead>
                <tr class="table-head">
                    <th scope="col" class="valuehead" style="text-align: left">Journal ID</th>
                    <th scope="col" class="valuehead" style="text-align: left">Document Type</th>
                    <th scope="col" class="valuehead" style="text-align: left">Document Number</th>
                    <th scope="col" class="valuehead" style="text-align: left">Posting Date</th>
                    <th scope="col" class="valuehead" style="text-align: left">Description</th>
                    <th scope="col" class="valuehead" style="text-align: right">Dr</th>
                    <th scope="col" class="valuehead" style="text-align: right">Cr</th>
                    <!--<th scope="col" class="valuehead" style="text-align: left">Action</th>-->
                </tr>
            </thead>            
            <tbody *ngFor="let detail of filteredRecords; let i = index">
              <tr *ngFor="let transaction of detail.details">
                  <td class="value" style="text-align: left">{{ transaction.glTransactionId?.glTransactionId }}</td>
                  <td class="value" style="text-align: left">{{ transaction.glTransactionId?.documentType }}</td>
                  <td class="value" style="text-align: left">{{ transaction.glTransactionId?.documentNumber }}</td>
                  <td class="value" style="text-align: left">{{ transaction.glTransactionId?.date | date: "dd-MM-yyyy" }}</td>
                  <td class="value" style="text-align: left">{{ transaction.glTransactionId?.description }}</td>
                  <td class="value" style="text-align: right">
                      {{ transaction.drAmount | currency }}
                  </td>
                  <td class="value" style="text-align: right">
                      {{ transaction.crAmount | currency }}
                  </td>
                  <!--<td class="value" style="text-align: left">
                      <button class="btn btn-orange btn-sm"
                          style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
                          title="View">
                          <i class="bi bi-eye" style="color: #DEBE15;"></i>
                      </button>
                  </td>-->
              </tr>
          </tbody>          
        </table>
    </div>
</div>