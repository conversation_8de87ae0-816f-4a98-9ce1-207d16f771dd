import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { BillService } from '../bill.service';
import { DomSanitizer } from '@angular/platform-browser';
import { ApInvoiceHead } from '../bill';
import Swal from 'sweetalert2';
import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { HttpErrorResponse } from '@angular/common/http';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-payable-bill-report',
  templateUrl: './payable-bill-list-report.component.html',
  styleUrls: ['./payable-bill-list-report.component.css']
})
export class PayableBillListReportComponent implements OnInit {
   @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
   @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
   @ViewChild('BillreportPreviewFrame') BillreportPreviewFrame!: ElementRef;

  fromDate: string = '';
  toDate: string = '';
  status: string = '%';  
  suppliers: BusinessPartner[] = [];
  billData: ApInvoiceHead = new ApInvoiceHead();
  getAllSuppliers = false;  
  isLoading: boolean = false;
  private audio!: HTMLAudioElement;

  constructor(
    private billService: BillService,
    public sanitizer: DomSanitizer,
    private businessPartnerService: BusinessPartnerService,
    private swalAlerts: SwalAlertsService
  ) {}

  ngOnInit() {
    this.billData.businessPartnerId = '0';
    this.loadSuppliers();
  }

  previewBills(fromDate: string, toDate: string, status: string, businessPartnerId: any) {
  if (!fromDate || !toDate) {

    this.swalAlerts.showSwalWarning('Warning!', 'Please select Date Range', 'Missing date range for Payable Bill report.');
    return;
  }
  this.isLoading = true;
  const entityId = +localStorage.getItem('entityId')!;
  const entityUUID = localStorage.getItem('entityUuid')!;
  const bpId = (businessPartnerId === 0 || businessPartnerId === '' || businessPartnerId == null) ? null : +businessPartnerId;

  const requestData = {
    fromDate,
    toDate,
    status,
    entityId,
    businessPartnerId: bpId,
    entityUUID
  };

  this.billService.getBillListReport(requestData).subscribe(
    data => {
      const base64String = data.response;
      if (base64String) {
        this.loadPdfIntoIframe(base64String);
      } else {
        this.isLoading = false;
        alert('No Payable Bill data for preview.');
      }
    },
    error => {
      this.isLoading = false;
      alert('Error loading Payable Bill preview.');
    }
  );
}
 
  private loadPdfIntoIframe(base64String: string) {
    if (base64String && base64String.trim().length >= 50) {
      const pdfData = 'data:application/pdf;base64,' + base64String;
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
      const iframe = this.BillreportPreviewFrame.nativeElement;
  
      iframe.onload = () => {
        this.isLoading = false;
      };
  
      iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
  
      // Open modal manually using Bootstrap JS
      const modalElement = document.getElementById('simpleModal');
      const modal = new bootstrap.Modal(modalElement!);
      modal.show();
    } else {
       this.isLoading = false;
       this.swalAlerts.showSwalWarning('No Data', 'No Payable Bill data for preview.', 'No Payable Bill data was returned for the selected range.');
  
    }
  }
  


  loadSuppliers() {
  const entityId = +((localStorage.getItem('entityId')) + '');
  this.businessPartnerService.getSupplierListByEntity(entityId).subscribe(
    (customers: BusinessPartner[]) => {
        this.suppliers = customers.filter(
            (supplier) =>
              supplier.businessPartnerTypeId?.businessPartnerType === 'Supplier'
          );
    },
    (error: HttpErrorResponse) => {
      console.error('Error fetching Suppliers', error);

      //  Use SwalAlertsService for error with Chimp support
      this.swalAlerts.showErrorWithChimpSupport(
        'Failed to load Suppliers.',
        'Unable to fetch Suppliers list for this entity. Please check if the Suppliers service is responding.'
      );
    }
  );
}


  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }



  onSupplierChange(event: any) {
    const selectedSupplierId = event.target.value;
    const selectedSupplier = this.suppliers.find(cust => cust.businessPartnerId === +selectedSupplierId);

    // Set the reference field
    this.billData.supplierName = selectedSupplier?.bpName || '';
  }


    // Method to toggle all Suppliers' data
    toggleAllSuppliers() {
      this.getAllSuppliers = !this.getAllSuppliers;
      if (this.getAllSuppliers) {
        this.billData.businessPartnerId = '';  // Clear selected customer when showing all
      }
    }

}
