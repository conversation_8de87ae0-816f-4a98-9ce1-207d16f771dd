<app-payroll-nevigation></app-payroll-nevigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <div class="label sub-container">
    <h2>PayRun Pay Template</h2>
  </div>
  <div class="sub-container">
    <div class="payroll-head">
      <div class="payroll-head-icon">
        <div class="circle">
          {{ personal[0].firstName[0] | uppercase
          }}{{ personal[0].lastName[0] | uppercase }}
        </div>
      </div>
      <div class="payroll-head-content">
        <h2>{{ personal[0].firstName }} {{ personal[0].lastName }}</h2>
        <div class="mt-0">
          {{ personal[0].email }}
        </div>
      </div>
    </div>
  </div>

  <div>
    <!-- Ordinary Earning -->

    <div class="Earnings">
      <div class="earnings-container mt-1">
        <div class="pay_item_head">
          <h2>Ordinary Earning</h2>
        </div>
        <table class="table no-borders">
          <thead>
            <tr>
              <!-- <th></th> -->
              <th class="text-center">Hours</th>
              <th class="text-center">Rate</th>
              <th class="text-center">Amount</th>
              <th class="actions">Actions</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let earning of payRunDetails; let i = index">
              <tr *ngIf="earning.type == 'EARNING' && earning.code == 0">
                <!-- <td>{{ earning.codeType }}</td> -->
                <td class="amount" *ngIf="isEditing !== earning">
                  {{ earning.hours || "0.00" }}
                </td>
                <td *ngIf="isEditing === earning">
                  <input
                    type="number"
                    [(ngModel)]="earning.hours"
                    (ngModelChange)="updateAmount(earning)"
                    class="form-control"
                  />
                </td>
                <td class="amount" *ngIf="isEditing !== earning">
                  {{ earning.rate || "0.00" | number : "1.4-4" }}
                </td>
                <td *ngIf="isEditing === earning">
                  <input
                    type="number"
                    [(ngModel)]="earning.rate"
                    (ngModelChange)="updateAmount(earning)"
                    class="form-control"
                  />
                </td>
                <td class="amount" *ngIf="isEditing !== earning">
                  {{ earning.amount || "0.00" | number : "1.4-4" }}
                </td>
                <td *ngIf="isEditing === earning">
                  <input
                    type="number"
                    disabled
                    [(ngModel)]="earning.amount"
                    class="form-control"
                  />
                </td>
                <td class="actions">
                  <button
                    *ngIf="isEditing !== earning"
                    class="btn btn-orange btn-sm"
                    style="
                      margin-left: 10px;
                      border: none;
                      background: none;
                      padding-left: 10px;
                      font-size: 1.2rem;
                    "
                    (click)="editEarning(earning)"
                    title="Edit"
                  >
                    <i
                      class="ri-edit-box-line"
                      style="
                        color: #4262ff;
                        text-shadow: 2px 2px 4px rgba(0, 123, 255, 0.6);
                      "
                    ></i>
                  </button>
                  <button
                    class="btn btn-success btn-sm"
                    style="
                      margin-left: 10px;
                      border: none;
                      background: none;
                      padding-left: 10px;
                      font-size: 1.2rem;
                    "
                    *ngIf="isEditing === earning"
                    (click)="saveEarning(earning)"
                    title="Save"
                  >
                    <i
                      class="ri-save-line"
                      style="
                        color: #28a745;
                        text-shadow: 2px 2px 4px rgba(40, 167, 69, 0.6);
                      "
                    ></i>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>

        <!-- Earning -->

        <div class="pay_item_head">
          <h2>Earnings</h2>
        </div>
        <table class="table no-borders">
          <thead>
            <tr>
              <th>Earning Type</th>
              <th>Hours</th>
              <th>Rate</th>
              <th>Amount</th>
              <th class="actions">Actions</th>
              <!-- <th>Total</th> -->
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let earning of payRunDetails; let i = index">
              <tr
                *ngIf="
                  (earning.type == 'EARNING' && earning.code != 0) ||
                  earning.type == 'LEAVE' || earning.type == 'LEAVE LOADING'
                "
              >
                <td>{{ earning.codeType }}</td>
                <td *ngIf="isEditing !== earning">
                  {{ earning.hours === 0.0
                    ? ""
                    :earning.hours | number : "1.4-4" }}
                </td>
                <td *ngIf="isEditing === earning && earning.hours !== 0">
                  <input
                    type="number"
                    [(ngModel)]="earning.hours"
                    (ngModelChange)="updateAmount(earning)"
                    class="form-control"
                  />
                </td>
                <td *ngIf="isEditing === earning && earning.hours === 0.0"></td>
                <td *ngIf="isEditing !== earning">
                  {{
                    earning.rate === 0.0
                      ? ""
                      : (earning.rate | number : "1.4-4")
                  }}
                </td>
                <td *ngIf="isEditing === earning && earning.rate !== 0.0">
                  <input
                    type="number"
                    [(ngModel)]="earning.rate"
                    (ngModelChange)="updateAmount(earning)"
                    class="form-control"
                  />
                </td>
                <td *ngIf="isEditing === earning && earning.rate === 0.0"></td>
                <td *ngIf="isEditing !== earning">
                  {{ earning.amount || "0.00" | number : "1.4-4" }}
                </td>
                <td *ngIf="isEditing === earning">
                  <input
                    type="number"
                    [disabled]="earning.rate !== 0.0 && earning.hours !== 0.0"
                    [(ngModel)]="earning.amount"
                    (ngModelChange)="updateAmount(earning)"
                    class="form-control"
                  />
                </td>
                <td class="actions">
                  <button
                    *ngIf="isEditing !== earning"
                    class="btn btn-danger btn-sm"
                    (click)="deletePayRun(earning.payRunDetailId)"
                    style="border: none; background: none; font-size: 1.2rem"
                    title="Delete"
                  >
                    <i
                      class="ri-delete-bin-line"
                      style="
                        color: #ff0000;
                        text-shadow: 2px 2px 4px rgba(205, 117, 70, 0.6);
                      "
                    ></i>
                  </button>
                  <button
                    *ngIf="isEditing !== earning"
                    class="btn btn-orange btn-sm"
                    style="border: none; background: none; font-size: 1.2rem"
                    (click)="editEarning(earning)"
                    title="Edit"
                  >
                    <i
                      class="ri-edit-box-line"
                      style="
                        color: #4262ff;
                        text-shadow: 2px 2px 4px rgba(0, 123, 255, 0.6);
                      "
                    ></i>
                  </button>
                  <button
                    class="btn btn-success btn-sm"
                    style="border: none; background: none; font-size: 1.2rem"
                    *ngIf="isEditing === earning"
                    (click)="saveEarning(earning)"
                    title="Save"
                  >
                    <i
                      class="ri-save-line"
                      style="
                        color: #28a745;
                        text-shadow: 2px 2px 4px rgba(40, 167, 69, 0.6);
                      "
                    ></i>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>

        <div class="earning-total">
          <button
            class="btn-add-earning"
            (click)="openEarningModal()"
            data-bs-toggle="modal"
            data-bs-target="#modal-two"
          >
            + Add Earnings
          </button>
          <div class="value">Total</div>
          <span>{{ totalEarnings | currency:'USD':'symbol':'1.2-4' }}</span>

          <span></span>
        </div>
      </div>

      <!-- Modal for Adding Earnings -->
      <div
        class="modal fade"
        id="modal-two"
        tabindex="-1"
        aria-labelledby="earningModalLabel"
        aria-hidden="true"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
        (hidden.bs.modal)="closeEarningModal()"
      >
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="earningModalLabel">Add Earning</h5>
              <button
                type="button"
                class="custom-close-btn"
                data-bs-dismiss="modal"
                aria-label="Close"
                (click)="closeEarningModal()"
              >
                <i class="bi bi-x-circle"></i>
              </button>
            </div>
            <div class="modal-body">
              <!-- Earning type -->
              <div class="mb-3">
                <label for="earningType" class="form-label">Earning Type</label>
                <select
                  id="earningType"
                  class="form-select"
                  [(ngModel)]="selectedEarningsName"
                  (ngModelChange)="onEarningTypeChange($event)"
                >
                  <option value="" disabled selected>
                    Select a Earning Type
                  </option>
                  <option *ngFor="let item of earningDropdown" [ngValue]="item">
                    {{ item.type === "earning" ? "Earning: " : "Leave: "
                    }}{{ item.name }}
                  </option>
                </select>
              </div>

              <div class="mb-3" *ngIf="newEarning.typeOfUnits === 'Rate'">
                <label for="earningRate" class="form-label">Rate</label>
                <input
                  type="number"
                  id="earningRate"
                  class="form-control"
                  [(ngModel)]="newEarning.rate"
                  placeholder="0.00"
                  readonly
                />
              </div>
              <div class="mb-3" *ngIf="newEarning.typeOfUnits === 'Rate'">
                <label for="earningHours" class="form-label">Hours</label>
                <input
                  type="number"
                  id="earningHours"
                  class="form-control"
                  [(ngModel)]="newEarning.hours"
                  (ngModelChange)="calculateAmount()"
                  placeholder="0.00"
                />
              </div>

              <div class="mb-3">
                <label for="earningAmount" class="form-label">Amount</label>
                <input
                  type="number"
                  id="earningAmount"
                  class="form-control"
                  [(ngModel)]="newEarning.amount"
                  [readonly]="
                    newEarning.typeOfUnits === 'Rate' ||
                    newEarning.typeOfUnits === 'Fixed Amount'
                  "
                  placeholder="0.00"
                />
              </div>
            </div>
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-primary"
                (click)="addPayRunDetail('EARNING')"
                data-bs-dismiss="modal"
                [disabled]="
                  !selectedEarningsName ||
                  !newEarning.amount ||
                  newEarning.amount <= 0
                "
              >
                Add
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Leave Accrual Container -->
      <div class="earnings-container mt-3">
        <div class="pay_item_head">
          <h2>Leave Accrual</h2>
        </div>
        <table class="table no-borders">
          <thead>
            <tr>
              <!-- <th>Superannuation Fund</th> -->
              <!-- <th>Rate</th> -->
              <!-- <th>Amount</th> -->
              <!-- <th>Total</th> -->
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let leave of leaves; let i = index">
              <tr>
                <!-- <td>{{ superannuation.codeType }}</td> -->
                <td>{{ leave.leaveType.leaveType }}</td>
                <td>{{ leave.accrualCurrentBalance| number : "1.4-4" }}</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
        <div class="earning-total">
          <div class="value">Total leave accrual hours</div>
          <span>{{ payRunDetailsSummary.accruedHours | number : "1.4-4"  }}</span>

          <span></span>
        </div>
      </div>

      <!-- Deductions Container -->

      <div class="earnings-container mt-3">
        <div class="pay_item_head">
          <h2>Deductions</h2>
        </div>
        <table class="table no-borders">
          <thead>
            <tr>
              <th>Deduction Type</th>
              <th></th>
              <th>Amount</th>
              <th class="actions">Actions</th>
              <!-- <th>Total</th> -->
            </tr>
          </thead>
          <tbody>
            <ng-container
              *ngFor="let deduction of payRunDetails; let i = index"
            >
              <tr *ngIf="deduction.type == 'DEDUCTION'">
                <td>{{ deduction.codeType }}</td>
                <td>
                  {{ deduction.rate === null || deduction.rate === 0.0 ? "" : deduction.rate + "%" }}
                </td>
                <td>{{ deduction.amount | number : "1.4-4" }}</td>
                <td class="actions">
                  <button
                    class="btn btn-danger btn-sm"
                    (click)="deletePayRun(deduction.payRunDetailId)"
                    style="
                      margin-left: 10px;
                      border: none;
                      background: none;
                      padding-left: 10px;
                      font-size: 1.2rem;
                    "
                    title="Delete"
                  >
                    <i
                      class="ri-delete-bin-line"
                      style="
                        color: #ff0000;
                        text-shadow: 2px 2px 4px rgba(205, 117, 70, 0.6);
                      "
                    ></i>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
        <div class="earning-total">
          <button
            class="btn-add-earning"
            data-bs-toggle="modal"
            data-bs-target="#modal-deduction"
          >
            + Add Deduction
          </button>
          <div class="value">Total</div>
          <span>{{ totalDeductions | number : "1.4-4" | currency }}</span>
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>

      <!-- Modal for Adding Deduction -->
      <div
        class="modal fade"
        id="modal-deduction"
        tabindex="-1"
        aria-labelledby="deductionModalLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="deductionModalLabel">
                Add Deduction
              </h5>
              <button
                type="button"
                class="custom-close-btn"
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                <i class="bi bi-x-circle"></i>
              </button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label for="deductionType" class="form-label"
                  >Deduction Type</label
                >
                <select
                  id="deductionType"
                  class="form-select"
                  [(ngModel)]="selectedDeduction"
                >
                  <option value="" disabled selected>
                    Select a Deduction type
                  </option>
                  <option
                    *ngFor="let deduction of deductions"
                    [ngValue]="deduction"
                  >
                    {{ deduction.deductionName }}
                  </option>
                </select>
              </div>
              <div class="mb-3">
                <label for="deductionAmount" class="form-label">Amount</label>
                <input
                  type="number"
                  id="deductionAmount"
                  class="form-control"
                  [(ngModel)]="newPayRunDetail.amount"
                  placeholder="0.00"
                />
              </div>
            </div>
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-primary"
                (click)="addPayRunDetail('DEDUCTION')"
                data-bs-dismiss="modal"
                [disabled]="!selectedDeduction || !newPayRunDetail.amount"
              >
                Add
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Superannuation Container -->
      <div class="earnings-container mt-3">
        <div class="pay_item_head">
          <h2>Superannuation</h2>
        </div>
        <table class="table no-borders">
          <thead>
            <tr>
              <th>Superannuation Fund</th>
              <th>Rate</th>
              <th>Amount</th>
              <!-- <th>Total</th> -->
            </tr>
          </thead>
          <tbody>
            <ng-container
              *ngFor="let superannuation of payRunDetails; let i = index"
            >
              <tr *ngIf="superannuation.type == 'SUPERANNUATION'">
                <td>{{ superannuation.codeType }}</td>
                <td>
                  {{
                    superannuation.rate === null
                      ? ""
                      : superannuation.rate + "%"
                  }}
                </td>
                <td>{{ superannuation.amount | number : "1.4-4" }}</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>

      <!-- Tax Container -->
      <div class="earnings-container mt-3">
        <div class="pay_item_head">
          <h2>PAYG Tax</h2>
        </div>
        <table class="table no-borders">
          <thead>
            <tr>
              <!-- <th>Superannuation Fund</th> -->
              <!-- <th>Rate</th> -->
              <!-- <th>Amount</th> -->
              <!-- <th>Total</th> -->
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let tax of payRunDetails; let i = index">
              <tr *ngIf="tax.type == 'PAYG'">
                <!-- <td>{{ superannuation.codeType }}</td> -->
                <td>PAYG Tax amount</td>
                <td>{{ tax.amount | number : "1.4-4" }}</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>

      <!-- Reimbursement Container -->

      <div class="earnings-container mt-3">
        <div class="pay_item_head">
          <h2>Reimbursements</h2>
        </div>
        <table class="table no-borders">
          <thead>
            <tr>
              <th>Reimbursement Type</th>
              <th>Amount</th>
              <th class="actions">Actions</th>
              <!-- <th>Total</th> -->
            </tr>
          </thead>
          <tbody>
            <ng-container
              *ngFor="let reimbursement of payRunDetails; let i = index"
            >
              <tr *ngIf="reimbursement.type == 'REIMBURSEMENT'">
                <td>{{ reimbursement.codeType }}</td>
                <td>{{ reimbursement.amount || "0.00" | number : "1.4-4" }}</td>
                <td class="actions">
                  <button
                    class="btn btn-danger btn-sm"
                    (click)="deletePayRun(reimbursement.payRunDetailId)"
                    style="
                      margin-left: 10px;
                      border: none;
                      background: none;
                      padding-left: 10px;
                      font-size: 1.2rem;
                    "
                    title="Delete"
                  >
                    <i
                      class="ri-delete-bin-line"
                      style="
                        color: #ff0000;
                        text-shadow: 2px 2px 4px rgba(205, 117, 70, 0.6);
                      "
                    ></i>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
        <div class="earning-total">
          <button
            class="btn-add-earning"
            data-bs-toggle="modal"
            data-bs-target="#modal-reimbursement"
          >
            + Add Reimbursement
          </button>
          <div class="value">Total</div>
          <span>{{ totalReimbursements | number : "1.4-4" | currency }}</span>
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>

      <!-- Modal for Adding Reimbursement -->
      <div
        class="modal fade"
        id="modal-reimbursement"
        tabindex="-1"
        aria-labelledby="reimbursementModalLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="reimbursementModalLabel">
                Add Reimbursement
              </h5>
              <button
                type="button"
                class="custom-close-btn"
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                <i class="bi bi-x-circle"></i>
              </button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label for="reimbursementType" class="form-label"
                  >Reimbursement Type</label
                >
                <select
                  id="reimbursementType"
                  class="form-select"
                  [(ngModel)]="selectedReimbursement"
                >
                  <option value="" disabled selected>
                    Select a Reimbursement Type
                  </option>
                  <option
                    *ngFor="let reimbursement of reimbursements"
                    [ngValue]="reimbursement"
                  >
                    {{ reimbursement.reimbursementType }}
                  </option>
                </select>
              </div>
              <div class="mb-3">
                <label for="reimbursementAmount" class="form-label"
                  >Amount</label
                >
                <input
                  type="number"
                  id="reimbursementAmount"
                  class="form-control"
                  [(ngModel)]="newPayRunDetail.amount"
                  placeholder="0.00"
                />
              </div>
            </div>
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-primary"
                (click)="addPayRunDetail('REIMBURSEMENT')"
                data-bs-dismiss="modal"
                [disabled]="!selectedReimbursement || !newPayRunDetail.amount"
              >
                Add
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
