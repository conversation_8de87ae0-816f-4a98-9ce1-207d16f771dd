<app-header></app-header>
<app-navbar></app-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<body>
    <div class="body">
        <div class="container">
            <div class="popup">
                <div class="popup-header">
                    <h3>Update Business Partner</h3>
                </div>
                <form #cuspop="ngForm" (ngSubmit)="cuspop.form.valid && onSubmitCustomerForm()" class="row g-1"
                    novalidate="feedback-form" (keydown)="preventSubmit($event)">
                    <div class="class-name full-width">
                        <div class="form-group">
                            <label for="partner-type">Business Partner Type</label>
                            <select class="input-style full-width" id="partner-type"
                                [(ngModel)]="businessPartner.businessPartnerTypeId.businessPartnerTypeId" name="businessPartnerType"
                                required readonly>
                                <option value="" selected disabled>Select Partner Type</option>
                                <option *ngFor="let type of businessPartnerType" [value]="type.businessPartnerTypeId">
                                    {{ type.businessPartnerType }}
                                </option>
                            </select>
                            <div *ngIf="cuspop.submitted && cuspop.controls['businessPartnerType'].invalid" class="text-danger">
                                <div *ngIf="cuspop.controls['businessPartnerType'].errors?.['required']">
                                    Business Partner Type is required.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="class-name">
                        <div class="form-group">
                            <label for="bp-name">Business Partner Name</label>
                            <input type="text" id="bp-name" [(ngModel)]="businessPartner.bpName" name="bpName"
                                required />

                            <div *ngIf="cuspop.submitted && cuspop.controls['bpName'].invalid" class="text-danger">
                                <div *ngIf="cuspop.controls['bpName'].errors?.['required']">Business Partner Name is
                                    required.</div>
                            </div>
                        </div>
                    </div>

                    <div class="class-name">
                        <div class="form-group">
                            <label for="email">Email </label>
                            <input type="email" id="email" [(ngModel)]="businessPartner.email" name="email"
                                pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"  />
                            <div *ngIf="cuspop.submitted && cuspop.controls['email'].invalid" class="text-danger">
                                <div *ngIf="cuspop.controls['email'].errors?.['pattern']">Invalid email format.</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="contactPhoneNumber">Contact Number</label>
                            <input type="text" id="contactPhoneNumber" [(ngModel)]="businessPartner.contactPhoneNumber"
                                name="contactPhoneNumber"   />
                            <div *ngIf="cuspop.submitted && cuspop.controls['contactPhoneNumber'].invalid"
                                class="text-danger">
                               </div>
                        </div>
                    </div>
                    <div class="class-name">
                        <div class="form-group">
                            <label for="abn-number">ABN Number <small>(Optional)</small></label>
                            <input type="text" id="abn-number" [(ngModel)]="businessPartner.abnNumber"
                                name="abnNumber" />
                        </div>
                    </div>
                    <div class="class-name">
                        <div class="form-group">
                            <label for="acn-number">ACN Number</label>
                            <input type="text" id="acn-number" [(ngModel)]="businessPartner.acnNumber"
                                name="acnNumber" />
                        </div>
                        <div class="form-group">
                            <label for="default-payment-terms">Default Payment Terms <small>(Optional)</small></label>
                            <select id="default-payment-terms" [(ngModel)]="businessPartner.defaultPaymentTerms"
                                name="defaultPaymentTerms">
                                <option value="" disabled selected>Select default payment terms</option>
                                <option value="net30">Net 30</option>
                                <option value="2/10Net30">2/10 Net 30</option>
                                <option value="dueOnReceipt">Due on Receipt</option>
                            </select>
                        </div>
                    </div>
                    <div class="class-name">
                        <div class="form-group">
                            <label for="business-address">Business Address</label>
                            <input type="text" id="business-address" [(ngModel)]="businessPartner.businessAddress"
                                name="businessAddress" />
                        </div>
                        <div class="form-group">
                            <label for="delivery-address">Delivery Address</label>
                            <input type="text" id="delivery-address" [(ngModel)]="businessPartner.deliveryAddress"
                                name="deliveryAddress" />
                        </div>
                    </div>
                    <div class="class-name">

                    </div>
                    <hr>
                    <h2>Additional</h2>
                    <div class="class-name">
                        <div class="form-group">
                            <label for="bank-account-name">Bank Account Name</label>
                            <input type="text" id="bank-account-name" [(ngModel)]="businessPartner.bankAccountName"
                                name="bankAccountName" />
                        </div>
                        <div class="form-group">
                            <label for="bsb">BSB</label>
                            <input type="text" id="bsb" [(ngModel)]="businessPartner.bsb" name="bsb" />
                        </div>
                        <div class="form-group">
                            <label for="bank-account-number">Bank Account Number</label>
                            <input type="text" id="bank-account-number" [(ngModel)]="businessPartner.accountNumber"
                                name="accountNumber" />
                        </div>
                    </div>
                    <div class="popup-footer">
                        <button type="button" class="cancel-btn" (click)="navigateToBusinessPartnerCancel()" >Cancel</button>
                        <button type="submit" class="add-btn">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>