<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
    <div class="actions">
        <h1>Accept Entity</h1>
    </div>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr class="table-head">
                        <th scope="col" class="valueCheckbox" style="width: 10%;"><input type="checkbox" /></th>
                        <th scope="col" class="valuehead" style="width: 30%;">User ID</th>
                        <th scope="col" class="valuehead" style="width: 40%;">User Name</th>
                        <th style="width: 20%;"></th>
                    </tr>
                </thead>
                <!-- Table body -->
                <tbody>
                    <tr>
                        <td class="valueCheckbox"><input type="checkbox"/></td>
                        <td class="value"></td>
                        <td class="value"></td>
                        <td style="text-align: center;" class="value">
                            <button class="btn btn-danger btn-sm" style="margin-right: 5px; border: none; background-color: #008000; padding: 2px 10px; font-size: 1rem; border-radius: 20px;" data-bs-toggle="tooltip" title="accept">
                                <i class="bi bi-check-circle-fill"></i> Accept
                            </button>
                            <button class="btn btn-danger btn-sm" style="margin-left: 5px; border: none; background-color: #ff0000; padding: 2px 10px; font-size: 1rem; border-radius: 20px;" data-bs-toggle="tooltip" title="reject">
                                <i class="bi bi-x-circle-fill"></i> Reject
                            </button>
                        </td>

                    </tr>
                </tbody>
            </table>
        </div>
</div>