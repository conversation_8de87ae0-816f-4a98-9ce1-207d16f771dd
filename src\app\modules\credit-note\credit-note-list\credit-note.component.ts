import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CreditNote } from '../credit-note';
import { CreditNoteService } from '../credit-note.service';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { DomSanitizer } from '@angular/platform-browser';
import { fromEvent, Subscription } from 'rxjs';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { PeriodClosingService } from '../../finance-module/period-closing/period-closing.service';
import { DateAdapter } from '@angular/material/core';


@Component({
  selector: 'app-credit-note',
  templateUrl: './credit-note.component.html',
  styleUrls: ['./credit-note.component.css'],
})
export class CreditNoteComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
 
  [x: string]: any;
  creditNotes: CreditNote[] = []; // Assuming CreditNote is your model for quotations
  searchTerm: string = ''; // Store the search term
  filteredCreditNotes: CreditNote[] = [];
  selectedCreditNotes: Set<CreditNote> = new Set<CreditNote>();
  creditNoteHeadList: CreditNote[] = [];
  isAllSelected: boolean = false;
  creditNoteData: CreditNote = new CreditNote();
  activeTab = 'all';

  startDate: string | null = null; // Bind to the start date input
  endDate: string | null = null;   // Bind to the end date input


  constructor(
    private creditNoteService: CreditNoteService, 
    private businessPartnerService: BusinessPartnerService,   
    private periodClosingService: PeriodClosingService, 
    private router: Router, 
    public sanitizer: DomSanitizer,
    private dateAdapter: DateAdapter<Date>,
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }

  ngOnInit() {
    this.fetchCreditNotes();
  }


  private getCreditNoteHeadList() {

    this.creditNoteService.getCreditNoteList().subscribe(data => {
      this.creditNoteHeadList = data;
      this.filterCreditNotes(); // Filter credit notes initially
    });
  }

  toggleSelection(creditNote: CreditNote, event: any): void {
    if (event.target.checked) {
      this.selectedCreditNotes.add(creditNote);
    } else {
      this.selectedCreditNotes.delete(creditNote);
    }
  }



  fetchCreditNotes() {

    const entityId = +((localStorage.getItem('entityId')) + "");

    this.creditNoteService.getAllSalesCreditNotesHeadList(entityId).subscribe(
      (data: CreditNote[]) => {
        this.creditNotes = data;
        this.filteredCreditNotes = this.creditNotes;

          // 🔐 Check lock for each creditNotes based on posting date
      this.filteredCreditNotes.forEach((creditNotes) => {
        const date = creditNotes.documentDate;
        this.periodClosingService.isDateLocked(entityId, date).subscribe({
          next: (locked: boolean) => {
            (creditNotes as any).isLocked = locked; // add isLocked dynamically if model doesn't have it
          },
          error: (err) => {
            console.error('Error checking period lock for payment:', err);
            (creditNotes as any).isLocked = false;
          }
        });
      });

      },
      (error) => {

      }
    );
  }

  onSearchChange() {
    this.filterCreditNotes(); // Call filter function on input change
  }

  filterCreditNotes(): void {
    if (!this.searchTerm && !this.startDate && !this.endDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Search Criteria',
        text: 'Please provide at least one search criterion: Credit Note Number or Customer, Start Date, or End Date.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    let filtered = this.creditNotes;
  
  
    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(creditNote => 
        creditNote.creditNoteNumber?.toString().toLowerCase().includes(searchTermLower) ||
        creditNote.customerName?.toLowerCase().includes(searchTermLower)
      );
    }
  
    // Filter by date range
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(creditNote => 
        new Date(creditNote.documentDate) >= startDate
      );
    }
  
    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(creditNote => 
        new Date(creditNote.documentDate) <= endDate
      );
    }
  
    this.filteredCreditNotes = filtered;
  }
  
  resetFilters() {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.filteredCreditNotes = this.creditNotes;
    this.filterCreditNotes(); // Reapply filter to reset the table
  }
  


deleteCreditNote(creditNoteId: number): void {
  Swal.fire({
    title: 'Are you sure?',
    text: 'Do you really want to delete this Credit Note?',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Yes, delete it!',
    cancelButtonText: 'No, keep it',
    confirmButtonColor: '#ff7e5f',
    cancelButtonColor: '#be0032',
  }).then((result) => {
    if (result.isConfirmed) {

      this.creditNoteService.deleteCreditNote(creditNoteId)
        .subscribe(
          () => {

            Swal.fire({
              title: 'Deleted!',
              text: 'Credit Note has been deleted.',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            }).then(() => {
              // Optionally update the UI or reload data
              this.fetchCreditNotes();
            });
          },
          (error) => {
            console.error('Failed to delete Credit Note:', error);

            Swal.fire({
              title: 'Error!',
              text: 'Failed to delete Credit Note.',
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#be0032',
              cancelButtonColor: '#007bff',
              showCancelButton: true, 
            }).then((result) => {
              if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                if (this.chatBotComponent) {
                  Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while Chimp processes your request.',
                    allowOutsideClick: false,
                    didOpen: () => {
                      Swal.showLoading();
                      this.chatBotComponent.setInputData('Failed to delete Credit Note.');
                      this.chatBotComponent.responseReceived.subscribe(response => {
                        Swal.close();
                        this.chatResponseComponent.showPopup = true;
                        this.chatResponseComponent.responseData = response;
                        this.playLoadingSound();
                        this.stopLoadingSound() 
                      });
                    },
                  });
                } else {
                  console.error('ChatBotComponent is not available.');
                }
              }
            });
          }
        );
    }
  });
}


  createCreditNote() {
        this.router.navigate(['/invoice']);
  }
  
  

  selectAll(event: any) {
    const isChecked = event.target.checked;
    this.creditNotes.forEach(creditNote => creditNote.selected = isChecked);
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }
  exportToExcel(): void {
    if (!this.filteredCreditNotes || this.filteredCreditNotes.length === 0) {
      alert('No data available to export.');
      return;
    }

    const exportData = this.filteredCreditNotes.map(creditNote => ({
      'Credit Note Number': creditNote.creditNoteNumber,
      'Invoice Number': creditNote.invoiceNumber,
      'Customer Name': creditNote.customerName,
      'Document Date': new Date(creditNote.documentDate).toISOString().split('T')[0], // Format as YYYY-MM-DD
      'Total Credit Amount': creditNote.totalCreditAmount // Currency formatting handled in Excel
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Credit Notes');

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    this.saveAsExcelFile(excelBuffer, `Credit_Notes_${timestamp}`);
}

private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    });

    saveAs(data, `${fileName}.xlsx`);
}
}
