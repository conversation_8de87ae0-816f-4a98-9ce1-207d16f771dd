import { Component } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Entity } from 'src/app/modules/entity/entity';
import { EntityService } from 'src/app/modules/entity/entity.service';
import * as moment from 'moment';
import { BasReport } from '../../bas';
import { BillService } from '../../../bill/bill.service';

@Component({
  selector: 'app-bas-period',
  templateUrl: './bas-period.component.html',
  styleUrls: ['./bas-period.component.css']
})
export class BasPeriodComponent {

  entity: Entity = new Entity();
  lastBasReport: BasReport | null = null;
  nextBasPeriodText: string = '';
 completedStatements: any[] = [];



   constructor(private entityService: EntityService, private billService: BillService, private router: Router, public sanitizer: DomSanitizer) { }
  
    ngOnInit() {
        this.getBusinessEntityById();
       // this.getLastBasReport();
    }

    getBusinessEntityById() {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.entityService.getBusinessEntityById(entityId).subscribe(
      (response) => {
        this.entity = response;
          this.getLastBasReport();
      },
      (error) => {
        console.error('Error fetching entity data:', error);
      }
    );
  }
  
  getNextBasPeriod(lastPeriodEnd: string, basPeriod: string): { nextStart: string, nextEnd: string } {
  const lastEnd = moment(lastPeriodEnd, 'YYYY-MM-DD');
  const nextStart = lastEnd.clone().add(1, 'day');
  let nextEnd: moment.Moment;

  if (basPeriod === 'monthly') {
    nextEnd = nextStart.clone().add(1, 'month').subtract(1, 'day');
  } else if (basPeriod === 'quarterly') {
    nextEnd = nextStart.clone().add(3, 'months').subtract(1, 'day');
  } else if (basPeriod === 'annually') {
    nextEnd = nextStart.clone().add(1, 'year').subtract(1, 'day');
  } else {
    // Default fallback (e.g., treat unknowns as Monthly)
    nextEnd = nextStart.clone().add(1, 'month').subtract(1, 'day');
  }

  return {
    nextStart: nextStart.format('YYYY-MM-DD'),
    nextEnd: nextEnd.format('YYYY-MM-DD'),
  };
}



getLastBasReport() {
  const entityId = +(localStorage.getItem('entityId') || '');
  this.billService.getBasReportsByEntityId(entityId).subscribe(
    (reports: BasReport[]) => {
      if (reports && reports.length > 0) {
        // Sort descending by periodEnd
        reports.sort((a, b) => new Date(b.periodEnd).getTime() - new Date(a.periodEnd).getTime());

        this.lastBasReport = reports[0]; // Latest one for next period calc

        // ✅ Convert all reports into display-friendly objects
        this.completedStatements = reports.map((report) => {
          const start = moment(report.periodStart);
          const end = moment(report.periodEnd);

          return {
            basReportId: report.basReportId, 
            period: `${start.format('MMM')}–${end.format('MMM YYYY')}`,  // e.g. Jan–Mar 2025
            date: moment(report.basLoadgeDate).format('D MMM YYYY'),       // e.g. 21 May 2025
            user: report.userName || 'Unknown',
            amountText: 'Total Purchases',                                // Title text
            amount: report.totalPurchase,                                 // Amount value
            status: report.status || 'Pending'                            // Status for badge
          };
        });

        // Compute next BAS period display
        if (this.entity?.basPeriod && this.lastBasReport?.periodEnd) {
          const { nextStart, nextEnd } = this.getNextBasPeriod(this.lastBasReport.periodEnd, this.entity.basPeriod);

          const start = moment(nextStart);
          const end = moment(nextEnd);
          this.nextBasPeriodText = `${start.format('D MMM YYYY')} - ${end.format('D MMM YYYY')}`;
        }
      }
    },
    (err) => console.error('Error loading BAS reports:', err)
  );
}





    handleCreateBAS() {
    this.router.navigate(['/bas-loadge']);
  }

   reviewBas(id: number) {
    this.billService.getBasReportsByEntityId(id).subscribe(bill => {
     
        this.router.navigate(['/bas-view', id]);
    }, error => {
      console.error("Error fetching bill:", error);
     
    });
  }

}


