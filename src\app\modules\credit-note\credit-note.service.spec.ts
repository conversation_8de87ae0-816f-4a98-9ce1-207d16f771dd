import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { CreditNote } from './credit-note';

@Injectable({
  providedIn: 'root',
})
export class CreditNoteService {
  private apiUrl = 'http://localhost:8187'; // Update with your backend API URL

  constructor(private http: HttpClient) {}

  saveCreditNote(creditNote: CreditNote): Observable<CreditNote> {
    return this.http.post<CreditNote>(`${this.apiUrl}/saveCreditNote`, creditNote);
  }
}
