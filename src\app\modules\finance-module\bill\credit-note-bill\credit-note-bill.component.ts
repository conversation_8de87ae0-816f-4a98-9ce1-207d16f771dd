import { Component, ViewChild } from '@angular/core';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { ApInvoiceDetail, ApInvoiceHead, CoaLedgerAccount, CreditNoteBillDetails, CreditNoteBillHead } from '../bill';
import { Entity } from 'src/app/modules/entity/entity';
import { ActivatedRoute, Router } from '@angular/router';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { BillService } from '../bill.service';
import Swal from 'sweetalert2';
import { NgForm } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { DateAdapter } from '@angular/material/core';

@Component({
  selector: 'app-credit-note-bill',
  templateUrl: './credit-note-bill.component.html',
  styleUrls: ['./credit-note-bill.component.css']
})
export class CreditNoteBillComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;


  creditNoteBillHead: CreditNoteBillHead = new CreditNoteBillHead();
  details: CreditNoteBillDetails = new CreditNoteBillDetails();
  businessEntityId: number = 0;
  lastCreditNoteNumber: string = '';
  businessEntity: Entity = new Entity();
  selectedApInvoices: any[] = [];
  totalCreditAmount: number = 0;
  glAccounts :CoaLedgerAccount[] = [];
  
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private entityService: EntityService,
    private billService: BillService,
    private dateAdapter: DateAdapter<Date>,
  ) { 
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }


  ngOnInit() {

    //this.fetchGlAccounts();
    this.route.queryParams.subscribe(params => {
      const billIds = JSON.parse(params['billIds'] || '[]');
      if (billIds.length > 0) {
        this.loadBills(billIds);
        this.fetchGlAccountsForBills(billIds);
       
      } else {
        Swal.fire({
          title: 'No Bills Found',
          text: 'No Bill IDs were provided.',
          icon: 'warning',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('No Bills Found');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    });
    this.getBusinessEntityById();

    if (!this.creditNoteBillHead.documentDate) {
      this.creditNoteBillHead.documentDate = this.getTodayDate();
    }
  }


  glAccountsMap: { [billId: number]: CoaLedgerAccount[] } = {}; // Store GL accounts per bill

fetchGlAccountsForBills(billIds: number[]) {
  this.glAccountsMap = {}; // Reset previous GL account mappings

  billIds.forEach(billId => {
    this.billService.getApInvoiceDetailsByApInvoiceHeadId(billId).subscribe(
      (invoiceDetails: ApInvoiceDetail[]) => {  
        console.log("invoice gl", invoiceDetails);
        
        this.glAccountsMap[billId] = invoiceDetails.map(detail => ({
          coaLedgerAccountId: detail.coaLedgerAccountId,
          ledgerAccountCode: detail.ledgerAccountCode || '',
          ledgerAccountName: detail.ledgerAccountName || ''
        }));
        console.log("gl acount map", this.glAccountsMap);
        
        // Ensure Angular detects the change
        this.glAccountsMap = { ...this.glAccountsMap }; 
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching GL Accounts for Bill ID:', billId, error);
        Swal.fire({
          title: 'Error!',
          text: `Failed to load GL Accounts for Bill ID ${billId}.`,
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonColor: '#be0032',
          showCancelButton: true,
        });
      }
    );
  });
}



    onGLChange(event: any, index: number) {
      const selectedAccountId = +event.target.value;
      const billId = this.selectedApInvoices[index].apInvoiceHeadId; // Get the current bill ID
      const selectedAccount = this.glAccountsMap[billId]?.find(
        (account) => account.coaLedgerAccountId === selectedAccountId
      );
    
      if (selectedAccount) {
        this.selectedApInvoices[index].coaLedgerAccountId = selectedAccount.coaLedgerAccountId;
        this.selectedApInvoices[index].ledgerAccountName = selectedAccount.ledgerAccountName;
      }
    }
    
  
  

  // Utility function to get today's date in 'YYYY-MM-DD' format
  getTodayDate(): string {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const dd = String(today.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }


  loadBills(billIds: number[]): void {
    const billRequests = billIds.map(id => this.billService.getApInvoiceDetailsByApInvoiceHeadId(id).toPromise());
    Promise.all(billRequests)
      .then(bills => {
        this.selectedApInvoices = bills.flat();;
        console.log("selected ap invoices", this.selectedApInvoices);
        
      })
      .catch(error => {
        Swal.fire({
          title: 'Error Loading Bills',
          text: 'There was an error fetching the Bill details.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Error Loading Bills');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      });
  }

  

  getTotalPaidAmount(): number {
    return this.selectedApInvoices.reduce((total, expense) => total + (expense.creditAmount || 0), 0);
  }


  getExpenseNewBalance(expense: any): number {
    const balanceAmount = expense.dueAmount + expense.tax || 0;
    const creditAmount = expense.creditAmount || 0;
    return balanceAmount - creditAmount;
  }



  onCancel() {
    this.router.navigate(['credit-note-bill-list']);
  }



  preventEnter(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault(); // Prevent form submission on "Enter"
    }
  }




    onSubmit(f: NgForm) {
  // Credit amount must exactly match the due amount
  const invalidBills = this.selectedApInvoices.filter(expense =>
    !expense.creditAmount || expense.creditAmount !== expense.dueAmount + expense.tax
  );

  if (invalidBills.length > 0) {
    Swal.fire({
      title: 'Mismatch in Amount!',
      text: 'Each Credit Amount must match the Due Amount exactly.',
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f'
    });
    return;
  }

  this.creditNoteBillHead.totalCreditAmount = this.getTotalPaidAmount();
  console.log("selected ap invoices before saving", this.selectedApInvoices);
  
  this.creditNoteBillHead.details = this.selectedApInvoices.map(expense => {
    let detail = new CreditNoteBillDetails();
    detail.apInvoiceHeadId = expense.apInvoiceHeadId.apInvoiceHeadId;
    detail.apInvoiceDetailId = expense.apInvoiceDetailId;
    detail.referenceNo = expense.referenceNo;
    detail.netAmount = expense.netAmount;
    detail.balanceAmount = this.getExpenseNewBalance(expense);
    detail.creditAmount = expense.creditAmount;
    detail.coaLedgerAccountId = expense.coaLedgerAccountId;  
    detail.ledgerAccountName = expense.ledgerAccountName; 
    return detail;
  });

  this.saveCreditNote(this.selectedApInvoices);
}


saveCreditNote(bills: any[]) {
  console.log(" saving bills ", bills);
  
  if (!this.creditNoteBillHead.documentDate) {
    this.creditNoteBillHead.documentDate = this.getTodayDate();
  }

  // Block save if any credit amount doesn't exactly match due
  const invalidBills = bills.filter(expense => expense.creditAmount !== expense.dueAmount + expense.tax);

  if (invalidBills.length > 0) {
    Swal.fire({
      title: 'Mismatch in Amounts!',
      text: 'Credit Amount must equal Due Amount for all bills. Please correct it before continuing.',
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f'
    });
    return;
  }

  this.proceedToSave();
}


  proceedToSave() {
    // Assuming the selectedApInvoices array is already populated and validated
    if (this.selectedApInvoices.length > 0) {
      
      this.creditNoteBillHead.businessPartnerId = this.selectedApInvoices[0].apInvoiceHeadId.businessPartnerId;
      this.creditNoteBillHead.apInvoiceHeadId = this.selectedApInvoices[0].apInvoiceHeadId.apInvoiceHeadId;
      this.creditNoteBillHead.supplierName = this.selectedApInvoices[0].apInvoiceHeadId.supplierName;
      this.creditNoteBillHead.referenceNos = this.selectedApInvoices.length > 0
        ? this.selectedApInvoices[0].referenceNo
        : '';

      console.log(" supplier name ", this.selectedApInvoices[0].apInvoiceHeadId.supplierName);
        
    }

  
    this.creditNoteBillHead.documentStatus = 'Open';
    this.creditNoteBillHead.userId = +(localStorage.getItem('userid') + "");
    this.creditNoteBillHead.entityId = +(localStorage.getItem('entityId') + "");

    // Save the credit note
    this.billService.saveCreditNoteBillHead(this.creditNoteBillHead).subscribe(
      (response: any) => {
        this.updateCreditNoteNumberBill();

        Swal.fire({
          title: 'Success!',
          text: 'The Credit Note has been successfully saved.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/credit-note-bill-list']);
          }
        });
      },
      (error: HttpErrorResponse) => {
        console.error('Error saving Credit Note', error);
        Swal.fire({
          title: 'Error!',
          text: 'Unable to save the  Credit Note. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Unable to save the  Credit Note. Please try again.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }

        });
      }
    );
  }

  // Method to update expense balance in the backend
  updateBillBalance(apInvoiceHeadId: number, newBalanceAmount: number) {
    this.billService.updateBillBalance(apInvoiceHeadId, newBalanceAmount).subscribe(
      (response: any) => {
      },
      (error: HttpErrorResponse) => {
        console.error(`Error updating balance for bill ${apInvoiceHeadId}:`, error);
      }
    );
  }


  getBusinessEntityById() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '');
    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
      (data) => {
        this.businessEntity = data;
        this.lastCreditNoteNumber = this.incrementPaymentNumber(
          this.businessEntity.creditNoteNumberBill
        );
        this.creditNoteBillHead.creditNoteNumberBill = this.lastCreditNoteNumber;
      },
      (error) => console.error(error)
    );
  }


    incrementPaymentNumber(creditNoteNumberBill: string): string {
      if (!creditNoteNumberBill) {
        return 'CB000001'; // Default value
      }
      // Ensure the numeric part exists and is valid
      const numericPart = creditNoteNumberBill.slice(2); // Remove the "PE" prefix
      if (isNaN(Number(numericPart))) {
        console.error(`Invalid Credit Note Number: ${creditNoteNumberBill}`);
        return 'CB000001';
      }
      const incrementedNumber = (Number(numericPart) + 1).toString().padStart(numericPart.length, '0');
      return 'CB' + incrementedNumber;
    }
    


  updateCreditNoteNumberBill() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '0');
    this.businessEntity.creditNoteNumberBill = this.creditNoteBillHead.creditNoteNumberBill;
    this.entityService.updateCreditNoteNumberBill(this.businessEntity, this.businessEntityId).subscribe(
      (data) => {
      },
      (error) => {
        console.error(error);
      }
    );
  }
  
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}