import { Component, OnInit, ViewChild } from '@angular/core';
import { Deduction } from '../payroll-settings/payroll-setting';
import { NgForm } from '@angular/forms';
import { start } from '@popperjs/core';
import Swal from 'sweetalert2';
import { DeductionService } from '../payroll-settings/services/deduction.service';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-update-deduction',
  templateUrl: './update-deduction.component.html',
  styleUrls: ['./update-deduction.component.css']
})
export class UpdateDeductionComponent implements OnInit {
  deductionId: number = 0;
  deduction: Deduction = new Deduction();
  isPopupVisible: boolean = false;

  deductionCategory: string[] = ['twise monthly', 'onece a month']

  constructor(private router: Router, private deductionService: DeductionService, private route: ActivatedRoute) { }

  ngOnInit(): void {
    this.deductionId = Number(this.route.snapshot.paramMap.get('id'));
    if (this.deductionId) {
      this.getAllDeductions(this.deductionId)
    }
  }
  getAllDeductions(deduction: number): void {
    this.deductionService.getAllDeductions(this.deductionId).subscribe({
      next: (data: Deduction) => {
        this.deduction = data;
      },
      error: (err) => {
        console.error('Error fetching earning:', err);
      },
    });
  }

  navigateToPayrollSettings(): void {
    window.location.assign("/payroll-settings");
  }

  glAccounts: string[] = ['Account A', 'Account B', 'Account C', 'Account D'];

  updateDeduction(): void {
    const entityId = +((localStorage.getItem('entityId')) + "");
    const userId = +((localStorage.getItem('userid')) + "");
    const date = new Date().toISOString();

    const payload = {
      ...this.deduction,
      entityId,
      userId,
      date,
    };

    this.deductionService.updateDeduction(this.deductionId, payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Ordinary Time Earnings update successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745'
        }).then(() => {
          this.deduction = new Deduction();
          window.location.assign("/payroll-settings");

        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to update Ordinary Time Earnings. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032'
        });
      }
    );
  }

}


