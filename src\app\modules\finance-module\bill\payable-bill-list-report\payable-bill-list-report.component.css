.report-form-container {
    position: relative;
    max-width: 600px;
    margin-left: 0;
    margin-right: auto;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.report-form-container::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 10px;
    background: linear-gradient(90deg, #4262FF 0%, #512CA2 100%);
    z-index: -1;
}

.report-form .form-group {
    margin-bottom: 15px;
}

.date-range,
.amount-range {
    display: flex;
    align-items: center;
}

.date-range input,
.amount-range input {
    flex: 1;
    margin-right: 5px;
    padding: 8px;
    font-size: 16px;
}

.date-range span,
.amount-range span {
    margin: 0 5px;
    color: #333;
    font-size: 14px;
    font-weight: bold;
}

select,
input[type="date"],
input[type="number"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
    background-color: #f9f9f9;
}

.generate-report-btn {
    display: block;
    margin-left: auto;
}

button.generate-report-btn {
    margin-top: 20px;
    padding: 8px 22px;
    background: linear-gradient(90deg, #4262FF 0%, #512CA2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 18px;
    transition: background 0.3s ease;
}

button.generate-report-btn:hover {
    background: linear-gradient(90deg, #512CA2 0%, #4262FF 100%);
}

.modal-content {
    max-width: 740px;
    margin: 0 auto;
}

/* Spinner alignment */
.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* Hide the iframe while loading */
.d-none {
    display: none;
}

/* Additional spacing for form */
.report-form {
    padding: 10px;
}

/* Adjusting header alignment */
h4 {
    text-align: left;
    font-size: 18px;
    color: #333;
    margin-bottom: 10px;
}

@media (max-width: 680px) {
    .date-range,
    .amount-range {
        flex-direction: column;
        align-items: flex-start;
    }

    .report-form-container {
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
    }
}
