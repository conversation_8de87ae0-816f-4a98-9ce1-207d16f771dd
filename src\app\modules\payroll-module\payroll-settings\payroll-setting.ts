// export class OnePayCalendar {
import { GlAccount } from './../../finance-module/gl-account/gl-account';
import { EmployeeMaster, Personal } from './empolyee/employee';

//       public payCycleName: string = '';
//       public payStartDate: string = '';
//       public nextPayDate: string = '';
//       public payCalendar: MultiplePayCalendar | null = null;

//   }

export class MultiplePayCalendar {
  public calendarName: string = '';
  public payStartDate: any = '';
  public nextPayDate: any = '';
  public fromDay: any = '';
  public payDay: any = '';
  public payCalendarCheckbox: boolean = false;
  public payCycle?: PayCycle | null = null;
}
export class PayCycle {
  public payCycleId: number = 0;
  public cycleName: string = '';
  public entityId: number = 0;
  public userId: number = 0;
  public numberOfDate: number = 0;
}
export class Disable {
  public status: boolean = false;
}

export class PayPeriod {
  payPeriodId: number;
  name: string;
  payCalendar: PayCalendar;
  payStartDate: string;
  nextPayDate: string;
  status: String;
  date?: string;
  unscheduled: boolean;

  constructor() {
    this.payPeriodId = 0;
    this.name = '';
    this.payCalendar = new PayCalendar();
    this.payStartDate = '';
    this.nextPayDate = '';
    this.status = '';
    this.unscheduled = false;
  }
}
export class PayCalendar {
  payCalendarId: number;
  calendarName: string;
  payCycleName: string;
  payCycle: PayCycle;
  payStartDate: string;
  nextPayDate: string;
  payDay: string;
  fromDay: string;
  payCalendarCheckbox: boolean;
  entityId: number;
  userId: number;
  date: string;
  constructor() {
    this.payCalendarId = 0;
    this.calendarName = '';
    this.payCycleName = '';
    this.payCycle = new PayCycle();
    this.payStartDate = '';
    this.nextPayDate = '';
    this.payDay = '';
    this.fromDay = '';
    this.payCalendarCheckbox = false;
    this.entityId = 0;
    this.userId = 0;
    this.date = '';
  }
}
export class PayrollSetting {}

// export class Earnings{
//   earningId?: number=0;
//   payItemType: payItemType= new this.payItemType();
// }

export class Earning {
  earningId: number = 0;
  public entityId: number = 0;
  public userId: number = 0;
  public earningsName: string = '';
  public displayName: string = '';
  public rate: number = 0.0;
  public typeOfUnits: string = '';
  public category: string = '';
  public glAccount: string = '';
  public wiReportable: boolean = false;
  public exemptFromPAYG: boolean = false;
  public exemptFromSuperannuation: boolean = false;
  public date: string = '';
  public glAccountName: string = '';
}

export class Leave {
  public leaveCategory: leaveCategory | null = null;
  leaveTypeId: number = 0;
  public entityId: number = 0;
  public leaveType: string = '';
  public leaveCalculationMethod: string = '';
  public hoursAccruedFullTime: number = 0.0;
  public hoursWorkedFortnightly: number = 0.0;
}

export class leaveCategory {
  leaveCategoryId: number = 0;
  entityId: number = 0;
  userId: number = 0;
  leaveCategory: string = '';
  units: string = '';
  normalEntitlement: string = '';
  leaveLoadingRate: number = 0.0;
  showBalances: boolean = false;
  date: string = '';
}
// export class GetEarning {
//   earningId: number;
//   entityId: number;
//   userId: number;
//   earningsName: string;
//   displayName: string;
//   rate: number;
//   typeOfUnits: string;
//   category: string;
//   glAccount: string;
//   wiReportable: boolean;
//   exemptFromPAYG: boolean;
//   exemptFromSuperannuation: boolean;
//   date: string;

//   constructor() {
//     this.earningId = 0;
//     this.entityId = 0;
//     this.userId =  0;
//     this.earningsName = '';
//     this.displayName = '';
//     this.rate = 0;
//     this.typeOfUnits = '';
//     this.category = '';
//     this.glAccount = '';
//     this.wiReportable = true;
//     this.exemptFromPAYG = true;
//     this.exemptFromSuperannuation = true;
//     this.date = '';
//   }
// }

export class PayItemType {
  payItemTypeId: number = 0;
  entityId: number = 0;
  payItemType: string = '';
}

export class EmployeeEarning {
  employeeId: number = 0;
  earningAmount: number = 0.0;
}

export class Deduction {
  deductionId: number = 0;
  entityId: number = 0;
  userId: number = 0;
  deductionName: string = '';
  deductionCategory: string = 'Post Tax';
  glAccount: string = '';
  wiReportable: string = '';
  exemptFromPAYG: string = '';
  exemptFromSuperannuation: string = '';
  date: string = ''; // Ensure proper date handling in the service layer.
  glAccountName: string = '';

}

export class Reimbursement {
  reimbursementId: number = 0;
  entityId: number = 0;
  userId: number = 0;
  description: string = '';
  reimbursementType: string = '';
  glAccount: string = '';
  date: string = ''; 
  glAccountName: string = '';
}

export class PayRunMaster {
  payRunId: number = 0;
  payCalendar: PayCalendar | null = null;
  entityId: number = 0;
  userId: number = 0;
  payPeriod: PayPeriod = new PayPeriod();
  paymentDate: string = '';
  earnings: number = 0.0;
  deductions: number = 0.0;
  reimbursements: number = 0.0;
  tax: number = 0.0;
  superAmount: number = 0.0;
  netPay: number = 0.0;
  stpFilling: string = '';
  employeePayments: number = 0.0;
  otherPayments: number = 0.0;
  contact: string = '';
  dueDate: string = '';
  amount: number = 0.0;
  totalPayable: number = 0.0;
  totalEmployee: number = 0;
  date: string = '';
  status: string = '';
  atoMessageId: string = '';
  atoStatus: string = '';

  constructor() {
    this.payPeriod,
      this.userId,
      this.amount,
      this.contact,
      this.date,
      this.status,
      this.earnings,
      this.deductions,
      this.reimbursements,
      this.dueDate,
      this.employeePayments,
      this.entityId,
      this.netPay,
      this.tax,
      this.superAmount;
    this.otherPayments, this.payCalendar, this.payRunId, this.atoMessageId, this.atoStatus;
  }
}

export class PayProcessDTO {
  employeeId: number = 0;
  firstName: string = '';
  lastName: string = '';
  totalEarnings: number = 0.0;
  totalDeductions: number = 0.0;
  totalReimbursements: number = 0.0;
  superannuation: number = 0.0;
  totalEmployee: number = 0;
  tax: number = 0;
  netPay: number = 0;
  excluded: boolean = false;
  inactiveFrom: string = '';
  paymentDate:  string = '';
  finalSTPStatus: string = '';

  constructor() {
    this.firstName,
      this.lastName,
      this.netPay,
      this.superannuation,
      this.tax,
      this.totalDeductions,
      this.totalEarnings,
      this.totalEmployee,
      this.totalReimbursements;
      this.inactiveFrom;
      this.paymentDate;
      this.finalSTPStatus;
  }
}

export class PayRunDetail {
  payRunDetailId: number = 0;
  payRun: PayRunMaster | null = null;
  entityId: number = 0;
  code: number = 0;
  glAccount: string = '';
  glAccountName: string = '';
  employee: Personal | null = null;
  type: string = '';
  codeType: string = '';
  amount: number = 0.0;
  hours: number = 0.0;
  rate: number = 0.0;
  status: string = '';
  beamProcessId: string = '';
  beamStatus: string = '';
}

export class EarningCategory {
  earningCategoryId: number = 0;
  categoryName: string = '';
}

export class PayRunDetailsSummary {
  summaryId: number = 0;
  payRun: PayRunMaster | null = null;
  employee: EmployeeMaster | null = null;
  entityId: number = 0;
  earningsTotal: number = 0;
  deductionsTotal: number = 0;
  reimbursementsTotal: number = 0;
  superannuationTotal: number = 0;
  taxTotal: number = 0
  netPay: number = 0;
  earningsForSuper: number = 0
  deductionsForSuper: number = 0;
  earningsForTax: number = 0;
  deductionsForTax: number = 0;
  superAmount: number = 0;
  taxAmount: number = 0;
  tax: number = 0;
  isExcluded: boolean = false;
  accruedHours: number = 0;
  excluded: boolean = false;
}

export class PayrollConfig {
  payrollConfigId: number = 0;
  bankAccount: string = '';
  wagesExpenseAccount: string = '';
  wagesPayableAccount: string = '';
  superannuationLiabilityAccount: string = '';
  superannuationExpenseAccount: string = '';
  paygLiabilityAccount: string = '';
  bankAccountName: string = '';
  wagesExpenseAccountName: string = '';
  wagesPayableAccountName: string = '';
  superannuationLiabilityAccountName: string = '';
  superannuationExpenseAccountName: string = '';
  paygLiabilityAccountName: string = '';
 
}

