import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AccountantDto } from 'src/app/modules/admin/components/user/user';
import { UserService } from 'src/app/modules/admin/components/user/user.service';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';

@Component({
  selector: 'app-bookkeeper-or-accountant',
  templateUrl: './bookkeeper-or-accountant.component.html',
  styleUrls: ['./bookkeeper-or-accountant.component.css']
})
export class BookkeeperOrAccountantComponent {


   accountants: AccountantDto[] = [];
   entityId = +localStorage.getItem('entityId')!;

  constructor(private userService: UserService,
    private swalAlerts: SwalAlertsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.userService.getAccountantList().subscribe({
      next: (data) => {
        this.accountants = data;
      },
      error: (err) => {
        console.error('Failed to load accountants', err);
      }
    });
  }

    inviteAccountant(accountant: AccountantDto): void {
    const payload = {
      firstName: accountant.firstName,
      email: accountant.username,
      entityId: this.entityId,
    };

    this.userService.inviteAccountant(payload).subscribe({
      next: () => {
        this.swalAlerts.showSuccessDialog(
        'Success!',
        'Invitation sent to accountant.',
        () => {
       this.router.navigate([`/bookkeeper`]);
      }
    );
     
      },
      error: (err) => {
        console.error('Failed to send invitation', err);
        this.swalAlerts.showErrorDialog('Failed to send invitation.');
      },
    });
  }
}
