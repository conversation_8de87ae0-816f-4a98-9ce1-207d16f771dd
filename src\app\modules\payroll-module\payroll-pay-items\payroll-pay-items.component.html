<app-payroll-nevigation></app-payroll-nevigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">


  <div class="actions sub-container">
    <h2>Pay Items</h2>
  </div>
  


  <!-- Pay Items Section -->
  <div class="pay-items-section">
    <!-- Sidebar -->
    <div class="side-bar">
      <ul class="nav nav_pay_item">
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Earnings')"
            (click)="setActiveSide('Earnings'); $event.preventDefault()"
          >
            Earning
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Deductions')"
            (click)="setActiveSide('Deductions'); $event.preventDefault()"
          >
            Deduction
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Reimbursements')"
            (click)="setActiveSide('Reimbursements'); $event.preventDefault()"
          >
            Reimbursement
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Leave')"
            (click)="setActiveSide('Leave'); $event.preventDefault()"
          >
            Leave
          </a>
        </li>
      </ul>
      <div
        class="form-check form-switch"
        *ngIf="isActiveSide('Reimbursements')"
        style="margin-left: 20px; margin-top: 20px"
      >
        
      </div>
    </div>

    <!-- Main Content -->
    <div class="pay_item-content">
      <div class="Earnings" *ngIf="isActiveSide('Earnings')">
        <div class="pay_item_head">
          <h2>Earnings</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#Ordinary-Time_Earnings"
            >
              Add
            </button>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Earning Type</th>
                <th>Earning Category</th>
                <th>Rate</th>
                <!-- <th>Type of Units</th> -->
                <th>GL Account</th>
                <th>Reportable as W1</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let earning of earnings
                    | paginate : { itemsPerPage: 5, currentPage: earningsPage }
                "
              >
                <td>{{ earning.earningsName }}</td>
                <td>{{ earning.category }}</td>
                <td>{{ earning.rate }}</td>
                <!-- <td>{{ earning.payItemType }}</td> -->
                <td>{{ earning.glAccountName }}</td>
                <td>{{ earning.wiReportable ? 'Yes' : 'No' }}</td>
                <td class="value">
                  <button class="btn btn-orange btn-sm" (click)="updateEarnings(earning.earningId)" style="
                                    margin-right: 2px;
                                    border: none;
                                    background: none;
                                    padding: 2px;
                                    font-size: 1rem;
                                    visibility: hidden;
                                  " title="Edit">
                    <i class="ri-edit-box-line" style="color: #4262ff"></i>
                  </button>

                  <ng-container *ngIf="!getButtonState1(earning.earningId); else disabledButton">
                    <button
                      class="btn btn-danger btn-sm"
                      (click)="deleteEarning(earning.earningId)"
                      style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
                      title="Delete"
                    >
                      <i class="ri-delete-bin-line" style="color: #ff0000"></i>
                    </button>
                  </ng-container>
                  <ng-template #disabledButton>
                    <button
                      class="btn btn-secondary btn-sm"
                      style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
                      title="Disabled"
                    >
                      <i class="ri-lock-line" style="color: #808080"></i>
                    </button>
                  </ng-template>

                  <!-- <button class="btn btn-danger btn-sm" (click)="deleteEarning(earning.earningId)" style="
                                    margin-right: 2px;
                                    border: none;
                                    background: none;
                                    padding: 2px;
                                    font-size: 1rem;
                                  " title="Delete">
                    <i class="ri-delete-bin-line" style="color: #ff0000"></i>
                  </button> -->
                </td>
              </tr>
            </tbody>
          </table>

          <pagination-controls
            class="d-flex justify-content-end"
            (pageChange)="earningsPage = $event"
          >
          </pagination-controls>
        </div>
      </div>

          <div class="Deductions"  *ngIf="isActiveSide('Deductions')">
            <div class="pay_item_head">
              <h2>Deductions</h2>
              <div class="btn-group">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="modal" data-bs-target="#add-deductions">
                  Add
                </button>
              </div>
            </div>
  
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Deduction Type</th>
                    <th>Deduction Category</th>
                    <th>GL Account</th>
                    <!-- <th>Reduces PAYG</th> -->
                    <th>Reduces SGC</th>
                    <th>Excluded From W1</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let deduction of deductions | paginate: { itemsPerPage: 5, currentPage: deductionsPage }">
                    <td>{{ deduction.deductionName }}</td>
                    <td>{{ deduction.deductionCategory }}</td>
                    <td>{{ deduction.glAccountName }}</td>
                    <!-- <td>{{ deduction.exemptFromPAYG ? 'Yes' : 'No' }}</td> -->
                    <td>{{ deduction.exemptFromSuperannuation ? 'Yes' : 'No' }}</td>
                    <td>{{ deduction.wiReportable ? 'Yes' : 'No' }}</td>
                    <td class="value">
                      <button class="btn btn-orange btn-sm" (click)="updateDeductions(deduction.deductionId)" style="
                                                        margin-right: 2px;
                                                        border: none;
                                                        background: none;
                                                        padding: 2px;
                                                        font-size: 1rem;
                                                        visibility: hidden;
                                                      " title="Edit">
                        <i class="ri-edit-box-line" style="color: #4262ff"></i>
                      </button>
                      <ng-container *ngIf="!getButtonState2(deduction.deductionId); else disabledButton">
                        <button
                          class="btn btn-danger btn-sm"
                          (click)="deleteDeduction(deduction.deductionId)"
                          style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
                          title="Delete"
                        >
                          <i class="ri-delete-bin-line" style="color: #ff0000"></i>
                        </button>
                      </ng-container>
                      <ng-template #disabledButton>
                        <button
                          class="btn btn-secondary btn-sm"
                          style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
                          title="Disabled"
                        >
                          <i class="ri-lock-line" style="color: #808080"></i>
                        </button>
                      </ng-template>
                    </td>
                  </tr>
                </tbody>
              </table>
            
              <pagination-controls 
                class="d-flex justify-content-end" 
                (pageChange)="deductionsPage = $event">
              </pagination-controls>
            </div>
            
          </div>

      <div class="Reimbursements" *ngIf="isActiveSide('Reimbursements')">
        <div class="pay_item_head">
          <h2>Reimbursement</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#add-reimbursements"
            >
              Add
            </button>
          </div>
        </div>

            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Reimbursement Type</th>
                    <th>Description</th>
                    <th>GL Account</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let reimbursement of reimbursements | paginate: { itemsPerPage: 5, currentPage: reimbursementsPage }">
                    <td>{{ reimbursement.reimbursementType }}</td>
                    <td>{{ reimbursement.description }}</td>
                    <td>{{ reimbursement.glAccountName }}</td>
                    <td class="value">
                      <button class="btn btn-orange btn-sm" style="
                                        margin-right: 2px;
                                        border: none;
                                        background: none;
                                        padding: 2px;
                                        font-size: 1rem;
                                        visibility: hidden;
                                      " title="Edit">
                        <i class="ri-edit-box-line" style="color: #4262ff"></i>
                      </button>
                    <ng-container *ngIf="!getButtonState3(reimbursement.reimbursementId); else disabledButton">
                      <button
                        class="btn btn-danger btn-sm"
                        (click)="deleteReimbursement(reimbursement.reimbursementId)"
                        style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
                        title="Delete"
                      >
                        <i class="ri-delete-bin-line" style="color: #ff0000"></i>
                      </button>
                    </ng-container>
                    <ng-template #disabledButton>
                      <button
                        class="btn btn-secondary btn-sm"
                        style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
                        title="Disabled"
                      >
                        <i class="ri-lock-line" style="color: #808080"></i>
                      </button>
                    </ng-template>
                  </td>
                  </tr>
                </tbody>
              </table>
            
              <pagination-controls 
                class="d-flex justify-content-end" 
                (pageChange)="reimbursementsPage = $event">
              </pagination-controls>
            </div>
            
          </div>

   

      <div class="Leave" *ngIf="isActiveSide('Leave')">
        <div class="pay_item_head">
          <h2>Leave</h2>
         <!-- <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#add-leave-type-01"
            >
              Add Leave Type
            </button>
          </div>-->
        </div>

        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Leave Name</th>
                <th>Leave Category</th>
                <!-- <th>Units</th> -->
                <th>Normal Entitlement</th>
                <th>Leave Loading Rate</th>
                <th>Show on Payslip</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let leave of leave
                    | paginate : { itemsPerPage: 5, currentPage: leavesPage }
                "
              >
                <td>{{ leave.leaveType }}</td>
                <td>{{ leave.leaveCategory.leaveCategory }}</td>
                <!-- <td>{{ leave.leaveCategory.units }}</td> -->
                <td>{{ leave.leaveCategory.normalEntitlement }}</td>
                <td>{{ leave.leaveCategory.leaveLoadingRate }}</td>
                <td>{{ leave.leaveCategory.showBalances ? 'Yes': 'No'}}</td>
                <td class="value">
                  <button class="btn btn-orange btn-sm" data-bs-toggle="modal"
              data-bs-target="#Leave-Edit" (click)="editLeave(leave.leaveTypeId)" style="
                                                    margin-right: 2px;
                                                    border: none;
                                                    background: none;
                                                    padding: 2px;
                                                    font-size: 1rem;
                                                    visibility: visible;
                                                  " title="Edit">
                    <i class="ri-edit-box-line" style="color: #4262ff"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <pagination-controls
            class="d-flex justify-content-end"
            (pageChange)="leavesPage = $event"
          >
          </pagination-controls>
        </div>
      </div>

     

      <div class="Leave" *ngIf="isActiveSide('Leave_Form_2')">
        <div class="pay_item_head">
          <h2>Leave</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              style="margin-right: 10px"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#assign-leave-type"
            >
              Leave Loading
            </button>
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#add-leave-type-form-2"
            >
              Add Leave Type
            </button>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Leave Name</th>
                <th>Leave Category</th>
                <th>Units</th>
                <th>Normal Entitlement</th>
                <th>Leave Loading Rate</th>
                <th>Show on Payslip</th>
                <th>Exempt from Superannuation</th>
                <th>Show Balance on Pay Slip</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let leave of leaveTwo
                    | paginate : { itemsPerPage: 5, currentPage: leaveTwoPage }
                "
              >
                <td>{{ leave.name }}</td>
                <td>{{ leave.category }}</td>
                <td>{{ leave.units }}</td>
                <td>{{ leave.entitlement }}</td>
                <td>{{ leave.loadingRate }}</td>
                <td>{{ leave.showOnPayslip }}</td>
                <td>{{ leave.exemptSuperannuation }}</td>
                <td>{{ leave.showBalance }}</td>
              </tr>
            </tbody>
          </table>

          <pagination-controls
            class="d-flex justify-content-end"
            (pageChange)="leaveTwoPage = $event"
          >
          </pagination-controls>
        </div>
      </div>

      <div
        style="margin-top: 20px; margin-left: 20px; margin-bottom: 20px"
        *ngIf="isActiveSide('Leave_Form_2')"
      >
        <button
          type="button"
          class="btn btn-primary"
          data-bs-toggle="modal"
          data-bs-target="#add-leave-accural"
        >
          + Add Leave Accural
        </button>
      </div>

      <div
        class="Leave-form-3"
        style="background-color: transparent"
        *ngIf="isActiveSide('Leave_Form_3')"
      >
        <div class="pay_item_head">
          <h2>Leave</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#assign-leave-type"
            >
              Assign Leave Type
            </button>
          </div>
        </div>

        <div class="pay-item-2" style="margin-top: 10px">
          <div class="pay-item-one">
            <div class="pay_item_head">
              <h2>Leave Loading Balance</h2>
              <div class="btn-group">
                <button class="btn btn-primary dropdown-toggle" type="button">
                  leave loading Eligibility
                </button>
              </div>
            </div>

            <div class="pay-item-two">
              <div class="pay-item-col1">
                <span>Leave Loading Balance</span>
                <strong style="color: #4262ff; font-weight: bold"
                  >88.0245 Hours</strong
                >
              </div>
              <div class="pay-item-col2">
                <span>Personal (Sick/Carer's) Leave</span>
                <strong style="color: #4262ff; font-weight: bold"
                  >88.0245 Hours</strong
                >
              </div>
            </div>

            <div class="pay_item_head">
              <h2>Leave</h2>
              <div class="btn-group">
                <button
                  class="btn btn-primary dropdown-toggle"
                  type="button"
                  [class.active]="isActiveSide('Leave_Form_2')"
                  (click)="
                    setActiveSide('Leave_Form_2'); $event.preventDefault()
                  "
                  data-bs-dismiss="modal"
                >
                  New Request
                </button>
              </div>
            </div>
          </div>

          <div class="pay-item-one">
            <div class="pay-item-two" style="gap: 15px">
              <div class="pay-item-col3">
                <!-- <i class="bi bi-search"></i> -->
                <input
                  type="text"
                  class="form-control"
                  placeholder=" Search"
                  aria-label="Search"
                  style="width: 100%; background-color: #f3f3f3"
                />
              </div>
              <div class="filter-buttons">
                <button class="btn btn-light">
                  <i class="bi bi-funnel"></i> Search
                </button>
                <button class="btn btn-light">
                  <i class="bi bi-arrow-down-up"></i> Status
                </button>
              </div>
            </div>

            <div class="pay-item-three">
              <h2>To Review</h2>
              <div class="pay-item-four">
                <div class="pro-pic">JL</div>
                <div class="User-Details">
                  <h5>
                    James Safron <small class="text-muted">Annual Leave</small>
                  </h5>
                  <p>
                    <small style="color: #4262ff">02 Apr 2024 Day Off</small>
                  </p>
                </div>
                <div class="approve-btn">
                  <button class="btn btn-outline-primary">Approve</button>
                </div>
                <div class="app-icon">
                  <i class="bi bi-three-dots-vertical"></i>
                </div>
              </div>
            </div>

            <div class="pay-item-three">
              <h2>Upcoming</h2>
              <div class="pay-item-four">
                <div class="pro-pic">JL</div>
                <div class="User-Details">
                  <h5>
                    James Safron <small class="text-muted">Annual Leave</small>
                  </h5>
                  <p>
                    <small style="color: #4262ff">02 Apr 2024 Day Off</small>
                  </p>
                </div>
                <div class="approve-btn">
                  <button class="btn btn-outline-primary">Approve</button>
                </div>
                <div class="app-icon">
                  <i class="bi bi-three-dots-vertical"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        style="margin-top: 20px; margin-left: 20px; margin-bottom: 20px"
        *ngIf="isActiveSide('Leave_Form_3')"
      >
        <button
          type="button"
          class="btn btn-primary"
          data-bs-toggle="modal"
          data-bs-target="#draft-pay-slip"
        >
          + Adjust Draft Payslip
        </button>
      </div>

      <div
        style="margin-top: 20px; margin-left: 20px; margin-bottom: 20px"
        *ngIf="isActiveSide('Leave_Form_3')"
      >
        <button
          type="button"
          class="btn btn-primary"
          data-bs-toggle="modal"
          data-bs-target="#add-leave-loading-percentage"
        >
          + Add a leave Loading Percentage
        </button>
      </div>

      <div
        class="Leave-form-final"
        style="background-color: transparent"
        *ngIf="isActiveSide('Leave-form-final')"
      >
        <div class="pay_item_head">
          <h2>Leave</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#edit-leave-type"
            >
              Edit Leave Type
            </button>
          </div>
        </div>

        <div class="leave-emp">
          <div class="emp-name">
            <label for="employee-name">Employee Name</label>
            <p class="employee-name">Elizabeth Bennison</p>
          </div>
          <div class="salary-earnings">
            <label for="annual-salary">Annual Salary</label>
            <button class="view-btn">View</button>
          </div>
          <div class="salary-earnings">
            <label for="earnings-ytd">Earnings YTD</label>
            <button class="view-btn">View</button>
          </div>
          <div class="date-next-payment">
            <label for="next-payment-date">Next Payment Date</label>
            <strong class="next-payment-date">Oct 28 2024</strong>
          </div>
        </div>

        <div class="pay-item-2" style="margin-top: 10px">
          <div class="pay-item-one">
            <div class="pay_item_head">
              <h2>Leave Loading Balance</h2>
              <div class="btn-group">
                <button class="btn btn-primary dropdown-toggle" type="button">
                  leave loading Eligibility
                </button>
              </div>
            </div>

            <div class="pay-item-two">
              <div class="pay-item-col1">
                <span>Leave Loading Balance</span>
                <strong style="color: #4262ff; font-weight: bold"
                  >88.0245 Hours</strong
                >
              </div>
              <div class="pay-item-col2">
                <span>Personal (Sick/Carer's) Leave</span>
                <strong style="color: #4262ff; font-weight: bold"
                  >88.0245 Hours</strong
                >
              </div>
            </div>

            <div class="pay_item_head">
              <h2>Leave</h2>
              <div class="btn-group">
                <button
                  class="btn btn-primary dropdown-toggle"
                  type="button"
                  data-bs-toggle="modal"
                  data-bs-target="#add-leave-type-form-2"
                >
                  New Request
                </button>
              </div>
            </div>
          </div>

          <div class="pay-item-one">
            <div class="pay-item-two" style="gap: 15px">
              <div class="pay-item-col3">
                <!-- <i class="bi bi-search"></i> -->
                <input
                  type="text"
                  class="form-control"
                  placeholder=" Search"
                  aria-label="Search"
                  style="width: 100%; background-color: #f3f3f3"
                />
              </div>
              <div class="filter-buttons">
                <button class="btn btn-light">
                  <i class="bi bi-funnel"></i> Search
                </button>
                <button class="btn btn-light">
                  <i class="bi bi-arrow-down-up"></i> Status
                </button>
              </div>
            </div>

            <div class="pay-item-three">
              <h2>To Review</h2>
              <div class="pay-item-four">
                <div class="pro-pic">JL</div>
                <div class="User-Details">
                  <h5>
                    James Safron <small class="text-muted">Annual Leave</small>
                  </h5>
                  <p>
                    <small style="color: #4262ff">02 Apr 2024 Day Off</small>
                  </p>
                </div>
                <div class="approve-btn">
                  <button class="btn btn-outline-primary">Approve</button>
                </div>
                <div class="app-icon">
                  <i class="bi bi-three-dots-vertical"></i>
                </div>
              </div>
            </div>

            <div class="pay-item-three">
              <h2>Upcoming</h2>
              <div class="pay-item-four">
                <div class="pro-pic">JL</div>
                <div class="User-Details">
                  <h5>
                    James Safron <small class="text-muted">Annual Leave</small>
                  </h5>
                  <p>
                    <small style="color: #4262ff">02 Apr 2024 Day Off</small>
                  </p>
                </div>
                <div class="approve-btn">
                  <button class="btn btn-outline-primary">Approve</button>
                </div>
                <div class="app-icon">
                  <i class="bi bi-three-dots-vertical"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      
    </div>



    
  </div>


  <!-- <div class="payroll-history" *ngIf="isActiveSide('Reimbursements')">
    <h3>
      <button
        class="btn btn-toggle"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#historyContent"
        aria-expanded="false"
        aria-controls="historyContent"
      >
        <i class="bi bi-chevron-down" style="margin-right: 50px"></i>History
      </button>
    </h3>
    <div class="collapse" id="historyContent">
      <p>No history available yet.</p>
    </div>
  </div> -->
</div>



  <div
    class="modal fade"
    id="Ordinary-Time_Earnings"
    tabindex="-1"
    aria-labelledby="Ordinary-Time_Earnings"
    aria-hidden="true"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="ot-modal-header">
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
            style="margin-left: 95%"
            (click)="navigateToPayrollItems()"
          >
            <i class="bi bi-x-circle"></i>
          </button>
          <i
            class="bi bi-person-workspace"
            style="font-size: 70px; color: #4262ff"
          ></i>
          <div class="modal-header">
            <h5
              class="modal-title"
              id="add_pay_calendar"
              style="margin-left: 20%"
            >
              Ordinary Time Earnings
            </h5>
          </div>
        </div>
        <div class="modal-body">
          <form #earningForm="ngForm" (ngSubmit)="onEarningAdd()">
            <div class="mb-3">
              <label for="earnings-name" class="form-label"
                >Earning Type</label
              >
              <input
                name="earningsName"
                type="text"
                class="form-control"
                id="earnings-name"
                [(ngModel)]="earning.earningsName"
                required
                #earningsName="ngModel"
              />
              <div *ngIf="earningsName.invalid && earningsName.touched" class="text-danger">
                Earning Type is required.
              </div>
            </div>
            <div class="mb-3">
              <label for="Category" class="form-label">Category</label>
              <select name="earningsCategory" class="form-control" id="Category" [(ngModel)]="earning.category" required
                #earningsCategory="ngModel">
                <option value="" disabled selected>Select a earning Category</option>
                  <option *ngFor="let type of earningCategories" [value]="type.categoryName">
                    {{ type.categoryName }}
                  </option>
                              </select>
              <div *ngIf="earningsCategory.invalid && earningsCategory.touched" class="text-danger">
                Category is required.
              </div>
            </div>
            <!-- <div class="mb-3">
              <label for="dis-name" class="form-label"
                >Display Name (optional)</label
              >
              <input
                name="earningsDisplayName"
                type="text"
                class="form-control"
                id="dis-name"
                [(ngModel)]="earning.displayName"
              />
            </div> -->
            <div class="mb-3">
              <label for="unit-types" class="form-label">Payment Type</label>
              <select name="earningsTypeOfUnit" class="form-control" id="unit-types" [(ngModel)]="earning.typeOfUnits" required
                #earningsTypeOfUnit="ngModel">
                <option value="" disabled selected>Select a payment type</option>
                <option *ngFor="let type of paymentTypes" [value]="type">{{ type }}</option>
              </select>
              <div *ngIf="earningsTypeOfUnit.invalid && earningsTypeOfUnit.touched" class="text-danger">
                Payment Type is required.
              </div>
            </div>

            <div class="mb-3">
              <label for="rate" class="form-label">{{ rateLabel }}</label>
              <input
                name="earningsRate"
                type="text"
                class="form-control"
                id="rate"
                [(ngModel)]="earning.rate"
                required
                #earningsRate="ngModel"
              />
              <div *ngIf="earningsRate.invalid && earningsRate.touched" class="text-danger">
                Rate is required and must be a valid number.
              </div>
            </div>
            
            
            <div class="mb-3">
              <label for="gl-account" class="form-label">Expense Account</label>
              <select
                name="earningsGlAccount"
                class="form-control"
                id="gl-account"
                [(ngModel)]="earning.glAccount"
                (ngModelChange)="onGlAccountChange()"
                #earningsGlAccount="ngModel"
              >
                <option value="" disabled selected>Select an Expense Account</option>
                <ng-container *ngFor="let account of glAccounts">
                  <option *ngIf="account.coaHeaderId.accountHeaderType === 'Expenses'" [value]="account.ledgerAccountCode">
                    {{ account.ledgerAccountName }} : {{ account.ledgerAccountCode }}
                  </option>
                </ng-container>
              </select>
              <div *ngIf="earningsGlAccount.invalid && earningsGlAccount.touched" class="text-danger">
                Expense Account is required.
              </div>
            </div>
            
            <!-- Display Ledger Account Name Instead of Code -->
            
            <!-- *ngIf="account.   == GST on Expenses" -->
            <!-- <div class="mb-3">
              <label for="wi" class="form-label">WI (Reportable)</label>
              <select
                name="earningsWIReportable"
                class="form-control"
                id="wi"
                [(ngModel)]="earning.wiReportable"
              >
                <option value="">None</option>
              </select>
            </div> -->
            <div class="mb-3" style="margin-top: 20px; margin-left: 20px">
              <input type="checkbox" [(ngModel)]="earning.exemptFromPAYG" name="exemptFromPAYG" />
              <strong style="margin-left: 10px">Exempt from PAYG withholding</strong>
              <br />
              <input type="checkbox" [(ngModel)]="earning.exemptFromSuperannuation" name="exemptFromSuperannuation" />
              <strong style="margin-left: 10px">Exempt from Superannuation Guarantee Contribution</strong>
              <br />
              <input type="checkbox" [(ngModel)]="earning.wiReportable" name="wiReportable" />
              <strong style="margin-left: 10px">W1 Reportable</strong>
            </div>     
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="navigateToPayrollItems()">
                Cancel
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="earningForm.invalid" data-bs-dismiss="modal" (click)="navigateToPayrollItems()">
                Add
              </button>
            </div>

          </form>
        
          <!-- <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
              (click)="navigateToPayrollItems()"
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              (click)="onEarningAdd()"
            >
              Add
            </button>
          </div> -->
        </div>
      </div>
    </div>
  </div>


  <!-- Add -->

  <div
    class="modal fade"
    id="add-deductions"
    tabindex="-1"
    aria-labelledby="add-deductions"
    aria-hidden="true"
  >
    <div class="modal-dialog" >
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="add-deductions">Add Deduction</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form #deductionForm = 'ngForm' (ngSubmit)="addDeduction()" novalidate>
            <div class="mb-3">
              <label for="deduction-name" class="form-label"
                >Deduction Type</label
              >
              <input type="text" class="form-control" id="deduction-name" name="deduction-name" [(ngModel)]="deduction.deductionName" required
              #deductionName="ngModel"/>
              <div *ngIf="deductionName.invalid && deductionName.touched" class="text-danger">
                Deduction Type is required.
              </div>
            </div>

            <div class="mb-3">
              <label for="deduction-cat" class="form-label">Deduction Category</label>
              <select class="form-control" id="deduction-cat" name="deductionCategory" [(ngModel)]="deduction.deductionCategory"
                required #deductionCategoryModel="ngModel">
                <option value="" disabled selected>Select a Deduction Category</option>
                <option *ngFor="let category of deductionCategory" [value]="category">{{ category }}</option>
              </select>
              <div *ngIf="deductionCategoryModel.invalid && deductionCategoryModel.touched" class="text-danger">
                Deduction Category is required.
              </div>

            </div>

            <div class="mb-3">
              <label for="gl-account" class="form-label">GL Account</label>
              <select name="earningsGlAccount" class="form-control" id="gl-account" [(ngModel)]="deduction.glAccount" (ngModelChange)="onGlAccountChangeDeduction()" required>
                #glAccount="ngModel">
                <option value="" disabled selected>Select a GL Account</option>
                <option *ngFor="let account of glAccounts" [value]="account.ledgerAccountCode"> {{ account.ledgerAccountName }} : {{ account.ledgerAccountCode }}</option>
              </select>
              <div *ngIf="glAccount.invalid && glAccount.touched" class="text-danger">
                GL Account is required.
              </div>
            </div>
           
            <div class="mb-3" style="margin-top: 20px; margin-left: 20px">
              <input type="checkbox" name="wi-reportable" [(ngModel)]="deduction.wiReportable"/><strong style="margin-left: 10px">
                W1 (Reportable)</strong
              >
              <br />
              <!-- <input type="checkbox" name="exemptFromPAYG" [(ngModel)]="deduction.exemptFromPAYG"/><strong style="margin-left: 10px">
                Exempt from PAYG</strong
              > -->
              <input type="checkbox" name="exemptFromSuperannuation" [(ngModel)]="deduction.exemptFromSuperannuation"/><strong style="margin-left: 10px">
                Exempt from Superannuation</strong
              >
            </div>

            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                Cancel
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="deductionForm.invalid" data-bs-dismiss="modal" (click)="navigateToPayrollItems()">
                Add
              </button>
            </div>
          </form>
        </div>
        
      </div>
    </div>
  </div>

  <div
    class="modal fade"
    id="add-reimbursements"
    tabindex="-1"
    aria-labelledby="add-reimbursements"
    aria-hidden="true"
  >
    <div class="modal-dialog" >
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="add-reimbursements">
            Add Reimbursements Line
          </h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form #reimbursementForm="ngForm" (ngSubmit)="onReimbursementAdd()">
            <div class="mb-3">
              <label for="reimbursements_type" class="form-label"
                >Reimbursement Type</label
              >
              <input type="text" class="form-control" id="reimbursements_type" name="ReimbursementsType" [(ngModel)]="reimbursement.reimbursementType" required
              #reimbursementsTypeModel="ngModel"/>
              <div *ngIf="reimbursementsTypeModel.invalid && reimbursementsTypeModel.touched" class="text-danger">
                Reimbursement Type is required.
              </div>
            </div>
            <div class="mb-3">
              <label for="description" class="form-label">Description</label>
              <textarea class="form-control" id="description" rows="3" name="description" [(ngModel)]="reimbursement.description"
                required #descriptionModel="ngModel"></textarea>
              <div *ngIf="descriptionModel.invalid && descriptionModel.touched" class="text-danger">
                Description is required.
              </div>
            </div>

            <div class="mb-3">
              <label for="gl-account" class="form-label">GL Account</label>
             <select name="glAccount" class="form-control" id="gl-account" [(ngModel)]="reimbursement.glAccount" (ngModelChange)="onGlAccountChangeRem()" required
                #glAccount="ngModel">
                <option value="" disabled selected>Select a GL Account</option>
                <option *ngFor="let account of glAccounts" [value]="account.ledgerAccountCode"> {{ account.ledgerAccountName }} : {{ account.ledgerAccountCode }}</option>
              </select>
              <div *ngIf="glAccount.invalid && glAccount.touched" class="text-danger">
                GL Account is required.
              </div>
            </div>

            <!-- Form Buttons -->
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                Cancel
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="reimbursementForm.invalid" data-bs-dismiss="modal" (click)="navigateToPayrollItems()">
                Add
              </button>
            </div>
          </form>
        </div>
        <!-- <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary" (click)="onReimbursementAdd()">Add</button>
        </div> -->
      </div>
    </div>
  </div>

  <!--Leave type form modal -->
  <div id="myModal" class="modal fade" role="dialog">
    <div class="modal-dialog">
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">
            &times;
          </button>
          <h4 class="modal-title">Modal Header</h4>
        </div>
        <div class="modal-body">
          <p>Some text in the modal.</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>

  <!--Leave type form modal 02-->
  <div
    class="modal fade"
    id="add-leave-type-form-2"
    tabindex="-1"
    aria-labelledby="add-leave-type-form-2"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="add-leave-type1">Add Leave Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave" class="form-label">Leave</label>
              <input type="text" class="form-control" id="leave" />
            </div>
            <div class="mb-3">
              <label for="leave-cat" class="form-label">Leave Category</label>
              <input type="text" class="form-control" id="leave-cat" />
            </div>
            <div class="mb-3">
              <label for="leave-cal-method" class="form-label"
                >Leave Calculation Method</label
              >
              <select class="form-control" id="leave-cal-method">
                <option value="">Select</option>
              </select>
            </div>
            <div class="mb-3" style="display: flex; gap: 20px">
              <div style="width: 65%">
                <label for="hours-au-annually" class="form-label"
                  >Hours Accrued Annually</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="hours-au-annually"
                />
              </div>
              <div style="width: 30%">
                <label for="open-bal" class="form-label">Opening Balance</label>
                <input type="text" class="form-control" id="open-bal" />
              </div>
            </div>
            <div class="mb-3">
              <label for="hours" class="form-label">Hours</label>
              <input type="text" class="form-control" id="hours" />
            </div>
            <div class="mb-3">
              <label for="unused-balance" class="form-label"
                >On Termination Unused Balance is</label
              >
              <select class="form-control" id="unused-balance">
                <option value="">Select</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary">Add</button>
        </div>
      </div>
    </div>
  </div>

  <!--Leave accural form modal-->
  <div
    class="modal fade"
    id="add-leave-accural"
    tabindex="-1"
    aria-labelledby="add-leave-accural"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="add-leave-accural">Add Leave Accural</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave-type" class="form-label">Leave Type</label>
              <select class="form-control" id="leave-type">
                <option value="">Annual Leave</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="units-type" class="form-label">Type of Units</label>
              <input type="text" class="form-control" id="units-type" />
            </div>
            <div class="mb-3">
              <label for="leave-load-rate" class="form-label"
                >Leave Loading Rate</label
              >
              <select class="form-control" id="leave-load-rate">
                <option value="">Select</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="nom-ent" class="form-label">Normal Entitlement</label>
              <input type="text" class="form-control" id="nom-ent" />
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary">Add</button>
        </div>
      </div>
    </div>
  </div>

  <!--Edit Leave details-->
  <div class="modal fade" id="Leave-Edit" tabindex="-1" aria-labelledby="Leave-Edit"
    aria-hidden="true">
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="ot-modal-header">
          <button type="button" class="custom-close-btn" data-bs-dismiss="modal" style="margin-left: 95%"
            (click)="navigateToPayrollItems()">
            <i class="bi bi-x-circle"></i>
          </button>
          <i class="bi bi-person-workspace" style="font-size: 70px; color: #4262ff"></i>
          <div class="modal-header">
            <h5 class="modal-title" id="add_pay_calendar" style="text-align: center; margin-left: 30%;">
              Edit Leave Details
            </h5>
          </div>
        </div>
        <div class="modal-body">
          <form #earningForm="ngForm">
            <div class="mb-3">
              <label for="leave-name" class="form-label">Leave Name</label>
              <input name="leaveName" type="text" class="form-control" id="leaves-name"
                [(ngModel)]="editingLeave.leaveType" required #earningsName="ngModel" readonly/>
              <div *ngIf="earningsName.invalid && earningsName.touched" class="text-danger">
                Earning Type is required.
              </div>
            </div>
            <div class="mb-3">
              <label for="Category" class="form-label">Leave Category</label>
              <input name="leaveCategory" type="text" class="form-control" id="Category" [(ngModel)]="editingLeave.leaveCategory.leaveCategory" readonly
                required #earningsCategory="ngModel" />
              <div *ngIf="earningsCategory.invalid && earningsCategory.touched" class="text-danger">
                Earning Category is required.
              </div>
            </div>
            <!-- <div class="mb-3">
              <label for="unit-types" class="form-label">Units</label>
              <input name="leaveUnit" type="text" class="form-control" id="Units" 
                [(ngModel)]="editingLeave.leaveCategory.units" required #earningsCategory="ngModel"/>
              <div *ngIf="earningsTypeOfUnit.invalid && earningsTypeOfUnit.touched" class="text-danger">
                Payment Type is required.
              </div>
            </div> -->
            <div class="mb-3">
              <label for="normalEntitlement" class="form-label">Normal Entitlement</label>
              <input name="normalEntitlement" type="text" class="form-control" id="normalEntitlement" [(ngModel)]="editingLeave.leaveCategory.normalEntitlement" required
                pattern="^[0-9]+(\.[0-9]{1,2})?$" #earningsRate="ngModel" />
              <div *ngIf="earningsRate.invalid && earningsRate.touched" class="text-danger">
                Enter a valid rate (e.g., 100 or 100.50).
              </div>
            </div>

            <div class="mb-3">
              <label for="leaveLoadingRate" class="form-label">Leave Loading Rate</label>
              <input name="leaveLoadingRate" type="number" class="form-control" id="leaveLoadingRate"
                [(ngModel)]="editingLeave.leaveCategory.leaveLoadingRate" required pattern="^[0-9]+(\.[0-9]{1,2})?$"
                #earningsRate="ngModel" [disabled]="editingLeave.leaveCategory.leaveCategory !== 'Annual Leave'" />
              <div *ngIf="earningsRate.invalid && earningsRate.touched" class="text-danger">
                Enter a valid rate (e.g., 100 or 100.50).
              </div>
            </div>

            <div class="mb-3">
              <label for="showOnPayslip" class="form-label">Show on Payslip</label>
              <select name="showOnPayslip" class="form-control" id="showOnPayslip"
                [(ngModel)]="editingLeave.leaveCategory.showBalances" required>
                <option [ngValue]="true">Yes</option>
                <option [ngValue]="false">No</option>
              </select>
            </div>

  
          </form>
  
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="navigateToPayrollItems()">
              Cancel
            </button>
            <button type="button" class="btn btn-primary" (click)="updateLeaveList(editingLeave)" data-bs-dismiss="modal" (click)="navigateToPayrollItems()">
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

 

  <!--Assign leave type modal-->
  <div
    class="modal fade"
    id="assign-leave-type"
    tabindex="-1"
    aria-labelledby="assign-leave-type"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="assign-leave-type">Add Leave Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave-type" class="form-label">Leave Type</label>
              <input type="text" class="form-control" id="leave-type" />
            </div>
            <div class="mb-3">
              <label for="leave-cat" class="form-label">Leave Category</label>
              <input type="text" class="form-control" id="leave-cat" />
            </div>
            <div class="mb-3">
              <label for="leave-cal-method" class="form-label"
                >Leave Calculation Method</label
              >
              <select class="form-control" id="leave-cal-method">
                <option value="">Select</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="hours-au-annually" class="form-label"
                >Hours Accrued Annually by a F.T Employee</label
              >
              <input type="text" class="form-control" id="hours-au-annually" />
            </div>
            <div class="mb-3" style="display: flex; gap: 20px">
              <div style="width: 65%">
                <label for="hours-au-annually" class="form-label"
                  >Hours Accrued Annually</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="hours-au-annually"
                />
              </div>
              <div style="width: 30%">
                <label for="open-bal" class="form-label">Opening Balance</label>
                <input type="text" class="form-control" id="open-bal" />
              </div>
            </div>
            <div class="mb-3">
              <label for="unused-balance" class="form-label"
                >On Termination Unused Balance is</label
              >
              <select class="form-control" id="unused-balance">
                <option value="">Select</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button
            type="button"
            class="btn btn-primary"
            [class.active]="isActiveSide('Leave_Form_3')"
            (click)="setActiveSide('Leave_Form_3'); $event.preventDefault()"
            data-bs-dismiss="modal"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  </div>


  <!--Leave loading percentage modal-->
  <div
    class="modal fade"
    id="add-leave-loading-percentage"
    tabindex="-1"
    aria-labelledby="add-leave-loading-percentage"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="add-leave-loading-percentage">
            Add leave Loading Percentage
          </h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave-type" class="form-label">Leave Type</label>
              <select class="form-control" id="leave-type">
                <option value="">Select</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="leave-loading-percentage" class="form-label"
                >Leave loading Percentage</label
              >
              <input
                type="text"
                class="form-control"
                id="leave-loading-percentage"
              />
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button
            type="button"
            class="btn btn-primary"
            [class.active]="isActiveSide('Leave-form-final')"
            (click)="setActiveSide('Leave-form-final'); $event.preventDefault()"
            data-bs-dismiss="modal"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  </div>

  <!--Edit leave type modal-->
  <div
    class="modal fade"
    id="edit-leave-type"
    tabindex="-1"
    aria-labelledby="edit-leave-type"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="assign-leave-type">Edit Leave Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave-type" class="form-label">Leave Type</label>
              <input type="text" class="form-control" id="leave-type" />
            </div>
            <div class="mb-3">
              <label for="leave-cat" class="form-label">Leave Category</label>
              <input type="text" class="form-control" id="leave-cat" />
            </div>
            <div class="mb-3">
              <label for="leave-cal-method" class="form-label"
                >Leave Calculation Method</label
              >
              <select class="form-control" id="leave-cal-method">
                <option value="">Select</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="hours-au-annually" class="form-label"
                >Hours Accrued Annually by a F.T Employee</label
              >
              <input type="text" class="form-control" id="hours-au-annually" />
            </div>
            <div class="mb-3" style="display: flex; gap: 20px">
              <div style="width: 65%">
                <label for="hours-au-annually" class="form-label"
                  >Hours Accrued Annually</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="hours-au-annually"
                />
              </div>
              <div style="width: 30%">
                <label for="open-bal" class="form-label">Opening Balance</label>
                <input type="text" class="form-control" id="open-bal" />
              </div>
            </div>
            <div class="mb-3">
              <label for="unused-balance" class="form-label"
                >On Termination Unused Balance is</label
              >
              <select class="form-control" id="unused-balance">
                <option value="">Select</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary">Save</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Select Earning Type -->
  <div
    class="modal fade"
    id="modal-two"
    tabindex="-1"
    aria-labelledby="modalTitle"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 400px">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalTitle">Select Earning Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            &times;
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="form-check" *ngFor="let type of earningTypes">
              <input
                type="radio"
                class="form-check-input"
                [value]="type"
                name="earningType"
                [(ngModel)]="selectedEarningType"
              />
              <label class="form-check-label">{{ type }}</label>
            </div>
          </form>
        </div>
        <div
          class="modal-footer"
          style="display: flex; align-items: center; justify-content: center"
        >
          <button
            type="button"
            class="btn btn-primary"
            (click)="addEarningLine()"
            data-bs-dismiss="modal"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  </div>
<!-- 
  <!-
  <div
    class="modal fade"
    id="modal-leave"
    tabindex="-1"
    aria-labelledby="modalTitle"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 400px">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalTitle">Select Leave Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            &times;
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="form-check" *ngFor="let type of leaveTypes">
              <input
                type="radio"
                class="form-check-input"
                [value]="type"
                name="leaveType"
                [(ngModel)]="selectedLeaveType"
              />
              <label class="form-check-label">{{ type }}</label>
            </div>
          </form>
        </div>
        <div
          class="modal-footer"
          style="display: flex; align-items: center; justify-content: center"
        >
          <button
            type="button"
            class="btn btn-primary"
            (click)="addLeaveLine()"
            data-bs-dismiss="modal"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  </div> -->
