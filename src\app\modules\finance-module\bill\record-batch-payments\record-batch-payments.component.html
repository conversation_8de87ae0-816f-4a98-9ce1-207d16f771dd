<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
    <div class="header">
      <h3>List of Payment Records</h3>
    </div>
  
    <form #f="ngForm"   class="styled-form" novalidate>
  
      <div class="bd">
  
        <div class="form-section">
  
          <div class="form-row"
            style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
            <div class="form-group" style="display: flex; align-items: center; flex-grow: 1;">
              <label for="voucherNumber">Payment Reference No. :</label>
              <input id="voucherNumber" type="text" required [(ngModel)]="creditNoteBillHead.voucherNumber"
                name="voucherNumber"  #voucherNumber="ngModel" disabled />
            </div>
          </div>
  
  
          <div class="form-row" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
            <div class="form-group" style="display: flex; align-items: center; flex-grow: 1;">
              <label for="date">Document Date:</label>
              <div class="input-container">
                <input
                  matInput
                  [matDatepicker]="documentDatePicker"
                  id="date" 
                  required 
                  [(ngModel)]="creditNoteBillHead.date"
                  name="date" 
                />
                <mat-datepicker-toggle matSuffix [for]="documentDatePicker"></mat-datepicker-toggle>
              </div>
              <mat-datepicker #documentDatePicker></mat-datepicker>

              <div *ngIf="f.controls['date']?.touched && f.controls['date']?.invalid" class="text-danger">
                Date is required.
              </div>
            </div>
          </div>

          <div class="form-row" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
            <div class="form-group" style="display: flex; align-items: center; flex-grow: 1;">
              <label for="bankAccount" >Bank Account:</label>
           <select
    id="bankAccount"
    class="form-select"
    name="bankAccount"
    required
    [(ngModel)]="creditNoteBillHead.coaLedgerAccountId"
    (change)="onBankGLChange($event)"
    style="padding: 10px; border-radius: 5px; border: 1px solid #ccc;"
  >
    <option value="" disabled selected>Select Bank Account</option>
    <option *ngFor="let account of glAccounts" [value]="account.coaLedgerAccountId">
      {{ account.ledgerAccountName }} - {{ account.bankName }}
    </option>
  </select>
            </div>
          </div>
  
        </div>
  
        <div class="table-section">
          <div class="table-responsive">
            <table class="table">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Ref. Number</th>
                  <th>Date</th>
                  <th style="text-align: right;">Amount</th>
                  <th style="text-align: right;">Due Amount</th>
                  <!--<th>Account</th>-->
                  <th style="text-align: right;">Paid Amount</th>
                  <th style="text-align: right;">New Balance</th>
                </tr>
              </thead>
              <tbody>
                <!-- Loop through selected  -->
                <tr *ngFor="let expense of selectedApInvoices; let i = index">
                  <td>{{ i + 1 }}</td>
    
                  <!-- Ref Number -->
                  <td>
                    <input type="text" class="form-control1" required [(ngModel)]="expense.referenceNo"
                      name="referenceNo{{i}}" disabled />
                  </td>
    
                  <!--  Date -->
                  <td>
                    <input type="text" class="form-control" [ngModel]="expense.postingDate | date: 'dd-MM-yyyy'"
                      name="postingDate{{i}}" disabled />
                  </td>
    
                  <!-- Amount -->
                  <td>
                    <input type="text" class="form-control" [value]="expense.netAmount | currency:'USD':'symbol':'1.2-2'"
                      disabled />
                    <input type="hidden" [(ngModel)]="expense.netAmount" name="netAmount{{i}}" />
                  </td>
    
                  <!-- Balance -->
                  <td>
                    <input type="text" class="form-control"
                      [value]="expense.dueAmount ? (expense.dueAmount | currency:'USD':'symbol':'1.2-2') : '0.00'"
                      disabled />
                    <input type="hidden" [(ngModel)]="expense.dueAmount" name="dueAmount{{i}}" />
                  </td>

              
                  <!--<td>
                    <select
                      class="form-control"
                      name="coaLedgerAccountId-{{ i }}"
                      required
                      [(ngModel)]="expense.coaLedgerAccountId"
                      (change)="onGLChange($event, i)"
                    >
                      <option value="" disabled selected>GL Account</option>
                      <option 
                        *ngFor="let account of glAccountsMap[expense.apInvoiceHeadId] || []" 
                        [value]="account.coaLedgerAccountId"
                      >
                        {{ account.ledgerAccountName }}
                      </option>
                    </select>
                  </td>-->
                  
    
                  <!-- Credit Amount 
                  <td  [ngClass]="{ 'empty-input': !expense.creditAmount , 'filled-input': expense.creditAmount || expense.creditAmount == 0 }">
                    <input type="number" class="form-control" required [(ngModel)]="expense.creditAmount"
                      name="creditAmount{{i}}" (keydown)="preventEnter($event)" min="0"
                      [ngClass]="{ 'is-invalid': f.submitted && f.controls['creditAmount' + i].invalid }" />
                    <div *ngIf=" f.controls['creditAmount' + i]?.invalid"
                      class="text-danger">
                      Credit Amount is required.
                    </div>
                  </td>-->

                  <!-- Credit Amount -->
  <td  >
    <input 
      type="number" 
      class="form-control" 
      required 
      [(ngModel)]="expense.paidAmount"
      name="paidAmount{{i}}" 
      (keydown)="preventEnter($event)" 
      min="0"
      #paidAmount="ngModel"
      [ngClass]="{ 'is-invalid': f.submitted && paidAmount.invalid }" 
    />
    <div *ngIf="f.submitted && paidAmount.invalid" class="text-danger">
      Paid Amount is required.
    </div>
  </td>



    
                  <!-- New expense Balance -->
                  <td>
                    <input id="ExpenseBalance2" type="text" class="form-control"
                      [value]="getExpenseNewBalance(expense) | currency:'USD':'symbol':'1.2-2'" disabled />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
  
        <div class="form-section_2">
          <div class="form-row"
            style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px; ">
            <div class="form-group_2">
              <label for="totalPaidAmount">Total Credit Amount</label>
              <input id="totalPaidAmount" type="text" class="form-control"
                [value]="getTotalPaidAmount() | currency:'USD':'symbol':'1.2-2'" disabled />
            </div>
  
          </div>
  
          <div class="form-row"
            style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px; ">
            <div class="form-group_2">
              <label for="remarks">Remarks:</label>
              <textarea id="remarks" required [(ngModel)]="creditNoteBillHead.remarks" name="remarks"
                rows="3"></textarea>
            </div>
          </div>
  
  
          <div class="d-flex justify-content-end mt-5 mb-4 btns" style="background-color: transparent;">
            <button type="button" class="btn btn-secondary me-2" (click)="onCancel()">Cancel</button>
            <button type="submit" class="btn btn-primary" (click)="onSubmit(f)">Save Payment</button>
          </div>
        </div>
      </div>
        
    </form>
  </div>