import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { HttpService } from 'src/app/http.service';
import { UserService } from '../user.service';
import { ViewportScroller } from '@angular/common';
import { AuthService } from 'src/app/auth.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-user-login',
  templateUrl: './user-login.component.html',
  styleUrls: ['./user-login.component.css'],
})
export class UserLoginComponent implements OnInit {
  username: string = '';
  password: string = '';
  errorMessage: string = '';
  isPasswordVisible: boolean = false;
  isLoggingIn: boolean = false;

  maxLoginAttempts = 3;
  loginAttemptsKey = 'login-attempts';
  captchaToken: string = '';
  siteKey: string = environment.recaptchaSiteKey;

  constructor(
    private router: Router,
    private httpService: HttpService,
    private userService: UserService,
    private viewportScroller: ViewportScroller,
    private route: ActivatedRoute,
    private authService: AuthService
  ) {}

  ngOnInit(): void {}

  get failedAttempts(): number {
    return Number(localStorage.getItem(this.loginAttemptsKey)) || 0;
  }

  get showCaptcha(): boolean {
    return this.failedAttempts >= this.maxLoginAttempts;
  }

  onCaptchaResolved(token: string) {
    this.captchaToken = token;
  }

  onSubmitLogin(event: Event): void {
    event.preventDefault();

     if (!this.username || !this.password) {
    this.errorMessage = 'Please enter email and password';
    return;
  }


    // CAPTCHA required after 3 failed attempts
    if (this.showCaptcha && !this.captchaToken) {
      this.errorMessage = 'Please complete the CAPTCHA.';
      return;
    }

    this.isLoggingIn = true;
    this.loginUser({ username: this.username, password: this.password });
  }

  loginUser(credentials: { username: string; password: string }): void {
    const selectedEntityId = Number(this.route.snapshot.queryParamMap.get('entityId'));

    const loginPayload: any = {
      username: credentials.username,
      password: credentials.password,
    };

    if (selectedEntityId) {
      loginPayload.entityId = selectedEntityId;
    }

    this.username = credentials.username;

    this.httpService.request('POST', '/login', loginPayload).subscribe(
      (response) => {
        this.userService.isUserVerified(this.username).subscribe((data) => {
          if (data) {
            if (selectedEntityId && !response.entityIds.includes(selectedEntityId)) {
              this.errorMessage = 'Selected entity is not assigned to this user.';
              return;
            }

            if (selectedEntityId) {
              response.entityIds = [selectedEntityId];
            }

            this.storeUserData(response, !!selectedEntityId);
            this.errorMessage = '';
            localStorage.removeItem(this.loginAttemptsKey); // reset on success
            this.isLoggingIn = false;
          } else {
            this.errorMessage = 'Please verify your email address.';
            this.isLoggingIn = false;
          }
        });
      },
      (error) => {
        this.handleLoginError(error);
        this.isLoggingIn = false;
      }
    );
  }

  storeUserData(response: any, skipNavigation: boolean): void {
    this.authService.setToken(response.token);
    localStorage.setItem('user', JSON.stringify(response));
    localStorage.setItem('userid', String(response.id));
    localStorage.setItem('firstName', response.firstName);
    localStorage.setItem('lastName', response.lastName);
    localStorage.setItem('userUuid', response.userUuid);
    localStorage.setItem('userTypeId', String(response.userTypeId.userTypeId));

    const entityId = response.entityIds[0];
    localStorage.setItem('entityId', String(entityId));

    this.httpService.request('GET', `/business-entity/uuid/${entityId}`, null).subscribe({
      next: (uuidResponse: any) => {
        if (uuidResponse && uuidResponse.entityUuid) {
          localStorage.setItem('entityUuid', uuidResponse.entityUuid);
        }
      },
      error: (err) => {
        console.error('Failed to fetch entity UUID:', err);
      }
    });

    if (!skipNavigation) {
      this.userNavigation(response.userTypeId.userTypeId);
    } else {
      this.router.navigate(['/sales-dashboard']);
    }
  }

  handleLoginError(error: any): void {
    this.httpService.setAuthToken(null);
    console.error('Login Failed:', error);

    let attempts = this.failedAttempts + 1;
    localStorage.setItem(this.loginAttemptsKey, attempts.toString());

    if (error.status === 401) {
      this.errorMessage = 'User has no role in Azure AD';
    } else {
      this.errorMessage =
        error.error?.message === 'Unknown user' ||
        error.error?.message === 'Invalid password'
          ? error.error.message
          : 'Invalid username or password';
    }
  }

  navigateUserRegistration(): void {
    this.router.navigate(['/create-user']);
  }

  navigateForgotPassword(): void {
    this.router.navigate(['/forgot-password']);
  }

  userNavigation(userTypeId: number): void {
    this.userService.getUserTypeById(userTypeId).subscribe((response) => {
      const navigationRoute =
        response.userType === 'System Admin'
          ? '/country'
          : response.userType === 'Accountant'
          ? '/select-entity'
          : '/sales-dashboard';
      this.router.navigate([navigationRoute]);
    });
  }

  scrollToPricing() {
    if (this.router.url === '/home') {
      setTimeout(() => {
        this.viewportScroller.scrollToAnchor('pricing');
      }, 0);
    } else {
      this.router.navigate(['/home'], { fragment: 'pricing' }).then(() => {
        setTimeout(() => {
          this.viewportScroller.scrollToAnchor('pricing');
        }, 300);
      });
    }
  }
}
