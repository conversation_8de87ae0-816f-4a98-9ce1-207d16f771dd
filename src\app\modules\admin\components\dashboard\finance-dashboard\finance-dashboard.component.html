<app-finance-navbar></app-finance-navbar>

<div class="container">
<h2>Dashboard</h2>
<div class="card-wrapper">
<div class="card read-only-card">
  <h3>Bank</h3>
  <div class="divider"></div>
  
<table class="table-responsive table">
  <thead>
    <tr>
      <th style="color: black;">Business Bank Account</th>
      <th style="color: black; ">Balance in Bank</th>
      <th style="color: black; ">Balance in Ledger Chimp</th>
    </tr>
  </thead>

  <tbody>
    <tr *ngFor="let bankAccount of filteredBankAccounts; let i = index">
      <td class="value">{{ bankAccount.accountNumber }}</td>
      <td class="value text-end">{{ bankAccount.balance | currency:'USD':'symbol':'1.0-0' }}</td>
      <td class="value text-end">{{ bankAccount.systemBalance | currency:'USD':'symbol':'1.0-0' }}</td>
    </tr>
  </tbody>
</table>

</div>


    <div class="card read-only-card">
    <h3>GST Summary</h3>
    <div class="divider"></div>
    <table class="table-responsive">
        <tbody>
        <tr>
            <td>GST Payable</td><td style="text-align: right;">${{gstCollected | number: '1.2-2'}}</td>
           <!-- <td>Period : {{fromDate}} to {{toDate}}</td>-->
        </tr>
        <tr>
            <td>Claimable</td><td style="text-align: right;">${{gstPaid | number: '1.2-2'}}</td>
            <td></td>
        </tr>
        <br>
        <tr>
            <td><strong>Total GST Payable</strong></td>
        </tr>
        <tr>
            <td style="color: #4262ff;">${{gstPayable | number: '1.2-2'}}</td>
        </tr>
        </tbody>
    </table>
    </div>
    </div>

<div class="card-wrapper">
    <div class="card" style="margin-bottom: 0%;">
    <h3>Bills Owed To</h3>
    <div class="divider"></div>
    <table class="table-responsive">
        <tbody>
        <tr>
            <td>{{ billData.pending.count }}</td>
            <td>Pending Bills</td>
            <td style="text-align: right;">{{ billData.pending.amount | currency:'USD':'symbol':'1.0-0' }}</td>
        </tr>
        <tr>
            <td>{{ billData.paid.count }}</td>
            <td>Paid Bills</td>
            <td style="text-align: right;">{{ billData.paid.amount | currency:'USD':'symbol':'1.0-0' }}</td>
        </tr>
        <tr>
            <td>{{ billData.overdue.count }}</td>
            <td>Overdue Bills</td>
            <td style="text-align: right;">{{ billData.overdue.amount | currency:'USD':'symbol':'1.0-0' }}</td>
        </tr>
        <tr>
            <td>{{ billData.canceled.count }}</td>
            <td>Canceled Bills</td>
            <td style="text-align: right;">{{ billData.canceled.amount | currency:'USD':'symbol':'1.0-0' }}</td>
        </tr>
        </tbody>
    </table>
    </div>


    
    <div class="card" style="margin-bottom: 0%;">
    <h3>Expenses</h3>
    <div class="divider"></div>
    <table class="table-responsive">
        <tbody>
        <tr>
            <td>{{ ExpenceData.pending.count }}</td>
            <td>Pending Expenses</td>
            <td style="text-align: right;">{{ ExpenceData.pending.amount | currency:'USD':'symbol':'1.0-0' }}</td>
        </tr>
        <tr>
            <td>{{ ExpenceData.overdue.count }}</td>
            <td>Overdue Expenses</td>
            <td style="text-align: right;">{{ ExpenceData.overdue.amount | currency:'USD':'symbol':'1.0-0' }}</td>
        </tr>
        <tr>
            <td>{{ ExpenceData.canceled.count }}</td>
            <td>Canceled Expenses</td>
            <td style="text-align: right;">{{ ExpenceData.canceled.amount | currency:'USD':'symbol':'1.0-0' }}</td>
        </tr>
        </tbody>
    </table>
    </div>
     
    <div class="card">
      <h3>Invoices Owed To</h3>
      <div class="divider"></div>
      <table class="table-responsive">
        <tbody>
          <tr>
            <td>{{ invoiceData.awaiting.count }}</td>
            <td>Awaiting payment</td>
            <td style="text-align: right;">{{ invoiceData.awaiting.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.overdue.count }}</td>
            <td>Overdue</td>
            <td style="text-align: right;">{{ invoiceData.overdue.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.paid.count }}</td>
            <td>Paid invoices</td>
            <td style="text-align: right;">{{ invoiceData.paid.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.pending.count }}</td>
            <td>Pending</td>
            <td style="text-align: right;">{{ invoiceData.pending.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.revised.count }}</td>
            <td>Revised</td>
            <td style="text-align: right;">{{ invoiceData.revised.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.canceled.count }}</td>
            <td>Canceled</td>
            <td style="text-align: right;">{{ invoiceData.canceled.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.sent.count }}</td>
            <td>Sent</td>
            <td style="text-align: right;">{{ invoiceData.sent.amount | number: '1.2-2' }}</td>
          </tr>
          
        </tbody>
      </table>

  
    <div ></div>

    </div>

    <div class="card">
      <h3>Quotes</h3>
      <div class="divider"></div>
      <table class="table-responsive">
        <tbody>
          <tr>
            <td>{{ quoteData.pending.count }}</td>
            <td>Pending</td>
            <td style="text-align: right;">{{ quoteData.pending.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ quoteData.sent.count }}</td>
            <td>Sent</td>
            <td style="text-align: right;">{{ quoteData.sent.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ quoteData.revised.count }}</td>
            <td>Revised</td>
            <td style="text-align: right;">{{ quoteData.revised.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ quoteData.expired.count }}</td>
            <td>Expired</td>
            <td style="text-align: right;">{{ quoteData.expired.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ quoteData.toInvoice.count }}</td>
            <td>To Invoice</td>
            <td style="text-align: right;">{{ quoteData.toInvoice.amount | number: '1.2-2' }}</td>
          </tr>
        </tbody>
      </table>

  </div>

</div>

<div class="card">
    <h3>Income & Expense Tracker</h3>
    <div class="divider"></div>
    <table class="table-responsive">
    <thead>
        <tr>
        <th></th>
        <th>Year to Date</th>
        <th>Quarter to Date</th>
        <th>Month to Date</th>
        </tr>
    </thead>
    <tbody>
        <tr>
        <td>Income</td>
        <td></td>
        <td></td>
        <td></td>
        </tr>
        <tr>
        <td>Top 6 Expenses</td>
        <td></td>
        <td></td>
        <td></td>
        </tr>
        <tr>
        <td>Top 6 Expenses</td>
        <td></td>
        <td></td>
        <td></td>
        </tr>
        <tr>
        <td>Top 6 Expenses</td>
        <td></td>
        <td></td>
        <td></td>
        </tr>
        <tr>
        <td>Top 6 Expenses</td>
        <td></td>
        <td></td>
        <td></td>
        </tr>
        <tr>
        <td>Top 6 Expenses</td>
        <td></td>
        <td></td>
        <td></td>
        </tr>
        <tr>
        <td>Top 6 Expenses</td>
        <td></td>
        <td></td>
        <td></td>
        </tr>
    </tbody>
    </table>
</div>

