export class GlAccount {
  ledgerAccountName: string = '';
  ledgerAccountCode: string = '';
  coaHeaderId: CoaHeaders = new CoaHeaders();

}

export class CoaHeaders {
    coaHeaderId: any = 0;
    templateId: number = 0;
    pAccount: string = '';
    accountHeaderName: string = '';
    accountHeaderDescription: string = '';
    accountHeaderType: string = '';
    series: string = '';
    entityId: number = 0;
  }
  
  export class CoaLedgerAccount {
    coaLedgerAccountId: any = 0;
    coaHeaderId: CoaHeaders = new CoaHeaders();
    ledgerAccountName: string = '';
    ledgerAccountCode: string = '';
    ledgerAccountDescription: string = '';
    taxAccount: string = '';
    status: string = '';
    defaultTaxCode: string = '';
    entityId: number = 0;
    bankName: string ='';
    bankAccountId: number = 0;
  }
  