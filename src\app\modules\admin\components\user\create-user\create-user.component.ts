import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { HttpService } from 'src/app/http.service';
import { User, UserType } from '../user';
import { Entity, InviteLog } from '../../../../entity/entity';
import { EntityService } from 'src/app/modules/entity/entity.service';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  Subscription,
  SubscriptionFee,
} from 'src/app/modules/subscription/subscription';
import { SubscriptionService } from 'src/app/modules/subscription/subscription.service';
import { HomeService } from 'src/app/home/<USER>/home.service';
import { UserService } from '../user.service';
import { CountryService } from 'src/app/modules/entity/country/country.service';
import { Country } from 'src/app/modules/entity/country/country';
import { environment } from 'src/environments/environment';
import { COAService } from 'src/app/modules/finance-module/chart-of-accounts/coa.service';
import { FiscalPeriodService } from 'src/app/modules/finance-module/fiscal-period/fiscal-period.service';
import { NgxImageCompressService } from 'ngx-image-compress';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';

@Component({
  selector: 'app-create-user',
  templateUrl: './create-user.component.html',
  styleUrls: ['./create-user.component.css'],
})
export class CreateUserComponent implements OnInit {
  showUserRegistration = true;
  entityName: string = '';
  gstInformation: boolean = false;
  userForm: FormGroup;
  selectedSubscriptionFeeId: number | null = null;
  user: User = new User();
  userType: UserType = new UserType();
  entity: Entity = new Entity();
  subscription: Subscription = new Subscription();
  subscriptionFee: SubscriptionFee | undefined = new SubscriptionFee();
  country: Country = new Country();
  addressValidationMessage: string | undefined;
  abnValidationMessage: string | undefined;
  structureValidationMessage: string | undefined;
  isAddressValid: boolean | undefined;
  isAbnValid: boolean | undefined;
  isStructureValid: boolean | undefined;
  isUsernameExits: boolean = false;
  usernameExistsMessage: string = '';
  suggestedAddresses: any[] = [];
  selectedFile: File | null = null;
  url = '';
  isEntityInvitation: boolean = false;
  inviteLog: InviteLog = new InviteLog();
  planName: string = '';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private httpService: HttpService,
    private http: HttpClient,
    private userService: UserService,
    private entityService: EntityService,
    private subscriptionService: SubscriptionService,
    private countryService: CountryService,
    private coaService: COAService,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private fiscalPeriodService: FiscalPeriodService,
    private imageCompress: NgxImageCompressService,
    private swalAlerts: SwalAlertsService
  ) {
    this.userForm = this.fb.group(
      {
        firstName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z0-9 ]*$')],
        ],
        lastName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z0-9 ]*$')],
        ],
        email: ['', [Validators.required, this.customEmailValidator()]],
        password: [
          '',
          [
            Validators.required,
            Validators.pattern(
              '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()_+\\-=\\[\\]{};\'":\\\\|,.<>\\/?`~])[A-Za-z\\d!@#$%^&*()_+\\-=\\[\\]{};\'":\\\\|,.<>\\/?`~]{8,}$'
            ),
          ],
        ],
        rePassword: ['', Validators.required],
      },
      { validator: this.passwordMatchValidator }
    );
  }

  isPasswordVisible: boolean = false;

  passwordMatchValidator(form: FormGroup) {
    return form.get('password')?.value === form.get('rePassword')?.value
      ? null
      : { mismatch: true };
  }

  customEmailValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
      const isValid = emailRegex.test(control.value);
      return isValid ? null : { customEmail: true };
    };
  }

  ngOnInit() {
    localStorage.clear();
    (window as any).callback = (response: any) => this.handleCallback(response);

    this.userService
      .getUserTypeByUserType('Primary user')
      .subscribe((response) => (this.userType = response));

    this.countryService
      .getCountryByName('Australia')
      .subscribe((response) => (this.country = response));

    this.route.queryParams.subscribe((params) => {
      this.isEntityInvitation = params.hasOwnProperty('inviteLogId');
      if (this.isEntityInvitation) {
        this.entityService
          .getEntityInviteLogById(Number(params['inviteLogId']))
          .subscribe((data) => {
            this.inviteLog = data;
            this.user.username = data.email;
          });
      }
      this.selectedSubscriptionFeeId = Number(params['subscriptionPlanId']);
      this.planName = params['planName'];
    });
  }

  navigateToUserRegistration(): void {
    this.showUserRegistration = true;
    if (this.selectedFile) {
      this.url = this.url; 
    }
  }

  navigateToEntityRegistration(event: Event): void {
    event.preventDefault();
    if (this.userForm.invalid) {
      this.markFormGroupTouched(this.userForm);
      return;
    }

    // Check if user exists before proceeding
    const email = this.userForm.get('email')?.value;
    if (email) {
      this.userService.checkUser(email).subscribe((response) => {
        if (response) {
          this.isUsernameExits = true;
          this.usernameExistsMessage =
            'Email is already in use. Please choose another.';
          return;
        } else {
          this.isUsernameExits = false;
          this.usernameExistsMessage = '';
          this.showUserRegistration = false;
        }
      });
    } else {
      this.showUserRegistration = false;
    }
  }

  navigateToPreviousStep(): void {
    const subscriptionPlanId = this.route.snapshot.queryParams['subscriptionPlanId'];
    this.router.navigate(['/user-agreement'], { queryParams: { subscriptionPlanId } });
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      control.markAsDirty();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  showGSTRegistrationInfo() {
    this.gstInformation = true;
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (e: any) => (this.url = e.target.result);
    }
  }

  isSubmitting: boolean = false;

  async onSubmitRegister(): Promise<void> {
    if (!this.entity.abn || this.entity.abn.trim() === '') {
      this.isAbnValid = false;
      this.abnValidationMessage = 'Please enter a valid ABN.';
      this.entity.entityName = '';
      return;
    } else {
      this.isAbnValid = true;
      this.abnValidationMessage = '';
    }

    if (!this.entity.entityName) {
      this.isAbnValid = false;
      this.abnValidationMessage = 'Please look up the the ABN.';
      this.entity.entityName = '';
      return;
    } else {
      this.isAbnValid = true;
      this.abnValidationMessage = '';
    }

    if (!this.entity.businessStructure) {
      this.isStructureValid = false;
      this.structureValidationMessage = 'Please select the Business Structure.';
      this.entity.businessStructure = '';
      return;
    } else {
      this.isStructureValid = true;
      this.structureValidationMessage = '';
    }

    if (!this.entity.businessAddress) {
      this.isAddressValid = false;
      this.addressValidationMessage = 'Please enter a Business address.';
      this.entity.businessAddress = '';
      return;
    } else {
      this.isAddressValid = true;
      this.addressValidationMessage = '';
    }

    if (!this.isAbnValid || !this.isAddressValid) {
      return;
    }

    this.isSubmitting = true;

    try {
      await this.onEntityRegister();
      await this.delay(500);
     
      const userRegistered = await this.onUserRegister(); // save user
     if (!userRegistered) {
      this.swalAlerts.showErrorWithChimpSupport(
        'User registration failed.',
        'User registration failed while setting up your account.'
      );
      throw new Error('User registration failed. Aborting.');
    }
      await this.setLocalStorage();
      if (this.selectedSubscriptionFeeId == 1) {
        await this.setSubscription();
      }
    } catch (error) {
      console.error(error);
    } finally {
      this.isSubmitting = false;
    }
  }

  onAbnChange(abn: string) {
  this.entity.entityName = '';
  this.abnValidationMessage = '';
  this.isAbnValid = undefined; 
  
}


  delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async onUserRegister(): Promise<boolean> {
    this.user.entityIds = [this.entity.entityId];
    this.user.userTypeId = this.userType;

   // Check if subscriptionPlanId is already declared, and just assign its value.
  const subscriptionPlanIdFromParams = this.route.snapshot.queryParams['subscriptionPlanId'];

  // Add it to the user object
  this.user.subscriptionPlanId = Number(subscriptionPlanIdFromParams); // Convert to number if needed

    try {
      const response = await this.userService.register(this.user).toPromise();
      localStorage.setItem('userid', response.id + '');
      localStorage.setItem('entityId', response.entityIds[0]);
      this.user = response;
      this.userService.verifyUser(this.user.username).subscribe();
      this.router.navigate(['/create-entity'], {
      queryParams: { subscriptionPlanId: subscriptionPlanIdFromParams },
    });
    return true;
    } catch (error) {
      this.userService.setAuthToken(null);
      console.error(error);
     return false;
    }

  }

  async onEntityRegister(): Promise<void> {
    this.entity.abn = this.entity.abn.split(' ').join('');
    this.entity.countryId = this.country;
    this.entity.taxApplicability = this.gstInformation ? 'yes' : 'no';

    try {
      let fileToUpload: File | null = this.selectedFile;

      if (!fileToUpload) {
        const fileBlob = await this.loadImageFromAssets(
          'assets/images/Ledger_Chimp - icon_2.png'
        );
        fileToUpload = new File([fileBlob], 'Ledger_Chimp - icon_2.png', {
          type: 'image/png',
        });
      } else {
        // Compress the image before upload
        const reader = new FileReader();
        const base64: string = await new Promise((resolve, reject) => {
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(fileToUpload!);
        });

        // console.log('Original Base64:', base64); // Log original base64
        // console.log('Original File Size (MB):', (fileToUpload.size / (1024 * 1024)).toFixed(2)); // Log original file size in MB

        const compressedBase64 = await this.imageCompress.compressFile(base64, -1, 50, 50);
        // console.log('Compressed Base64:', compressedBase64); // Log compressed base64

        const arr = compressedBase64.split(',');
        const mime = arr[0].match(/:(.*?);/)![1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        fileToUpload = new File([u8arr], fileToUpload.name, { type: mime });

        // console.log('Compressed File:', fileToUpload); // Log compressed file details
        // console.log('Compressed File Size (MB):', (fileToUpload.size / (1024 * 1024)).toFixed(2)); // Log compressed file size in MB

        if (fileToUpload.size > fileToUpload.size) {
          console.warn('Compressed file size is larger than the original file size. Review compression logic.');
        }
      }

      const response = await this.entityService
        .saveEntity(this.entity, fileToUpload)
        .toPromise();
      // console.log('Entity Save Response:', response); // Log response from saveEntity
      this.entity = response;

      if (this.isEntityInvitation) {
        this.inviteLog.entityId = response.entityId;
        this.entityService
          .updateEntityInviteLog(this.inviteLog.inviteLogId, this.inviteLog)
          .subscribe((data) => {
            // console.log('Invite Log Update Response:', data); // Log response from updateEntityInviteLog
            this.userService
              .addUserToEntity(data.userId.userId, data.entityId)
              .subscribe((data) => {
              });
          });
      }

      if (this.gstInformation) {
        this.coaService
          .createCoaForEntity(1, response.entityId)
          .subscribe((response) => {
            console.log(response);
          });
      } else {
        this.coaService
          .createCoaForEntity(2, response.entityId)
          .subscribe((response) => {
            console.log(response);
          });
      }

      this.fiscalPeriodService
        .createFiscalPeriodForEntity(response.entityId)
        .subscribe((response) => {
          console.log(response);
        });
    } catch (error) {
      this.httpService.setAuthToken(null);
      console.error(error);
    }
  }

  private loadImageFromAssets(imagePath: string): Promise<Blob> {
    return fetch(imagePath)
      .then((response) => response.blob())
      .catch((err) => {
        console.error('Error fetching image from assets:', err);
        throw err;
      });
  }

  async setSubscription(): Promise<void> {
    try {
      this.subscriptionFee = await this.subscriptionService
        .getSubscriptionFeeById(this.selectedSubscriptionFeeId!)
        .toPromise();

      this.subscription.entityId = this.entity;
      this.subscription.subscriptionFeeId = this.subscriptionFee;

      const data = await this.subscriptionService
        .saveSubscription(this.subscription)
        .toPromise();
      const subscriptionPlanId =
        this.route.snapshot.queryParams['subscriptionPlanId'];
      this.router.navigate(['/create-entity'], {
        queryParams: { subscriptionPlanId },
      });
    } catch (error) {
      console.error(error);
    }
  }

  async setLocalStorage(): Promise<void> {
    try {
      localStorage.setItem('user', this.user.username + '');
      localStorage.setItem('firstName', this.user.firstName);
      localStorage.setItem('lastName', this.user.lastName);
      localStorage.setItem('entityId', this.entity.entityId.toString());
    } catch (error) {
      console.error(error);
    }
  }

  handleABNlookup() {
    const abn = this.entity.abn.split(' ').join('');
    const url = `${environment.abnLookUpBaseUrl}?abn=${abn}&callback=callback&guid=${environment.abnLookUpGuid}`;

    if (!this.entity.abn || this.entity.abn.trim() === '') {
      this.isAbnValid = false;
      this.abnValidationMessage = 'Please enter a valid ABN.';
      this.entity.entityName = '';
      return;
    } else {
      this.entityService.checkAbnExists(abn).subscribe((response) => {
        if (response) {
          this.isAbnValid = false;
          this.abnValidationMessage =
            'This entity has already been registered.';
          this.entity.entityName = '';
          return;
        } else {
          this.isAbnValid = true;
          const script = document.createElement('script');
          script.src = url;
          document.body.appendChild(script);
        }
      });
    }
  }

  handleCallback(response: any) {
    if (response.AbnStatus === 'Active') {
      if (
        response.Message ===
        'The GUID entered is not recognised as a Registered Party'
      ) {
        console.error('Invalid GUID');
        this.entityName = 'Error fetching entity name';
        this.isAbnValid = false;
      } else {
        this.entity.entityName = response.EntityName;
        this.isAbnValid = true;
        this.abnValidationMessage = '';
      }
    } else {
      this.isAbnValid = false;
      this.abnValidationMessage = 'Not a valid ABN';
      this.entity.entityName = '';
    }
    this.cdr.detectChanges();
  }

  checkBusinessAddress() {
    const query = this.entity.businessAddress
      ? this.entity.businessAddress.trim()
      : '';
    const apiUrl = `${environment.addressfinderBaseUrl}?key=${
      environment.addressFinderApiKey
    }&q=${encodeURIComponent(query)}&format=json&source=gnaf%2Cpaf`;

    if (!query) {
      this.isAddressValid = false;
      this.addressValidationMessage = 'Please enter a business address.';
      this.suggestedAddresses = [];
      return;
    }

    this.http.get(apiUrl).subscribe(
      (response: any) => {
        if (response && response.completions) {
          this.suggestedAddresses = response.completions;
        } else {
          this.suggestedAddresses = [];
        }
        this.isAddressValid = true;
        this.addressValidationMessage = '';
      },
      (error) => {
        // this.isAddressValid = false;
        // this.addressValidationMessage = 'Error occurred. Try again';
        console.error('Error fetching address suggestions:', error);
      }
    );
  }

  selectAddress(address: any) {
    this.entity.businessAddress = address.full_address;
    this.suggestedAddresses = [];
    this.isAddressValid = true;
    this.addressValidationMessage = '';
  }

  checkUser() {
    // Reset the error message when typing
    this.isUsernameExits = false;
    this.usernameExistsMessage = '';

    if (this.userForm.get('email')?.valid) {
      const email = this.userForm.get('email')?.value;
      this.userService.checkUser(email).subscribe((response) => {
        if (response) {
          this.isUsernameExits = true;
          this.usernameExistsMessage =
            'Email is already in use. Please choose another.';
        }
      });
    }
  }

  getPasswordValidationMessage(): string {
    return 'Password must be at least 8 characters long and include uppercase, lowercase, number, and one of the following special characters: !@#$%^&*()_+-=[]{};\'":\\|,.<>/?`~';
  }
}
