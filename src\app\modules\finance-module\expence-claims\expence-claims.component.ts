import { Component, ViewChild } from '@angular/core';
import { Entity } from 'src/app/modules/entity/entity';
import { BusinessPartner, BusinessPartnerType } from 'src/app/modules/business-partner/business-partner';
import { HttpService } from 'src/app/http.service';
import { ExpenceClaimsService } from './expence-claims.service';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from 'src/app/modules/admin/components/user/user.service';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { NgForm } from '@angular/forms';
import { CoaLedgerAccount, OtherExpensesDetail, OtherExpensesHead } from './expence-claims';
import { BillService } from '../bill/bill.service';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';
import * as pdfjsLib from 'pdfjs-dist';
import { PeriodClosingService } from '../period-closing/period-closing.service';
import { DateAdapter } from '@angular/material/core';
(pdfjsLib as any).GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;


@Component({
  selector: 'app-expence-claims',
  templateUrl: './expence-claims.component.html',
  styleUrls: ['./expence-claims.component.css']
})
export class ExpenceClaimsComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  @ViewChild('canvas', { static: true }) canvasRef!: { nativeElement: HTMLCanvasElement };
  otherExpensesHeadData: OtherExpensesHead = new OtherExpensesHead();
  otherExpensesDetail: OtherExpensesDetail = new OtherExpensesDetail();
  businessEntity: Entity = new Entity();
  

  unitPriceInvalid: boolean = false;
  taxApplicable: boolean = false;
  businessPartner: BusinessPartner = new BusinessPartner();
  private audio!: HTMLAudioElement;
  lastReferenceNumber: string = '';
  businessEntityId: number = 0;
  selectedFile: File | null = null;
  url = '';
  isLoading: boolean = false;
  accountNumbers: string[] = ['********', '********', '********', '********'];
  selectedAccount: string = '';
  accounts: string[] = ['12', '87', '45', '10'];
  selectedAccountNumber: string = '';
  glAccounts :CoaLedgerAccount[] = [];
  selectedGlAccount: string | null = null;
  taxApplicabilityEnabled = true;
  defaultTaxRate = 0;
  previewImageSrc: string | null = null;

  constructor(
    private httpService: HttpService,
    private expenceClaimsService: ExpenceClaimsService,
    private entityService: EntityService,
     private billService: BillService,
    private router: Router,
    private route: ActivatedRoute,
    private userService: UserService,
    private periodClosingService: PeriodClosingService,
    private swalAlertService: SwalAlertsService,
    private dateAdapter: DateAdapter<Date>,
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
   }

  ngOnInit() {
    this.setTodayDate();
    this.addEmptyRows(1);
    this.fetchGlAccounts();
    this.getBusinessEntityById();
    
  }

  /**fetchGlAccounts(): void {
    this.expenceClaimsService.getAllCoaLedgerAccounts().subscribe((accounts) => {
      console.log('API Response:', accounts); // Log the API response
      this.glAccounts = accounts.map((account: { ledgerAccountName: any; }) => account.ledgerAccountName);
      console.log('Mapped GL Accounts:', this.glAccounts); // Log the mapped GL accounts
    });
  }**/


    
    fetchGlAccounts() {
      const entityId = +localStorage.getItem('entityId')!;
      this.billService.getActiveCoaLedgerAccountListByEntityBill(entityId).subscribe(
        (glAccounts: CoaLedgerAccount[]) => {
          this.glAccounts = glAccounts;
        },
        (error: HttpErrorResponse) => {
          console.error('Error fetching GL Accounts', error);
          Swal.fire({
            title: 'Error!',
            text: 'Failed to load GL Accounts.',
            icon: 'error',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#be0032',
            cancelButtonColor: '#007bff',
            showCancelButton: true,
          }).then((result) => {
            if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData('Failed to load gl Accounts.');
                    this.chatBotComponent.responseReceived.subscribe(response => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    });
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
        }
      );
      
      // Initialize ledgerAccountName for each item in apInvoiceDetails
      this.otherExpensesHeadData.otherExpensesDetails.forEach((detail) => {
        detail.ledgerAccountName = '';
      });
    }


    onGLChange(event: any, index: number) {
      const selectedAccountId = +event.target.value; // Get the selected ID
      const selectedAccount = this.glAccounts.find(
        (account) => account.coaLedgerAccountId === selectedAccountId
      ); // Find the selected account object
    
      if (selectedAccount) {
        // Update the corresponding detail with the selected account's information
        this.otherExpensesHeadData.otherExpensesDetails[index].coaLedgerAccountId = selectedAccount.coaLedgerAccountId;
        this.otherExpensesHeadData.otherExpensesDetails[index].ledgerAccountName = selectedAccount.ledgerAccountName;
      }
    }
    

  addEmptyRows(count: number) {
    for (let i = 0; i < count; i++) {
      this.otherExpensesHeadData.otherExpensesDetails.push({
        expensesNumber: this.otherExpensesHeadData.expensesNumber,
        expenseType:'',
        description: '',
        amount: 0,
        total_amount:0,
        gl_account:'',
        tax: 0,
        coaLedgerAccountId: 0,
        ledgerAccountName: ''
      });
    }
  }


  addNewRow() {
    this.otherExpensesHeadData.otherExpensesDetails.push({
      expensesNumber: this.otherExpensesHeadData.expensesNumber,
      expenseType:'',
        description: '',
        amount: 0,
        total_amount:0,
        gl_account:'',
        tax: 0,
        coaLedgerAccountId: 0,
        ledgerAccountName: ''
    });
  }


  onDescriptionInput(index: number) {
    if (index === this.otherExpensesHeadData.otherExpensesDetails.length - 1) {
      // this.addNewRow();
    }
  }

  calculateSubTotal() {
    let subTotal = 0;
    let totalTax = 0;

    this.otherExpensesHeadData.otherExpensesDetails.forEach((detail) => {
      const itemAmount = detail.amount;
      
      // Add tax if applicable
      if (detail.taxApplicability && this.taxApplicable) {
        const taxRate = this.businessEntity?.countryId.defaultTaxRate || 0; // Default tax rate
        detail.tax = itemAmount * (taxRate / 100); // Calculate tax
      } else {
        detail.tax = 0; // Reset tax if not applicable
      }

      subTotal += detail.amount; // Add item amount to subtotal
      totalTax += detail.tax; // Add tax to total tax
    });

    this.otherExpensesHeadData.grossAmount = subTotal;
    this.otherExpensesHeadData.totalGst = totalTax;

    this.calculateGrandTotal();
  }

  calculateGrandTotal() {
    const { grossAmount, totalGst } = this.otherExpensesHeadData;
    this.otherExpensesHeadData.netAmount = grossAmount + totalGst; // Total with tax
  }

  onTaxApplicableChange(index: number): void {
    const detail = this.otherExpensesHeadData.otherExpensesDetails[index];

    // Fetch entityId from localStorage
    const entityId = +(localStorage.getItem('entityId') || 0);

    if (entityId) {
      this.entityService.getBusinessEntityById(entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (this.taxApplicable && detail.taxApplicability) {
            detail.tax = detail.amount * (taxRate / 100); // Apply tax
          } else {
            detail.tax = 0; // Reset tax
          }

          this.calculateSubTotal(); // Recalculate totals
        },
        (error) => {
          console.error('Error fetching entity data:', error);
          detail.tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      detail.tax = 0;
      this.calculateSubTotal();
    }
  }

  updateAmount(index: number): void {
    const detail = this.otherExpensesHeadData.otherExpensesDetails[index];
    detail.total_amount = detail.amount; // Calculate base amount

    this.onTaxApplicableChange(index); // Re-evaluate tax based on applicability
  }

  updateQuantity(index: number) {
    this.updateAmount(index); // Ensure amount and tax are updated when quantity changes
  }

  removeItem(index: number) {
    this.otherExpensesHeadData.otherExpensesDetails.splice(index, 1); // Remove the item
    this.calculateSubTotal(); // Recalculate totals
  }

  

  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }


  setTodayDate() {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const dd = String(today.getDate()).padStart(2, '0');

    this.otherExpensesHeadData.spentOn = `${yyyy}-${mm}-${dd}`;
  
  }

  onExpensesDateChange() {  
        const entityId = +localStorage.getItem('entityId')!;
        const date = this.otherExpensesHeadData.spentOn;
      
        if (date) {
          this.periodClosingService.isDateLocked(entityId, date).subscribe({
            next: (isLocked: boolean) => {
              if (isLocked) {
                Swal.fire({
                  icon: 'error',
                  title: 'Posting Date is Locked',
                  text: 'The selected date falls within a closed accounting period. Please choose another date.',
                  confirmButtonColor: '#ff7e5f'
                });
      
                // Reset the posting date
              this.setTodayDate();
              }
            },
            error: (err) => {
              console.error('Error validating posting date lock', err);
            }
          });
        }
      }
      
      

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }


  onSubmit(f: NgForm) {
    // Check if the form is valid
    if (f.valid) {
        const hasInvalidRows = this.otherExpensesHeadData.otherExpensesDetails.some(
        (detail) =>
          !detail.description || detail.description.trim() === ''
      );

         const hasNoUnitPrice = this.otherExpensesHeadData.otherExpensesDetails.some(
         (detail) => detail.amount === 0
      );


    if (hasInvalidRows) {
      this.swalAlertService.showErrorWithChimpSupport(
         'Each row must have a description provided.',
          'Each row must have a description provided.'
         );
          return;
        }


         if (this.otherExpensesHeadData.otherExpensesDetails.some(detail => !detail.coaLedgerAccountId)) {
       this.swalAlertService.showErrorWithChimpSupport(
         'Each row must have a GL Account selected.',
          'GL Account is missing in one or more rows after scanning the bill.'
       );
        return; 
     }

    if (hasNoUnitPrice) {
      Swal.fire({
        title: 'Warning!',
        text: 'Selected item(s) have zero amount. Do you want to continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
        footer:
          '<a href="#" id="custom-link" style="background-color: #007bff; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">Ask Chimp</a>',
      }).then((result) => {
        if (result.isConfirmed) {
          // Proceed with saving or drafting
          this.saveExpenses();
        }
      });
      return;
    }


      if (hasInvalidRows) {
        // If any row does not have a description, show an error message
        Swal.fire({
          title: 'Error!',
          text: 'Each row must have a description provided.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
          cancelButtonText: 'Ask Chimp',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Each row must have a description provided.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return; // Prevent form submission
      }

     
      this.saveExpenses();
    }
  }

  
  saveExpenses() {
    this.otherExpensesHeadData.status = 'Pending';
    this.otherExpensesHeadData.balanceAmount = this.otherExpensesHeadData.netAmount;
    this.otherExpensesHeadData.userId = +((localStorage.getItem('userid')) + "");
    this.otherExpensesHeadData.entityId = +((localStorage.getItem('entityId')) + "");


    this.expenceClaimsService.saveExpenses(this.otherExpensesHeadData).subscribe(
      
      (response: any) => {
        this.updateBusinessEntityReferenceNumber();
        Swal.fire({
          title: 'Success!',
          text: 'The Expense has been Successfully saved.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/expence-claims-list']);
          }
        });
      },
      (error: HttpErrorResponse) => {
        console.error('Error saving Expense', error);
        Swal.fire({
          title: 'Error!',
          text: 'Unable to Save the Expense. Please try again',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Unable to Save the Expense. Please try again');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
    );
  }


  
     onCancel() {
    this.router.navigate(['/expence-claims-list']);
  }


  onFileSelected(event: any) {
    if (event.target.files && event.target.files[0]) {
      this.selectedFile = event.target.files[0];

      if (this.selectedFile) {
        const reader = new FileReader();

        reader.readAsDataURL(this.selectedFile);

        reader.onload = (event: ProgressEvent<FileReader>) => {
          const result = event.target?.result;
          if (typeof result === 'string') {
            this.url = result;
          }
        };
      }
    }
  }



 getBusinessEntityById() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '');
    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
      (data) => {
        this.businessEntity = data;
       this.taxApplicabilityEnabled = data.taxApplicability === 'yes';
       this.defaultTaxRate = data.countryId?.defaultTaxRate || 0; this.taxApplicabilityEnabled = data.taxApplicability === 'yes';
        this.lastReferenceNumber = this.incrementPaymentNumber(
          this.businessEntity.expensesNumber
        );
        this.otherExpensesHeadData.expensesNumber = this.lastReferenceNumber;
      },
      (error) => console.error(error)
    );
  }


  incrementPaymentNumber(expensesNumber: string): string {
    // If the expensesNumber  is null or empty, initialize it to 'B000001'
    if (!expensesNumber) {
      return 'E000001';
    }

    // Assuming the expensesNumber is in the format 'I000039'
    const prefix = expensesNumber.charAt(0); // Extract the 'I'
    const numericPart = expensesNumber.slice(1); // Extract the '000039'

    // Increment the numeric part and pad with leading zeros to maintain the format
    const incrementedNumber = (Number(numericPart) + 1).toString().padStart(numericPart.length, '0');

    // Combine the prefix and the incremented number
    return prefix + incrementedNumber;
  }


  updateBusinessEntityReferenceNumber() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '0');
    this.businessEntity.expensesNumber = this.otherExpensesHeadData.expensesNumber;
    this.entityService.updateExpensesNumber(this.businessEntity, this.businessEntityId).subscribe(
      (data) => {
      },
      (error) => {
        console.error(error);
      }
    );
  }

  onScanBillClick(fileInput: HTMLInputElement): void {
      fileInput.click();
    }
  
    fileSelected(event: Event): void {
        const input = event.target as HTMLInputElement;
        if (input.files && input.files.length > 0) {
          this.selectedFile = input.files[0];
    
          const fileType = this.selectedFile.type;
    
        if (fileType.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = (e) => {
            this.previewImageSrc = e.target?.result as string;
          };
          reader.readAsDataURL(this.selectedFile);
        } else if (fileType === 'application/pdf') {
          const reader = new FileReader();
          reader.onload = async (e) => {
            const typedarray = new Uint8Array(e.target!.result as ArrayBuffer);
            const pdf = await pdfjsLib.getDocument({ data: typedarray }).promise;
            const page = await pdf.getPage(1);
            const canvas = this.canvasRef.nativeElement;
            const context = canvas.getContext('2d')!;
            const viewport = page.getViewport({ scale: 1.5 });
            canvas.height = viewport.height;
            canvas.width = viewport.width;
            await page.render({ canvasContext: context, viewport }).promise;
    
            this.previewImageSrc = canvas.toDataURL('image/png');
          };
          reader.readAsArrayBuffer(this.selectedFile);
        } else {
          this.previewImageSrc = null; 
        }
    
          this.uploadImage();
        }
      }
    
    uploadImage(): void {
      if (this.selectedFile) {
        this.isLoading = true;
  
        this.billService.uploadImage(this.selectedFile).subscribe({
          next: (response: any[]) => {
            this.isLoading = false;
            this.populateExpenseDetails(response);
            this.calculateSubTotal();
          },
          error: (error) => {
            this.isLoading = false;
            console.error('Backend Error:', error);
  
            let errorMessage = 'Unknown processing error';
  
            if (error?.error) {
              if (typeof error.error === 'string') {
                errorMessage = error.error;
              } else if (error.error?.error) {
                errorMessage = error.error.error;
              } else if (error.error?.message) {
                errorMessage = error.error.message;
              }
            }
  
            Swal.fire({
              icon: 'error',
              title: 'Processing Error',
              text: errorMessage,
              confirmButtonColor: '#be0032',
            });
          },
        });
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No file selected.',
        });
      }
    }

    populateExpenseDetails(data: any[]): void {
    this.otherExpensesHeadData.otherExpensesDetails = [];

    data.forEach((item: any) => {
   
    this.otherExpensesHeadData.otherExpensesDetails.push({
      
      expenseType: item.expenseType || '',
      expensesNumber: item.expensesNumber || '',
      description: item.description || '',
      amount: item.unitPrice || 0.0,
      total_amount: item.amount || 0.0,
      tax: item.tax || 0.0,
      coaLedgerAccountId: item.coaLedgerAccountId || 0,
      ledgerAccountName: item.ledgerAccountName || '',
      gl_account: item.ledgerAccountCode || '',
    });

    });
  

  }
}