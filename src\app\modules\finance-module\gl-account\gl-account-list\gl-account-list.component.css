* {
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.main-container {
  background-color: rgb(241, 241, 241);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.actions {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.actions h3 {
  flex: 1;
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

.gradient-btn {
  background: linear-gradient(to right, #512ca2, #4262ff);
  border: none; /* Remove border if desired */
  color: white; /* Ensure text/icon contrast */
}

.gradient-btn:hover {
  background: linear-gradient(
    to right,
    #4262ff,
    #512ca2
  ); /* Optional hover effect */
  color: white;
}

.dropdown-menu .dropdown-item {
  cursor: pointer;
}

.dropdown-menu .dropdown-item:hover {
  background-color: #4262ff;
}

.nav-item {
  margin-right: 10px;
  width: 13.4%;
  text-align: center;
  font-family: Inter;
  font-size: 16px;
  font-weight: 500;
}

.nav-item hover {
  background-color: #4262ff;
  color: white;
  border-radius: 10px;
}

.search-create {
  display: flex;
  width: 100%;
  justify-content: space-between; /* Distribute space evenly */
  margin-bottom: 20px;
  gap: 10px;
}

.search-create .input-container {
  position: relative;
  width: 100%;
  /* background-color: yellow; */
}

.search-create input {
  width: 510px;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 13px;
  border: 1px solid #ccc;
  flex-grow: 1;
}

.search-create .input-container i.bi-search {
  position: absolute;
  left: 470px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
}

.search-create .button-container {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  white-space: nowrap; /* Prevents button from wrapping */
}

.search-create button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 10px;
  border: none;
}

.search-create button:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

table {
  width: 100%;
  margin-bottom: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.table-responsive {
  border-radius: none;
}

.table-head th {
  position: relative;
  padding: 10px;
  background-color: #d7dbf5;
  text-align: left;
  /* color: rgb(0, 0, 0); */
  background: linear-gradient(
    90deg,
    rgba(66, 98, 255, 0.06) 0%,
    rgba(63, 20, 153, 0.06) 100%
  );
  font-size: 14px;
  font-weight: bold;
}

.table-head th:first-child {
  border-top-left-radius: 10px;
}

.table-head th:last-child {
  border-top-right-radius: 10px;
}

tbody tr {
  background-color: white;
  border-bottom: rgb(171, 171, 171) 1px solid;
  font-size: small;
  color: rgb(102, 102, 102);
}

tbody tr:hover {
  background-color: #f1f1f1;
  /* Light grey color on hover */
}

td,
th {
  padding: 10px;
}

td.valueCheckbox,
th.valueCheckbox {
  width: 5%;
}

td.value,
th.valuehead {
  width: 18%;
}

th.valuehead {
  text-align: center;
}

/* Modal styles */
.modal-content {
  width: 100%; /* Ensure content fills the dialog width */
  padding: 20px;
  border-radius: 10px; /* Rounded corners */
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between; /* Space between title and close button */
}

.modal-header .modal-title {
  font-family: "Inter", sans-serif;
  font-weight: bold;
  font-size: 20px;
  color: black; /* Consistent title color */
}

.modal-header .btn-close {
  outline: none;
  box-shadow: none;
  font-size: 50px;
}

.modal-body {
  font-family: "Arial", sans-serif;
  color: #333; /* Darker text color for readability */
}

.modal-body .form-label {
  font-weight: 500;
  color: #000000; /* Label color matches theme */
}

.modal-body .form-control,
.modal-body .form-select {
  border-radius: 8px;
  border: 1px solid #ccc;
  padding: 10px;
  font-size: 16px;
}

.modal-footer {
  border-top: none; /* Remove default border */
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.modal-footer .btn-secondary,
.modal-footer .btn-primary {
  font-size: 16px;
  border-radius: 8px;
  font-weight: bold;
}

.modal-footer .btn-secondary {
  padding: 10px 20px;
  background: none;
  color: #4262ff;
  border: 1px solid #4262ff;
}

.modal-footer .btn-secondary:hover {
  background-color: #4262ff;
  color: #fff;
  border: none;
}

.modal-footer .btn-primary {
  padding: 10px 40px;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: #fff;
  border: none;
}

.modal-footer .btn-primary:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

/* Center the modal in the middle of the page */
.modal-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh; /* Full height */
  max-width: 40%; /* Increase width to 80% of the viewport */
}

.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translateY(-50px); /* Starting animation position */
}

.modal.show .modal-dialog {
  transform: translateY(0); /* End animation position */
}

.custom-close-btn {
  background: none;
  border: none;
  font-size: 1.5rem; /* Adjust size as needed */
  color: #666; /* Adjust color to match your theme */
  cursor: pointer;
  outline: none;
  display: flex;
  align-items: center;
  margin-right: -8px; /* Adjust to align with the edge if needed */
}

.custom-close-btn:hover {
  color: #4262ff; /* Optional hover color */
}

@media (max-width: 1020px) {
  .search-create {
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: flex-start;
    /* Distribute space evenly */
    margin-bottom: 20px;
    gap: 10px;
  }

  .search-create .input-container {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .search-create input {
    width: 100%;
    padding: 10px 40px;
    font-size: 16px;
    border-radius: 13px;
    border: 1px solid #ccc;
    flex-grow: 1;
  }

  .search-create .input-container i.bi-search {
    position: absolute;
    left: 470px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    pointer-events: none;
  }

  .search-create .input-container i.bi-search {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    pointer-events: none;
  }

  .modal-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    /* Full height */
    max-width: 100%;
    /* Increase width to 80% of the viewport */
  }

  .modal-footer .btn-secondary,
  .modal-footer .btn-primary {
    font-size: 16px;
    border-radius: 8px;
    font-weight: bold;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .actions h3 {
    font-size: 28px;
  }

  .table-responsive {
    overflow-x: scroll;
    white-space: nowrap;
  }

  .modal-footer {
  border-top: none; /* Remove default border */
  display: flex;
  flex-direction: column-reverse;
  
}
}
