* {
  font-family: "Inter", sans-serif !important;
}
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.actions h1 {
  flex: 1;
  margin-bottom: 20px;
  font-family: "Inter", sans-serif !important;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

.tab-view-button {
  border: 1px solid lightgray;
  border-bottom: 0;
  padding: 15px 25px;
  font-weight: 500;
  background: transparent;
}

.tab-view-button:not(:first-child) {
  border-left: 0;
}

.bottom-border {
  border-bottom: 1px solid lightgray;
}

.active-button {
  position: relative;
  background-color: white;
}
.active-button::after {
  position: absolute;
  content: "";
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: white;
}

.primary-button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: 600;
  padding: 10px 25px;
  cursor: pointer;
  border-radius: 5px;
  border: 1px solid #4262ff;
  margin-left: 10px;
  font-size: 15px;
}

p {
  margin: 0;
  padding: 0;
}

table {
  width: 100%;
  border-collapse: collapse;
}

table tr td {
  position: relative;
  padding: 10px 15px;
  align-items: center;
}

table tr{
  border-top: 1px solid lightgray;
}

table tr:last-child{
  border-block: 1px solid lightgray;
}

table tr td:first-child{
  border-radius: 5px 0px 0px 5px;
}
table tr td:last-child{
  border-radius: 0px 5px 5px 0px;
}

table tr td:not(:last-child)::before{
  position: absolute;
  content: '';
  height: 20px;
  width: 1px;
  border-right: 1px solid lightgray;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
