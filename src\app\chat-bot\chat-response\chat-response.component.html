<div class="chat-container" *ngIf="showPopup">
  <div class="chat-box" >
    <div class="chat-header" >
      <img class="chat-header-img" src="../../assets/images/Ledger_Chimp - icon_2.png" alt="Chatbot Image">
      <div class="chat-title">Ledger<PERSON>him<PERSON> Chat Assistant</div>
      <button class="close-btn" (click)="closePopup()">&#x2715;</button>
    </div>
    <div class="conversation" #conversationContainer>
      <div class="text3">Hello! Welcome to the LedgerChimp Chat Assistant</div>
      <div class="text2" [innerHTML]="responseData"></div> 
      <p *ngFor="let message of conversation" [ngClass]="{'sent': message.sender === 'User'}">
        <ng-container *ngIf="message.sender === 'User'">
          <strong>{{ message.sender }}:</strong>
        </ng-container>  
        <ng-container *ngIf="message.sender === 'Assistant'">
          <img class="img-assistant" src="../../assets/images/Ledger_Chimp - icon_2.png" alt="Assistant Icon">
          <span class="assistant-label">Assistant:</span>
        </ng-container>
        <span [innerHTML]="message.text"></span>
      </p>
      <div *ngIf="isWaitingForResponse" class="loading-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
    <div class="input-container">
      <input [(ngModel)]="input" type="text" placeholder="Need more help? Ask Me...." (keydown.enter)="sendData()" />
      <div class="send-icon" (click)="sendData()">
        <img class="img-send" src="../../assets/images/send.png" alt="Send Icon">
      </div>
    </div>
  </div>
</div>
