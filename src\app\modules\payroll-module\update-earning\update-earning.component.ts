import { Component, OnInit, ViewChild } from '@angular/core';
import { Earning } from '../payroll-settings/payroll-setting';
import { NgForm } from '@angular/forms';
import { EarningService } from '../payroll-settings/services/earning.service';
import { ReadPropExpr } from '@angular/compiler';
import { start } from '@popperjs/core';
import Swal from 'sweetalert2';
import { enableDebugTools } from '@angular/platform-browser';
import { ActivatedRoute, Route, Router } from '@angular/router';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-update-earning',
  templateUrl: './update-earning.component.html',
  styleUrls: ['./update-earning.component.css']
})
export class UpdateEarningComponent implements OnInit {

  earningId: number = 0;
  earning: Earning = new Earning();
  isPopupVisible: boolean = false;
  // activeTab: string = 'calendars';


  deductionCategory: string[] = ['twise monthly', 'onece a month']

  constructor(private earningService: EarningService, private router: Router, private route: ActivatedRoute) { }

  ngOnInit(): void {
    this.earningId = Number(this.route.snapshot.paramMap.get('id'));
    if (this.earningId) {
      this.getAllEarnings(this.earningId)
    }
  }
  getAllEarnings(earningId: number): void {
    this.earningService.getAllEarnings(earningId).subscribe({
      next: (data: Earning) => {
        this.earning = data; 
      },
      error: (err) => {
        console.error('Error fetching earning:', err);
      },
    });
  }
  

  navigateToPayrollSettings(): void {
    window.location.assign("/payroll-settings");
  }

  glAccounts: string[] = ['Account A', 'Account B', 'Account C', 'Account D'];

  updateEarning(): void {
    const entityId = +((localStorage.getItem('entityId')) + "");
    const userId = +((localStorage.getItem('userid')) + "");
    const date = new Date().toISOString();

    const payload = {
      ...this.earning,
      entityId,
      userId,
      date,
    };

    this.earningService.updateEarning(this.earningId, payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Ordinary Time Earnings update successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745'
        }).then(() => {
          this.earning = new Earning();
          window.location.assign("/payroll-settings");

        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to update Ordinary Time Earnings. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032'
        });
      }
    );
  }
}

