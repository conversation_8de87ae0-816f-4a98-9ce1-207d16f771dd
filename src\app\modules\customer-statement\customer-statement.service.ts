
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
@Injectable({
  providedIn: 'root'
})
export class CustomerStatementService {

  private readonly baseURL = environment.salesApiUrl;

  constructor(private http: HttpClient) {}


  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }


getCustomerStatementReport(requestData: {
  fromDate: string,
  toDate: string,
  entityId: number,
  businessPartnerId: number | null,
  entityUUID: string
}): Observable<any> {
   return this.request('POST',`/getCustomerStatementReport`, requestData);
}


getAccountsStatementReport(requestData: {
  fromDate: string,
  toDate: string,
  entityId: number,
  businessPartnerId: number | null,
  entityUUID: string
}): Observable<any> {
   return this.request('POST',`/getAccountsStatementReport`, requestData);
}


}
