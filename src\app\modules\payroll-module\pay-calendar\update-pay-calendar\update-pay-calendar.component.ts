import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgForm } from '@angular/forms';
import { MultiplePayCalendar, PayCalendar, PayCycle, PayPeriod } from '../../payroll-settings/payroll-setting';
import Swal from 'sweetalert2';
import { PayCalendarService } from '../../payroll-settings/services/pay-calendar.service';

@Component({
  selector: 'app-update-pay-calendar',
  templateUrl: './update-pay-calendar.component.html',
  styleUrls: ['./update-pay-calendar.component.css']
})
export class UpdatePayCalendarComponent implements OnInit {

  @ViewChild('payMultipleCalendarForm') payMultipleCalendarForm: NgForm | undefined;

  multiplePayCalendarData: MultiplePayCalendar = new MultiplePayCalendar();
  payCalendars: PayCalendar[] = [];
  payCycles: PayCycle[] = [];  
  payCalendarId: number = 0;
  payCycleId: number = 0;
  calendarName: string = '';
  payCycleName: string = '';
  days: number[] = Array.from({ length: 31 }, (_, i) => i + 1);
  fromDate: number | null = null;
  toDate: number | null = null;
  payDate: number | null = null;
  selectedOption: string = 'fromTo';
  datePipe: any;

  constructor(
    private payCalendarService: PayCalendarService,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.getAllPayCycles()
    this.payCalendarId = Number(this.route.snapshot.paramMap.get('id'));
    if (this.payCalendarId) {
      this.getPayCalendarById(this.payCalendarId);
    }

  }

  clearDateInputs() {
    this.multiplePayCalendarData.fromDay =  '' ;
    this.multiplePayCalendarData.payDay = '' ;
  }
  navigateToPayrollSettings(): void {
    window.location.assign("/payroll-calendar");
  }
  getAllPayCycles(): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.payCalendarService.getAllPayCycles(entityId).subscribe({
      next: (data: PayCycle[]) => {
        this.payCycles = data;
      },
      error: (err) => {
        console.error('Error fetching PayCycles:', err);
      }
    });
  }
  

  getPayCalendarById(payCalendarId: number): void {
    this.payCalendarService.getPayCalendarById(payCalendarId).subscribe({
      next: (data: PayCalendar) => { 
        this.payCalendars = [data]; 
        this.multiplePayCalendarData.calendarName = data.calendarName;
        const selectedCycle = this.payCycles.find(
          (cycle) => cycle.payCycleId === data.payCycle.payCycleId
        );
        if (selectedCycle) {
          this.multiplePayCalendarData.payCycle = selectedCycle;
        }
        this.multiplePayCalendarData.payDay=data.payDay;
        this.multiplePayCalendarData.fromDay=data.fromDay; 
        this.multiplePayCalendarData.payStartDate = data.payStartDate;
        this.multiplePayCalendarData.nextPayDate = data.nextPayDate;
        this.payCycleId = data.payCycle.payCycleId;
      },
      error: (err) => {
        console.error('Error fetching payCalendar:', err);
      }
    });
  }
  onPayCycleChange(): void {
    const selectedCycle = this.payCycles.find(
      (cycle) => cycle.cycleName === this.multiplePayCalendarData.payCycle?.cycleName
    );

    if (selectedCycle) {
      this.multiplePayCalendarData.payCycle = selectedCycle;
    }

    this.updateNextPayDate();
  }

  onPayStartDateChange(): void {
    this.updateNextPayDate();
  }

  private updateNextPayDate(): void {
    const selectedCycle = this.multiplePayCalendarData.payCycle;
    if (selectedCycle && this.multiplePayCalendarData.payStartDate) {
      const payStartDate = new Date(this.multiplePayCalendarData.payStartDate);
      const nextPayDate = new Date(
        payStartDate.setDate(payStartDate.getDate() + selectedCycle.numberOfDate)
      );
      this.multiplePayCalendarData.nextPayDate = nextPayDate.toISOString().split('T')[0]; 
    }
  }
  validateDateRange(): void {
    if (this.fromDate && this.toDate && this.toDate - this.fromDate < -1) {
      alert('Invalid date range selected, Please select again');
    }
  }
  
 updatePayCalendar(): void {
    const entityId = +((localStorage.getItem('entityId')) + "");
    const userId = +((localStorage.getItem('userid')) + "");
    const date = new Date().toISOString();
    

    const payload = {
      ...this.multiplePayCalendarData,
      entityId,
      userId,
      date,
      
    };
    
    this.payCalendarService.updatePayCalendarById(this.payCalendarId, payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Pay calendar updated successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          window.location.assign("/payroll-calendar");
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to update Pay Calendar. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }
  // onCalendarChange(event: Event): void {
  //   const selectedName = (event.target as HTMLSelectElement).value;
  //   const selectedPeriod = this.payPeriods.find(period => period.name === selectedName);
  
  //   if (selectedPeriod) {
  //     this.multiplePayCalendarData.payPeriod = selectedPeriod;
  //     this.multiplePayCalendarData.payCycle = selectedPeriod.payCycleName;
  //      } else {
  //     this.multiplePayCalendarData.payPeriod = null;
  //     this.multiplePayCalendarData.payCycle = '';
  //   }
  // }
  
}
