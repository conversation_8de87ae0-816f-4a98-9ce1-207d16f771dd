<app-payroll-nevigation></app-payroll-nevigation>
<div class="container">
  <div class="modal fade" id="simpleModal2" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered custom-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="simpleModalLabel">Employee Pay Slip</h5>
          <button type="button" class="btn-close" (click)="goBack()" aria-label="Close" data-bs-dismiss="modal"><i class="bi bi-x-circle"></i></button>
        </div>
        <div class="modal-body">
          <!-- Loading Spinner -->
          <div *ngIf="isLoading" class="spinner-container">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>

          <!-- IFrame for Report Preview -->
          <div class="iframe-container" [hidden]="isLoading">
            <iframe #reportPreviewFrame id="reportPreviewFrame2"></iframe>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
