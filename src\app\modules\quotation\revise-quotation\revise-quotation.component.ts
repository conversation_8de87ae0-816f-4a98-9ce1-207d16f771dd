import { Component, ElementRef, ViewChild } from '@angular/core';
import { Entity, EntityTradingName } from '../../entity/entity';
import { QuotationDetail, QuoteHead, SalesItem } from '../quotation';
import { QuotationService } from '../quotation.service';
import { EntityService } from '../../entity/entity.service';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpService } from 'src/app/http.service';
import { BusinessPartner, BusinessPartnerType } from '../../business-partner/business-partner';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { HttpErrorResponse } from '@angular/common/http';
import { NgForm } from '@angular/forms';

import { Observable, map, catchError, of } from 'rxjs';
import { StorageService } from '../../entity/storage.service';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';

@Component({
  selector: 'app-revise-quotation',
  templateUrl: './revise-quotation.component.html',
  styleUrls: ['./revise-quotation.component.css']
})
export class ReviseQuotationComponent {

  quotationData: QuoteHead = {} as QuoteHead;
  salesItems: SalesItem[] = [];
  businessEntity: Entity = new Entity();
  quoteDetails: QuotationDetail[] = [];
  itemCode: string = '';
  taxApplicable: boolean = false;
  lastQuotationNumber: string = '';
  businessEntityId: number = 0;
  itemPopUp: boolean = false;
  customers: BusinessPartner[] = [];
  businessPartner: BusinessPartner = new BusinessPartner();
  newItem: SalesItem = {
    salesItemId: 0,
    entityId: 1,
    userId: 1,
    itemTypeId: 1,
    itemCode: '',
    itemName: '',
    sellingPrice: 0,
    standardDiscount: 0,
    salesAccount: '',
    taxApplicability: '',
    itemStatus: 'active',
    description: '',
    unitPrice: 0,
    amount: 0,
    transactionDate: '2023-07-15',
  };
  unitPriceInvalid: boolean = false;
  selectedFile: File | null = null;
  url = '';
  businessPartnerType: BusinessPartnerType[] = [];

  filteredSalesItems: SalesItem[] = [];
  allSalesItems: SalesItem[] = [];
  entityTradingNames: EntityTradingName[] = [];
  showTaxApplicabilityDropdown: boolean = true;
  quotationStatus: any;
  entityId: number = 0;
  userId: number = 0;
  showUpdateLink = true;
  constructor(
    private route: ActivatedRoute,
    private quotationService: QuotationService,
    private router: Router,
    private entityService: EntityService,
    private businessPartnerService: BusinessPartnerService,
    private storageService: StorageService,
    private swalAlertsService: SwalAlertsService,
    
      ) {
        this.entityId = this.storageService.getEntityId();
        this.userId = this.storageService.getUserId();
      }
  
  ngOnInit() {
    this.getEntityTradingNamesByEntityId();
    this.loadCustomers();
    this.fetchAllSalesItems();
    this.loadQuotationData();
    

      const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      const userType = user.userType; // assuming userType is here

      if (userType === 'General user' || userType === 'Accountant') {
        this.showUpdateLink = false;
      }
    }
  }

  getBusinessEntityById(){
    if (!this.entityId) {
      this.showTaxApplicabilityDropdown = false;
      this.newItem.taxApplicability = 'no';
      return;
    }

    this.entityService.getBusinessEntityById(this.entityId).subscribe(
      (entity: Entity) => {
        this.lastQuotationNumber = entity.quoteNumber;
        

        this.showTaxApplicabilityDropdown = entity.taxApplicability === 'yes';
        this.newItem.taxApplicability = entity.taxApplicability || 'no';
      },
      error => {
        console.error('Error fetching entity:', error);
        this.showTaxApplicabilityDropdown = false;
        this.newItem.taxApplicability = 'no';
      }
    );

  }


  getEntityTradingNamesByEntityId() {
    this.businessEntityId = +((localStorage.getItem('entityId')) + "");

    this.entityService.getEntityTradingNamesByEntityId(this.businessEntityId).subscribe(data => {
      this.entityTradingNames = data;

    }, error => console.error(error));
  }

  customSearchFn(term: string, item: any) {
    term = term.toLowerCase();
    const itemCode = (item.itemCode || '').toLowerCase(); // Default to empty string if undefined
    const description = (item.description || '').toLowerCase(); // Default to empty string if undefined
    return itemCode.includes(term) || description.includes(term);
  }



  addNewRow() {
    this.quotationData.details.push(this.createEmptyRow());
  }

  // Helper method to create an empty row
  createEmptyRow() {
    return {
      salesItemId: new SalesItem(),
      taxCategoryId: 0,
      quoteNumber: this.quotationData.quoteNumber,
      quantity: 0,
      unitPrice: 0,
      description: '',
      discount: 0,
      tax: 0,
      amount: 0,
      discountType: '$',
      itemName: ''
    };
  }

  // New method to reset taxApplicability
  resetTaxApplicability(index: number): void {
    this.quotationData.details[index].taxApplicability = false; // Reset to unchecked
    this.quotationData.details[index].tax = 0; // Reset tax value
  }

  updateQuotationDetails(selectedItem: SalesItem, index: number) {
    // Update the details for the selected index
    this.quotationData.details[index].salesItemId = selectedItem;
    this.quotationData.details[index].unitPrice = selectedItem.unitPrice;
    this.quotationData.details[index].amount = this.quotationData.details[index].quantity * selectedItem.unitPrice;

    this.checkTaxApplicability(index);

    this.calculateSubTotal(); // Recalculate subtotal after item is selected

    // Check if this is the last row, if so, add a new row
    if (index === this.quotationData.details.length - 1) {
      // this.addNewRow();
    }
  }

  onDescriptionInput(index: number) {
    if (index === this.quotationData.details.length - 1) {
      // this.addNewRow();
    }
  }

  getAllSalesQuotesHead() {
    return this.quotationService.getAllSalesQuotesHead();
  }

  loadQuotationData() {
    const id = +this.route.snapshot.paramMap.get('id')!;
    this.quotationService.getSalesQuotesHeadById(id).subscribe(
      (data: QuoteHead) => {
        this.quotationData = data;
        this.getBusinessEntityById();
        this.loadQuotationDetails(id);
        
      },
      (error) => this.handleApiError('Failed to fetch quotation data.', error)
    );
  }

  loadQuotationDetails(id: number) {
    this.quotationData.details = [];
    this.salesItems = [];

    this.quotationService.getQuoteDetailsByQuoteHeadId(id).subscribe(
      (details: QuotationDetail[]) => {
        this.quotationData.details = details;
        this.salesItems = details.map(detail => detail.salesItemId);
      },
      (error) => this.handleApiError('Failed to fetch quotation details.', error)
    );
  }


  applyDiscount(detail: QuotationDetail, itemAmount: number) {
    if (detail.quantity === 0) {
      detail.amount = 0;
    } else {
      if (detail.discountType === 'B') {
        detail.amount = itemAmount - (itemAmount * (detail.discount / 100));
      } else if (detail.discountType === '$') {
        detail.amount = itemAmount - detail.discount;
      } else {
        detail.amount = itemAmount;
      }

     
        // Ensure amount doesn't go below zero
        detail.amount = Math.max(0, detail.amount);
    }
  }

  updateAmount(index: number): void {
    const detail = this.quotationData.details[index];
    const itemAmount = detail.quantity * detail.unitPrice;

    if (detail.quantity < 0 || detail.unitPrice < 0 || detail.discount < 0) {
      this.swalAlertsService.showWarning("Negative values are not allowed.", () => {
        this.resetInvalidInput(detail);
      });
      return;
    }

    if (detail.quantity === 0 && (detail.discount > 0 || detail.discountType !== '')) {
      this.swalAlertsService.showWarning("Please specify a quantity first.", () => {
        detail.discount = 0;
        detail.discountType = '$';
        detail.amount = 0;
        detail.tax = 0;
      });
      return;
    }

    this.applyDiscount(detail, itemAmount);

    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (!this.quotationData.details[index].taxApplicability) {
            this.checkTaxApplicability(index);
          }

          if (this.quotationData.details[index].taxApplicability && this.taxApplicable) {
            this.applyFlatTax(index, taxRate);
          } else {
            detail.tax = 0;
          }

          this.calculateSubTotal();
        },
        error => {
          console.error('Error fetching entity data:', error);
          detail.tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      detail.tax = 0;
      this.calculateSubTotal();
    }
  }


  resetInvalidInput(detail: QuotationDetail) {
    if (detail.quantity < 0) {
      detail.quantity = 0;
    }

    if (detail.discount < 0) {
      detail.discount = 0; // Reset discount
    }

    if (detail.unitPrice < 0) {
      detail.unitPrice = 0;
    }
    detail.discount = detail.discount; // Reset discount
    detail.discountType = detail.discountType; // Reset discount type
    detail.amount = detail.amount; // Reset amount
    detail.unitPrice = detail.unitPrice;
    detail.quantity = detail.quantity;
    detail.tax = detail.tax; // Reset tax

  }


  updateDiscountType(index: number, value: string) {
    const detail = this.quotationData.details[index];
    const itemAmount = detail.quantity * detail.unitPrice;
    detail.discountType = value;

    // Reset discount amount when changing type
    detail.discount = 0;

    if (detail.quantity === 0) {
      this.swalAlertsService.showWarning("Please add a quantity before applying a discount", () => {
        detail.discountType = '$';
      });
    } else {
      // this.applyDiscount(detail, itemAmount);
      this.updateAmount(index);
    }

    this.calculateSubTotal();
  }


  updateQuantity(index: number) {
    this.updateAmount(index); // Ensure amount is updated whenever quantity changes
  }


  calculateTotalDiscount(): number {
    let totalDiscount = 0;
    
     if (!this.quotationData?.details || this.quotationData.details.length === 0) {
    this.quotationData.totalDiscAmount = 0;
    return 0;
  }

    this.quotationData.details.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;
      let discountAmount = 0;

      if (detail.discountType === 'B') {
        discountAmount = itemAmount * (detail.discount / 100);
      } else if (detail.discountType === '$') {
        discountAmount = detail.discount;
      }

      totalDiscount += discountAmount;
    });

    this.quotationData.totalDiscAmount = totalDiscount;
    return totalDiscount;
  }

  checkTaxApplicability(index: number): void {
    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          this.updateDetailsTaxApplicability(index, taxRate);
          this.calculateSubTotal();
        },
        error => {
          console.error('Error fetching entity data:', error);
          this.updateDetailsTaxApplicability(index, 0);
          this.calculateSubTotal();
        }
      );
    } else {
      this.updateDetailsTaxApplicability(index, 0);
      this.calculateSubTotal();
    }
  }

  updateDetailsTaxApplicability(index: number, taxRate: number): void {
    const detail = this.quotationData.details[index];
    const itemTaxApplicable = detail.salesItemId.taxApplicability === 'yes';

    if (this.taxApplicable && itemTaxApplicable) {
      detail.tax = detail.amount * (taxRate / 100);
    } else {
      detail.tax = 0;
    }
  }

  onTaxApplicableChange(index: number): void {
    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (this.taxApplicable && this.quotationData.details[index].taxApplicability) {
            this.applyFlatTax(index, taxRate);
          } else {
            this.quotationData.details[index].tax = 0;
          }

          this.calculateSubTotal();
        },
        error => {
          console.error('Error fetching entity data:', error);
          this.quotationData.details[index].tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      this.quotationData.details[index].tax = 0;
      this.calculateSubTotal();
    }
  }

  applyFlatTax(index: number, taxRate: number): void {
    const detail = this.quotationData.details[index];
    detail.tax = detail.amount * (taxRate / 100);
    this.calculateSubTotal();
  }

  
  calculateSubTotal() {
    let subTotal = 0;
    let totalTax = 0;
    let totalDiscount = 0;

    this.quotationData.details.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;

      if (detail.quantity > 0) {
        this.applyDiscount(detail, itemAmount);
        subTotal += detail.amount;
        totalTax += detail.tax;
        totalDiscount += (detail.discountType === 'B') ? (itemAmount * (detail.discount / 100)) : detail.discount;
      } else {
        subTotal += 0;
        totalTax += 0;
        totalDiscount += 0;
      }
    });

    this.quotationData.subTotal = subTotal;
    this.quotationData.totalGst = totalTax;
    this.quotationData.totalDiscAmount = totalDiscount;
    this.calculateGrandTotal();
  }
  calculateGrandTotal() {
    const { subTotal, totalGst } = this.quotationData;
    return this.quotationData.grandTotal = subTotal + totalGst;
  }


  removeItem(index: number) {
    this.salesItems.splice(index, 1);
    this.quotationData.details.splice(index, 1);
    this.calculateSubTotal();
    this.calculateTotalDiscount();
  }



  
  /**Date Validation **/
  updateValidityMinDate() {
    const quoteDate = this.quotationData.quoteDate;
    if (quoteDate) {
      const quoteDateObj = new Date(quoteDate);
      const yyyy = quoteDateObj.getFullYear();
      const mm = String(quoteDateObj.getMonth() + 1).padStart(2, '0');
      const dd = String(quoteDateObj.getDate()).padStart(2, '0');

      const minValidUntilDate = `${yyyy}-${mm}-${dd}`;
      (document.getElementById('validityUntil') as HTMLInputElement).min = minValidUntilDate;
    }
  }

  onQuoteDateChange() {
    this.updateValidityMinDate();
    this.validateDates();
  }

  onValidUntilDateChange(): void {
    this.validateDates();
  }

  validateDates(): boolean {
    const quoteDate = new Date(this.quotationData.quoteDate);
    const validUntilDate = new Date(this.quotationData.validUntilDate);

    if (validUntilDate < quoteDate) {
      // Pass the message to showWarning and handle the callback (if needed)
      this.swalAlertsService.showWarning('The "Valid Until" date cannot be before the "Quotation Date".', () => {
      });
      return false;  // Prevent form submission
    }
    return true;  // Proceed with saving
  }



  onSubmit(f: NgForm) {
    if (!f.valid) return;

    // Validate if each row has either an item or a description
    if (this.hasInvalidRows()) {
      this.handleApiError('Each row must include either a selected item or a provided description.');
      return;
    }

    // If all rows are valid, proceed with the form submission
    this.quotationData.status = this.quotationStatus;
    this.checkForZeroQuantity(this.quotationStatus);
  }

  hasInvalidRows(): boolean {
    return this.quotationData.details.some(detail =>
      (!detail.salesItemId || !detail.salesItemId.itemCode) && (!detail.description || detail.description.trim() === '')
    );
  }

  checkForZeroQuantity(status: string) {
    if (this.hasZeroQuantity()) {
      this.swalAlertsService.showWarning('Selected item(s) have zero quantity. Do you want to continue?', () => this.saveReviseQuote(status));
      return;
    }

    if (this.isEmptyQuotation(status)) {
      this.handleApiError('You must add at least one item.');
      return;
    }

    this.saveReviseQuote(status);
  }

  hasZeroQuantity(): boolean {
    return this.quotationData.details.some(detail => detail.quantity === 0);
  }

  isEmptyQuotation(status: string): boolean {
    return (status === 'Pending' || status === 'Draft') && this.quotationData.details.length === 0;
  }



  saveReviseQuote(status: string): void {
    if (!this.validateDates()) return;
  
    this.swalAlertsService.showConfirmationDialog(
      'Are you sure?',
      'Do you really want to revise this Quote?',
      () => this.processQuoteRevision(status)
    );
  }
  
  private processQuoteRevision(status: string): void {
    const newQuote: QuoteHead = {
      ...this.quotationData,
      quoteId: 0,
      originalQuoteNumber: this.lastQuotationNumber,
      userId: +(localStorage.getItem('userid') || '0'),
      status: status,
      grandTotal: this.calculateGrandTotal(),
      quoteNumber: '', 
      quoteDetails: this.quoteDetails.map(detail => ({ ...detail, quoteDetailId: 0 }))
    };

    this.quotationService.saveQuoteHeadRevise(newQuote).subscribe(
      () => {
        this.swalAlertsService.showSuccessDialog(
          'Success!',
          'Quote saved successfully as a new record.',
          () => this.reviseQuote(this.quotationData.quoteId)
        );
      },
      (error) => this.handleApiError(error, 'Unable to save the quotation. Please try again')
    );
  }
  
  reviseQuote(id: number): void {
    this.quotationService.reviseQuote(this.quotationData.quoteId).subscribe(
      () => {
        this.swalAlertsService.showSuccessDialog(
          'Revised!',
          'Quote has been revised successfully.',
          () => this.router.navigate(['/quotation'])
        );
      },
      (error) => this.handleApiError(error, 'Failed to revise Quote.')
    );
  }
  

 


  
  /** Item **/


  fetchAllSalesItems() {
    if (!this.entityId) {
      console.error("No valid entity ID found.");
      this.swalAlertsService.showErrorDialog("No valid entity ID found. Please check your settings.");
      return;
    }

    this.quotationService.getAllSalesItemsByEntity(this.entityId).subscribe(
      (items: SalesItem[]) => {
        this.allSalesItems = items.filter(item => item.itemCode !== 'SISSERVICE'); // Store all items
        this.filteredSalesItems = items; // Initialize filtered items with all items
      },
      (error: any) => {
        console.error("Error fetching items", error);
        this.swalAlertsService.showErrorDialog("Unable to fetch sales items. Please try again.");
      }
    );
  }


  
  onItemSelected(selectedItem: SalesItem, index: number) {
    if (!selectedItem || !selectedItem.itemCode) {
      this.swalAlertsService.showErrorDialog('Please select a valid item.');
      return;
    }

    const existingItemIndex = this.findExistingItemIndex(selectedItem, index);

    if (existingItemIndex !== -1) {
      this.askToAddItemAgain(selectedItem, index);
    } else {
      this.updateQuotationDetails(selectedItem, index);
      this.resetTaxApplicability(index);
    }
  }

  // Helper method to find an existing item in the quotation details
  findExistingItemIndex(selectedItem: SalesItem, index: number): number {
    return this.quotationData.details.findIndex(
      (detail, i) => detail.salesItemId.itemCode === selectedItem.itemCode && i !== index
    );
  }


  askToAddItemAgain(selectedItem: SalesItem, index: number) {
    this.swalAlertsService.showConfirmationDialog(
      'Item already exists',
      'Do you want to add this item again?',
      () => {
        this.updateQuotationDetails(selectedItem, index);
        this.resetTaxApplicability(index);
      },
      () => {
        // Remove the entire row if the user clicks "No"
        this.quotationData.details.splice(index, 1);
      }
    );
  }

  onItemAdded(savedItem: SalesItem) {
  this.fetchAllSalesItems(); 
  this.itemCode = savedItem.itemCode; 
}

  /** Customer **/
  loadCustomers() {
    this.businessPartnerService.getCustomerListByEntity(this.entityId).subscribe(
      (customers: BusinessPartner[]) => {
        this.customers = customers;
      },
      (error: HttpErrorResponse) => {
        this.handleApiError("Failed to load customers.");
      }
    );
    this.quotationData.businessPartnerId = "";
  }

  loadBusinessPartnerTypes() {
    this.businessPartnerService.getBusinessPartnerTypesList().subscribe(
      (businessPartnerType: BusinessPartnerType[]) => {
        // Find the "Customer" type from the list
        const customerType = businessPartnerType.find(
          (type) => type.businessPartnerType.toLowerCase() === "customer"
        );

        if (customerType) {
          // Assign the customer type to the businessPartner object
          this.businessPartner.businessPartnerTypeId.businessPartnerTypeId =
            customerType.businessPartnerTypeId;
        }
        // Optionally store the filtered list if needed
        this.businessPartnerType = businessPartnerType;
      },
      (error: HttpErrorResponse) => {
        this.handleApiError("Failed to load Business Partner Type.");
      }
    );
  }


  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(cust => cust.businessPartnerId === +selectedCustomerId);

    // Set the reference field
    this.quotationData.customerName = selectedCustomer?.bpName || '';
  }


  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  setStatus(status: string) {
    this.quotationStatus = status;
  }

  handleApiError(errorMessage: string, error: any = null) {
    this.swalAlertsService.showErrorDialog(errorMessage);
  }


/** 
  goBack() {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Any unsaved changes will be lost. Do you want to cancel?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, cancel',
      cancelButtonText: 'No, stay here',
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d'
    }).then((result) => {
      if (result.isConfirmed) {
        this.router.navigate(['/quotaion']);
      }
    });
  }
**/



/** 
  deleteItem(quoteDetailId: any): void {
    // const itemId = this.quoteDetails[index].id;

    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you really want to delete this item?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it',
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d'
    }).then((result) => {
      if (result.isConfirmed) {

        this.quotationService.deleteQuoteItem(quoteDetailId)
          .subscribe(
            () => {


              Swal.fire({
                title: 'Deleted!',
                text: 'Item has been deleted.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#007bff',
              }).then(() => {
                // Optionally update the UI or reload data
                // Simulate click on the update button
                // this.updateButton.nativeElement.click();
                this.loadQuotationData();
              });
            },
            (error) => {
              console.error('Failed to delete item:', error);

              Swal.fire({
                title: 'Error!',
                text: 'Failed to delete item.',
                icon: 'error',
                confirmButtonText: 'OK',
                cancelButtonText: 'Ask Chimp',
                confirmButtonColor: '#be0032',
                cancelButtonColor: '#007bff',
                showCancelButton: true,
              }).then((result) => {
                if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                  if (this.chatBotComponent) {
                    Swal.fire({
                      title: 'Processing...',
                      text: 'Please wait while Chimp processes your request.',
                      allowOutsideClick: false,
                      didOpen: () => {
                        Swal.showLoading();
                        this.chatBotComponent.setInputData('Failed to delete item.');
                        this.chatBotComponent.responseReceived.subscribe(response => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound()
                        });
                      },
                    });
                  } else {
                    console.error('ChatBotComponent is not available.');
                  }
                }
              });
            }
          );
      }
    });
  }



  deleteQuoteDetail(index: number): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you really want to delete this item from the Quote?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it',
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d'
    }).then((result) => {
      if (result.isConfirmed) {

        // Remove the item from the quoteDetails array
        this.quoteDetails.splice(index, 1);

        Swal.fire({
          title: 'Deleted!',
          text: 'Item has been removed from the Quote.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff'
        });
      }
    });
  }**/





  

}

