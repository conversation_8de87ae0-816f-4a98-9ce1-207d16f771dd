<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
    <br>
    <h4>Customer Invoice Summary</h4>
    <hr>
    <div class="report-section">
        <div class="report-form-container">
            <div class="report-form">

                <div class="form-group">
                    <label for="fromDate">Date </label>
                    <div class="date-range">
                        <input type="date" id="fromDate" [(ngModel)]="fromDate">
                        <span>to</span>
                        <input type="date" id="toDate" [(ngModel)]="toDate">
                    </div>
                </div>

                <br>
                <div class="form-group">
                    <label for="status">Status</label>
                    <select class="form-select" id="status" [(ngModel)]="status">
                        <option value="%">All</option>
                        <option value="sent">Sent</option>
                        <option value="paid">Paid</option>
                        <option value="pending">Pending</option>
                    </select>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="customer">Customer</label>
                        <select class="form-select" id="customer" [(ngModel)]="invoiceHead.businessPartnerId" (change)="onCustomerChange($event)" name="customerName" required>
                            <option value="0" >All Customer</option>
                            <option *ngFor="let customer of customers" [value]="customer.businessPartnerId">
                                {{ customer.bpName }}
                            </option>
                        </select>
                    </div>

                    <div class="form-group">
                    </div>
                </div>

                <br>
             
                <!-- Overdue Options Dropdown -->
                  <div *ngIf="status === 'overdue'" class="form-group">
                    <label for="overdueOptions">Overdue Options</label>
                    <select id="overdueOptions" [(ngModel)]="overdueOption">
                        <option value="dates">Overdue Date Range</option>
                        <option value="amounts">Overdue Amounts</option>
                        <option value="both">Both</option>
                    </select>
                </div> 

                <!-- Overdue Date Range -->
              <div *ngIf="overdueOption === 'dates' || overdueOption === 'both'" class="form-group">
                    <label for="overdueFromDate">Overdue Date Range</label>
                    <div class="date-range">
                        <input type="date" id="overdueFromDate" [(ngModel)]="fromDate">
                        <span>to</span>
                        <input type="date" id="overdueToDate" [(ngModel)]="toDate">
                    </div>
                </div>
 
                <!-- Overdue Amount Range -->
                 <div *ngIf="overdueOption === 'amounts' || overdueOption === 'both'" class="form-group">
                    <label for="overdueAmountRange">Overdue Amount Range</label>
                    <div class="amount-range">
                        <input type="number" id="minAmount" placeholder="Min Amount" [(ngModel)]="minAmount">
                        <span>to</span>
                        <input type="number" id="maxAmount" placeholder="Max Amount" [(ngModel)]="maxAmount">
                    </div>
                </div> 
                <button class="generate-report-btn" (click)="previewInvoiceList(fromDate, toDate, status, invoiceHead.businessPartnerId);" >
                    Generate Report
                </button>
            </div>
        </div>

        <!-- Invoice Preview -->
        <div class="modal fade" id="simpleModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 740px;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="simpleModalLabel">Customer Invoice Summary</h5>
                         <!-- Flex container for right alignment -->
                <div class="d-flex justify-content-end flex-grow-1">
                    <div class="btn-group col-auto" role="group" aria-label="Basic radio toggle button group">
                        <!--<button type="button" class="btn btn-outline-danger ms-0" (click)="downloadAs('pdf')">
                            <img src="assets/images/pdf.jpeg" style="width: 30px"> PDF
                        </button>-->
                       <!-- <button type="button" class="btn btn-outline-success ms-2" (click)="downloadAs('excel')">
                            <img src="assets/images/excel.jpeg" style="width: 30px"> Excel
                        </button>-->
                    </div>
                </div>
                      
                        <button type="button" class="btn-close custom-close-btn" aria-label="Close" data-bs-dismiss="modal">
                            <span aria-hidden="true"></span>
                        </button>

                    </div>
                    <div class="modal-body">
                        <!-- Loading Spinner -->
                        <div *ngIf="isLoading" class="spinner-container">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>

                        <!-- IFrame for Invoice Preview -->
                        <div style="margin-top: 20px;" [ngClass]="{'d-none': isLoading}">

                            <iframe #invoiceListPreviewFrame id="#invoiceListPreviewFrame" width="700px" height="700px"></iframe>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>