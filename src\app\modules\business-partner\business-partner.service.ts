import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { BusinessPartner, BusinessPartnerType } from './business-partner';

@Injectable({
  providedIn: 'root'
})
export class BusinessPartnerService {

  private readonly baseURL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  saveBusinessPartner(businessPartner: BusinessPartner ): Observable<BusinessPartner> {
    return this.request('POST', '/saveBusinessPartner', businessPartner);
  }

  getBusinessPartnerList(): Observable<BusinessPartner[]> {
    return this.request('GET', '/businessPartnerList', {}, {});
  }

  getBusinessPartnersListByEntity(entityId: any): Observable<BusinessPartner[]> {
    return this.request('GET', '/getBusinessPartnersListByEntity', {}, { entityId: entityId });
  }
  
  getBusinessPartnerTypesList(): Observable<BusinessPartnerType[]> {
    return this.request('GET', '/businessPartnerTypeList', {}, {});
  }
  getBusinessPartnerById(id: number): Observable<BusinessPartner> {

    return this.request("GET", `/getBusinessPartnerById/${id}`, {});
  }
  deleteBusinessPartner(id: number): Observable<void> {
    return this.request('DELETE', `/businessPartnerById/${id}`, null);
  }
  updateBusinessPartner(id: number, businessPartner: BusinessPartner): Observable<void> {
    return this.request('PUT', `/updateBusinessPartner/${id}`, businessPartner); 
  }
  
  getCustomerListByEntity(entityId: any): Observable<BusinessPartner[]> {
    return this.request('GET', '/getCustomersByEntity', {}, { entityId: entityId });
  }

  getSupplierListByEntity(entityId: any): Observable<BusinessPartner[]> {
    return this.request('GET', '/getSuppliersByEntity', {}, { entityId: entityId });
  }

}


