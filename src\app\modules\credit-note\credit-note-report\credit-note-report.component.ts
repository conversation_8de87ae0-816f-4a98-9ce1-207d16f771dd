import { Component, ElementRef, ViewChild } from '@angular/core';
import { CreditNoteService } from '../credit-note.service';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { BusinessPartner } from '../../business-partner/business-partner';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { QuoteHead } from '../../quotation/quotation';
import { HttpErrorResponse } from '@angular/common/http';
import { CreditNote } from '../credit-note';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import Swal from 'sweetalert2';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-credit-note-report',
  templateUrl: './credit-note-report.component.html',
  styleUrls: ['./credit-note-report.component.css']
})
export class CreditNoteReportComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  @ViewChild('creditNotePreviewFrame') creditNotePreviewFrame!: ElementRef;

  private audio!: HTMLAudioElement;
  selectedReportType: any;
  fromDate: any;
  toDate: any;
  status: string = '%';
  customers: BusinessPartner[] = [];
  creditnote: CreditNote = new CreditNote();
  isLoading = false;
  getAllCustomers = false;
  constructor(
    private creditNoteService: CreditNoteService,
    private router: Router, 
    public sanitizer: DomSanitizer,
    private businessPartnerService: BusinessPartnerService,
    private swalAlerts: SwalAlertsService
  ) { }

  ngOnInit() {
    this.creditnote.businessPartnerId = '0';
    this.loadCustomers();
  }

  
  previewCreditNote(fromDate: string, toDate: string, status: string, businessPartnerId: any) {
  if (!fromDate || !toDate) {

    this.swalAlerts.showSwalWarning('Warning!', 'Please select Date Range', 'Missing date range for credit note report.');
    return;
  }
  this.isLoading = true;
  const entityId = +localStorage.getItem('entityId')!;
  const entityUUID = localStorage.getItem('entityUuid')!;
  const bpId = (businessPartnerId === 0 || businessPartnerId === '' || businessPartnerId == null) ? null : +businessPartnerId;

  const requestData = {
    fromDate,
    toDate,
    status,
    entityId,
    businessPartnerId: bpId,
    entityUUID
  };

  this.creditNoteService.getCreditNoteListReport(requestData).subscribe(
    data => {
      const base64String = data.response;
      if (base64String) {
        this.loadPdfIntoIframe(base64String);
      } else {
        this.isLoading = false;
        alert('No credit note data for preview.');
      }
    },
    error => {
      this.isLoading = false;
      alert('Error loading credit note preview.');
    }
  );
}

    
    private loadPdfIntoIframe(base64String: string) {
      if (base64String && base64String.trim().length >= 50) {
        const pdfData = 'data:application/pdf;base64,' + base64String;
        const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
        const iframe = this.creditNotePreviewFrame.nativeElement;
    
        iframe.onload = () => {
          this.isLoading = false;
        };
    
        iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
    
        // Open modal manually using Bootstrap JS
        const modalElement = document.getElementById('simpleModal');
        const modal = new bootstrap.Modal(modalElement!);
        modal.show();
      } else {
         this.isLoading = false;
         this.swalAlerts.showSwalWarning('No Data', 'No Credit note data for preview.', 'No Credit note data was returned for the selected range.');
    
      }
    }
    

    
 loadCustomers() {
  const entityId = +((localStorage.getItem('entityId')) + '');
  this.businessPartnerService.getCustomerListByEntity(entityId).subscribe(
    (customers: BusinessPartner[]) => {
      this.customers = customers;
    },
    (error: HttpErrorResponse) => {
      console.error('Error fetching customers', error);

      //  Use SwalAlertsService for error with Chimp support
      this.swalAlerts.showErrorWithChimpSupport(
        'Failed to load customers.',
        'Unable to fetch customer list for this entity. Please check if the customer service is responding.'
      );
    }
  );
}



  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(cust => cust.businessPartnerId === +selectedCustomerId);

    // Set the reference field
    this.creditnote.customerName = selectedCustomer?.bpName || '';
  }

  // Method to toggle all customers' data
  toggleAllCustomers() {
    this.getAllCustomers = !this.getAllCustomers;
    if (this.getAllCustomers) {
      this.creditnote.businessPartnerId = '';
    }
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}
