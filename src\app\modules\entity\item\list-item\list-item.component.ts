import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ItemService } from '../item.service';
import { Item } from '../item';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';

@Component({
  selector: 'app-list-item',
  templateUrl: './list-item.component.html',
  styleUrls: ['./list-item.component.css'],
})
export class ListItemComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  items: Item[] = [];

  constructor(
    private router: Router,
    private itemService: ItemService
  ) {}

  ngOnInit(): void {
    this.getItems();
  }

  getItems(): void {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.itemService.getAllSalesItemsByEntity(entityId).subscribe(
      (data: Item[]) => {
        this.items = data;
      },
      (error) => {
        console.error('Error fetching items:', error);
        this.showErrorAlert('Error fetching items. Please try again later.');
      }
    );
  }

  createNewItem(): void {
    this.router.navigate(['/create-item']);
  }

  getItemById(id: number): void {
    this.itemService.getItemById(id).subscribe(
      (data: Item) => {
        this.items = [data];
      },
      (error) => {
        console.error('Error fetching item by ID:', error);
        this.showErrorAlert(`Item with ID ${id} could not be found.`);
      }
    );
  }

  deleteItem(id: number): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you really want to delete this item?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (result.isConfirmed) {
        this.itemService.deleteItem(id).subscribe(
          () => {
            this.showSuccessAlert('Item has been deleted.');
            this.getItems(); // Refresh the item list after deletion
          },
          (error) => {
            console.error('Failed to delete item:', error);
            this.showErrorAlert('Failed to delete item. Please try again later.');
          }
        );
      }
    });
  }

  updateItem(id: number): void {
    this.router.navigate(['/update-item', id]);
  }

  private showSuccessAlert(message: string): void {
    Swal.fire({
      title: 'Success!',
      text: message,
      icon: 'success',
      confirmButtonText: 'OK',
      confirmButtonColor: '#28a745',
    });
  }

  private showErrorAlert(message: string): void {
    Swal.fire({
      title: 'Error!',
      text: message,
      icon: 'error',
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#be0032',
      cancelButtonColor: '#007bff',
      showCancelButton: true, 
      }).then((result) => {
        if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData(message);
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound() 
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
    });
  }


  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }

}
