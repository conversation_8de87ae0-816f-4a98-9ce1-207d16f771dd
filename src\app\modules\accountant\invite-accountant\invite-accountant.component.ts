import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { UserType } from '../../admin/components/user/user';
import { UserService } from '../../admin/components/user/user.service';
import { SubscriptionService } from '../../subscription/subscription.service';

@Component({
  selector: 'app-invite-accountant',
  templateUrl: './invite-accountant.component.html',
  styleUrls: ['./invite-accountant.component.css'],
})
export class InviteAccountantComponent implements OnInit {
  firstName: string = '';
  lastName: string = '';
  entityId: number = 0;
  email: string = '';
  isAccountant: boolean = false;
  isAccountantExists: boolean = false;
  userType: UserType = new UserType();
  subscriptionPlanId: number = 0;
  userTypes: UserType[] = [];

  constructor(
    private router: Router, 
    private userService: UserService,
    private subscriptionService: SubscriptionService) {}

  ngOnInit(): void {
    this.entityId = Number(localStorage.getItem('entityId'));
    this.loadUserType();
     this.getSubscriptionPlan();
  }

  loadUserType() {
    const userTypeId = Number(localStorage.getItem('userTypeId'));

    this.userService.getUserTypeById(userTypeId).subscribe(
      (response) => {
        if (response.userType === 'Accountant') {
          this.userType = response;
        }
      },
      (error) => {
        console.error('Error fetching user type data:', error);
      }
    );
  }

  
  getSubscriptionPlan() {
    this.subscriptionService.getSubscriptionByEntityId(this.entityId).subscribe(
      (response) => {
        this.subscriptionPlanId = response.subscriptionFeeId?.subscriptionPlanId?.subscriptionPlanId ?? 0;
      },
      (error) => {
        console.error('Error fetching subscription plan:', error);
        this.showErrorSwal('Failed to load subscription plan.');
      }
    );
  }
  
  async inviteAccountant() {
    const payload = {
      firstName: this.firstName,
      lastName: this.lastName,
      email: this.email,
      entityId: this.entityId,
      subscriptionPlanId: Number(this.subscriptionPlanId),
      userTypeId: this.userType.userTypeId,
    };

    this.userService
      .checkAccountantExists(this.email, this.userType.userTypeId, this.entityId)
      .subscribe((response) => {
        if (response) {
          this.userService.inviteAccountant(payload).subscribe(
            (response) => {
              Swal.fire({
                title: 'Invitation Sent!',
                text: 'User invitation has been sent successfully.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
              }).then(() => {
                this.router.navigate(['/invite-accountant']);
              });
            },
            (error) => {
              console.error('Error sending invitation:', error);
              this.showErrorSwal('Accountant already exists with the provided email in this entity.');
            }
          );
        } else {
          this.userService.inviteUser(payload).subscribe(
            (response) => {
              Swal.fire({
                title: 'Invitation Sent!',
                text: 'User invitation has been sent successfully.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
              }).then(() => {
                this.router.navigate(['/invite-accountant']);
              });
            },
            (error) => {
              console.error('Error sending invitation:', error);
              this.showErrorSwal('Failed to send the invitation.');
            }
          );
        }
      });
  }

  navigateToDashboard() {
    window.location.reload();
  
  }

  showErrorSwal(message: string): void {
    Swal.fire({
      title: 'Error',
      text: message,
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#be0032',
    });
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
}
