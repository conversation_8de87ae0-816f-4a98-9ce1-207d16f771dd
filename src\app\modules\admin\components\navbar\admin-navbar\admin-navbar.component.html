<nav class="navbar">

  <div class="nav-items">
    <ul>
      <li><a (click)="navigateCountry()">Country</a></li>
      <li><a (click)="navigateManageSubscription()">Subscription Manage</a></li>

      <li class="dropdown">
        <a (click)="toggleDropdown()">User Manage</a>
        <ul class="dropdown-menu" [class.show]="isDropdownOpen">
          <li class="dropdown-list" *ngIf="isDropdownOpen">
            <a class="dropdown-list-content" (click)="navigateUserInvitation()"
              >Invite User</a
            >
          </li>
          <li class="dropdown-list" *ngIf="isDropdownOpen">
            <a
              class="dropdown-list-content"
              (click)="navigateUserRegistration()"
              >Create User</a
            >
          </li>
        </ul>
      </li>
      <li><a (click)="navigateBusinessEntityEdit()">Settings</a></li>
    </ul>
  </div>
</nav>
