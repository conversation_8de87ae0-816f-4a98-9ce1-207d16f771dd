import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { GlPostingDetails } from '../../journal-voucher/journal-voucher';
import { JournalVoucherService } from '../../journal-voucher/journal-voucher.service';

@Component({
  selector: 'app-gl-posting-view-pop-up',
  templateUrl: './gl-posting-view-pop-up.component.html',
  styleUrls: ['./gl-posting-view-pop-up.component.css']
})
export class GlPostingViewPopUpComponent {
  transactionId: number;
  documentType: string;
  documentNumber: string;
  quotesDetails:  GlPostingDetails[] = []; 
  filteredJournalVoucherDetails?: GlPostingDetails;

  constructor(@Inject(MAT_DIALOG_DATA) public data: any, private journalVoucherService: JournalVoucherService) {
    this.transactionId = data.transactionId;
    this.documentType = data.documentType;
    this.documentNumber = data.documentNumber;
    
  }

  ngOnInit() {
    this.fetchQuotesDetails();
    }

  fetchQuotesDetails() {
    const entityId = parseInt(localStorage.getItem('entityId') || "0", 10);

    console.log("entity id:" , entityId);

    if (!entityId || isNaN(entityId)) {
      console.error("Invalid entityId. Cannot fetch quotations.");
      return;
  }
  
    this.journalVoucherService.getGlPostingDetailsList(entityId).subscribe(
        (data: GlPostingDetails[]) => {
          this.quotesDetails = data
          console.log('Popup details:', this.quotesDetails);
          this.findJournalVoucherDetails();
        },
        (error) => {
          console.error('Error fetching quotations:', error);
        }
      );
    }

    findJournalVoucherDetails() {
      this.filteredJournalVoucherDetails = this.quotesDetails.find(
        quotesDetail => quotesDetail.glTransactionId?.glTransactionId === this.transactionId
      );
      console.log('filtered details:', this.filteredJournalVoucherDetails);
    }
  
}
