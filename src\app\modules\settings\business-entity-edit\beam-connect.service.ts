import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpService } from 'src/app/http.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class BeamConnectService {

  private readonly baseURL = environment.apiUrl;
  
    constructor(private http: HttpClient, private httpService: HttpService) {}
  
    getAuthToken(): string | null {
      return window.sessionStorage.getItem('auth_token');
    }
  
    request(
      method: string,
      url: string,
      data: any,
      params?: any
    ): Observable<any> {
      let headers = new HttpHeaders();
  
      const authToken = this.getAuthToken();

  if (authToken) {
    headers = headers.set('Authorization', 'Bearer ' + authToken);
  } else {
    // Add secure API key for protected-but-public endpoints
    headers = headers.set('X-API-KEY', environment.secureApiKey);
  }
  
      const options = {
        headers: headers,
        params: new HttpParams({ fromObject: params }),
      };
  
      switch (method.toUpperCase()) {
        case 'GET':
          return this.http.get(this.baseURL + url, options);
        case 'POST':
          return this.http.post(this.baseURL + url, data, options);
        case 'PUT':
          return this.http.put(this.baseURL + url, data, options);
        case 'DELETE':
          return this.http.delete(this.baseURL + url, options);
        // Add more HTTP methods as needed
        default:
          throw new Error('Unsupported HTTP method');
      }
    }


    getBeamLink(entityId: any, userId: any): Observable<any> {
     return this.request('POST', '/api/beam/participant-register',{}, {entityId: entityId, userId: userId});
      }

         getParticipantDetails(entityId: any): Observable<any> {
          return this.request('GET', `/api/beam/getParticipant-details`, {}, {entityId: entityId});
        }
        
}
