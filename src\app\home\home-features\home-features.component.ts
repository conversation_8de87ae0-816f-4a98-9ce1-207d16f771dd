import { Component } from '@angular/core';
import { HomeService } from '../service/home.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-home-features',
  templateUrl: './home-features.component.html',
  styleUrls: ['./home-features.component.css'],
})
export class HomeFeaturesComponent {
  
  constructor(private router: Router, private homeService: HomeService) {}

  navigateUserLogin(): void {
    this.router.navigate(['/user-login']);
  }

  navigateContactUs(): void {
    this.router.navigate(['/contact-us']);
  }
}
