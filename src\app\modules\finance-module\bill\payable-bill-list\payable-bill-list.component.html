<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <!-- Heading And Button -->
  <div class="actions">
    <h1>Supplier Bills Overview</h1>
    <div class="btn-group" #dropdownRef [class.show]="isDropdownOpen">
      <!-- Dropdown button with vertical dots -->
      <button
        type="button"
        class="btn btn-secondary dropdown-toggle gradient-btn"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        (click)="toggleDropdown()"
      >
        <i class="bi bi-three-dots-vertical"></i>
        <!-- Vertical dots icon -->
      </button>
      <ul class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen">
        <li>
          <a class="dropdown-item" (click)="recordBatchPayment()">Record Payment</a>
        </li>
        <li>
          <a class="dropdown-item" (click)="onCreditNoteMultiple()">Credit Note</a>
        </li>
        <li>
          <a class="dropdown-item" (click)="cancelSelectedBills()">Cancel</a>
        </li>        
        <!--<li>
          <a class="dropdown-item">Preview & Print</a>
        </li>-->
      </ul>
    </div>
  </div>

  <div class="search-create">
    <button type="button" (click)="addPaybleBill()" class="invoice-convert">
      Add Payable Bill
    </button>
    <button (click)="exportToExcel()" class="btn btn-primary">
      Export to Excel
    </button>
  </div>

  <!-- Tabs -->
  <div>
    <ul class="nav nav-tabs mb-3 justify-content-start">
      <li class="nav-item">
        <a
          class="nav-link"
          [class.active]="activeTab === 'all'"
          (click)="setActiveTab('all')"
          >All</a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link"
          [class.active]="activeTab === 'pending'"
          (click)="setActiveTab('pending')"
          >Pending</a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link"
          [class.active]="activeTab === 'paid'"
          (click)="setActiveTab('paid')"
          >Paid</a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link"
          [class.active]="activeTab === 'canceled'"
          (click)="setActiveTab('canceled')"
          >Canceled</a
        >
      </li>
    </ul>
  </div>

  <div class="Card">
    <!-- Filter and Search Section -->
    <div class="row1">
      <!-- Input for number, reference -->
      <div class="row1_col1">
        <label for="search-input">Reference No or Supplier</label>
        <div class="input-container">
          <input
            type="text"
            class="search-input"
            id="search-input" placeholder="Reference"
            [(ngModel)]="searchTerm"
          />
          <i class="bi bi-search"></i>
        </div>
      </div>

      <div class="row1_col3">
        <label for="StartDate">Start Date</label>
        <div style="position: relative; width: 100%;">
          <input
            matInput
            [matDatepicker]="startDatePicker"
            class="date-picker"
            id="StartDate"
            [(ngModel)]="startDate"
            placeholder="dd/mm/yyyy"
          />
          <mat-datepicker-toggle matSuffix [for]="startDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
        </div>
        <mat-datepicker #startDatePicker></mat-datepicker>
      </div>

      <div class="row1_col4">
        <label for="EndDate">End Date</label>
        <div style="position: relative; width: 100%;">
          <input
            matInput
            [matDatepicker]="endDatePicker"
            class="date-picker"
            id="EndDate"
            [(ngModel)]="endDate"
            placeholder="dd/mm/yyyy"
          />
          <mat-datepicker-toggle matSuffix [for]="endDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
        </div>
        <mat-datepicker #endDatePicker></mat-datepicker>
      </div>
    </div>

    <div class="row2">
      <div class="row2_col3">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>
      <div class="row2_col1">
                 <button type="button" class="primary-button" (click)="filterQuotes()">
          Search
        </button>
      </div>
    </div>
  </div>

  <div class="table-responsive">
    <table>
      <thead>
        <tr class="table-head">
          <th scope="col" class="valueCheckbox">
       
            <input
            type="checkbox"
            [checked]="isAllSelected"
            (change)="selectAll($event)"
          />
          </th>
          <th scope="col" class="valuehead">Reference No</th>
          <th scope="col" class="valuehead">Supplier</th>
          <th scope="col" class="valuehead">Bill Date</th>
          <th scope="col" class="valuehead">Due Date</th>
          <th scope="col" class="valuehead" style="text-align: right">Bill Amount</th>
          <th scope="col" class="valuehead" style="text-align: right">Due Amount</th>
          <th scope="col" class="valuehead" style="text-align: center;">Status</th>
          <th scope="col" class="valuehead">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let quote of filteredApInvoices; let i = index">
        
            <td class="valueCheckbox">
              <input
                type="checkbox"
                [(ngModel)]="quote.selected" 
                [checked]="selectedApInvoices.has(quote)"
                (change)="toggleSelection(quote, $event)"
              />
            </td>
      

          <td class="value">
  <ng-container *ngIf="quote.scanned; else notScanned">
    <span style="color: #4262ff;">{{ quote.referenceNo }}</span>
    <i class="bi bi-check2-circle" title="Scanned via OCR" style="color: #267f2f; font-size: 1rem; margin-left: 4px;"></i>
  </ng-container>
  <ng-template #notScanned>
    {{ quote.referenceNo }}
  </ng-template>
</td>

          <td class="value">{{ quote.supplierName }}</td>
          <td class="value">{{ quote.postingDate | date : "dd-MM-yyyy" }}</td>
          <td class="value">{{ quote.dueDate | date : "dd-MM-yyyy" }}</td>
          <td class="value" style="text-align: right;">{{ quote.netAmount | currency }}</td>
          <td class="value" style="text-align: right;">{{ quote.dueAmount | currency }}</td>

          <td
            class="value"
            style="text-align: center;"
            [ngClass]="{
              'text-pending': quote.status === 'Pending',
              'text-overdue': quote.status === 'Overdue',
              'text-paid': quote.status === 'Paid',
              'text-Closed': quote.status === 'Closed',
              'text-canceled': quote.status === 'Canceled',
              'text-awaiting-payment': quote.status === 'Awaiting Payment',   
            }"
          >
            <span
              class="lable"
              [ngClass]="{
                'border-pending': quote.status === 'Pending',
                'border-overdue': quote.status === 'Overdue',
                'border-paid': quote.status === 'Paid',
                'border-Closed': quote.status === 'Closed',
                'border-canceled': quote.status === 'Canceled',
                'border-awaiting-payment': quote.status === 'Awaiting Payment',   
              }">
              {{ quote.status }}</span>
          </td>
          

          <td class="value">
              <button (click)="viewBill(quote.apInvoiceHeadId)"

                     [disabled]="
                    quote.status === 'Canceled'|| quote.isLocked
                    "
                   
                  class="btn btn-orange btn-sm" style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" 
                  title="View">
                    <i class="bi bi-arrows-fullscreen" style="color: #4262FF;"
                      [style.color]="
                      quote.status === 'Canceled' || quote.isLocked
                       ? '#aaa' 
                        : '#4262ff' 
                          "></i>
              </button>

              <!-- Delete Button -->
              <button
              (click)="deleteApInvoice(quote.apInvoiceHeadId)"
              title="Delete Bill"
              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
              >
                <i class="ri-delete-bin-line" style="color: #ff0000"
               [style.color]="
                quote.status === 'Pending' ||
                quote.status === 'Overdue' ||
                quote.status === 'Closed' ||
                quote.status === 'Paid' ||
                quote.status === 'Canceled' ||
                quote.status === 'Awaiting Payment'
              "></i>
              </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
