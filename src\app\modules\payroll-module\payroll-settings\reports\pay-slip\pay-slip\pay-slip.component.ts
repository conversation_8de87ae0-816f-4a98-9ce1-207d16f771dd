import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import Swal from 'sweetalert2';
import { PayRunDetailService } from '../../../services/pay-run-detail.service';
import { ActivatedRoute, Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { Location } from '@angular/common';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-pay-slip',
  templateUrl: './pay-slip.component.html',
  styleUrls: ['./pay-slip.component.css']
})
export class PaySlipComponent implements OnInit {
  @ViewChild('reportPreviewFrame') reportPreviewFrame!: ElementRef;
  @ViewChild('simpleModal') simpleModal!: ElementRef;

  employeeId!: number;
  calendarId!: number;
  periodId!: number;
  isLoading: boolean = false;

  constructor(
    private payRunDetailService: PayRunDetailService, 
    private route: ActivatedRoute, 
    private router: Router,
    private sanitizer: DomSanitizer, 
    private location: Location
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('employeeId');
      const calendarId = params.get('payCalendarId');
      const periodId = params.get('payPeriodId');
      if (id && calendarId && periodId) {
        this.employeeId = +id;
        this.calendarId = +calendarId;
        this.periodId = +periodId;
        this.generateReport();
      }
    });
  }

  generateReport(): void {
    this.isLoading = true;
    
    const modalElement = document.getElementById('simpleModal2');
    if (modalElement) {
      const modal = new bootstrap.Modal(modalElement, {
        backdrop: 'static',
        keyboard: false
      });
      modal.show();
    }
    
    const entityId = +(localStorage.getItem('entityId') || '0');
    
    this.payRunDetailService.generatePaySlipReport(this.employeeId, entityId, this.calendarId, this.periodId).subscribe({
      next: (data: any) => {
        const base64String = data.response;

        if (base64String) {
          this.loadPdfIntoIframe(base64String);
        } else {
          this.isLoading = false;
          Swal.fire({
            title: 'No Data',
            text: 'No paySlip details found for the selected Row.',
            icon: 'info',
            confirmButtonText: 'OK',
            confirmButtonColor: '#007bff'
          }).then(() => {
            this.goBack();
          });
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error generating report:', error);
        Swal.fire({
          title: 'Error',
          text: 'Failed to generate the report.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032'
        }).then(() => {
          this.goBack();
        });
      }
    });
  }

  private loadPdfIntoIframe(base64String: string): void {
    if (!this.reportPreviewFrame || !this.reportPreviewFrame.nativeElement) {
      this.isLoading = false;
      return;
    }

    let dataLoaded = false;

    if (base64String && base64String.trim().length >= 50) {
      const pdfData = 'data:application/pdf;base64,' + base64String;
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
      const iframe = this.reportPreviewFrame.nativeElement;

      iframe.onload = () => {
        this.isLoading = false;
        dataLoaded = true;
      };

      iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
    }

    setTimeout(() => {
      if (!dataLoaded) {
        this.isLoading = false;
        Swal.fire({
          title: 'No Data',
          text: 'No Pay Slip for preview.',
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff'
        }).then(() => {
          this.goBack();
        });
      }
    }, 2000);
  }

  goBack(): void {
    this.location.back();
    setTimeout(() => {
      window.location.reload();
    }, 100);
  }
  
  
}