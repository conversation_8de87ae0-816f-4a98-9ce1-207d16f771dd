import { Component, ElementRef, ViewChild } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { BusinessPartner } from '../../business-partner/business-partner';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { SupplierStatementService } from './supplier-statement.service';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { InvoiceService } from '../../invoice/invoice.service';
import { ApInvoiceHead } from '../bill/bill';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-supplier-statement',
  templateUrl: './supplier-statement.component.html',
  styleUrls: ['./supplier-statement.component.css']
})
export class SupplierStatementComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  toDate : string = '';
  fromDate: string = '';
  isLoading = false;
  suppliers: BusinessPartner[] = [];
  invoiceHead: ApInvoiceHead = new ApInvoiceHead();
  getAllSuppliers = false;

  @ViewChild('supplierStatementPreviewFrame') supplierStatementPreviewFrame!: ElementRef;

  constructor(
    private router: Router,
    private sanitizer: DomSanitizer,
    private swalAlerts: SwalAlertsService,
    private businessPartnerService: BusinessPartnerService, private supplierStatementService: SupplierStatementService) { }

    ngOnInit() {
    this.invoiceHead.businessPartnerId = '0';
    this.loadSuppliers();
  }

     previewSupplierStatement(fromDate: string, toDate: string, businessPartnerId: any) {
        if (!fromDate || !toDate) {
      
          this.swalAlerts.showSwalWarning('Warning!', 'Please select Date Range', 'Missing date range for Supplier Statement report.');
          return;
        }
        this.isLoading = true;
        const entityId = +localStorage.getItem('entityId')!;
        const entityUUID = localStorage.getItem('entityUuid')!;
        const bpId = (businessPartnerId === 0 || businessPartnerId === '' || businessPartnerId == null) ? null : +businessPartnerId;
      
        const requestData = {
          fromDate,
          toDate,
          entityId,
          businessPartnerId: bpId,
          entityUUID
        };
      
        this.supplierStatementService.getSupplierStatementReport(requestData).subscribe(
          data => {
            const base64String = data.response;
            if (base64String) {
              this.loadPdfIntoIframe(base64String);
            } else {
              this.isLoading = false;
              alert('No Supplier Statement for preview.');
            }
          },
          error => {
            this.isLoading = false;
            alert('Error loading Supplier Statement preview.');
          }
        );
      }
      
          
            private loadPdfIntoIframe(base64String: string) {
              if (base64String && base64String.trim().length >= 50) {
                const pdfData = 'data:application/pdf;base64,' + base64String;
                const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
                const iframe = this.supplierStatementPreviewFrame.nativeElement;
            
                iframe.onload = () => {
                  this.isLoading = false;
                };
            
                iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
            
                // Open modal manually using Bootstrap JS
                const modalElement = document.getElementById('simpleModal');
                const modal = new bootstrap.Modal(modalElement!);
                modal.show();
              } else {
                 this.isLoading = false;
                 this.swalAlerts.showSwalWarning('No Data', 'No Supplier Statement for preview.', 'No Supplier Statement was returned for the selected range.');
            
              }
            }
            
            
        loadSuppliers() {
        const entityId = +((localStorage.getItem('entityId')) + '');
        this.businessPartnerService.getSupplierListByEntity(entityId).subscribe(
          (customers: BusinessPartner[]) => {
              this.suppliers = customers.filter(
                  (supplier) =>
                    supplier.businessPartnerTypeId?.businessPartnerType === 'Supplier'
                );
          },
          (error: HttpErrorResponse) => {
            console.error('Error fetching Suppliers', error);
      
            //  Use SwalAlertsService for error with Chimp support
            this.swalAlerts.showErrorWithChimpSupport(
              'Failed to load Suppliers.',
              'Unable to fetch Suppliers list for this entity. Please check if the Suppliers service is responding.'
            );
          }
        );
      }



  onSupplierChange(event: any) {
    const selectedSupplierId = event.target.value;
    const selectedSupplier = this.suppliers.find(supplier => supplier.businessPartnerId === +selectedSupplierId);

    this.invoiceHead.supplierName = selectedSupplier?.bpName || '';
  }

  toggleAllSuppliers() {
    this.getAllSuppliers = !this.getAllSuppliers;
    if (this.getAllSuppliers) {
      this.invoiceHead.businessPartnerId = '';
    }
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

}
