import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private tokenKey = 'auth_token';
  private inactivityTimer: any;
  private listenersInitialized = false;
  // private inactivityTimeout = 1 * 60 * 1000; // 1 minute
  // private inactivityTimeout = 15 * 60 * 1000; // 15 minutes
  // private inactivityTimeout = 30 * 60 * 1000; // 30 minutes
  // private inactivityTimeout = 60 * 60 * 1000; // 1 hour
   private inactivityTimeout = 2 * 60 * 60 * 1000; // 2 hours

  private activityEvents = ['click', 'mousemove', 'keydown', 'scroll'];
  private boundResetFn = this.resetInactivityTimer.bind(this);

  constructor(private router: Router) {}

  // Get token
  getToken(): string | null {
    return sessionStorage.getItem(this.tokenKey);
  }

  // Save token and start timer
  setToken(token: string): void {
    sessionStorage.setItem(this.tokenKey, token);
    this.startInactivityTimer();
  }

  // Check if user is logged in
  isLoggedIn(): boolean {
    const token = this.getToken();
    return !!token && !this.isTokenExpired(token);
  }

  // Check if token is expired
  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const exp = payload.exp;
      const now = Math.floor(Date.now() / 1000);
      return exp < now;
    } catch (e) {
      return true;
    }
  }

  // Optional: auto-logout on expired token
  validateSession(): void {
    const token = this.getToken();
    if (!token || this.isTokenExpired(token)) {
      this.logout();
      Swal.fire('Session expired', 'Please login again.', 'info');
    }
  }

  // Start inactivity timer
  startInactivityTimer(): void {
    clearTimeout(this.inactivityTimer);
    // console.log('✅ Inactivity timer started');

    this.inactivityTimer = setTimeout(() => {
      // console.log('⏰ Inactivity timeout reached');
      this.logout();
      Swal.fire('Session expired', 'You were logged out due to inactivity.', 'info');
    }, this.inactivityTimeout);

    if (!this.listenersInitialized) {
      this.activityEvents.forEach(event =>
        document.addEventListener(event, this.boundResetFn)
      );
      this.listenersInitialized = true;
      // console.log('✅ Inactivity listeners added');
    }
  }

  // Reset the inactivity timer
  resetInactivityTimer(): void {
    // console.log('♻️ Inactivity timer reset');
    this.startInactivityTimer();
  }

  // Remove all activity listeners
  private removeActivityListeners(): void {
    if (this.listenersInitialized) {
      this.activityEvents.forEach(event =>
        document.removeEventListener(event, this.boundResetFn)
      );
      this.listenersInitialized = false;
      // console.log('❌ Inactivity listeners removed');
    }
  }

  // Full logout
  logout(): void {
    // console.log('🚪 Logging out...');

    // Stop the timer
    clearTimeout(this.inactivityTimer);
    this.inactivityTimer = null;

    // Remove event listeners
    this.removeActivityListeners();

    // Clear session
    sessionStorage.removeItem(this.tokenKey);
    localStorage.clear();

    // Navigate to login
    this.router.navigate(['/user-login']);
  }
}
