<app-admin-navigation></app-admin-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<body>
  <div class="body">
    <div class="container">
      <div class="popup">
        <div class="popup-header">
          <h3>Create Business Partner</h3>
        </div>
        <form
          #cuspop="ngForm"
          (ngSubmit)="cuspop.form.valid && onSubmitCustomerForm()"
          class="row g-1"
          novalidate="feedback-form"
          (keydown)="preventSubmit($event)"
        >
          <div class="class-name">
            <div class="form-group">
              <label for="partner-type">Business Partner Type</label>
              <select
                class="input-style"
                id="partner-type"
                [(ngModel)]="
                  businessPartner.businessPartnerTypeId.businessPartnerTypeId
                "
                name="businessPartnerType"
                required
                [disabled]="isFinance"
              >
                <option value="" selected disabled>Select Partner Type</option>
                <option
                  *ngFor="let type of businessPartnerType"
                  [value]="type.businessPartnerTypeId"
                >
                  {{ type.businessPartnerType }}
                </option>
              </select>
              <div
                *ngIf="
                  cuspop.submitted &&
                  cuspop.controls['businessPartnerType']?.invalid
                "
                class="text-danger"
              >
                <div
                  *ngIf="cuspop.controls['businessPartnerType']?.errors?.['required']"
                >
                  Business Partner Type is required.
                </div>
              </div>
            </div>
            <div class="form-group"></div>
          </div>
          <div class="class-name">
            <div class="form-group">
              <label for="bp-name"
                >Business Partner Name <span class="text-danger">*</span></label
              >
              <input
                type="text"
                id="bp-name"
                [(ngModel)]="businessPartner.bpName"
                name="bpName"
                required
              />

              <div
                *ngIf="cuspop.submitted && cuspop.controls['bpName'].invalid"
                class="text-danger"
              >
                <div *ngIf="cuspop.controls['bpName'].errors?.['required']">
                  Business Partner Name is required.
                </div>
              </div>
            </div>
          </div>

          <div class="class-name">
            <div class="form-group">
              <label for="email">Email </label>
              <input
                type="email"
                id="email"
                [(ngModel)]="businessPartner.email"
                name="email"
                pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
              />
              <div
                *ngIf="cuspop.submitted && cuspop.controls['email'].invalid"
                class="text-danger"
              >
                <div *ngIf="cuspop.controls['email'].errors?.['pattern']">
                  Invalid email format.
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="contactPhoneNumber">Contact Number</label>
              <input
                type="text"
                id="contactPhoneNumber"
                [(ngModel)]="businessPartner.contactPhoneNumber"
                name="contactPhoneNumber"
                #contactPhoneNumber="ngModel"
                pattern="^\+?[0-9\s-]{7,15}$"
              />
              <div
                *ngIf="
                  (contactPhoneNumber.dirty || cuspop.submitted) &&
                  contactPhoneNumber.invalid
                "
                class="text-danger"
              >
                <div *ngIf="contactPhoneNumber.errors?.['pattern']">
                  Invalid contact number format.
                </div>
              </div>
            </div>
          </div>
          <div class="class-name">
            <div class="form-group">
              <label for="abn-number"
                >ABN Number <small>(Optional)</small></label
              >
              <input
                type="text"
                id="bp-name"
                [(ngModel)]="businessPartner.abnNumber"
                name="abnNumber"
              />
            </div>
          </div>
          <div class="class-name">
            <div class="form-group">
              <label for="acn-number">ACN Number</label>
              <input
                type="text"
                id="acn-number"
                [(ngModel)]="businessPartner.acnNumber"
                name="acnNumber"
              />
            </div>
            <div class="form-group">
              <label for="default-payment-terms"
                >Default Payment Terms <small>(Optional)</small></label
              >
              <select
                id="default-payment-terms"
                [(ngModel)]="businessPartner.defaultPaymentTerms"
                name="defaultPaymentTerms"
              >
                <option value="" disabled selected>
                  Select default payment terms
                </option>
                <option value="net30">Net 30</option>
                <option value="2/10Net30">2/10 Net 30</option>
                <option value="dueOnReceipt">Due on Receipt</option>
              </select>
            </div>
          </div>
          <div class="class-name">
            <div class="form-group position-relative">
              <label for="business-address">Business Address</label>
              <input
                type="text"
                id="business-address"
                [(ngModel)]="businessPartner.businessAddress"
                name="businessAddress"
                (keyup)="checkBusinessAddress()"
                class="form-control"
              />
              <ul
                *ngIf="suggestedAddresses.length > 0"
                class="dropdown-menu show"
                style="
                  position: absolute;
                  width: 100%;
                  max-height: 200px;
                  overflow-y: auto;
                  z-index: 1000;
                "
              >
                <li
                  *ngFor="let address of suggestedAddresses"
                  class="dropdown-item"
                  (click)="selectAddress(address)"
                  style="cursor: pointer"
                >
                  {{ address.full_address }}
                </li>
              </ul>
            </div>
            <div class="form-group position-relative">
              <label for="delivery-address">Delivery Address</label>
              <input
                type="text"
                id="delivery-address"
                [(ngModel)]="businessPartner.deliveryAddress"
                name="deliveryAddress"
                (keyup)="checkDeliveryAddress()"
                class="form-control"
                [disabled]="isDeliveryAddressSynced"
              />
              <ul
                *ngIf="suggestedDeliveryAddresses.length > 0"
                class="dropdown-menu show"
                style="
                  position: absolute;
                  width: 100%;
                  max-height: 200px;
                  overflow-y: auto;
                  z-index: 1000;
                "
              >
                <li
                  *ngFor="let address of suggestedDeliveryAddresses"
                  class="dropdown-item"
                  (click)="selectDeliveryAddress(address)"
                  style="cursor: pointer"
                >
                  {{ address.full_address }}
                </li>
              </ul>
            </div>
          </div>
          <div class="class-name"></div>

          <div class="class-name">
            <div class="form-group position-relative"></div>
            <div class="col-md-6">
              <label
                [class.text-muted]="
                  !businessPartner.businessAddress ||
                  businessPartner.businessAddress.trim() === ''
                "
                style="margin-left: 10px;"
              >
                <input
                  type="checkbox"
                  (change)="syncDeliveryAddress($event)"
                  [disabled]="
                    !businessPartner.businessAddress ||
                    businessPartner.businessAddress.trim() === ''
                  "
                />
                Same as Business Address
              </label>
            </div>
          </div>

          <hr />
          <h2>Additional</h2>
          <div class="class-name">
            <div class="form-group">
              <label for="bank-account-name">Bank Account Name</label>
              <input
                type="text"
                id="bank-account-name"
                [(ngModel)]="businessPartner.bankAccountName"
                name="bankAccountName"
              />
            </div>
            <div class="form-group">
              <label for="bsb">BSB</label>
              <input
                type="text"
                id="bsb"
                [(ngModel)]="businessPartner.bsb"
                name="bsb"
              />
            </div>
            <div class="form-group">
              <label for="bank-account-number">Bank Account Number</label>
              <input
                type="text"
                id="bank-account-number"
                [(ngModel)]="businessPartner.accountNumber"
                name="accountNumber"
              />
            </div>
          </div>
          <div class="popup-footer">
            <button type="button" class="cancel-btn" (click)="confirmCancel()">
              Cancel
            </button>
            <button type="submit" class="add-btn">Save</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</body>
