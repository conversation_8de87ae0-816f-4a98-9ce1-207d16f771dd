<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <form
    #f="ngForm"
    (ngSubmit)="f.form.valid && onSubmit(f)"
    class="row g-1"
    novalidate="feedback-form"
    (keydown)="preventSubmit($event)"
  >
    <div class="heading">
      <h3>Create Invoice</h3>
      <div class="header-btn-section">
        <label class="transparent-button">
          <div class="invoice-file">
            <input
              type="file"
              id="invoice-file"
              name="invoice-file"
              (change)="onInvoiceFileSelected($event)"
              style="display: none"
            />
            Upload File
          </div>
        </label>
      </div>
      <div class="button-group">
        <button
          type="submit"
          class="transparent-button"
          (click)="setStatus('Pending')"
          [disabled]="isSaving"
        >
          {{ isSaving ? 'Saving...' : 'Save' }}
        </button>
      </div>
    </div>
    <div class="bd">
      <div class="form-section">

        <!-- First row: Invoice Number and Posting Date -->
        <div class="row">
          <!-- Invoice Number -->
          <div class="col-md-6">
            <div class="form-group">
              <div class="row">
                <div class="col-12">
                  <label for="invoiceNo" class="form-label">Invoice Number</label>
                </div>
                <div class="col-12">
                  <input
                    type="text"
                    id="invoiceNumber"
                    name="invoiceNumber"
                    class="form-control"
                    [(ngModel)]="invoiceHead.invoiceNumber"
                    #invoiceNumber="ngModel"
                    readonly
                  />
                  <div *ngIf="f.submitted && f.controls['invoiceNumber'].invalid" class="text-danger">
                    <div *ngIf="f.controls['invoiceNumber'].errors?.['required']">Invoice No is required.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Posting Date -->
          <div class="col-md-6">
            <div class="form-group">
              <div class="row">
                <div class="col-12">
                  <label for="postingDate" class="form-label">Posting Date</label>
                </div>
                <div class="col-12">
                  <input
                    (change)="onInvoiceDateChange()"
                    type="date"
                    id="postingDate"
                    name="postingDate"
                    class="form-control"
                    [(ngModel)]="invoiceHead.postingDate"
                    #postingDate="ngModel"
                    required
                  />
                  <div *ngIf="f.submitted && f.controls['postingDate'].invalid" class="text-danger">
                    <div *ngIf="f.controls['postingDate'].errors?.['required']">Posting Date is required.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Second row: Customer and Due Date -->
        <div class="row">
          <!-- Customer -->
          <div class="col-md-6">
            <div class="form-group">
              <div class="row">
                <div class="col-12">
                  <label for="customer" class="form-label">Customer</label>
                </div>
                <div class="col-12">
                  <select
                    class="form-select"
                    id="customer"
                    [(ngModel)]="invoiceHead.businessPartnerId"
                    (change)="onCustomerChange($event)"
                    name="customerName"
                    required
                  >
                    <option value="" selected disabled>Select Customer</option>
                    <option *ngFor="let customer of customers" [value]="customer.businessPartnerId">
                      {{ customer.bpName }}
                    </option>
                  </select>

                  <div class="d-flex justify-content-between align-items-center mt-1">
                    <div *ngIf="f.submitted && f.controls['customerName'].invalid" class="text-danger">
                      <div *ngIf="f.controls['customerName'].errors?.['required']">Customer is required.</div>
                    </div>
                    <button
                      type="button"
                      (click)="loadBusinessPartnerTypes()"
                      class="create-customer-btn ms-auto"
                      data-bs-target="#customerPopUpModal"
                      data-bs-toggle="modal"
                    >
                      Create New Customer
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Due Date -->
          <div class="col-md-6">
            <div class="form-group">
              <div class="row">
                <div class="col-12">
                  <label for="dueDate" class="form-label">Due Date</label>
                </div>
                <div class="col-12">
                  <input
                    type="date"
                    id="dueDate"
                    name="dueDate"
                    class="form-control"
                    [(ngModel)]="invoiceHead.dueDate"
                    #dueDate="ngModel"
                    (blur)="onValidUntilDateChange()"
                    required
                  />
                  <div *ngIf="f.submitted && f.controls['dueDate'].invalid" class="text-danger mt-1">
                    <div *ngIf="f.controls['dueDate'].errors?.['required']">Due Date is required.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Third row: Trading Name -->
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="form-group">
              <div class="row">
                <div class="col-12">
                  <label for="tradingName" class="form-label">Trading Name</label>
                </div>
                <div class="col-12">
                  <select
                    class="form-select"
                    id="tradingName"
                    [(ngModel)]="invoiceHead.entityTradingNameId"
                    name="tradingName"
                    [disabled]="entityTradingNames.length <= 1"
                    required
                  >
                    <option value="" selected disabled>Select Trading Name</option>
                    <option *ngFor="let tradingName of entityTradingNames" [value]="tradingName.entityTradingNameId">
                      {{ tradingName.tradingName }}
                    </option>
                  </select>
                  <div *ngIf="f.submitted && f.controls['tradingName'].invalid" class="text-danger mt-1">
                    <div *ngIf="f.controls['tradingName'].errors?.['required']">Trading Name is required.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

  <!-- Uploaded document -->
<div class="col-md-6">
  <div class="form-group">
    <div class="row">
      <div class="col-12">
        <label class="form-label">Uploaded Document</label>
      </div>
      <div class="col-12">
        <ng-container *ngIf="uploadedFileName">
          <div *ngIf="url; else fileNameOnly">
            <img
              [src]="url"
              alt="Uploaded Invoice Document"
              class="img-thumbnail"
              style="max-height: 200px; object-fit: contain;"
            />
          </div>
          <ng-template #fileNameOnly>
            <p class="mb-0">
              <i class="bi bi-file-earmark-text"></i> {{ uploadedFileName }}
            </p>
          </ng-template>
        </ng-container>
        <div *ngIf="!uploadedFileName" class="text-muted">No file uploaded</div>
      </div>
    </div>
  </div>
</div>

        </div>

      </div>

        <div class="form-row">

          <div class="search-bar d-flex justify-content-start">
            <button type="button" class="btn btn-primary new-item" data-bs-target="#itemPopupModal" data-bs-toggle="modal">
              <i class="bi bi-plus-circle-fill"></i> Add New Item
            </button>
          </div>
        </div>

      <div class="table-section">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th style="width: 45%">Item</th>
                <th style="width: 35%">Description</th>
                <th style="width: 12%">Account</th>
                <th style="width: 7%">Quantity</th>
                <th style="width: 10%;">
                  Unit Price
                </th>
                <th style="width: 8%">Discount</th>
                <th style="width: 8%">Discount Type</th>
                <th style="width: 8%">
                  {{ businessEntity.countryId.defaultTaxType }}({{
                    businessEntity.countryId.defaultTaxRate
                  }}%)
                </th>
                <th style="width: 5%; text-align: right;">Tax</th>
                <th style="width: 14%; text-align: right; padding-right: 0;">
                  Amount ({{ businessEntity.countryId.defaultCurrency }})
                </th>
                <th style="width: 3%"></th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="let detail of invoiceHead.invoiceDetails; let i = index"
              >
                <td>
                  <!-- ng-select will only show if itemCode is not 'SISSERVICE' -->
                  <ng-select
                    *ngIf="
                      invoiceHead.invoiceDetails[i].salesItem?.itemCode !==
                      'SISSERVICE'
                    "
                    name="salesItem-{{ i }}"
                    [appendTo]="'body'"
                    [items]="allSalesItems"
                    bindLabel="description"
                    [(ngModel)]="invoiceHead.invoiceDetails[i].salesItem"
                    [searchable]="true"
                    [clearable]="false"
                    [disabled]="invoiceHead.invoiceDetails[i].description !== ''"
                    placeholder="Select item"
                    [searchFn]="customSearchFn"
                    (change)="
                      onItemSelected(invoiceHead.invoiceDetails[i].salesItem, i)
                    "
                  >
                    <ng-template ng-option-tmp let-item="item">
                      {{ item.itemCode }} - {{ item.description }}
                    </ng-template>
                  </ng-select>
                </td>

                <td>
                  <input
                    type="text"
                    [(ngModel)]="invoiceHead.invoiceDetails[i].description"
                    name="description-{{ i }}"
                    class="form-control"
                    placeholder="Enter description"
                    [disabled]="
                      invoiceHead.invoiceDetails[i].salesItem &&
                      invoiceHead.invoiceDetails[i].salesItem.itemCode !==
                        'SISSERVICE' &&
                      invoiceHead.invoiceDetails[i].salesItem.itemCode !== ''
                    "
                    (input)="onDescriptionInput(i)"
                  />
                </td>
                 <td>
                  <select
                    class="form-control"
                    name="coaLedgerAccountId-{{ i }}"
                    required
                    [(ngModel)]="
                      invoiceHead.invoiceDetails[i].coaLedgerAccountId
                    "
                    (change)="onGLChange($event, i)"
                  >
                    <option value="" disabled selected>GL Account</option>
                    <option
                      *ngFor="let account of glAccounts"
                      [value]="account.coaLedgerAccountId"
                    >
                      {{ account.ledgerAccountName }}
                    </option>
                  </select>
                </td>


                <td>
                  <input
                    type="number"
                    [(ngModel)]="invoiceHead.invoiceDetails[i].quantity"
                    (input)="updateQuantity(i)"
                    name="quantity-{{ i }}"
                    class="form-control"
                    min="0"
                  />
                </td>
                <td>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">$</span>
                    </div>
                    <input
                      type="number"
                      [(ngModel)]="invoiceHead.invoiceDetails[i].unitPrice"
                      (input)="updateAmount(i)"
                      name="unitPrice-{{ i }}"
                      class="form-control"
                      min="0"
                      step="0.01"
                      [disabled]="
                        invoiceHead.invoiceDetails[i].salesItem &&
                        invoiceHead.invoiceDetails[i].salesItem.itemCode !==
                          'SISSERVICE' &&
                        invoiceHead.invoiceDetails[i].salesItem.itemCode !== ''
                      "
                    />
                  </div>
                </td>
                <td>
                  <input
                    type="number"
                    [(ngModel)]="invoiceHead.invoiceDetails[i].discount"
                    (input)="updateAmount(i)"
                    name="discount-{{ i }}"
                    class="form-control"
                    min="0"
                  />
                </td>
                <td>
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="radio"
                      name="discountType{{ i }}"
                      id="percent{{ i }}"
                      value="B"
                      [(ngModel)]="invoiceHead.invoiceDetails[i].discountType"
                      (change)="updateDiscountType(i, 'B')"
                    />
                    <label class="form-check-label" for="percent{{ i }}">%</label>

                    <input
                      class="form-check-input"
                      type="radio"
                      name="discountType{{ i }}"
                      id="dollar{{ i }}"
                      value="$"
                      [(ngModel)]="invoiceHead.invoiceDetails[i].discountType"
                      (change)="updateDiscountType(i, '$')"
                    />
                    <label class="form-check-label" for="dollar{{ i }}">$</label>
                  </div>
                </td>

                <!-- Tax Applicable Checkbox (Disabled if Description is Empty) -->
                <td>
                  <div class="form-check-tax">
                    <label>
                      Incl.GST
                      <input
                        type="checkbox"
                        [(ngModel)]="
                          invoiceHead.invoiceDetails[i].taxApplicability
                        "
                        name="taxApplicable-{{ i }}"
                        [disabled]="
                          !showTaxApplicabilityDropdown ||
                          !invoiceHead.invoiceDetails[i].description ||
                          invoiceHead.invoiceDetails[i].description.trim() === ''
                        "
                        (change)="onTaxApplicableChange(i)"
                        style="margin-left: 5px"
                      />
                    </label>
                  </div>
                </td>

                <td style="text-align: right">
                  {{ invoiceHead.invoiceDetails[i].tax | currency }}
                </td>
                <td style="text-align: right">
                  {{ invoiceHead.invoiceDetails[i].amount | currency }}
                </td>
                <td>
                  <button
                    type="button"
                    class="btn btn-link"
                    (click)="removeItem(i)"
                  >
                    <i class="fa fa-trash" style="color: red"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="form-row">
          <div class="search-bar d-flex justify-content-start">
            <button type="button" class="btn btn-primary new-item" (click)="addNewRow()">
              <i class="bi bi-plus-circle-fill"></i> Add New Row
            </button>
          </div>
        </div>
      </div>

      <!-- note text area -->
      <div class="main-row-note">
        <div class="notes-totals-section">
          <div class="notes-section">
            <label for="notes">Notes</label>
            <textarea
              id="notes"
              class="form-control"
              [(ngModel)]="invoiceHead.note"
              name="note"
            ></textarea>
          </div>

          <div class="totals-section" style="margin-top: 25px;">
            <div class="totals-row">
              <span class="totals-row1">Subtotal (After Discount)</span>
              <span class="totals-row2">{{
                invoiceHead.totalAmount | currency
              }}</span>
            </div>
            <div class="totals-row">
              <span class="totals-row1">Total Discount</span>
              <span class="totals-row2">{{
                calculateTotalDiscount() | currency
              }}</span>
            </div>
            <div class="totals-row">
              <span class="totals-row1"
                >Total {{ businessEntity.countryId.defaultTaxType }}
              </span>
              <span class="totals-row2">{{
                invoiceHead.totalGst | currency
              }}</span>
            </div>
            <div class="totals-row" style="border-top: 1px solid #eee;padding-top: 20px;">
              <strong class="totals-row1">Grand Total</strong>
              <strong class="totals-row2">{{
                invoiceHead.grandTotal | currency
              }}</strong>
            </div>
          </div>
        </div>
      </div>

    </div>
  </form>
</div>


<app-add-business-partner-popup
  [entityId]="entityId"
  [showUpdateLink]="showUpdateLink"
  (partnerAdded)="loadCustomers()"
  (partnerSaved)="invoiceHead.businessPartnerId = $event.businessPartnerId">
</app-add-business-partner-popup>

<app-add-item-popup
  [entityId]="entityId"
  (itemAdded)="onItemAdded($event)">
</app-add-item-popup>
