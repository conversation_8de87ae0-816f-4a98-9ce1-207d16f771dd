<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <form #f="ngForm" (ngSubmit)="f.form.valid && onSubmit(f)" class="row g-1" novalidate="feedback-form"
    (keydown)="preventSubmit($event)">
    <div class="heading">
      <h3>Copy From Quote</h3>
      <div class="button-group">
        <button type="submit" class="transparent-button" (click)="setStatus('Pending')" [disabled]="isSaving">
          {{ isSaving ? 'Saving...' : 'Save' }}
        </button>
      </div>
    </div>

    <div class="bd">
      <div class="form-section">

        <!-- First row: Quotation Number and Valid Until -->
        <div class="form-row" style="display: flex; justify-content: space-between;">
          <!-- invoice Number -->
          <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1;">
            <div class="form-dataInput" style="display: flex;">
              <label for="invoiceNo" id="quotation" style="margin-right: 10px; white-space: nowrap;">Invoice Number</label>
              <input type="text" id="invoiceNumber" name="invoiceNumber" class="input-style"
                [(ngModel)]="invoiceHead.invoiceNumber" #invoiceNumber="ngModel" readonly />
            </div>
          </div>

          <!-- Error message for Invoice Number -->
          <!-- <div *ngIf="f.submitted && f.controls['invoiceNumber'].invalid" class="text-danger" style="flex-basis: 100%; margin-top: 5px;">
            <div *ngIf="f.controls['invoiceNumber'].errors?.['required']">Invoice No is required.</div>
          </div> -->

          <!-- posting date -->
          <div class="form-group" style="display: flex;flex-grow: 1;">
            <label for="postingDate" style="margin-right: 20px; white-space: nowrap;">Posting Date</label>
            <input (change)="onInvoiceDateChange()" type="date" id="postingDate" name="postingDate" class="Input"
              [(ngModel)]="invoiceHead.postingDate" #postingDate="ngModel" required />
          </div>

          <!-- Error message for Posting Date -->
          <div *ngIf="f.submitted && f.controls['postingDate'].invalid" class="text-danger" style="flex-basis: 100%; margin-top: 5px;">
            <div *ngIf="f.controls['postingDate'].errors?.['required']">Posting Date is required.</div>
          </div>
        </div>

        <!-- Second row: Customer and Quotation Date -->
        <div class="form-row" style="display: flex; justify-content: space-between;">
          <!-- Customer -->
          <div class="form-group" style="flex-grow: 1;">
            <div class="form-dataInput" style="display: flex;">
              <label for="customer" style="margin-right: 50px; white-space: nowrap;">Customer</label>
              <select class="form-select" id="customer" [(ngModel)]="invoiceHead.businessPartnerId"
                (change)="onCustomerChange($event)" name="customerName" required>
                <option value="" selected disabled>Select Customer</option>
                <option *ngFor="let customer of customers" [value]="customer.businessPartnerId">
                  {{ customer.bpName }}
                </option>
              </select>
            </div>

            <div class="create-customer-container" style="margin-top: 10px;">
              <button type="button" (click)="loadBusinessPartnerTypes()" class="create-customer-btn"
                data-bs-target="#customerPopUpModal" data-bs-toggle="modal">
                Create New Customer
              </button>
            </div>

            <!-- Error message for Customer -->
            <div *ngIf="f.submitted && f.controls['customerName'].invalid" class="text-danger">
              <div *ngIf="f.controls['customerName'].errors?.['required']">Customer is required.</div>
            </div>
          </div>

          <!-- Due Date -->
          <div class="form-group" style="display: flex; flex-grow: 1;">
            <label for="dueDate" style="margin-right: 40px; white-space: nowrap;">Due Date</label>
            <input type="date" id="dueDate" name="dueDate" class="input-style" [(ngModel)]="invoiceHead.dueDate"
              #dueDate="ngModel" (blur)="onValidUntilDateChange()" required />
          </div>

          <!-- Error message for Due Date -->
          <div *ngIf="f.submitted && f.controls['dueDate'].invalid" class="text-danger" style="flex-basis: 100%; margin-top: 5px;">
            <div *ngIf="f.controls['dueDate'].errors?.['required']">Due Date is required.</div>
          </div>
        </div>

        <!-- third row -->
        <div class="form-row" style="display: flex; justify-content: space-between;">
          <!-- Trading Name -->
          <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1;">
            <div class="form-dataInput" style="display: flex;">
              <label for="tradingName" style="margin-right: 20px; white-space: nowrap;">Trading Name</label>
              <select class="form-select" id="tradingName" [(ngModel)]="invoiceHead.entityTradingNameId"
                name="tradingName" [disabled]="entityTradingNames.length <= 1" required>
                <option value="" selected disabled>Select Trading Name</option>
                <option *ngFor="let tradingName of entityTradingNames" [value]="tradingName.entityTradingNameId">
                  {{ tradingName.tradingName }}
                </option>
              </select>
            </div>

            <div *ngIf="f.submitted && f.controls['tradingName'].invalid" class="text-danger" style="margin-left: 140px; margin-top: 5px;">
              <div *ngIf="f.controls['tradingName'].errors?.['required']">Trading Name is required.</div>
            </div>
          </div>

          <div class="form-group" style="display: flex; flex-grow: 1;"></div>
        </div>
      </div>

      <div class="form-row">
        <div class="search-bar d-flex justify-content-start">
          <button type="button" class="btn btn-primary new-item" data-bs-target="#itemPopupModal" data-bs-toggle="modal">
            <i class="bi bi-plus-circle-fill"></i> Add New Item
          </button>
        </div>
      </div>

      <div class="table-section">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th style="width: 35%;">Item</th>
                <th style="width: 25%;">Description</th>
                <th style="width: 7%;">Quantity</th>
                <th style="width: 17%;">Unit Price</th>
                <th style="width: 5%;">Discount</th>
                <th style="width: 8%; text-align: center;">Disc. Type</th>
                <th style="width: 8%;">{{ businessEntity.countryId.defaultTaxType }}({{ businessEntity.countryId.defaultTaxRate }}%)</th>
                <th style="width: 5%; text-align: right;">Tax</th>
                <th style="width: 14%; text-align: right; padding-right: 0;">Amount ({{ businessEntity.countryId.defaultCurrency}})</th>
                <th style="width: 3%;"></th>
              </tr>
            </thead>

            <tbody>
              <tr *ngFor="let detail of invoiceHead.invoiceDetails; let i = index">
                <td>
                  <!-- ng-select will only show if itemCode is not 'SISSERVICE' -->
                  <ng-select *ngIf="invoiceHead.invoiceDetails[i].salesItem?.itemCode !== 'SISSERVICE'"
                    name="salesItem-{{ i }}" [appendTo]="'body'" [items]="allSalesItems" bindLabel="description"
                    [(ngModel)]="invoiceHead.invoiceDetails[i].salesItem" [searchable]="true" [clearable]="false"
                    [disabled]="invoiceHead.invoiceDetails[i].description !== ''" placeholder="Select item"
                    [searchFn]="customSearchFn" (change)="onItemSelected(invoiceHead.invoiceDetails[i].salesItem, i)">
                    <ng-template ng-option-tmp let-item="item">
                      {{ item.itemCode }} - {{ item.description }}
                    </ng-template>
                  </ng-select>
                </td>

                <td>
                  <input type="text" [(ngModel)]="invoiceHead.invoiceDetails[i].description" name="description-{{ i }}"
                    class="form-control" placeholder="Enter description"
                    [disabled]="invoiceHead.invoiceDetails[i].salesItem && invoiceHead.invoiceDetails[i].salesItem.itemCode !== 'SISSERVICE' && invoiceHead.invoiceDetails[i].salesItem.itemCode !== ''"
                    (input)="onDescriptionInput(i)" />
                </td>

                <td>
                  <input type="number" [(ngModel)]="invoiceHead.invoiceDetails[i].quantity" (input)="updateQuantity(i)"
                    name="quantity-{{ i }}" class="form-control" min="0" />
                </td>

                <td>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">$</span>
                    </div>
                    <input type="number" [(ngModel)]="invoiceHead.invoiceDetails[i].unitPrice" (input)="updateAmount(i)"
                      name="unitPrice-{{ i }}" class="form-control" min="0" step="0.01"
                      [disabled]="invoiceHead.invoiceDetails[i].salesItem && invoiceHead.invoiceDetails[i].salesItem.itemCode !== 'SISSERVICE' && invoiceHead.invoiceDetails[i].salesItem.itemCode !== ''" />
                  </div>
                </td>

                <td>
                  <input type="number" [(ngModel)]="invoiceHead.invoiceDetails[i].discount" (input)="updateAmount(i)"
                    name="discount-{{ i }}" class="form-control" min="0" />
                </td>

                <td>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="discountType{{ i }}" id="percent{{ i }}" value="B"
                      [(ngModel)]="invoiceHead.invoiceDetails[i].discountType" (change)="updateDiscountType(i, 'B')" />
                    <label class="form-check-label" for="percent{{ i }}">%</label>
                    <input class="form-check-input" type="radio" name="discountType{{ i }}" id="dollar{{ i }}" value="$"
                      [(ngModel)]="invoiceHead.invoiceDetails[i].discountType" (change)="updateDiscountType(i, '$')" />
                    <label class="form-check-label" for="dollar{{ i }} ">$</label>
                  </div>
                </td>

                <td>
                  <div class="form-check-tax">
                    <label>
                      Incl.GST
                      <input type="checkbox" [(ngModel)]="invoiceHead.invoiceDetails[i].taxApplicability"
                        name="taxApplicable-{{ i }}"
                        [disabled]=" !showTaxApplicabilityDropdown || !invoiceHead.invoiceDetails[i].description || invoiceHead.invoiceDetails[i].description.trim() === ''"
                        (change)="onTaxApplicableChange(i)" style="margin-left: 5px;" />
                    </label>
                  </div>
                </td>

                <td style="text-align: right">{{ invoiceHead.invoiceDetails[i].tax | currency }}</td>

                <td style="text-align: right">{{ invoiceHead.invoiceDetails[i].amount | currency }}</td>

                <td>
                  <button type="button" class="btn btn-link" (click)="removeItem(i)">
                    <i class="fa fa-trash" style="color: red;"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Button to add a new row -->
          <button type="button" class="btn btn-primary new-item" (click)="addNewRow()">
              <i class="bi bi-plus-circle-fill"></i> Add New Row
          </button>
        </div>
      </div>

      <!-- note text area -->
      <div class="main-row-note">
        <div class="notes-totals-section">
          <div class="notes-section">
            <label for="notes">Notes</label>
            <textarea id="notes" class="form-control" [(ngModel)]="invoiceHead.note" name="note"></textarea>
          </div>

          <div class="totals-section" style="margin-top: 25px;">
            <div class="totals-row">
              <span class="totals-row1">Gross Total</span>
              <span class="totals-row2">{{ invoiceHead.grandTotal | currency }}</span>
            </div>
            <div class="totals-row">
              <span class="totals-row1">Total Discount</span>
              <span class="totals-row2">{{calculateTotalDiscount()| currency}}</span>
            </div>
            <div class="totals-row">
              <span class="totals-row1">Total {{ businessEntity.countryId.defaultTaxType }} </span>
              <span class="totals-row2">{{ invoiceHead.totalGst | currency }}</span>
            </div>
            <div class="totals-row" style="
            border-top: 1px solid #eee;padding-top: 20px;
            ">
              <strong class="totals-row1">Grand Total</strong>
              <strong class="totals-row2">{{ invoiceHead.grandTotal | currency }}</strong>
            </div>
          </div>
        </div>
      </div>

    </div>
  </form>
</div>



<app-add-business-partner-popup
  [entityId]="entityId"
  [showUpdateLink]="showUpdateLink"
  (partnerAdded)="loadCustomers()"
  (partnerSaved)="invoiceHead.businessPartnerId = $event.businessPartnerId">
</app-add-business-partner-popup>

<app-add-item-popup
  [entityId]="entityId"
  (itemAdded)="onItemAdded($event)">
</app-add-item-popup>
