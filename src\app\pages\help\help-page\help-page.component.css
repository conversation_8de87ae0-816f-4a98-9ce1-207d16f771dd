 /* Container styling */
  .container {
    width: 90%;
    margin: 2% auto;
    padding: 20px; /* Add padding for breathing space */
    background-color: #ffffff; /* White background for content area */
    border-radius: 10px; /* Rounded corners for the container */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* Soft shadow for depth */
  }
  
  /* FAQ content styling */
  .faq-content {
    margin: 20px 0;
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
  }
  
  /* Accordion styling */
  .accordion {
    border: none; /* Remove the default border around the accordion */
  }
  
  .accordion-item {
    border: none; /* Remove border from accordion items */
    margin-bottom: 15px; /* Space between accordion items */
    border-radius: 10px; /* Rounded corners for accordion items */
    overflow: hidden; /* Prevents content overflow */
  }
  
  .accordion-button {
    font-weight: bold;
    border-radius: 10px;
    border: 1px solid transparent; /* Transparent border for spacing */
    padding: 10px 15px; /* Increase padding */
    background-color: #e8f0fe; /* Light blue background for buttons */
    color: #4262ff; /* Button text color */
    transition: background-color 0.3s, transform 0.3s; /* Smooth transition for effects */
  }
  
  .accordion-button:hover {
    background-color: #c7d8ff; /* Darker blue on hover */
    transform: scale(1.02); /* Slightly scale up on hover */
  }
  
  .accordion-button:not(.collapsed) {
    background-color: #d2e2ff; /* Darker background when expanded */
    color: #333; /* Change text color when expanded */
  }
  
  .accordion-body {
    font-size: 0.95rem;
    padding: 15px; /* Increase padding */
    border-top: 1px solid #dcdcdc; /* Subtle border at the top */
    background-color: #f9f9f9; /* Light grey background for body */
    border-radius: 0 0 10px 10px; /* Rounded bottom corners */
  }
  
  /* Heading styles */
  h2 {
    flex: 1;
    margin-bottom: 10px; /* Reduce bottom margin for headings */
    font-family: 'Inter', sans-serif; /* Ensure to use a proper font-family */
    font-size: 30px; /* Adjust font size */
    font-weight: 700;
    color: #4262ff; /* Blue color for headings */
    border-bottom: 2px solid #4262ff; /* Underline effect */
    padding-bottom: 5px; /* Space between heading and underline */
  }
  
  /* Paragraph styles */
  p {
    margin-bottom: 20px; /* Maintain margin for paragraphs */
    font-size: large;
    color: #555; /* Slightly darker text for better readability */
  }
  
  /* Responsive styles */
  @media (max-width: 768px) {
    .container {
      width: 95%; /* Full width for smaller screens */
    }
  
    h2 {
      font-size: 24px; /* Smaller font size for headings on mobile */
    }
    
  }

  /* Responsive video container */
    .responsive-video {
      position: relative;
      width: 100%;
      max-width: 600px; /* Your preferred max width */
      margin: 20px auto;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .responsive-video video {
      width: 100%;
      height: auto;
      display: block;
      background-color: #000; /* Black background for letterboxing */
    }

    /* Desktop-specific adjustments */
    @media (min-width: 769px) {
      .responsive-video {
        max-width: 600px; /* Explicit desktop width */
      }
      
      .responsive-video video {
        min-height: 338px; /* 600px width * 9/16 aspect ratio */
      }
    }
  