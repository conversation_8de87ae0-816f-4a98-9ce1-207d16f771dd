import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class StorageService {

  constructor() { }


  getEntityId(): number {
    return parseInt(localStorage.getItem('entityId') || '0', 10) || 0;
  }


  getUserId(): number {
    return parseInt(localStorage.getItem('userid') || '0', 10) || 0;

  }

    getEntityUUID(): string  {
    return localStorage.getItem('entityUuid') || '';
  }

}
