import { After<PERSON>iew<PERSON>nit, Component, ElementRef, HostListener, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { InvoiceHead } from '../invoice';
import { InvoiceService } from '../invoice.service';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { fromEvent, map, Subscription } from 'rxjs';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { BusinessPartner } from '../../business-partner/business-partner';
import { FormBuilder, FormGroup, NgForm, Validators } from '@angular/forms';
import { EmailTemplate, EmailTemplateService } from '../../settings/email-template.service';
import { EntityService } from '../../entity/entity.service';
import { Entity, EntityTradingName } from '../../entity/entity';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { StorageService } from '../../entity/storage.service';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';
import { forkJoin } from 'rxjs';
import { PageEvent } from '@angular/material/paginator';
import { PeriodClosingService } from '../../finance-module/period-closing/period-closing.service';
import * as bootstrap from 'bootstrap';


@Component({
  selector: 'app-invoice',
  templateUrl: './invoice.component.html',
  styleUrls: ['./invoice.component.css'],
})
export class InvoiceComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('sendInvoice') sendInvoice!: NgForm;
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  activeTab = 'all';
  currentPage = 1;
  pageSize = 10;
  invoiceHeadList: InvoiceHead[] = [];
  selectedInvoices: Set<InvoiceHead> = new Set<InvoiceHead>();
  isAllSelected: boolean = false;
  searchTerm: string = ''; // Store the search term
  filteredInvoices: InvoiceHead[] = []; // Hold filtered invoices based on the search term
  businessPartner: BusinessPartner = new BusinessPartner();
  startDate: string | null = null; // Bind to the start date input
  endDate: string | null = null;   // Bind to the end date input
  entity: Entity = new Entity();
  entityTradingName: EntityTradingName = new EntityTradingName();
  emailTemplates: any[] = [];
  invoiceTemplate: any = null;
  subject: string = '';
  content: string = '';
  invoices: any[] = [];
  templateHtmlContent: string = '';
  recipientEmail: string = ''; // Loaded from the database
  TempRecipientEmail: string =''; // User-entered email (optional)
  invoiceHead: InvoiceHead = new InvoiceHead();
  userRole: string = '';
  constructor(private invoiceService: InvoiceService,
     private entityService: EntityService, 
     private route: ActivatedRoute,
     private emailTemplateService: EmailTemplateService,
     private businessPartnerService: BusinessPartnerService,
     private router: Router,
     private storageService: StorageService,
     private swalAlertsService: SwalAlertsService,
     private periodClosingService: PeriodClosingService,
     public sanitizer: DomSanitizer) {
  }

  ngOnInit(): void {
 
    this.getInvoiceHeadListByEntity();
    this.recipientEmail = this.invoiceHead.recipient || '';
    this.TempRecipientEmail = ''; // Reset for user input
 
    const userStr = localStorage.getItem('user');
    if (userStr) {
    const user = JSON.parse(userStr);
    this.userRole = user.roleName; // e.g., "Free", "Premium"
  }
  }

  
  //dropdown
  isDropdownOpen = false;

  @ViewChild('dropdownRef') dropdownRef!: ElementRef;

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown() {
    this.isDropdownOpen = false;
  }

  // Listen for clicks on the whole document
  @HostListener('document:click', ['$event']) 
  handleClickOutside(event: MouseEvent) {
    if (
      this.dropdownRef && !this.dropdownRef.nativeElement.contains(event.target) 
    ) {
      this.closeDropdown();
    }
  }

  loadRecipientEmail(invoice: InvoiceHead): void {
    if (invoice.businessPartnerId) {
      this.businessPartnerService.getBusinessPartnerById(invoice.businessPartnerId).subscribe(
        (businessPartner) => {
          if (businessPartner && businessPartner.email) {
            this.recipientEmail = businessPartner.email;
            this.TempRecipientEmail = businessPartner.email;
          } else {
            this.recipientEmail = ''; // Clear if no email found
            this.TempRecipientEmail = '';
            console.warn('No email found for the selected business partner.');
          }
        },
        (error) => {
          console.error('Error fetching recipient email:', error);
          this.recipientEmail = '';
          this.TempRecipientEmail = '';
        }
      );
    }
  }

  handleSendInvoiceClick(): void {
  this.closeDropdown();

  const entityId = +((localStorage.getItem('entityId')) + "");
  this.emailTemplateService.getEmailTemplateByEntityId(entityId).subscribe(
    (data: EmailTemplate[]) => {
      if (data && data.length > 0) {
        this.emailTemplates = data;
        this.invoiceTemplate = this.emailTemplates.find(template => template.emailType === 'invoice');

        if (!this.invoiceTemplate) return;

        const today = new Date();
        const currentYear = today.getFullYear().toString();

        // Only 1 invoice selected
        if (this.selectedInvoices.size === 1) {
          const selectedInvoice = Array.from(this.selectedInvoices)[0];

          if (selectedInvoice.isLocked) {
            Swal.fire({
              title: 'Locked Period Detected',
              text: `The selected invoice (${selectedInvoice.invoiceNumber}) belongs to a closed accounting period and cannot be emailed.`,
              icon: 'warning',
              confirmButtonText: 'OK',
              confirmButtonColor: '#ff7e5f',
            });
            return;
          }

          this.subject = this.invoiceTemplate.subject.replace('${invoiceNumber}', selectedInvoice.invoiceNumber);
          this.loadRecipientEmail(selectedInvoice);

          this.businessPartnerService.getBusinessPartnerById(selectedInvoice.businessPartnerId).subscribe(
            (bp) => {
              const bpName = bp?.bpName || 'Valued Customer';
              const invoiceNumber = selectedInvoice.invoiceNumber || 'N/A';

              this.templateHtmlContent = this.invoiceTemplate.content
                .replace('${businessPartnerName}', bpName)
                .replace('${invoiceNumber}', invoiceNumber)
                .replace('${currentYear}', currentYear);

              const parsed = new DOMParser().parseFromString(this.templateHtmlContent, 'text/html');
              this.content = parsed.body.textContent || "";

              // ✅ Open modal manually
              const modal = new bootstrap.Modal(document.getElementById('sendInvoiceModal')!);
              modal.show();
            }
          );
        } else {
          // Multiple invoices
          const lockedInvoices = Array.from(this.selectedInvoices).filter(i => i.isLocked);
          if (lockedInvoices.length > 0) {
            Swal.fire({
              title: 'Locked Period Detected',
              text: `One or more selected invoices belong to a closed accounting period and cannot be emailed: ${lockedInvoices.map(i => i.invoiceNumber).join(', ')}`,
              icon: 'warning',
              confirmButtonText: 'OK',
              confirmButtonColor: '#ff7e5f',
            });
            return;
          }

          this.subject = 'Your Invoices are Ready!';
          this.templateHtmlContent = this.invoiceTemplate.content
            .replace('${businessPartnerName}', 'Valued Customer')
            .replace('${invoiceNumber}', 'Multiple')
            .replace('${currentYear}', currentYear);

          const parsed = new DOMParser().parseFromString(this.templateHtmlContent, 'text/html');
          this.content = parsed.body.textContent || "";

          // ✅ Open modal manually
          const modal = new bootstrap.Modal(document.getElementById('sendInvoiceModal')!);
          modal.show();
        }
      }
    }
  );
}

  
  onCreateCreditNote(invoice: any) {
    this.router.navigate(['/create-credit-note'], {
      queryParams: {
        invoiceNumber: invoice.invoiceNumber,
        transactionDate: invoice.postingDate,
        amount: invoice.grandTotal,
        paidAmount: invoice.paidTotal,
        balanceDue: invoice.balanceDue
      }
    });
  }

// page = 0;
// size = 10;
// totalElements = 0;

// onPageChange(event: PageEvent) {
//   this.page = event.pageIndex;
//   this.size = event.pageSize;
//   // this.selectedInvoices.clear();
//   // this.isAllSelected = false;
//   this.getInvoiceHeadListByEntity();
// }

// private getInvoiceHeadListByEntity() {
//   const entityId = +((localStorage.getItem('entityId')) + '');
//   this.invoiceService.getInvoiceHeadListByEntityPaginated(entityId, this.page, this.size).subscribe(response => {
//     this.invoiceHeadList = response.content;
//     this.totalElements = response.totalElements;
//     this.filterInvoices();
//   });
// }



private getInvoiceHeadListByEntity() {
  const entityId = +((localStorage.getItem('entityId')) + '');

  this.invoiceService.getInvoiceHeadListByEntity(entityId).subscribe(
    (data: InvoiceHead[]) => {
      this.invoiceHeadList = data;
      this.filteredInvoices = this.invoiceHeadList; // Add filtered array if used

      // 🔐 Check lock for each invoice based on posting date
      this.filteredInvoices.forEach((invoice) => {
        const date = invoice.postingDate;
        this.periodClosingService.isDateLocked(entityId, date).subscribe({
          next: (locked: boolean) => {
            (invoice as any).isLocked = locked; // add isLocked dynamically if model doesn't have it
          },
          error: (err) => {
            console.error('Error checking period lock for invoice:', err);
            (invoice as any).isLocked = false;
          }
        });
      });

    },
    (error) => {
      console.error('Error fetching InvoiceHead list:', error);
    }
  );
}

  // Function to filter invoices based on the search term and active tab
  filterInvoices(): void {
    if (!this.searchTerm && !this.startDate && !this.endDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Search Criteria',
        text: 'Please provide at least one search criterion: Invoice Number or Customer, Start Date, or End Date.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    let filtered = this.invoiceHeadList;

    // Filter by active tab
    if (this.activeTab !== 'all') {
      filtered = filtered.filter(invoice => invoice.invoiceStatus.toLowerCase() === this.activeTab);
    }

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(invoice =>
        invoice.invoiceNumber.toString().toLowerCase().includes(searchTermLower) ||
        invoice.reference.toLowerCase().includes(searchTermLower)
      );
    }

    // Filter by date range
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(invoice => new Date(invoice.postingDate) >= startDate);
    }

    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(invoice => new Date(invoice.postingDate) <= endDate);
    }

    this.filteredInvoices = filtered;
  }

  // Reset filters
  resetFilters() {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.activeTab = 'all'; // Reset the active tab to 'all'
    this.filteredInvoices = this.invoiceHeadList;
  }

  // Function to handle changes in the search input
  onSearchChange() {
    this.filterInvoices(); // Call filter function on input change
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    if (this.searchTerm || this.startDate || this.endDate) {
      this.filterInvoices(); // Filter invoices only if search criteria are provided
    } else {
      this.filteredInvoices = this.activeTab === 'all' 
        ? this.invoiceHeadList 
        : this.invoiceHeadList.filter(invoice => invoice.invoiceStatus.toLowerCase() === this.activeTab);
    }
  }


  toggleSelection(invoice: InvoiceHead, event: any): void {
    if (event.target.checked) {
      this.selectedInvoices.add(invoice);
    } else {
      this.selectedInvoices.delete(invoice);
    }
  }

  selectAll(event: any): void {
    this.isAllSelected = event.target.checked;

    if (this.isAllSelected) {
      this.invoiceHeadList.forEach(invoice => {
        this.selectedInvoices.add(invoice);
      });
    } else {
      this.selectedInvoices.clear();
    }
  }

  @ViewChild('closePreview') closePreview: any;
  @ViewChild('closeSendInvoice') closeSendInvoice: any;
  isSending: boolean = false;
  sendSelectedInvoices(): void {
     const userString = localStorage.getItem('user');
     const entityId = +((localStorage.getItem('entityId')) + "");
    if (!userString) {
      // Handle case where user is not found
      return;
    }

    const currentUser = JSON.parse(userString);
    const userRole = currentUser.roleName;
    const userId = currentUser.id;

    if (userRole === 'Free') {
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth();
      const currentYear = currentDate.getFullYear();

      // ✅ Count invoices created by this user with status "Sent" this month
      const sentInvoicesThisMonth = this.invoiceHeadList.filter(invoice => {
        const createdDate = new Date(invoice.postingDate);
        return (
          invoice.userId === userId &&
          invoice.invoiceStatus === 'Sent' &&
          createdDate.getMonth() === currentMonth &&
          createdDate.getFullYear() === currentYear
        );
      }).length;

      if (sentInvoicesThisMonth >= 5) {

        Swal.fire({
          title: 'Limit Reached!',
          text: 'As a Free user, you can only send up to 5 invoices per month.',
          icon: 'info',
          confirmButtonText: 'Upgrade Plan',
          confirmButtonColor: '#007bff',
        }).then((result) => {
          if (result.isConfirmed) {

            window.location.href = '/manage-subscription';
          }
        });

        return;
      }
    }

    if (this.selectedInvoices.size === 0) {
      this.showError('No Invoices Selected', 'Please select invoices to send.', 'No Invoices Selected');
      return;
    }
  
    if (!this.TempRecipientEmail) {
      this.showError('Missing Email', 'Please enter a recipient email address.');
      return;
    }
  
    const sendInvoices = Array.from(this.selectedInvoices);
    const pending = sendInvoices.filter(inv => inv.invoiceStatus === 'Pending');
    const sent = sendInvoices.filter(inv => inv.invoiceStatus === 'Sent');
    const canceled = sendInvoices.filter(inv => inv.invoiceStatus === 'Canceled');
    const revised = sendInvoices.filter(inv => inv.invoiceStatus === 'Revised');
  
    if (canceled.length > 0) {
      this.showError('Error!', 'Some selected invoices are already canceled and cannot be sent.');
      return;
    }
  
    if (revised.length > 0) {
      this.showError('Error!', 'Some selected invoices are in revise status and cannot be sent.');
      return;
    }
  
  
    Swal.fire({
      title: 'Send Invoices',
      text: `Do you want to send the selected invoices to ${this.TempRecipientEmail}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Send',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (!result.isConfirmed) {
        this.clearSelectedCheckboxes();
        this.isAllSelected = false;
        return;
      }
  
      this.isSending = true;
  
      const fetchPromises = pending.map(inv => this.businessPartnerService.getBusinessPartnerById(inv.businessPartnerId).toPromise());
  
      Promise.all(fetchPromises)
        .then(partners => {
          partners.forEach((partner, i) => {
            pending[i].recipient = partner?.email ?? '';
          });
  
          const finalContent = this.templateHtmlContent.replace(
            /<body[^>]*>.*<\/body>/is,
            `<body><p>${this.content.replace(/\n/g, '</p><p>')}</p></body>`
          );
  
          const emailData = {
            TempRecipientEmail: this.TempRecipientEmail,
            subject: this.subject,
            content: finalContent,
            invoices: pending
          };

          const entityUuid = localStorage.getItem('entityUuid');

          if (!entityUuid) {
            this.isLoading = false;
            alert('Missing entity UUID.');
            return;
          }
  
          this.invoiceService.sendInvoicesWithEmail(emailData, entityUuid,entityId).subscribe(
            () => {
              Swal.fire({
                title: 'Success!',
                text: 'Invoices sent successfully.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
              }).then(() => this.resetAfterSend());
            },
            error => {
              this.isSending = false;
              console.error('Failed to send invoices:', error);
              this.showError('Error!', 'Failed to send invoices.', 'Failed to send invoices.');
            }
          );
        })
        .catch(err => {
          this.isSending = false;
          console.error('Failed to fetch business partners:', err);
          this.showError('Error!', 'Failed to fetch business partner details.', 'Failed to fetch business partner details.');
        });
    });
  }

  showError(title: string, text: string, chatbotInput?: string): void {
    Swal.fire({
      title,
      text,
      icon: 'error',
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#be0032',
      cancelButtonColor: '#007bff',
      showCancelButton: chatbotInput ? true : false,
    }).then(result => {
      if (chatbotInput && result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        this.askChimp(chatbotInput);
      }
    });
  }
  
  askChimp(input: string): void {
    if (!this.chatBotComponent) {
      console.error('ChatBotComponent is not available.');
      return;
    }
  
    Swal.fire({
      title: 'Processing...',
      text: 'Please wait while Chimp processes your request.',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
        this.chatBotComponent.setInputData(input);
        this.chatBotComponent.responseReceived.subscribe(response => {
          Swal.close();
          this.chatResponseComponent.showPopup = true;
          this.chatResponseComponent.responseData = response;
          this.playLoadingSound();
          this.stopLoadingSound();
        });
      },
    });
  }
  
  resetAfterSend(): void {
    this.isSending = false;
    this.closeSendInvoice.nativeElement.click();
    this.router.navigate(['/invoice']);
    this.getInvoiceHeadListByEntity();
    this.selectedInvoices.clear();
    this.isAllSelected = false;
  }
  

    cancelSelectedInvoices(): void {
  if (this.selectedInvoices.size > 0) {
    const invoicesToCancel = Array.from(this.selectedInvoices);

    //  Check for locked invoice
    
            const lockedInvoices = invoicesToCancel.filter(invoice => invoice.isLocked);
    
            if (lockedInvoices.length > 0) {
      
              Swal.fire({
                title: 'Locked Period Detected',
                text: `Selected Invoice(s) that belong to a closed accounting period and cannot be cancelled: ${lockedInvoices.map(b => b.invoiceNumber).join(', ')}`,
                icon: 'warning',
                confirmButtonText: 'OK',
                confirmButtonColor: '#ff7e5f'
              });
              return;
            }
    
    

    // Allow only "Sent" invoices to be canceled
    const nonSentInvoices = invoicesToCancel.filter(invoice => invoice.invoiceStatus !== 'Sent');

    if (nonSentInvoices.length > 0) {
      let errorMessage = 'Only invoices with status "Sent" can be canceled.\n';

      const statusGroups = nonSentInvoices.reduce((acc, invoice) => {
        acc[invoice.invoiceStatus] = (acc[invoice.invoiceStatus] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      Object.entries(statusGroups).forEach(([status, count]) => {
        errorMessage += `\n • ${count} invoice(s) with status "${status}"\n`;
      });

      this.swalAlertsService.showErrorDialog(errorMessage);
      return;
    }

    Swal.fire({
      title: 'Are you sure you want to cancel the selected invoices?',
      icon: 'warning',
      iconColor: '#512CA2',
      showCancelButton: true,
      confirmButtonText: 'Yes',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
      customClass: {
        confirmButton: 'swal-confirm-gradient',
        cancelButton: 'swal-cancel-transparent'
      },
    }).then((result) => {
      if (result.isConfirmed) {
        this.invoiceService.cancelSelectedInvoices(invoicesToCancel).subscribe(
          (response) => {
            this.swalAlertsService.showSuccessDialog('Success!', 'Invoices canceled successfully.', () => {
              this.router.navigate(['/invoice']);
              this.getInvoiceHeadListByEntity();
            });

            this.selectedInvoices.clear();
            this.isAllSelected = false;

            // Send cancellation email
            const entityUuid = localStorage.getItem('entityUuid');
            const entityId = +(localStorage.getItem('entityId') || '');

            if (!entityUuid) {
              this.isLoading = false;
              alert('Missing entity UUID.');
              return;
            }

            this.sendCancellationEmail(invoicesToCancel, entityUuid, entityId);
          },
          (error) => {
            console.error('Error canceling invoices:', error);
            this.swalAlertsService.showErrorDialog('Failed to cancel invoices.');
          }
        );
      }
    });
  } else {
    this.swalAlertsService.showErrorDialog('No invoices selected for cancellation.');
  }
}



  sendCancellationEmail(sentInvoices: InvoiceHead[], entityUuid: string, entityId: number): void {
    const emailRequests = sentInvoices
      .filter(invoice => invoice.businessPartnerId)
      .map(invoice =>
        this.businessPartnerService.getBusinessPartnerById(invoice.businessPartnerId).pipe(
          map(bp => ({
            email: bp?.email,
            bpName: bp?.bpName
          }))
        )
      );
  
    if (emailRequests.length === 0) return;
  
    forkJoin(emailRequests).subscribe(
      (partnerInfos: { email?: string; bpName?: string }[]) => {
        const emailRequests = [];
  
        for (let i = 0; i < sentInvoices.length; i++) {
          const invoice = sentInvoices[i];
          const partner = partnerInfos[i];
  
          if (!partner?.email || !partner.bpName) continue;
  
          const content = `
            Dear ${partner.bpName},<br><br>
            Your invoice <strong>${invoice.invoiceNumber}</strong> has been canceled.<br><br>
            Please contact us if you have any questions.<br><br>
            Best regards,<br>
            Ledger Chimp
          `;
  
          emailRequests.push({
            recipients: [partner.email],
            subject: `Invoice Cancellation Notice - ${invoice.invoiceNumber}`,
            content,
            invoices: [invoice],
            tempRecipientEmail: partner.email // optional: backend fallback
          });
        }
  
        // Send all emails one by one or in parallel
        emailRequests.forEach(emailData => {
          this.invoiceService.sendCancellationEmail(emailData, entityUuid,entityId).subscribe(
            (response) => console.log('Cancellation email sent successfully:', response),
            (error) => console.error('Error sending cancellation email:', error)
          );
        });
      },
      (error) => console.error('Error fetching business partner emails:', error)
    );
  }
  
  
    
  deleteInvoice(invoiceId: number): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you really want to delete this invoice?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (result.isConfirmed) {

        this.invoiceService.deleteInvoice(invoiceId)
          .subscribe(
            () => {

              Swal.fire({
                title: 'Deleted!',
                text: 'Invoice has been deleted.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
              }).then(() => {
                // Optionally update the UI or reload data
                this.getInvoiceHeadListByEntity();
              });
            },
            (error) => {
              console.error('Failed to delete invoice:', error);

              Swal.fire({
                title: 'Error!',
                text: 'Failed to delete invoice.',
                icon: 'error',
                confirmButtonText: 'OK',
                cancelButtonText: 'Ask Chimp',
                confirmButtonColor: '#be0032',
                cancelButtonColor: '#007bff',
                showCancelButton: true,
              }).then((result) => {
                if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                  if (this.chatBotComponent) {
                    Swal.fire({
                      title: 'Processing...',
                      text: 'Please wait while Chimp processes your request.',
                      allowOutsideClick: false,
                      didOpen: () => {
                        Swal.showLoading();
                        this.chatBotComponent.setInputData('Failed to delete invoice.');
                        this.chatBotComponent.responseReceived.subscribe(response => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound()
                        });
                      },
                    });
                  } else {
                    console.error('ChatBotComponent is not available.');
                  }
                }
              });
            }
          );
      }
    });
  }


  
  viewInvoice(id: number) {
    this.router.navigate(['/view-invoice', id]);
  }



    updateInvoice(id: number) {
  const invoice = this.invoiceHeadList.find(invoice => invoice.invoiceHeadId === id);

  if (!invoice) {
    this.swalAlertsService.showErrorDialog('Invoice not found.');
    return;
  }

  if (invoice.invoiceStatus === 'Pending') {
    this.router.navigate(['/update-invoice', id]);
  } else {
    let message = `Only invoices with status "Pending" can be edited. Current status: "${invoice.invoiceStatus}".`;

    Swal.fire({
      title: 'Editing Not Allowed!',
      text: message,
      icon: 'error',
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#be0032',
      cancelButtonColor: '#007bff',
      showCancelButton: true,
    }).then((result) => {
      if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        if (this.chatBotComponent) {
          Swal.fire({
            title: 'Processing...',
            text: 'Please wait while Chimp processes your request.',
            allowOutsideClick: false,
            didOpen: () => {
              Swal.showLoading();
              this.chatBotComponent.setInputData(message);
              this.chatBotComponent.responseReceived.subscribe(response => {
                Swal.close();
                this.chatResponseComponent.showPopup = true;
                this.chatResponseComponent.responseData = response;
                this.playLoadingSound();
                this.stopLoadingSound();
              });
            },
          });
        } else {
          console.error('ChatBotComponent is not available.');
        }
      }
    });
  }
}


  handleReviseInvoice() {
    // Ensure there is exactly one invoice selected for revision
    if (this.selectedInvoices.size === 0) {
      Swal.fire({
        title: 'Warning!',
        text: 'Please select an invoice to revise.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f',
      });
      return; // Exit if no invoice is selected
    }

    if (this.selectedInvoices.size > 1) {
      Swal.fire({
        title: 'Warning!',
        text: 'You can only revise one invoice at a time. Please select only one.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f',
      });
      return; // Exit if more than one invoice is selected
    }

    // Get the first selected invoice
    const selectedInvoice = Array.from(this.selectedInvoices)[0]; // Convert Set to array and get the first invoice
    const invoiceId = selectedInvoice.invoiceHeadId; // Get the invoice ID of the selected invoice

    // Check for locked (closed period)
    if (selectedInvoice.isLocked) {
    Swal.fire({
      title: 'Locked Period Detected',
      text: `The selected invoice (${selectedInvoice.invoiceNumber}) belongs to a closed accounting period and cannot be revised.`,
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f',
    });
    return;
  }
    // Check if the invoice status is 'Sent'
    if (selectedInvoice.invoiceStatus !== 'Sent') {
      Swal.fire({
        title: 'Warning!',
        text: 'You can only revise sent invoices.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f',
      });
      return; // Exit if the invoice is not in 'Sent' status
    }

    // Proceed to revise the selected invoice
    this.reviseInvoice(invoiceId); // Will only reach here if the invoice status is 'Sent'
  }


  reviseInvoice(id: number): void {
    this.router.navigate(['/revise-invoice', id]);
  }

  createNewInvoice() {
          /**  const userString = localStorage.getItem('user');
            if (!userString) {
              // Handle case where user is not found
              return;
            }
            
            const currentUser = JSON.parse(userString); // Now it's an object
            
            const userRole = currentUser.roleName;
            const userId = currentUser.id;
            
            if (userRole === 'Free') {
              const currentMonth = new Date().getMonth();
              const currentYear = new Date().getFullYear();
          
              // Count user's invoice created this month
              const freeUserInvoicesCount = this.invoiceHeadList.filter(invoice => {
                const createdDate = new Date(invoice.postingDate);
                return invoice.userId === userId &&
                       createdDate.getMonth() === currentMonth &&
                       createdDate.getFullYear() === currentYear;
              }).length;
          
              if (freeUserInvoicesCount >= 5) {
                Swal.fire({
                  title: 'Limit Reached!',
                  text: 'As a Free user, you can only create up to 5 Invoices per month.',
                  icon: 'info',
                  confirmButtonText: 'Upgrade Plan',
                  confirmButtonColor: '#007bff',
                });
                return;
              }
            }**/
    this.router.navigate(['/create-invoice']);
  }

  goToSavedQuote() {
    this.router.navigate(['/quotation']);
  }

  @ViewChild('invoicePreviewFrame') invoicePreviewFrame!: ElementRef;
  isLoading = false;
  private invoiceCache = new Map<number, string>();



  previewInvoice(invoiceId: number) {
    this.isLoading = true;

    const invoice = this.filteredInvoices.find(inv => inv.invoiceHeadId === invoiceId);

    if (invoice) {
      const fakeEvent = { target: { checked: true } };
      this.toggleSelection(invoice, fakeEvent);
    }

    const cachedBase64String = this.invoiceCache.get(invoiceId);
    if (cachedBase64String) {
      this.loadPdfIntoIframe(cachedBase64String);
      return;
    }
    const entityId = +((localStorage.getItem('entityId')) + "");
    const entityUuid = localStorage.getItem('entityUuid');

  if (!entityUuid) {
    this.isLoading = false;
    alert('Missing entity UUID.');
    return;
  }

    this.invoiceService.getInvoiceReport(invoiceId, entityId, entityUuid).subscribe(
      data => {
        const base64String = data.response;

        if (base64String) {
          this.invoiceCache.set(invoiceId, base64String);
          this.loadPdfIntoIframe(base64String);
        } else {
          this.isLoading = false;
          alert('No invoice data for preview.');
        }
      },
      error => {
        this.isLoading = false;
        alert('Error loading invoice preview.');
      }
    );
  }

  private loadPdfIntoIframe(base64String: string) {
    const pdfData = 'data:application/pdf;base64,' + base64String;
    const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
    const iframe = this.invoicePreviewFrame.nativeElement;
    iframe.onload = () => {
      this.isLoading = false;
    };
    iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
  }

  modalHideSubscription?: Subscription;

  ngAfterViewInit() {
    const modalElement = document.getElementById('invoicePreviewModal');
    if (modalElement) {
      this.modalHideSubscription = fromEvent(modalElement, 'hide.bs.modal').subscribe(() => {
        this.clearSelectedCheckboxes();
      });
    }
  }

  ngOnDestroy() {
    if (this.modalHideSubscription) {
      this.modalHideSubscription.unsubscribe();
    }
  }

  clearSelectedCheckboxes() {
    // Deselect all checkboxes
    this.selectedInvoices.clear();
  }


onCreateCreditNoteMultiple(): void {
  if (this.selectedInvoices.size > 1) {
    this.swalAlertsService.showSwalWarning(
    'Single Selection Required',
    'Please select only one invoice to proceed.',
    'Selection Error'
  );
return;
  }

  if (this.selectedInvoices.size ===0 ) {
    this.swalAlertsService.showSwalWarning('No Invoices Selected', 'Please select an invoice to create a credit note.', 'No Invoices Selected');
    return;
  }

  const invoicesToCredit = Array.from(this.selectedInvoices);

    const lockedInvoices = invoicesToCredit.filter(invoice => invoice.isLocked);
  
          if (lockedInvoices.length > 0) {
    
            Swal.fire({
              title: 'Locked Period Detected',
              text: `The selected Invoice that belong to a closed accounting period and cannot be credited: ${lockedInvoices.map(b => b.invoiceNumber).join(', ')}`,
              icon: 'warning',
              confirmButtonText: 'OK',
              confirmButtonColor: '#ff7e5f'
            });
            return;
          }
  

  // Ensure all invoices belong to the same customer
  const customerIds = invoicesToCredit.map(inv => inv.businessPartnerId);
  const allSameCustomer = customerIds.every(id => id === customerIds[0]);

  // if (!allSameCustomer) {
  //   this.swalAlertsService.showSwalWarning('Different Customers Selected', 'Please select invoices that belong to the same customer.', 'Different Customers Selected');
  //   return;
  // }

  // ✅ Allow invoices with status "Sent" or "Awaiting Payment" and balance > 0
  const validStatuses = ['Sent', 'Awaiting Payment'];

  const validInvoices = invoicesToCredit.filter(invoice =>
    validStatuses.includes(invoice.invoiceStatus) && invoice.balanceAmount > 0
  );

  const invalidInvoices = invoicesToCredit.filter(invoice =>
    !validStatuses.includes(invoice.invoiceStatus) || invoice.balanceAmount <= 0
  );

  if (validInvoices.length > 0) {
    const invoiceIds = validInvoices.map(inv => inv.invoiceHeadId);

    this.router.navigate(['/create-credit-note'], {
      queryParams: { invoiceIds: JSON.stringify(invoiceIds) }
    });

    if (invalidInvoices.length > 0) {
      const skipped = invalidInvoices.map(inv => inv.invoiceNumber).join(', ');
      this.swalAlertsService.showSwalWarning(
        'Some Invoices Were Skipped',
        `The following invoices were excluded because they are not "Sent" or "Awaiting Payment", or have zero balance: ${skipped}`,
        'Some Invoices Were Skipped'
      );
    }

  } else {
    this.swalAlertsService.showSwalWarning(
      'No Valid Invoices',
      'Only invoices with status "Sent" or "Awaiting Payment" and a balance greater than 0 can be used to create a credit note.',
      'No Valid Invoices'
    );
  }
}

onPaymentreceiptMultiple(): void {
  if (this.selectedInvoices.size === 0) {
    this.swalAlertsService.showSwalWarning('No Invoices Selected', 'Please select an invoice to create a Payment Receipt.', 'No Invoices Selected');
    return;
  }

  const invoicesToPay = Array.from(this.selectedInvoices);

  
    const lockedInvoices = invoicesToPay.filter(invoice => invoice.isLocked);
  
          if (lockedInvoices.length > 0) {
    
            Swal.fire({
              title: 'Locked Period Detected',
              text: `The selected Invoice that belong to a closed accounting period and cannot be paid: ${lockedInvoices.map(b => b.invoiceNumber).join(', ')}`,
              icon: 'warning',
              confirmButtonText: 'OK',
              confirmButtonColor: '#ff7e5f'
            });
            return;
          }
  

  // Ensure all selected invoices belong to the same customer
  const customerIds = invoicesToPay.map(inv => inv.businessPartnerId);
  const allSameCustomer = customerIds.every(id => id === customerIds[0]);

  if (!allSameCustomer) {
    this.swalAlertsService.showSwalWarning('Different Customers Selected', 'Please select invoices that belong to the same customer.', 'Different Customers Selected');
    return;
  }

  // ✅ Allow "Sent" and "Awaiting Payment" statuses with balance > 0
  const validStatuses = ['Sent', 'Awaiting Payment'];

  const validInvoices = invoicesToPay.filter(invoice =>
    validStatuses.includes(invoice.invoiceStatus) && invoice.balanceAmount > 0
  );

  const invalidInvoices = invoicesToPay.filter(invoice =>
    !validStatuses.includes(invoice.invoiceStatus) || invoice.balanceAmount <= 0
  );

  if (validInvoices.length > 0) {
    const invoiceIds = validInvoices.map(inv => inv.invoiceHeadId);

    this.router.navigate(['/create-payment-receipt'], {
      queryParams: { invoiceIds: JSON.stringify(invoiceIds) }
    });

    if (invalidInvoices.length > 0) {
      const skipped = invalidInvoices.map(inv => inv.invoiceNumber).join(', ');
      this.swalAlertsService.showSwalWarning(
        'Some Invoices Were Skipped',
        `The following invoices were excluded because they are not "Sent" or "Awaiting Payment", or have zero balance: ${skipped}`,
        'Some Invoices Were Skipped'
      );
    }

  } else {
    this.swalAlertsService.showSwalWarning(
      'No Valid Invoices',
      'Only invoices with status "Sent" or "Awaiting Payment" and a balance greater than 0 can be used to create a Payment Receipt.',
      'No Valid Invoices'
    );
  }
}


playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
 
 
 /**  exportToExcel(): void {
    if (!this.filteredInvoices || this.filteredInvoices.length === 0) {
      alert('No data available to export.');
      return;
    }

    const exportData = this.filteredInvoices.map(invoice => ({
      'Invoice Number': invoice.invoiceNumber,
      'Customer': invoice.reference,
      'Posting Date': new Date(invoice.postingDate).toISOString().split('T')[0], // Format as YYYY-MM-DD
      'Paid Date': invoice.paidDate ? new Date(invoice.paidDate).toISOString().split('T')[0] : '', // Handle null values
      'Balance Amount ($)': `$${invoice.balanceAmount.toFixed(2)}`, // Add dollar sign and format to 2 decimal places
      'Grand Total ($)': `$${invoice.grandTotal.toFixed(2)}`,
      'Invoice Status': invoice.invoiceStatus
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Invoices');

    const excelBubuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    this.saveAsExcelFile(excelBuffer, `Invoices_${timestamp}`);
}

private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    });

    saveAs(data, `${fileName}.xlsx`);
}**/




exportToExcel(): void {

  const businessEntityId = +((localStorage.getItem('entityId')) + "");
  if (!this.filteredInvoices || this.filteredInvoices.length === 0) {
    alert('No data available to export.');
    return;
  }

  // Fetch business entity details
  this.entityService.getBusinessEntityById(businessEntityId).subscribe((entity) => {
    if (!entity) {
      alert('Company details not found.');
      return;
    }

    // Convert invoice data
    const exportData = this.filteredInvoices.map(invoice => ({
      'Invoice Number': invoice.invoiceNumber,
      'Customer': invoice.reference,
      'Posting Date': new Date(invoice.postingDate).toISOString().split('T')[0],
      'Paid Date': invoice.paidDate ? new Date(invoice.paidDate).toISOString().split('T')[0] : '',
      'Balance Amount ($)': `$${invoice.balanceAmount.toFixed(2)}`,
      'Grand Total ($)': `$${invoice.grandTotal.toFixed(2)}`,
      'Invoice Status': invoice.invoiceStatus
    }));

    const worksheet = XLSX.utils.json_to_sheet([]);

    // Add company details at the top
    const header = [
      [entity.entityName], // Company Name
      [entity.businessAddress], // Address
      [''], // Empty row for spacing
      ['Invoice List'] // Title of the table
    ];
    
    XLSX.utils.sheet_add_aoa(worksheet, header, { origin: 'A1' }); // Insert at the top
    XLSX.utils.sheet_add_json(worksheet, exportData, { origin: 'A5', skipHeader: false }); // Start invoice data from row 5

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Invoices');

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    this.saveAsExcelFile(excelBuffer, `Invoices_${timestamp}`);
  }, (error) => {
    console.error('Error fetching company details:', error);
    alert('Failed to load company details.');
  });
}

private saveAsExcelFile(buffer: any, fileName: string): void {
  const data: Blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
  });

  saveAs(data, `${fileName}.xlsx`);
}

}
