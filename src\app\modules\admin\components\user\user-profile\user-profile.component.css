body {
  font-family: "Inter";
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-block: 50px;
  height: auto;
}

.form-container {
  background-color: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 750px;
  width: 100%;
  text-align: center;
}

.form-container h2 {
  margin-bottom: 30px;
  font-family: Inter;
  font-size: 30px;
  font-weight: 600;
  text-align: left;
  color: #535353;
}

.form-container h3 {
  color: #4262ff;
  margin-bottom: 40px;
  font-family: Inter;
  font-size: 20px;
  font-weight: 600;
  line-height: 38.73px;
  text-align: left;
}

.form-container h4 {
  text-align: left;
  margin-bottom: 20px;
  color: #333;
  font-family: Inter;
  font-size: 20px;
  font-weight: 600;
  line-height: 38.73px;
  text-align: left;
}

.form-container .user-name {
  display: flex;
  gap: 20px;
}

.form-container .class-name {
  display: flex;
  gap: 20px;
}

.form-container .class-name .form-group {
  width: 100%;
}

.form-group {
  margin-bottom: 30px;
  text-align: left;
  position: relative;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
  font-family: Inter;
  font-size: 15px;
  font-weight: 600;
  line-height: 25.41px;
  color: #444343;
  position: relative;
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 2px solid #c7c7c7;
  border-radius: 8px;
  box-sizing: border-box;
  font-size: 1em;
  font-family: Inter;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-left: 430px;
}

.form-actions button {
  padding: 10px 80px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-family: Inter;
  font-size: 14px;
  font-weight: 700;
  line-height: 29.05px;
  text-align: center;
  background-color: #4262ff;
  color: white;
  margin-top: 15px;
}

.input-group-1 {
  position: relative;
}

.input-group-1 input {
  padding-right: 110px;
}

.lookup-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  padding: 10px;
  border: none;
  border-radius: 5px;
  background-color: white;
  color: #4262ff;
  cursor: pointer;
  font-family: Inter;
  font-size: 14px;
  font-weight: 700;
  line-height: 24.2px;
  text-align: left;
}

.form-group-subscription-payment {
  margin-bottom: 20px;
  text-align: left;
  font-family: Inter;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-left: 400px;
  margin-top: 5px;
}

.check-box {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.check-box label {
  font-weight: bold;
}

.invite-button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  padding: 10px 00px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-family: Inter;
  font-size: 17px;
  font-weight: 700;
  line-height: 29.05px;
  text-align: center;
}

.invite-button:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

@media (max-width: 599px) {
  .form-actions {
    display: flex;
    justify-content: space-between;
    margin-left: 10px;
  }

  .form-actions button {
    padding: 10px 30px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-family: Inter;
    font-size: 14px;
    font-weight: 500;
    line-height: 29.05px;
    text-align: center;
    background-color: #4262ff;
    color: white;
    margin-top: 15px;
  }
}
