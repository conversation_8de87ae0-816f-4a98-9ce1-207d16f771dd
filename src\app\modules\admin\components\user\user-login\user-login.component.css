* {
  box-sizing: content-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: Inter;
  background-color: #1a1a1a;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  background-image: url("/assets/images/MacBook Air - 1.png");
  background-size: 100% auto;
  /* Ensures the image width matches the device screen width */
  background-repeat:repeat-y;
  background-position: top center;
  /* Ensures the image is centered on the top */
}
@media (min-width: 768px) {
.login-form {
    margin-top: 60px;
    background: #fff;
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    padding-inline: 40px;
    text-align: center;
    margin-bottom: 150px;
    box-shadow: 0px 1px 20px 15px #4262ff1a;
  }
}


@media (max-width: 768px) {
  body {
    background-size: 100% auto;
    /* Maintain the full width of the image on smaller screens */
  }

    .login-form {
      margin-top: 60px;
      background: #fff;
      border-radius: 20px;
      padding: 1rem;
      position: relative;
      text-align: center;
      margin-bottom: 150px;
      box-shadow: 0px 1px 20px 15px #4262ff1a;
    }

}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  flex-direction: column;
}


.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #a7a7a7;
}

h2 {
  margin-top: 10%;
  /* margin-left: 5%; */
  font-family: Segoe UI;
  font-size: 30px;
  font-weight: 700;
  text-align: left;
  color: #000000;
}

p {
  /* margin-left: 5%; */
  color: #a7a7a7;
  text-align: left;
  margin-bottom: 2.8rem;
}

.input-group {
  /* margin-left: 5%; */
  margin-bottom: 1rem;
  text-align: left;
  border-radius: 2px;
  border-color: #c7c7c7;
  /* background-color: #000000; */
}

.input-group label {
  display: block;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.input-group input {
  width: 100%;
  height: 50%;
  padding: 0.5rem;
  border: 1px solid #a7a7a7;
  border-radius: 9px;
  font-family: Inter;
  font-size: 15px;
  font-weight: 500;
  line-height: 25.41px;
}

.input-group input ::placeholder {
  color: #a7a7a7;
}

.actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  /* background-color: #000000; */
}

.login-btn {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: #fff;
  padding: 0.4rem 0;
  margin: 0.5rem 0rem;
  width: 100%;
  border: none;
  border-radius: 9px;
  cursor: pointer;
  font-family: Inter;
  font-weight: 600;
  line-height: 29.05px;
  text-align: center;
}

.login-btn:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

button.login-btn:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.create-account {
  background: #ffffff;
  color: #4262ff;
  padding: 0.3rem 0;
  margin: 0 0rem;
  width: 100%;
  border: 2px solid #4262ff;
  border-radius: 9px;
  cursor: pointer;
  font-family: Inter;
  font-weight: 600;
  line-height: 29.05px;
  text-align: center;
}

.create-account:hover {
  background: #4262ff;
  color: #ffffff;
}

.forgot-password {
  margin-left: 180px;
  color: #4262ff;
  text-decoration: none;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
}

.create-account:hover {
  text-decoration: none;
}

.alert {
  padding: 5px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 5px;
  font-size: 16px;
  line-height: 1.5;
  position: relative;
}

.alert-danger {
  color: #e4130f;
  background-color: #f2dede;
  border-color: #ebccd1;
}