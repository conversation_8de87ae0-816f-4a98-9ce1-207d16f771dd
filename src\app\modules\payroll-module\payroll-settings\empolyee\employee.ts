import { Deduction, Earning, PayCalendar, Reimbursement } from "../payroll-setting";

export class Personal {
    public employeeId: number = 0;
    public firstName: string = '';
    public lastName: string = '';
    public dateOfBirth: string = '';
    public gender: string = '';
    public address: string = '';
    public state: string = '';
    public postalCode: string = '';
    public phoneNumber: string = '';
    public emergencyPhoneNumber: string = '';
    public email: string = ''; 
    public status: boolean = false; 

}
export class Employment{
  public employmentId: number = 0;
  public startDate: string = '';
  public ordinaryEarningsRate: number = 0.0;
  public payCalendar: PayCalendar | null = null;
  public employmentBasis: string = '';
  public salaryType: string = '';
  public incomeType: string = '';
  public amount: number = 0.0;
  public hoursperWeek: number = 0.0;
  public eligibleForLeaveLoadingSuper: boolean | null = false;
  public eligibleForLeaveLoading: boolean  = false;

}

export class EmploymentData{
  public startDate: string = '';
  public ordinaryEarningsRate: number = 0.0;
  public payCalendar: PayCalendar | null = null;
  public personal: Personal | null = null;
  public employmentBasis: string = '';
  public salaryType: string = '';
  public amount: number = 0.0;
  public hoursperWeek: number = 0.0;
  employee: any;

}

export class BankAccountNew {
  bankAccountId: number  = 0 ;
  public bankAccountName: string = '';
  public remark: string = '';
  public accountName: string = '';
  public accountNumber: string = '';
  public bsbNumber: string = '';
  public isSaved: boolean = false;
  public isBalanceSalary2: boolean = true;
  public isAmount2: boolean = true;
  public amount: number = 0.0;
  
   
}

export class BankAccount {
  
  bankAccountId: number  = 0 ;
  public bankAccountName: string = 'primary';
  public remark: string = '';
  public accountName: string = '';
  public accountNumber: string = '';
  public bsbNumber: string = '';
  public amount: number = 0.0;
   
}

export class NewEarning {

  employeeEarningId: number  = 0 ;
  public earning?: Earning;
  public amount: number = 0.0
  public hours: number = 0.0
  public rate: number = 0.0
  public typeOfUnits:string = ''
}


export class NewDeduction {
  employeeDeductionId: number  = 0 ;
  public deduction: Deduction | null = null;
  public amount: number = 0.0
}
export class NewReimbursement {
  employeeReimbursementId: number  = 0 ;
  public reimbursement: Reimbursement | null = null;
  public amount: number = 0.0
}
export class NewLeave {

  public leaveType: LeaveType | null = null;
  public openingBalance: number = 0.0
  public accruedHoursPerWeek: number = 0.0
  public hoursPerWeek: number = 0.0
  public hoursAccruedAnnually: number = 0.0
  public leaveMethods: string = '';
 
}
export class Superannuation {

  fundABN: number = 0;
  fundName: string = '';
  usi: string = '';
  spin: string = '';
  productName: string = '';
  contributionRestrictions: string = '';
}

export class NewSuperannuation {

  public superannuationFund: Superannuation | null = null;
  public value: number = 0.0
  public type: string = '';
  public membershipNumber: string = '';
  public usi: string = '';
  public contributionType: string = '';
  
}
export class NewTax {

  // public tax: Tax | null = null;
  public amount: number = 0.0
}


export class EmployeeTax {
  taxId: number = 0;
  employee: any;
  entityId: number = 0;
  userId: number = 0;
  taxFileNumber: string = '';
  taxScale: string = '';
  taxLineName: string = '';
  residencyStatus: string = '';
  studyTrainingLoan: boolean = false;
  taxFreeThreshold: boolean = false;
  increaseTaxAmount: boolean = false;
  eligibleForLeaveLoadingSuper: boolean = false;
  taxOffset: string = '';
  date: string = '';

}

export class EmployeeMaster {
  employeeId: number = 0;
  entityId: number = 0;
  firstName: string = '';
  lastName: string = '';
  dateOfBirth: string = '';
  gender: string = '';
  address: string = '';
  state: string = '';
  postalCode: string = '';
  phoneNumber: string = '';
  email: string = '';
  employeeEmployments: EmployeeEmployment[] = [];
  employeeDeductions: EmployeeDeduction[] = [];
}


export class EmployeeDeduction {
  employeeDeductionId: number = 0;
  employee: EmployeeMaster = new EmployeeMaster();
  deduction: Deduction = new Deduction();
  entityId: number = 0;
  userId: number = 0;
  amount: number = 0.0;
  employeeName: string = '';
  date: string = '';
}

export class EmployeeEmployment {
  employmentId: number = 0;
  employee: EmployeeMaster = new EmployeeMaster();
  entityId: number = 0;
  userId: number = 0;
  ordinaryEarningsRate: string = '';
  payRollCalendar: string = '';
  employmentBasis: string = '';
  startDate: string = '';
  date: string = '';
}


export class LeaveCategory {
  leaveCategoryId: number= 0;
  entityId: number = 0;
  userId: number = 0;
  leaveCategory: string ='';
  units: string='';
  normalEntitlement: string='';
  leaveLoadingRate: number=0;
  showBalances: boolean= false;
  date: string ='';
}

export class LeaveType {
  leaveTypeId: number=0;
  leaveCategory: LeaveCategory = new LeaveCategory();
  entityId: number=0;
  leaveType: string='';
  leaveCalculationMethod: string='';
  hoursAccruedFullTime: number=0;
  hoursWorkedFortnightly: number=0;
}

export class EmployeeLeave {
  employeeLeaveId: number=0;
  employee: EmployeeMaster= new EmployeeMaster();  
  leaveType: LeaveType = new LeaveType(); 
  entityId: number=0;
  userId: number=0;
  units: number=0;
  normalEntitlement: number=0;
  leaveLoadingRate: number=0;
  showOnPaySlip: boolean= false;
  exemptFromSuperannuation: string='';
  showBalancesOnPayslip: boolean= false;
  balance: string='';
  date: string='';
  leaveHours: number=0;
  leaveAccrual: number=0;
  amount: number=0;
  accruedHoursPerWeek: number=0;
  hoursAccruedAnnually: number=0;
  hoursPerWeek: number=0;
  leaveMethods: string='';
  rate: number=0;
  accrualCurrentBalance: number = 0;

}



