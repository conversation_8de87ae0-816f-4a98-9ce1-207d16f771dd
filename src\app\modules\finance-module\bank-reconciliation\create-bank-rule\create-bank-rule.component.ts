import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { Component, OnInit } from '@angular/core';
import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';
import { HttpErrorResponse } from '@angular/common/http';
import { GlAccountService } from '../../gl-account/gl-account.service';
import { CoaLedgerAccount } from '../../gl-account/gl-account';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  BankRuleDetail,
  BankRuleDTO,
  BankRuleHead,
  BankRuleType,
} from '../bank-reconciliation';
import { BankReconciliationService } from '../bank-reconciliation.service';
import { BankAccount } from '../../bank/bank';
import { Router } from '@angular/router';

@Component({
  selector: 'app-create-bank-rule',
  templateUrl: './create-bank-rule.component.html',
  styleUrls: ['./create-bank-rule.component.css'],
})
export class CreateBankRuleComponent implements OnInit {
  selectedBankAccount!: BankAccount;
  activeTab: 'spend' | 'receive' = 'spend';
  matchFields: string[] = ['Descrption', 'Amount'];
  filterByOptions: string[] = ['Equals', 'Contains', 'Starts with'];
  supplierList: BusinessPartner[] = [];
  customerList: BusinessPartner[] = [];
  expenseLedgerAccountsList: CoaLedgerAccount[] = [];
  incomeLedgerAccountsList: CoaLedgerAccount[] = [];
  spendMoneyRuleForm!: FormGroup;
  receiveMoneyRuleForm!: FormGroup;

  constructor(
    private businessPartnerService: BusinessPartnerService,
    private BankRecService: BankReconciliationService,
    private glAccountService: GlAccountService,
    private fb: FormBuilder,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.selectedBankAccount = history.state?.selectedBankAccount;
    this.activeTab = history.state?.activeTab;
    if (!this.selectedBankAccount) {
      this.router.navigate(['/bank-account-list']);
      return;
    }
    this.loadBusinessPartners();
    this.loadLedgerAccounts();
    this.spendMoneyFormInit();
    this.receiveMoneyFormInit();
  }


  loadBusinessPartners() {
    const entityId = +(localStorage.getItem('entityId') || 0);
    this.businessPartnerService
      .getBusinessPartnersListByEntity(entityId)
      .subscribe(
        (partners: BusinessPartner[]) => {
          // Suppliers
          this.supplierList = partners.filter(
            (partner) =>
              partner.businessPartnerTypeId?.businessPartnerType === 'Supplier'
          );

          // Customers
          this.customerList = partners.filter(
            (partner) =>
              partner.businessPartnerTypeId?.businessPartnerType === 'Customer'
          );
        },
        (error: HttpErrorResponse) => {
          console.error('Error fetching Business Partner Type', error);
        }
      );
  }

  loadLedgerAccounts() {
    const entityId = +(localStorage.getItem('entityId') || 0);
    this.glAccountService.getCoaLedgerAccountListByEntity(entityId).subscribe({
      next: (response: CoaLedgerAccount[]) => {
        console.log('Ledger accounts loaded:', response);

        this.incomeLedgerAccountsList = response.filter(
          (ledgerAccount) =>
            ledgerAccount.coaHeaderId.accountHeaderType === 'Income' ||
            ledgerAccount.coaHeaderId.accountHeaderType === 'Other Income' ||
            ledgerAccount.coaHeaderId.accountHeaderType ===
              'Current liabilities' ||
            ledgerAccount.coaHeaderId.accountHeaderType ===
              'Non Current liabilities'
        );

        this.expenseLedgerAccountsList = response.filter(
          (ledgerAccount) =>
            ledgerAccount.coaHeaderId.accountHeaderType === 'Expenses' ||
            ledgerAccount.coaHeaderId.accountHeaderType === 'Current Assets' ||
            ledgerAccount.coaHeaderId.accountHeaderType === 'Non current assets'
        );
      },
      error: (err) => {
        console.error('Failed to load ledger accounts:', err);
      },
    });
  }

  receiveMoneyFormInit() {
    this.receiveMoneyRuleForm = this.fb.group({
      receiveRuleName: ['', Validators.required],
      receiveConditions: this.fb.array([]),
      receiveGLAccounts: this.fb.array([], Validators.required),
    });
    this.addReceiveCondition();
    this.addReceiveGLAccount(); // 👈 default row
  }

  spendMoneyFormInit() {
    this.spendMoneyRuleForm = this.fb.group({
      spendRuleName: ['', Validators.required],
      spendConditions: this.fb.array([]),
      spendGLAccounts: this.fb.array([], Validators.required),
    });
    this.addSpendCondition();
    this.addSpendGLAccount();
  }

  get receiveConditions(): FormArray {
    return this.receiveMoneyRuleForm.get('receiveConditions') as FormArray;
  }

  addReceiveCondition() {
    const group = this.fb.group({
      receiveMatchField: [null, Validators.required],
      receiveFilterBy: [null, Validators.required],
      receiveFilterText: ['', Validators.required],
    });

    this.receiveConditions.push(group);
  }

  removeReceiveCondition(index: number) {
    if (this.receiveConditions.length > 1) {
      this.receiveConditions.removeAt(index);
    }
  }

  get spendConditions(): FormArray {
    return this.spendMoneyRuleForm.get('spendConditions') as FormArray;
  }

  get spendGLAccounts(): FormArray {
    return this.spendMoneyRuleForm.get('spendGLAccounts') as FormArray;
  }

  addSpendGLAccount() {
    const group = this.fb.group({
      glAccountDescription: ['',Validators.required],
      glAccountId: [null, Validators.required],
      percentage: [
        0,
        [Validators.required, Validators.min(0.1), Validators.max(100)],
      ],
    });
    this.spendGLAccounts.push(group);
  }

  get totalSpendGLPercentage(): number {
    return this.spendGLAccounts.controls.reduce((sum, control) => {
      return sum + Number(control.get('percentage')?.value || 0);
    }, 0);
  }

  removeSpendGLAccount(index: number) {
    if (this.spendGLAccounts.length > 1) {
      this.spendGLAccounts.removeAt(index);
    }
  }

  addSpendCondition() {
    const group = this.fb.group({
      spendMatchField: [null, Validators.required],
      spendFilterBy: [null, Validators.required],
      spendFilterText: ['', Validators.required],
    });

    this.spendConditions.push(group);
  }

  removeSpendCondition(index: number) {
    if (this.spendConditions.length > 1) {
      this.spendConditions.removeAt(index);
    }
  }

  spendMoneyFormSubmit() {
    if (this.spendMoneyRuleForm.invalid) {
      this.spendMoneyRuleForm.markAllAsTouched();
      return;
    }

    const spendMoneyRuleValue = this.spendMoneyRuleForm.value;

    console.log(spendMoneyRuleValue);

    if (!spendMoneyRuleValue) {
      console.error('SpendMoneyRule value not exists');
      return;
    }

    const entityId = +(localStorage.getItem('entityId') || 0);
    const userId = +(localStorage.getItem('userid') || 0);

    const filterCondition: any[] = spendMoneyRuleValue.spendConditions;
    const glConditions: any[] = spendMoneyRuleValue.spendGLAccounts;

    if (!filterCondition || filterCondition.length == 0) {
      console.error('SpendMoneyRuleValue.spendConditions value not exists');
      return;
    }

    if (!glConditions || glConditions.length == 0) {
      console.error('SpendMoneyRuleValue.spendGLAccounts value not exists');
      return;
    }

    // Map conditions into BankRuleDetail array
    const filterRuleDetails: BankRuleDetail[] = filterCondition.map(
      (condition) => {
        return {
          ruleDetailType: 'F',
          ruleMatchField: condition.spendMatchField,
          ruleFilterBy: condition.spendFilterBy,
          ruleFilterText: condition.spendFilterText,
        };
      }
    );

    const glAccountAllocationsRuleDetails: BankRuleDetail[] = glConditions.map(
      (account) => {
        return {
          ruleDetailType: 'G',
          ruleDetailGlAccountId: account.glAccountId,
          ruleDetailGlAccountDescription:account.glAccountDescription,
          ruleDetailGlAccountPercentage: account.percentage,
        };
      }
    );

    // Create the Object
    const spendMoneyRuleHead: BankRuleHead = {
      bankRuleType: BankRuleType.SPEND,
      entityId: entityId,
      userId: userId,
      bankAccount: this.selectedBankAccount,
      bankRuleName: spendMoneyRuleValue.spendRuleName,
      selectedBusinessPartner: '',
    };

    const bankRuleDto: BankRuleDTO = {
      bankRuleHead: spendMoneyRuleHead,
      bankRuleDetailList: [
        ...filterRuleDetails,
        ...glAccountAllocationsRuleDetails,
      ],
    };

    // Save BankRule
    this.BankRecService.saveBankRule(bankRuleDto).subscribe(
      (data) => {
        if (data) {
          console.log(data);
          this.spendMoneyRuleForm.reset();
          this.spendConditions.clear();
          this.spendGLAccounts.clear();
          this.addSpendCondition();
          this.addSpendGLAccount();
        }
      },
      (error: HttpErrorResponse) => {
        console.error(error);
      }
    );
  }

  receiveMoneyFormSubmit() {
    if (this.receiveMoneyRuleForm.invalid) {
      this.receiveMoneyRuleForm.markAllAsTouched();
      return;
    }

    const receiveMoneyRuleValue = this.receiveMoneyRuleForm.value;

    if (!receiveMoneyRuleValue) {
      console.error('ReceiveMoneyRule value not exists');
      return;
    }

    const entityId = +(localStorage.getItem('entityId') || 0);
    const userId = +(localStorage.getItem('userid') || 0);

    const filterCondition: any[] = receiveMoneyRuleValue.receiveConditions;
    const glConditions: any[] = receiveMoneyRuleValue.receiveGLAccounts;

    if (!filterCondition || filterCondition.length === 0) {
      console.error('ReceiveMoneyRuleValue.receiveConditions value not exists');
      return;
    }

    if (!glConditions || glConditions.length === 0) {
      console.error('ReceiveMoneyRuleValue.receiveGLAccounts value not exists');
      return;
    }

    const filterRuleDetails: BankRuleDetail[] = filterCondition.map(
      (condition) => ({
        ruleDetailType: 'F',
        ruleMatchField: condition.receiveMatchField,
        ruleFilterBy: condition.receiveFilterBy,
        ruleFilterText: condition.receiveFilterText,
      })
    );

    const glAccountAllocationsRuleDetails: BankRuleDetail[] = glConditions.map(
      (account) => ({
        ruleDetailType: 'G',
        ruleDetailGlAccountId: account.glAccountId,
        ruleDetailGlAccountDescription:account.glAccountDescription,
        ruleDetailGlAccountPercentage: account.percentage,
      })
    );

    const receiveMoneyRuleHead: BankRuleHead = {
      bankRuleType: BankRuleType.RECEIVE,
      entityId: entityId,
      userId: userId,
      bankAccount: this.selectedBankAccount,
      bankRuleName: receiveMoneyRuleValue.receiveRuleName,
      selectedBusinessPartner: '',
    };

    const bankRuleDto: BankRuleDTO = {
      bankRuleHead: receiveMoneyRuleHead,
      bankRuleDetailList: [
        ...filterRuleDetails,
        ...glAccountAllocationsRuleDetails,
      ],
    };

    this.BankRecService.saveBankRule(bankRuleDto).subscribe(
      (data) => {
        if (data) {
          console.log('Receive Rule Saved:', data);
          this.receiveMoneyRuleForm.reset();
          this.receiveConditions.clear();
          this.receiveGLAccounts.clear();
          this.addReceiveCondition();
          this.addReceiveGLAccount();
        }
      },
      (error: HttpErrorResponse) => {
        console.error(error);
      }
    );
  }

  get receiveGLAccounts(): FormArray {
    return this.receiveMoneyRuleForm.get('receiveGLAccounts') as FormArray;
  }

  addReceiveGLAccount() {
    const group = this.fb.group({
      glAccountId: [null, Validators.required],
      glAccountDescription: ['',Validators.required],
      percentage: [
        0,
        [Validators.required, Validators.min(0), Validators.max(100)],
      ],
    });
    this.receiveGLAccounts.push(group);
  }

  removeReceiveGLAccount(index: number) {
    if (this.receiveGLAccounts.length > 1) {
      this.receiveGLAccounts.removeAt(index);
    }
  }

  get totalReceiveGLPercentage(): number {
    return this.receiveGLAccounts.controls.reduce((sum, control) => {
      return sum + Number(control.get('percentage')?.value || 0);
    }, 0);
  }
}
