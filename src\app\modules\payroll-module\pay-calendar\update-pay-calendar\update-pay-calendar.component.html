<app-payroll-nevigation></app-payroll-nevigation>
   <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="setup_multiple_pay_calenders">Update Pay Calendar</h5>
                <button type="button" class="custom-close-btn" data-bs-dismiss="modal" aria-label="Close" (click)="navigateToPayrollSettings()">
                    <i class="bi bi-x-circle"></i>
                </button>
            </div>
            <div class="modal-body">
              <form #payMultipleCalendarForm="ngForm" (ngSubmit)="updatePayCalendar()">
                <div class="mb-3">
                  <label for="Calendar" class="form-label">Calendar Name</label>
                  <input
                    type="text"
                    class="form-control"
                    id="name"
                    [(ngModel)]="multiplePayCalendarData.calendarName"
                    name="name"
                    required
                  />
                  <div
                    *ngIf="payMultipleCalendarForm.controls['name']?.invalid && payMultipleCalendarForm.controls['name']?.touched"
                    class="text-danger"
                  >
                    Calendar Name is required.
                  </div>
                </div>
              
                <div class="mb-3">
                  <label for="payCycle" class="form-label">Pay Cycle</label>
                  <select
                    class="form-control"
                    id="payCycle"
                    [(ngModel)]="multiplePayCalendarData.payCycle"
                    name="payCycle"
                    required
                    (change)="onPayCycleChange()"
                  >
                    <option value="" disabled selected>Select Pay Cycle</option>
                    <option *ngFor="let cycle of payCycles" [ngValue]="cycle">
                      {{ cycle.cycleName }}
                    </option>
                  </select>
                  <div
                    *ngIf="payMultipleCalendarForm.controls['payCycle']?.invalid && payMultipleCalendarForm.controls['payCycle']?.touched"
                    class="text-danger"
                  >
                    Pay cycle is required.
                  </div>
                </div>
              
                <!-- Monthly Pay Cycle Options -->
                <div *ngIf="multiplePayCalendarData.payCycle?.cycleName === 'Monthly'">
                  <div class="mb-3">
                    <label class="form-label">Select Option:</label>
                    <div class="radio-group">
                      <div class="radio-item">
                        <input
                          type="radio"
                          id="paidFromTo"
                          name="dateOption"
                          [(ngModel)]="selectedOption"
                          value="fromTo"
                          (change)="clearDateInputs()"
                        />
                        <label for="paidFromTo">Paid from - to</label>
                      </div>
                      <div class="radio-item">
                        <input
                          type="radio"
                          id="calendarMonth"
                          name="dateOption"
                          [(ngModel)]="selectedOption"
                          value="calendarMonth"
                          (change)="clearDateInputs()"
                        />
                        <label for="calendarMonth">Calendar month</label>
                      </div>
                    </div>
                  </div>
              
                  <!-- From and To Dates -->
                  <div *ngIf="selectedOption === 'fromTo'" class="mb-3">
                    <div class="date-group">
                      <div class="date-item">
                        <label for="fromDate" class="form-label">From Day</label>
                        <select
                          class="form-control"
                          id="fromDate"
                          [(ngModel)]="multiplePayCalendarData.fromDay"
                          name="fromDate"
                          
                        >
                          <option *ngFor="let day of days" [value]="day">{{ day }}</option>
                        </select>
                      </div>
                      <div class="date-item">
                        <label for="toDate" class="form-label">To Day</label>
                        <select
                          class="form-control"
                          id="toDate"
                          [(ngModel)]="multiplePayCalendarData.payDay"
                          name="toDate"
                          (change)="validateDateRange()"
                        >
                          <option *ngFor="let day of days" [value]="day">{{ day }}</option>
                        </select>
                      </div>
                    </div>
                  </div>
              
                  <!-- Calendar Month Pay Date -->
                  <div *ngIf="selectedOption === 'calendarMonth'" class="mb-3">
                    <label for="payDate" class="form-label">Pay Day</label>
                    <select
                      class="form-control"
                      id="payDate"
                      [(ngModel)]="multiplePayCalendarData.payDay"
                      name="payDate"
                    >
                      <option *ngFor="let day of days" [value]="day">{{ day }}</option>
                    </select>
                  </div>
                </div>
              
                <div class="mb-3">
                  <label for="payStartDate" class="form-label">Pay Start Date</label>
                  <input
                    class="form-control"
                    id="payStartDate"
                    [(ngModel)]="multiplePayCalendarData.payStartDate"
                    name="payStartDate"
                    required
                    (change)="onPayStartDateChange()"
                  />
                </div>
              
                <div class="mb-3">
                  <label for="nextPayDate" class="form-label">Next Pay Date</label>
                  <input
                    class="form-control"
                    id="nextPayDate"
                    [(ngModel)]="multiplePayCalendarData.nextPayDate"
                    name="nextPayDate"
                    required
                  />
                </div>
              
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="navigateToPayrollSettings()">Cancel</button>
                  <button type="submit" class="btn btn-primary" [disabled]="payMultipleCalendarForm.invalid">Update</button>
                </div>
              </form>
                              
        </div>             
        </div>
    </div>
 
