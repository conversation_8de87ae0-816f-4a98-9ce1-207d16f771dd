import { BankService } from 'src/app/modules/finance-module/bank/bank.service';
import { BankReconciliationService } from './../bank-reconciliation.service';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { BankAccount } from '../../bank/bank';
import { BankStatementHeader } from '../bank-reconciliation';
import { DomSanitizer } from '@angular/platform-browser';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-final-bank-rec-report',
  templateUrl: './final-bank-rec-report.component.html',
  styleUrls: ['./final-bank-rec-report.component.css'],
})
export class FinalBankRecReportComponent implements OnInit {
  searchFilter = {
    searchTerm: '',
    bankAccount: 'All',
  };
  bankAccountList: BankAccount[] = [];
  completedBankStatements: BankStatementHeader[] = [];
  isViewLoading: boolean = false;
  @ViewChild('finalBankRecView') finalBankRecView!: ElementRef;

  constructor(
    private bankReconciliationService: BankReconciliationService,
    private bankService: BankService,
    public sanitizer: DomSanitizer,
    private swalAlerts: SwalAlertsService
  ) {}

  ngOnInit(): void {
    this.loadAllBankAccounts();
    this.loadCompletedBankStatements();
  }

  loadAllBankAccounts() {
    const entityId = +(localStorage.getItem('entityId') || 0);
    if (!entityId) {
      console.warn('EntityId not Found');
      return;
    }

    this.bankService.getBankAccountsByEntityId(entityId).subscribe(
      (data: BankAccount[]) => {
        this.bankAccountList = data;
      },
      (error) => {
        console.error('Error fetching quotations:', error);
      }
    );
  }

  loadCompletedBankStatements() {
    const entityId = +(localStorage.getItem('entityId') || 0);
    this.bankReconciliationService
      .loadCompletedStatementHeaders(entityId)
      .subscribe(
        (data) => {
          if (data) {
            this.completedBankStatements = data;
          }
        },
        (err) => console.error(err)
      );
  }

  get filteredCompletedBankStatements(): BankStatementHeader[] {
    const term = this.searchFilter.searchTerm.trim().toLowerCase();
    const selectedBankAccount = this.searchFilter.bankAccount;

    return this.completedBankStatements.filter((bankStatementHeader) => {
      const matchesSearch =
        !term ||
        (bankStatementHeader.accountHolderName &&
          bankStatementHeader.accountHolderName.toLowerCase().includes(term)) ||
        (bankStatementHeader.accountNumber &&
          bankStatementHeader.accountNumber.toLowerCase().includes(term)) ||
        (bankStatementHeader.bankStatementId &&
          bankStatementHeader.bankStatementId.toLowerCase().includes(term));

      const matchesBank =
        selectedBankAccount === 'All' ||
        bankStatementHeader.bankName === selectedBankAccount;

      return matchesSearch && matchesBank;
    });
  }

  openBankRecFinalReoport(
    entityId: number | undefined,
    bankStatementId: string | undefined
  ) {
    if (!entityId || !bankStatementId) {
      console.error('EntityId or BankStatementId is null');
      return;
    }

    console.log('Working Here');
    this.bankReconciliationService
      .bankRecFinalReportView(entityId, bankStatementId)
      .subscribe(
        (data) => {
          const base64String = data.response;
          if (base64String) {
            this.loadPdfIntoIframe(base64String);
          } else {
            this.isViewLoading = false;
            alert('No Payment Receipt data for preview.');
          }
        },
        (error) => {
          this.isViewLoading = false;
          alert('Error loading Payment Receipt preview.');
        }
      );
  }

  private loadPdfIntoIframe(base64String: string) {
    if (base64String && base64String.trim().length >= 50) {
      const pdfData = 'data:application/pdf;base64,' + base64String;
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
        pdfData
      ) as any;
      const iframe = this.finalBankRecView.nativeElement;
      iframe.onload = () => {
        this.isViewLoading = false;
      };
      iframe.setAttribute(
        'src',
        sanitizedUrl.changingThisBreaksApplicationSecurity
      );
      const modalElement = document.getElementById('simpleModal');
      const modal = new bootstrap.Modal(modalElement!);
      modal.show();
    } else {
      this.isViewLoading = false;
      this.swalAlerts.showSwalWarning(
        'No Data',
        'No Payment Receipt data for preview.',
        'No Payment Receipt data was returned for the selected range.'
      );
    }
  }
}
