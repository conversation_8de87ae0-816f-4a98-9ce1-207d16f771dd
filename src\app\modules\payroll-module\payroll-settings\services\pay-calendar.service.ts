import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, tap } from 'rxjs';
import { MultiplePayCalendar,PayCalendar,PayCycle, PayPeriod } from '../payroll-setting';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class PayCalendarService {
  private readonly baseURL = environment.payrollApiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  saveMultiplePayCalendar(multiplePayCalendar: MultiplePayCalendar): Observable<MultiplePayCalendar> {
    return this.request('POST', '/payCalendar/save', multiplePayCalendar);
  }

  getAllPayCycles(entityId: number): Observable<PayCycle[]> {
    return this.request('GET', `/payCycle/getAll?entityId=${entityId}`, {});
  }
  
  // getAllPayPeriods(): Observable<PayPeriod[]> {
  //   return this.request('GET', '/payPeriod/getAll', {});
  // }
  
  getAllPayCalendars(entityId: number): Observable<PayCalendar[]> {
    return this.request('GET', `/payCalendar/getAll?entityId=${entityId}`, {});
  }

  getPayCalendarById(id: number): Observable<PayCalendar> {
    return this.request('GET', `/payCalendar/getById/${id}`, null);
  }
  
  updatePayCalendarById(id: number, payload: MultiplePayCalendar): Observable<any> {
    return this.request('PUT', `/payCalendar/update/${id}`, payload);
  }

  disabledCalendar(id: number, updatedStatus: { status: boolean }): Observable<MultiplePayCalendar> {
    return this.request('PUT', `/payCalendar/updateStatus/${id}`, updatedStatus);
  }
  deletePayCalendar(payCalendarId: number): Observable<any> {
    return this.request('DELETE', `/payCalendar/deleteById/${payCalendarId}`, {});
  }
  
}
