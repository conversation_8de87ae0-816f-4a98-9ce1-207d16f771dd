<app-home-header></app-home-header>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <div class="form-container">
    <h2>Setup Trading Name</h2>
    <div class="radio-group">
      <label>
        <input
          type="radio"
          name="tradingNameOption"
          value="single"
          [(ngModel)]="tradingNameOption"
          (click)="singleTradingName()"
        />
        Single Trading Name
      </label>
      <label>
        <input
          type="radio"
          name="tradingNameOption"
          value="multiple"
          [(ngModel)]="tradingNameOption"
        />
        Multiple Trading Names
      </label>
      <label>
        <input
          type="radio"
          name="tradingNameOption"
          value="none"
          [(ngModel)]="tradingNameOption"
          (click)="noTradingName()"
        />
        No Trading Name
      </label>
    </div>
    <form (ngSubmit)="onEntityRegister(entityForm.value)" #entityForm="ngForm">
      <div class="form-group">
        <label for="tradingName">Trading Name</label>
        <input
          type="text"
          id="tradingName"
          name="tradingName"
          [(ngModel)]="entityTradingName.tradingName"
          [readonly]="tradingNameOption === 'none'"
          [class.read-only]="tradingNameOption === 'none'"
          required
        />
        <button
          *ngIf="tradingNameOption === 'multiple'"
          type="button"
          class="verify-code"
          (click)="addAdditionalTradingName()"
        >
          Add Additinal Trading Name
        </button>
      </div>

      <div
        *ngFor="
          let name of newTradingName;
          let i = index;
          trackBy: trackByIndex
        "
        class="form-group"
      >
        <label for="addAdditionalTradingName-{{ i }}">
          Additional Trading Name {{ i + 1 }}
        </label>
        <input
          type="text"
          id="addAdditionalTradingName-{{ i }}"
          name="addAdditionalTradingName-{{ i }}"
          [(ngModel)]="newTradingName[i]"
          [readonly]="tradingNameOption === 'none'"
          [class.read-only]="tradingNameOption === 'none'"
          required
        />
        <button type="button" class="remove-btn" (click)="removeTradingName(i)">
          Remove
        </button>
      </div>

      <div class="form-group">
        <label for="industryClassification">Industry Classification</label>
        <select
          id="industryClassification"
          name="industryClassification"
          class="form-control"
          [(ngModel)]="entityTradingName.industryId"
          [disabled]="tradingNameOption === 'none'"
          [class.read-only]="tradingNameOption === 'none'"
        >
          <option [ngValue]="null">-- Select Industry --</option>
          <option *ngFor="let industry of industryList" [ngValue]="industry">
            {{ industry.industryName }}
          </option>
        </select>
      </div>
      <button type="submit" class="btn">
        {{ tradingNameOption === "none" ? "Proceed" : "Create Entity" }}
      </button>
    </form>
  </div>
</div>
