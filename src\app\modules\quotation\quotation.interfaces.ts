// Interfaces for quotation-related data structures

export interface EmailData {
  TempRecipientEmail: string;
  subject: string;
  content: string;
  quotes: any[]; // This should be typed based on your QuoteHead interface
}

export interface ExportData {
  'Quote ID': number;
  'Quote Number': string;
  'Customer Name': string;
  'Quote Date': string;
  'Valid Until': string;
  'Amount': number;
  'Status': string;
}

export interface FilterCriteria {
  searchTerm: string;
  startDate: string | null;
  endDate: string | null;
  activeTab: string;
}

export interface UserInfo {
  id: number;
  roleName: string;
  // Add other user properties as needed
}

export interface BusinessPartner {
  id: number;
  bpName: string;
  email: string;
  // Add other business partner properties as needed
}

export interface QuoteReportResponse {
  response: string;
  // Add other response properties as needed
}

export interface ModalElements {
  sendQuoteModal: HTMLElement | null;
  quotePreviewModal: HTMLElement | null;
}

export interface AudioControls {
  audio: HTMLAudioElement | null;
  play(): void;
  stop(): void;
}

export interface ValidationResult {
  isValid: boolean;
  message?: string;
}

export interface SelectionState {
  selectedQuotes: Set<any>; // Should be typed based on QuoteHead
  isAllSelected: boolean;
}

export interface EmailTemplateData {
  subject: string;
  content: string;
  templateHtmlContent: string;
  recipientEmail: string;
  TempRecipientEmail: string;
}

export interface DateRange {
  startDate: string | null;
  endDate: string | null;
}

export interface SearchFilters extends DateRange {
  searchTerm: string;
  activeTab: string;
}

export interface QuotationComponentState {
  quotes: any[]; // Should be typed based on QuoteHead
  filteredQuotes: any[]; // Should be typed based on QuoteHead
  selectedQuotes: Set<any>; // Should be typed based on QuoteHead
  isAllSelected: boolean;
  activeTab: string;
  searchTerm: string;
  startDate: string | null;
  endDate: string | null;
  isLoading: boolean;
  isSending: boolean;
  isDropdownOpen: boolean;
}

export interface EmailTemplateVariables {
  businessPartnerName: string;
  quoteNumber: string;
  currentYear: string;
}

export interface SwalResult {
  isConfirmed: boolean;
  isDismissed: boolean;
  dismiss?: any;
}
