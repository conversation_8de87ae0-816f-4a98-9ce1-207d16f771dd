import {
  ChangeDetectorRef,
  Component,
  HostListener,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  BankAccount,
  BankAccountNew,
  EmployeeLeave,
  EmployeeTax,
  Employment,
  LeaveType,
  NewDeduction,
  NewEarning,
  NewLeave,
  NewReimbursement,
  NewSuperannuation,
  NewTax,
  Personal,
  Superannuation,
} from './employee';
import { EmployeeService } from '../services/employee.service';
import { NgForm } from '@angular/forms';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { debounceTime, distinctUntilChanged, forkJoin, Subject } from 'rxjs';
import {
  Deduction,
  Earning,
  Leave,
  leaveCategory,
  PayCalendar,
  Reimbursement,
} from '../payroll-setting';
import { EarningService } from '../services/earning.service';
import { DeductionService } from '../services/deduction.service';
import { ReimbursementsService } from '../services/reimbursements.service';
import { PayCalendarService } from '../services/pay-calendar.service';
import { Entity } from 'src/app/modules/entity/entity';
import { LeaveService } from '../services/leave.service';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-empolyee',
  templateUrl: './empolyee.component.html',
  styleUrls: ['./empolyee.component.css'],
})
export class EmpolyeeComponent implements OnInit {
  @ViewChild('personalForm') personalForm: NgForm | undefined;
  @ViewChild('employmentForm') employmentForm: NgForm | undefined;
  @ViewChild('employeeTaxForm') employeeTaxForm: NgForm | undefined;
  @ViewChild('employeeLeaveForm') employeeLeaveForm: NgForm | undefined;

  personalData: Personal = new Personal();
  employmentData: Employment = new Employment();
  employeeTaxData: EmployeeTax = new EmployeeTax();
  employeeLeaveData: EmployeeLeave = new EmployeeLeave();
  activeTab: string = 'employee';
  searchTerm: string = '';
  employees: any[] = [];
  isNameExist: boolean = false;
  isTaxExist: boolean = false;
  isEmployementExist: boolean = false;
  isFormValid: boolean = false;
  isBalanceSalary: boolean = true;;
  isAmount: boolean = true;
  searchSubject = new Subject<{ term: string; entityId: number }>();
  newEarning: NewEarning = new NewEarning();
  newDeduction: NewDeduction = new NewDeduction();
  newReimbursement: NewReimbursement = new NewReimbursement();
  newLeave: NewLeave = new NewLeave();
  newTax: NewTax = new NewTax();
  newSuperannuation: NewSuperannuation = new NewSuperannuation();
  payTemplateEarnings: NewEarning[] = [];
  filteredEarnings: Earning[] = [];
  payTemplateDeductions: NewDeduction[] = [];
  payTemplateReimbursements: NewReimbursement[] = [];
  earning: Earning = new Earning();
  payTemplateLeaves: any[] = [];
  leaveAccrual: any[] = [];
  payTemplateTaxs: any[] = [];
  payTemplateSuperannuations: any[] = [];
  totalEarningAmount = 0;
  totalDeductionAmount = 0;
  totalReimbursementAmount = 0;
  totalLeaveAmount = 0;
  totalTaxAmount = 0;
  totalSuperannuationAmount = 0;
  earnings: Earning[] = [];
  deductions: Deduction[] = [];
  reimbursements: Reimbursement[] = [];
  leaves: NewLeave[] = [];
  taxs: NewTax[] = [];
  payCalendars: PayCalendar[] = [];
  employments: Employment[] = [];
  superannuations: Superannuation[] = [];
  bankAccountPrimary: BankAccount[] = [];
  bankAccounts: BankAccountNew[] = [];
  newSuperannuations: NewSuperannuation[] = [];
  leave: LeaveType[] = [];
  bankAccountData: BankAccount = new BankAccount();
  bankAccountDataNew: BankAccountNew = new BankAccountNew();
  isFormSaved: boolean = false;
  selectedEarningType: string | null = null;
  selectedEarningsName: string = '';
  selectedRate: number = 0.0;
  selectedDeductionsName: string = '';
  selectedReimbursementType: string = '';
  selectedLeaveType: string = '';
  selectedTaxType: string = '';
  selectedSuperannuationType: string = '';
  selectedPayCalendar: string | null = '';
  public earningsName: string = '';
  public rate: number = 0.0;
  public typeOfUnits: string = '';
  selectedInput: string = 'percentage';
  selectedInput2: string = 'rate';
  leavesPage = 1;
  leaveTwoPage = 1;
  addressValidationMessage: string | undefined;
  suggestedAddresses: any[] = [];
  isAddressValid: boolean | undefined;
  isFormVisible: boolean = true;
  

  constructor(
    private employeeService: EmployeeService,
    private payCalendarService: PayCalendarService,
    private router: Router,
    private earningService: EarningService,
    private deductionService: DeductionService,
    private reimbursementsService: ReimbursementsService,
    private leaveService: LeaveService,
    private cdr: ChangeDetectorRef,
    private http: HttpClient,
  ) {}
  ngOnInit(): void {
    this.checkFormValidity();
    this.getBankAccounts();
    this.getEarnings();
    this.getDeductions();
    this.getReimbursement();
    this.getPayTemplateEarning();
    this.getPayTemplateDeduction();
    this.getPayTemplateReimbursement();
    this.getPayTemplateLeave();
    this.getPayTemplateSuperannuation();
    this.getAllPayCalendars();
    this.getLeave();
    this.getEmployment();
    this.getTaxes();
    this.getSuperannuation();
    this.checkEligibility();
    this.searchSubject
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe((searchData: { term: string; entityId: number }) => {
        if (searchData.term.trim().length > 0) {
          this.fetchEmployees(searchData);
        } else {
          this.employees = [];
        }
      });
  }

  ngDoCheck() {
    this.checkEligibility(); // Run check when Angular detects changes
  }

  checkFormValidity(): void {
    if (this.personalForm) {
      this.personalForm.statusChanges?.subscribe((status) => {
        this.isFormValid = status === 'VALID';
      });
    }
  }

  toggleStatus() {
    this.personalData.status = !this.personalData.status;
    const updatedStatus = { status: this.personalData.status };

    this.employeeService
      .updateStatus(this.personalData.employeeId, updatedStatus)
      .subscribe(
        (_response: any) => {
          Swal.fire({
            title: 'Success!',
            text: this.personalData.status
              ? 'Employee activated successfully!'
              : 'Employee deactivated successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          });
        },
        (_error: any) => {
          this.personalData.status = !this.personalData.status;
          Swal.fire({
            title: 'Error!',
            text: 'Failed to update employee status. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
  }

  onSlideToggle(event: any) {
    const newStatus = event.checked;
    this.personalData.status = newStatus;

    const updatedStatus = { status: newStatus };
    this.employeeService
      .updateStatus(this.personalData.employeeId, updatedStatus)
      .subscribe(
        (_response: any) => {
          Swal.fire({
            title: 'Success!',
            text: newStatus
              ? 'Employee activated successfully!'
              : 'Employee deactivated successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          });
        },
        (_error: any) => {
          this.personalData.status = !newStatus;
          Swal.fire({
            title: 'Error!',
            text: 'Failed to update employee status. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
  }

  onSearchInput(): void {
    if (this.searchTerm.trim().length > 0) {
      const entityId = +(localStorage.getItem('entityId') || '0');
      this.searchSubject.next({ term: this.searchTerm.trim(), entityId });
    } else {
      this.employees = [];
    }
  }

  fetchEmployees(searchData: { term: string; entityId: number }): void {
    const { term, entityId } = searchData;
    this.employeeService.getFilteredNames(term, entityId).subscribe(
      (response) => {
        this.employees = response;
      },
      (error) => {
        console.error('Error fetching employees:', error);
      }
    );
  }

  selectEmployee(employee: any): void {
    this.searchTerm = `${employee.firstName} ${employee.lastName}`;
    this.personalData.employeeId = employee.employeeId;
    this.employmentData = new Employment();
    this.employeeTaxData = new EmployeeTax();
    this.selectedPayCalendar = '';
    this.getPayTemplateEarning();
    this.getPayTemplateDeduction();
    this.getPayTemplateReimbursement();
    this.getPayTemplateLeave();
    this.getPayTemplateSuperannuation();
    this.getBankAccounts();
    this.getEmployment();
    this.getTaxes();
    this.employees = [];
    this.personalData = { ...employee };

    if (this.personalData.emergencyPhoneNumber) {
      const [countryCode, phone] =
        this.personalData.emergencyPhoneNumber.split('-');
      this.selectedCountryCode = countryCode || '';
      this.emergencyPhoneNumber = phone || '';
      const matchedNumber = this.employees.find(
        (number) =>
          number.emergencyPhoneNumber === this.personalData.emergencyPhoneNumber
      );
      this.personalData.emergencyPhoneNumber =
        matchedNumber?.emergencyPhoneNumber || '';
    } else {
      this.emergencyPhoneNumber = '';
      this.personalData.emergencyPhoneNumber = '';
    }

    if (this.personalData.phoneNumber) {
      const [countryCode, phone] = this.personalData.phoneNumber.split('-');
      this.selectCode = countryCode || '';
      this.phoneNumber = phone || '';
      const matchedNumber2 = this.employees.find(
        (number) => number.phoneNumber === this.personalData.phoneNumber
      );
      this.personalData.phoneNumber = matchedNumber2?.phoneNumber || '';
    } else {
      this.phoneNumber = '';
      this.personalData.phoneNumber = '';
    }

    this.isNameExist = true;
  }

  onAddPersonal() {
    this.addPersonalData();
    this.setActiveSide('Employment');
  }
  getAllPayCalendars(): void {
    const entityId = +(localStorage.getItem('entityId') || '0');
    this.payCalendarService.getAllPayCalendars(entityId).subscribe({
      next: (data: PayCalendar[]) => {
        this.payCalendars = data;
      },
      error: (err) => {
        console.error('Error fetching payCalendars:', err);
      },
    });
  }
  getEmployment(): void {
    this.employeeService
      .getAllEmployments(this.personalData.employeeId)
      .subscribe({
        next: (data: Employment[]) => {
          if (data.length > 0) {
            this.employmentData = data[0];
            this.isEmployementExist = true;
          }
          if (this.employmentData.payCalendar) {
            const matchedCalendar = this.payCalendars.find(
              (calendar) =>
                calendar.calendarName ===
                this.employmentData.payCalendar?.calendarName
            );
            this.selectedPayCalendar = matchedCalendar?.calendarName || null;
          }
        },
        error: (err) => {
          console.error('Error fetching employment:', err);
        },
      });
  }
  getTaxes(): void {
    this.employeeService.getAllTaxes(this.personalData.employeeId).subscribe({
      next: (data: EmployeeTax[]) => {
        if (data.length > 0) {
          this.employeeTaxData = data[0];
          this.isTaxExist = true;
        }
      },
      error: (err) => {
        console.error('Error fetching employment:', err);
      },
    });
  }
  addPersonalData(): void {
    if (this.personalForm?.valid) {
      const entityId = +(localStorage.getItem('entityId') + '');

      this.personalData.status = true;
      const payload = {
        ...this.personalData,
        entityId,
      };

      this.employeeService.savePersonalData(payload).subscribe(
        (_response: any) => {
          this.personalData = _response;
          Swal.fire({
            title: 'Success!',
            text: 'Personal data added successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {
            this.isNameExist = true;
          });
        },
        (_error: any) => {
          let errorMessage = 'Failed to save Personal data. Please try again.';

          if (_error?.error) {
            if (typeof _error.error === 'string') {
              errorMessage = _error.error;
            } else if (_error.error?.error) {
              errorMessage = _error.error.error;
            } else if (_error.error?.message) {
              errorMessage = _error.error.message;
            }
          }
          Swal.fire({
            title: 'Error!',
            text: errorMessage,
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
    } else {
      alert('Please fill in all required fields.');
    }
  }

  onAddEmployeeLeave() {
    this.addEmployeeLeaveData();
  }

  addEmployeeLeaveData(): void {
    if (this.employeeLeaveForm?.valid) {
      const entityId = +(localStorage.getItem('entityId') + '');
      const userId = +(localStorage.getItem('userid') + '');
      const date = new Date().toISOString();

      const payload = {
        ...this.employeeLeaveData,
        employee: this.personalData,
        entityId,
        userId,
        date,
      };

      this.employeeService.saveEmployeeLeaveData(payload).subscribe(
        (_response: any) => {
          Swal.fire({
            title: 'Success!',
            text: 'Employee Leave Data added successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {
            this.employeeLeaveData = new EmployeeLeave();
          });
        },
        (_error: any) => {
          Swal.fire({
            title: 'Error!',
            text: 'Failed to save Employee Leave. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
    } else {
      alert('Please fill in all required fields.');
    }
  }

  onAddEmployeeTax() {
    this.addEmployeeTaxData();
  }

  addEmployeeTaxData(): void {
    if (this.employeeTaxForm?.valid) {
      const entityId = +(localStorage.getItem('entityId') + '');
      const userId = +(localStorage.getItem('userid') + '');
      const date = new Date().toISOString();

      const payload = {
        ...this.employeeTaxData,
        employee: this.personalData,
        entityId,
        userId,
        date,
      };

      this.employeeService.saveEmployeeTaxData(payload).subscribe(
        (_response: any) => {
          Swal.fire({
            title: 'Success!',
            text: 'Employee Tax Data added successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {
            // this.employeeTaxData = new EmployeeTax();
            this.isTaxExist = true;
            this.getTaxes();
          });
        },
        (_error: any) => {
          Swal.fire({
            title: 'Error!',
            text: 'Failed to save Tax Data. Are you ensure you entered your full name?',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
    } else {
      alert('Please fill in all required fields.');
    }
  }
  updateSalaryFields() {
    if (this.employmentData.salaryType === 'Annual Salary') {
      this.employmentData.ordinaryEarningsRate = 0; // Clear hourly rate
    } else if (this.employmentData.salaryType === 'Hourly Rate') {
      this.employmentData.amount = 0; // Clear annual salary
    }
  }
  onAddEmployment() {
    this.addEmploymentData();
    this.setActiveSide('Pay_Template');
  }
  onCalendarTypeChange(calendarName: string): void {
    const selectedPayCalendar = this.payCalendars.find(
      (calendar) => calendar.calendarName === calendarName
    );

    if (selectedPayCalendar) {
      this.employmentData.payCalendar = selectedPayCalendar;
    }
  }

  addEmploymentData(): void {
    if (this.employmentForm?.valid) {
      const entityId = +(localStorage.getItem('entityId') + '');
      const userId = +(localStorage.getItem('userid') + '');
      const date = new Date().toISOString();

      const payload = {
        ...this.employmentData,
        employee: this.personalData,
        entityId,
        userId,
        date,
      };

      this.employeeService.saveEmploymentData(payload).subscribe(
        (_response: any) => {
          Swal.fire({
            title: 'Success!',
            text: 'Employment Data added successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {
            // this.employmentData = new Employment();
            this.isEmployementExist = true;
            this.getEmployment();
          });
        },
        (_error: any) => {
          Swal.fire({
            title: 'Error!',
            text: 'Failed to save Employment Data. Are you ensure you entered your full name?',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
    } else {
      alert('Please fill in all required fields.');
    }
  }

  updateEmployement() {
    const payload = {
      ...this.employmentData,
    };
    this.employeeService
      .updateEmployement(this.employmentData.employmentId, payload)
      .subscribe(
        (_response: any) => {
          Swal.fire({
            title: 'Success!',
            text: 'Empolyement Data updated successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {});
        },
        (_error: any) => {
          Swal.fire({
            title: 'Error!',
            text: 'Failed to update Empolyement Data. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
  }

  updateEmpolyee(): void {
    const countryCode1 = this.selectCode || '';
    const phone1 = this.phoneNumber || '';
    this.personalData.phoneNumber = `${countryCode1}-${phone1}`;

    const countryCode = this.selectedCountryCode || '';
    const phone = this.emergencyPhoneNumber || '';
    this.personalData.emergencyPhoneNumber = `${countryCode}-${phone}`;

    const payload = {
      ...this.personalData,
    };

    this.employeeService
      .updateEmpolyee(this.personalData.employeeId, payload)
      .subscribe(
        (_response: any) => {
          Swal.fire({
            title: 'Success!',
            text: 'Empolyee Data updated successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {});
        },
        (_error: any) => {
          Swal.fire({
            title: 'Error!',
            text: 'Failed to update Empolyee Data. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    if (tab === 'calendars') {
      this.router.navigate(['/payroll-settings']);
    }
  }

  isActiveTab(tab: string): boolean {
    return this.activeTab === tab;
  }

  activeSide: string = 'Personal';

  isActiveSide(side: string): boolean {
    return this.activeSide === side;
  }

  setActiveSide(side: string): void {
    this.activeSide = side;
  }

  // BankAccounts

  getBankAccounts(): void {
    this.employeeService
      .getBankAccountList(this.personalData.employeeId)
      .subscribe((data: BankAccountNew[]) => {
  
        const primaryAccount = data.find(
          (account) => account.bankAccountName === "primary"
        );
        const additionalAccounts = data.filter(
          (account) => account.bankAccountName !== "primary"
        );
  
        if (primaryAccount) {
          this.bankAccountData = primaryAccount;
  
          if (this.bankAccountData.amount === 0) {
            this.isBalanceSalary = true;
            this.isAmount = true;
          } else {
            this.isAmount = false;
          }
          this.isFormSaved = true;
        } else {
          this.bankAccountDataNew = new BankAccountNew();
          this.isFormSaved = false;
        }
        additionalAccounts.forEach((account) => {
          if (account.amount === 0) {
            account.isBalanceSalary2 = true;
            account.isAmount2 = true;
            this.isAmount = true;
          } else {
            account.isBalanceSalary2 = false;
            account.isAmount2 = false;
          }
        });
  
        this.bankAccounts = additionalAccounts.map((account) => ({
          ...account,
          isSaved: true,
        }));
      });
  }
  
  saveBankAccount(): void {
    const entityId = +(localStorage.getItem('entityId') + '');
    const userId = +(localStorage.getItem('userid') + '');
    const date = new Date().toISOString();

    const payload = {
      ...this.bankAccountData,
      employee: this.personalData,

      entityId,
      userId,
      date,
    };

    this.employeeService.saveBankAccount(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: this.isBalanceSalary
            ? 'Bank Account Data added successfully!'
            : 'Bank Account Data added successfully! However, You need to add another bank account for the Balance Salary Remittance.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.isFormSaved = true;
          this.getBankAccounts();
          this.isAmount = false;
          this.isBalanceSalary = false;
          this.bankAccountDataNew.isBalanceSalary2 = true;
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save BankAccount Data. Are you ensure you entered your full name?',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }
  deleteBankAccount(index: number) {
    this.employeeService.deleteBankAccount(index).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'BankAccount delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.bankAccountData = new BankAccount(); 
          this.getBankAccounts();
          
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to BankAccount Earning. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }
  saveBankAccount2(account: BankAccountNew): void {
    const entityId = +(localStorage.getItem('entityId') + '');
    const userId = +(localStorage.getItem('userid') + '');
    const date = new Date().toISOString();

    const payload = {
      ...account,
      employee: this.personalData,
      entityId,
      userId,
      date,
    };

    this.employeeService.saveBankAccount(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: account.isBalanceSalary2
            ? 'Bank Account Data added successfully!'
            : 'Bank Account Data added successfully! However, You need to add another bank account for the Balance Salary Remittance.',
          
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          account.isSaved = true;
          this.getBankAccounts();
          // this.bankAccountDataNew.isAmount2  = false;
          // this.bankAccountDataNew.isBalanceSalary2 = true;
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save BankAccount Data. Please try again.\n\nAre you ensure you entered your full name?',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }
  DeleteBankAccountNew(index: number) {
    this.employeeService.deleteBankAccount(index).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'BankAccount delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.bankAccountDataNew = new BankAccountNew(); 
          this.getBankAccounts();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to BankAccount Earning. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  addBankAccount(): void {
    this.bankAccounts.push(new BankAccountNew());
    this.bankAccountDataNew.isBalanceSalary2 = true;
  }
  removeBankAccount(index: number): void {
    this.bankAccounts.splice(index, 1);
  
  }
  clearFirstForm(): void {
    this.bankAccountData = new BankAccount();
    this.isFormSaved = false;
  }

  // Pay template Earnings

  private getEarnings() {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.earningService.getEarningList(entityId).subscribe((data) => {
      this.earnings = data;
    });
  }

  private getPayTemplateEarning() {
    this.employeeService
      .getPayTemplateEarnings(this.personalData.employeeId)
      .subscribe((data) => {
        this.payTemplateEarnings = data;
        this.updateTotalEarnings();
      });
  }

  openEarningModal() {
    this.newEarning = {
      earning: new Earning(),
      amount: 0.0,
      hours: 0.0,
      rate: 0.0,
      typeOfUnits: '',
      employeeEarningId: 0,
    };
  }

  onEarningTypeChange(value: string): void {
    const selectedEarning = this.earnings.find(
      (earning) => earning.earningsName === value
    );

    if (selectedEarning) {
      this.newEarning.rate = selectedEarning.rate;
      this.newEarning.typeOfUnits = selectedEarning.typeOfUnits;

      if (this.newEarning.typeOfUnits === 'Fixed Amount') {
        this.newEarning.amount = this.newEarning.rate;
        this.newEarning.rate = 0.0;
      }
    } else {
      this.newEarning.amount = 0.0;
    }
    this.calculateAmount();
  }

  calculateAmount(): void {
    if (this.newEarning.typeOfUnits === 'Per Unit') {
      this.newEarning.amount = this.newEarning.hours
        ? this.newEarning.rate * this.newEarning.hours
        : 0.0;
    }
  }

  onCategoryChange() {
    this.filteredEarnings = this.earnings.filter(
      (earning) => earning.category === this.earning.category
    );
    this.selectedEarningsName = '';
    this.earning.earningsName = '';
    this.newEarning.rate = 0.0;
    this.newEarning.amount = 0.0;
  }

  addEarning(): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    const userId = +(localStorage.getItem('userid') || '');
    const date = new Date().toISOString();
    const selectedEarning = this.earnings.find(
      (earning) => earning.earningsName === this.selectedEarningsName
    );
    const payload = {
      ...this.newEarning,
      employee: this.personalData,
      earning: selectedEarning,
      entityId,
      userId,
      date,
    };

    this.employeeService.saveEarning(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Earning Data added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.newEarning = new NewEarning();
      
          this.getPayTemplateEarning();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Earning Data. Are you ensure you entered your full name?',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  // Delete earning
  deleteEarning(index: number) {
    this.employeeService.deleteEarning(index).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Earning delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayTemplateEarning();
          this.updateTotalEarnings();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Earning. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }
  // Update earnings total
  updateEarning() {
    this.updateTotalEarnings();
  }

  // Calculate total earnings
  updateTotalEarnings() {
    this.totalEarningAmount = this.payTemplateEarnings.reduce(
      (sum, earning) => sum + (earning.amount || 0),
      0
    );
  }
  closeEarningModal() {
    // this.selectedEarningsName = '';
    this.earning.category = '';
    this.earning.earningsName = '';
    this.selectedSuperannuationType = '';
    this.selectedLeaveType = '';
    this.newEarning.rate = 0.0;
    this.newEarning.amount = 0.0;

  }

  earningCategory = [
    'Regular Earnings',
    'Overtime Earnings',
    'Commission',
    'Bonus',
    'Tips',
    'Back Pay',
    'Other Earnings',
    'Lump sums',
  ];

  // Deduction

  private getDeductions() {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.deductionService.getDeductionList(entityId).subscribe((data) => {
      this.deductions = data;
    });
  }
  private getPayTemplateDeduction() {
    this.employeeService
      .getPayTemplateDeductions(this.personalData.employeeId)
      .subscribe((data) => {
        this.payTemplateDeductions = data;
        this.updateTotalDeduction();
      });
  }

  openDeductionModal() {
    this.newDeduction = {
      deduction: new Deduction(),
      amount: 0,
      employeeDeductionId: 0,
    };
  }

  closeDeductionModal() {
    this.selectedDeductionsName = '';
  }

  onDeductionTypeChange(value: string): void {
    if (this.newDeduction.deduction) {
      this.newDeduction.deduction.deductionName = value;
    }
  }

  addDeduction(): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    const userId = +(localStorage.getItem('userid') || '');
    const date = new Date().toISOString();
    const selectedDeduction = this.deductions.find(
      (deduction) => deduction.deductionName === this.selectedDeductionsName
    );

    const payload = {
      ...this.newDeduction,
      employee: this.personalData,
      deduction: selectedDeduction,
      entityId,
      userId,
      date,
    };

    this.employeeService.saveDeduction(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Deduction Data added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayTemplateDeduction();
          this.selectedDeductionsName = '';
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Deduction Data. Are you ensure you entered your full name?',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  deleteDeduction(index: number) {
    this.employeeService.deleteDeduction(index).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Deduction delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayTemplateDeduction();
          this.updateTotalDeduction();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Deduction. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  updateDeduction() {
    this.updateTotalDeduction();
  }

  removeDeduction(index: number) {
    this.deductions.splice(index, 1);
    this.updateTotalDeduction();
  }

  updateTotalDeduction() {
    this.totalDeductionAmount = this.payTemplateDeductions.reduce(
      (sum, deduction) => sum + (deduction.amount || 0),
      0
    );
  }

  // Reimbursement

  private getReimbursement() {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.reimbursementsService
      .getReimbursementsList(entityId)
      .subscribe((data) => {
        this.reimbursements = data;
      });
  }
  private getPayTemplateReimbursement() {
    this.employeeService
      .getPayTemplateReimbursements(this.personalData.employeeId)
      .subscribe((data) => {
        this.payTemplateReimbursements = data;
        this.updateTotalReimbursement();
      });
  }

  deleteReimbursement(index: number) {
    this.employeeService.deleteReimbursement(index).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Reimbursement delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayTemplateReimbursement();
          this.updateTotalReimbursement();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Reimbursement. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }
  openReimbursementModal() {
    this.newReimbursement = {
      reimbursement: new Reimbursement(),
      amount: 0,
      employeeReimbursementId: 0,
    };
  }

  closeReimbursementModal() {}

  onReimbursementTypeChange(value: string): void {
    if (this.newReimbursement.reimbursement) {
      this.newReimbursement.reimbursement.reimbursementType = value;
    }
  }

  addReimbursement(): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    const userId = +(localStorage.getItem('userid') || '');
    const date = new Date().toISOString();
    const selectedReimbursement = this.reimbursements.find(
      (reimbursement) =>
        reimbursement.reimbursementType === this.selectedReimbursementType
    );

    const payload = {
      ...this.newReimbursement,
      employee: this.personalData,
      reimbursement: selectedReimbursement,
      entityId,
      userId,
      date,
    };

    this.employeeService.saveReimbursement(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Reimbursement Data added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayTemplateReimbursement();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Reimbursement Data.Are you ensure you entered your full name?',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  updateReimbursement() {
    this.updateTotalReimbursement();
  }

  removeReimbursement(index: number) {
    this.payTemplateReimbursements.splice(index, 1);
    this.updateTotalReimbursement();
  }

  updateTotalReimbursement() {
    this.totalReimbursementAmount = this.payTemplateReimbursements.reduce(
      (sum, reimbursement) => sum + (reimbursement.amount || 0),
      0
    );
  }

  // Leave

  private getPayTemplateLeave() {
    forkJoin([
      this.employeeService.getPayTemplateLeaves(this.personalData.employeeId),
      this.employeeService.getLeaveAccrual(this.personalData.employeeId)
    ]).subscribe(([payLeaves, accruals]) => {
      this.payTemplateLeaves = payLeaves;
      this.leaveAccrual = accruals;
      this.payTemplateLeaves = this.payTemplateLeaves.map((leave) => {
        const matchingAccrual = this.leaveAccrual.find(
          (accrual) => String(accrual.leaveType?.leaveTypeId) === String(leave.leaveType?.leaveTypeId)
        );
        return {
          ...leave,
          currentBalance: matchingAccrual ? matchingAccrual.currentBalance : 0.00,
        };
      });
      this.payTemplateLeaves = [...this.payTemplateLeaves];
    });
  }
  
  hasLeaveAccrualData(): boolean {
    return this.leaveAccrual && this.leaveAccrual.length > 0;
  }


  deleteLeave(index: number) {
    this.employeeService.deleteLeave(index).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Leave delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayTemplateLeave();
          this.updateTotalLeave();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Leave. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }
  openLeaveModal() {
    this.newLeave = {
      leaveType: new LeaveType(),
      openingBalance: 0,
      accruedHoursPerWeek: 0,
      hoursPerWeek: 0,
      hoursAccruedAnnually: 0,
      leaveMethods: '',
    };
  }

  closeLeaveModal() {}

  isFormValidate(): boolean {
    if (!this.newLeave.leaveMethods) {
      return false;
    }
  
    switch (this.newLeave.leaveMethods) {
      case 'Fixed Sum Per Period':
        return (
          this.newLeave.hoursAccruedAnnually > 0 &&
          this.newLeave.hoursPerWeek > 0 &&
          this.newLeave.accruedHoursPerWeek > 0
        );
  
      case 'Manually Entered Rate':
        return this.newLeave.accruedHoursPerWeek > 0;
  
      case 'Based On Ordinary Earnings':
        return (
          this.newLeave.hoursAccruedAnnually > 0 &&
          this.newLeave.hoursPerWeek > 0
        );
  
      default:
        return true;
    }
  }

  leaveMethod: string[] = [
    'Fixed Sum Per Period',
    'Based On Ordinary Earnings',
    'Manually Entered Rate',
    'No Calculation Required',
  ];

  private getLeave() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.leaveService.getLeaveList(entityId).subscribe((data) => {
      this.leave = data;
    });
  }

  onLeaveTypeChange(value: string): void {
    this.selectedLeaveType = value;

    const selectedLeave = this.leave.find((leave) => leave.leaveType === value);

    // Assign values to `newLeave` based on the selected leave type
    if (selectedLeave) {
      this.newLeave.leaveType = selectedLeave;
      this.newLeave.hoursPerWeek =
        +selectedLeave.leaveCategory.normalEntitlement;
    }
  }

  addLeave(): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    const userId = +(localStorage.getItem('userid') || '');
    const date = new Date().toISOString();
    const selectedLeave = this.leave.find(
      (leave) => leave.leaveType === this.selectedLeaveType
    );

    const payload = {
      ...this.newLeave,
      employee: this.personalData,
      leaveType: selectedLeave,
      entityId,
      userId,
      date,
    };

    this.employeeService.saveLeave(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Leave Data added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayTemplateLeave();
          this.selectedLeaveType = '';
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Leave Data.Are you ensure you entered your full name?',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  updateLeave() {
    this.updateTotalLeave();
  }

  removeLeave(index: number) {
    this.payTemplateLeaves.splice(index, 1);
    this.updateTotalLeave();
  }

  updateTotalLeave() {
    this.totalLeaveAmount = this.payTemplateLeaves.reduce(
      (sum, leave) => sum + (leave.balance || 0),
      0
    );
  }

  // Tax

  deleteTax(index: number) {
    this.payTemplateTaxs.splice(index, 1);
    this.updateTotalTax();
  }
  openTaxModal() {
    this.newTax = {
      // tax: new Tax(),
      amount: 0,
    };
  }

  closeTaxModal() {}

  onTaxTypeChange(value: string): void {
    // if (this.newTax.tax) {
    //   this.newTax.tax.taxType = value;
    // }
  }

  addTax(): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    const userId = +(localStorage.getItem('userid') || '');
    const date = new Date().toISOString();
    // const selectedTax = this.taxs.find(
    //   tax => tax.taxType === this.selectedTaxType
    // );

    const payload = {
      ...this.newTax,
      employee: this.personalData,
      // tax: selectedTax,
      entityId,
      userId,
      date,
    };

    this.employeeService.saveTax(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Tax Data added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getTaxes();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Tax Data.Are you ensure you entered your full name?',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  updateTax() {
    const payload = {
      ...this.employeeTaxData,
    };
    this.employeeService
      .updateTax(this.employeeTaxData.taxId, payload)
      .subscribe(
        (_response: any) => {
          Swal.fire({
            title: 'Success!',
            text: 'Tax Data updated successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {});
        },
        (_error: any) => {
          Swal.fire({
            title: 'Error!',
            text: 'Failed to update Tax Data. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
  }

  removeTax(index: number) {
    this.payTemplateTaxs.splice(index, 1);
    this.updateTotalTax();
  }

  updateTotalTax() {
    this.totalTaxAmount = this.payTemplateTaxs.reduce(
      (sum, tax) => sum + (tax.amount || 0),
      0
    );
  }

  // Superannuation

  private getSuperannuation() {
    this.employeeService.getSuperannuationsList().subscribe((data) => {
      this.superannuations = data;
    });
  }

  private getPayTemplateSuperannuation() {
    this.employeeService
      .getPayTemplateSuperannuations(this.personalData.employeeId)
      .subscribe((data) => {
        this.payTemplateSuperannuations = data;
        this.updateTotalSuperannuation();
      });
  }

  deleteSuperannuation(index: number) {
    this.employeeService.deleteSuperannuation(index).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Superannuation delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayTemplateSuperannuation();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Superannuation. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  openSuperannuationModal() {
    this.newSuperannuation = {
      superannuationFund: new Superannuation(),
      value: 0,
      membershipNumber: '',
      usi: '',
      contributionType: '',
      type: '',
      
    };
  }
  setDefaultValue(event: any) {
    if (!this.newSuperannuation.value) { 
      this.newSuperannuation.value = 11; 
    }
  }
  
  closeSuperannuationModal() {
    this.removeSuperannuation(0);
    this.selectedSuperannuationType = '';
    this.newSuperannuation.usi = '';	
   
  }

  onSuperannuationTypeChange(selectedFundName: string): void {
    const selectedFund = this.superannuations.find(
      (fund) => fund.fundName === selectedFundName
    );

    if (selectedFund) {
      this.newSuperannuation.usi = selectedFund.usi;
    }
  }

  onContributionTypeChange(value: string): void {
    if (value === 'SGC') {
      this.newSuperannuation.type = 'Percentage';
      this.newSuperannuation.membershipNumber = '';
      this.newSuperannuation.usi = '';
      this.selectedSuperannuationType = '';
      this.clearSuperannuationTypeChange2()
      
    }else{
      this.newSuperannuation.type = '';
      this.newSuperannuation.membershipNumber = '';
      this.newSuperannuation.usi = '';
      this.selectedSuperannuationType = '';
      this.clearSuperannuationTypeChange2();
    }
  }
  

  addSuperannuation(): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    const userId = +(localStorage.getItem('userid') || '');
    const date = new Date().toISOString();
    const selectedSuperannuation = this.superannuations.find(
      (superannuation) =>
        superannuation.fundName === this.selectedSuperannuationType
    );

    this.newSuperannuation.type = "Percentage";
    this.newSuperannuation.contributionType = "SGC";
    const payload = {
      ...this.newSuperannuation,
      employee: this.personalData,
      superannuationFund: selectedSuperannuation,
      entityId,
      userId,
      date,
    };

    this.employeeService.saveSuperannuation(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Superannuation Data added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayTemplateSuperannuation();
          this.selectedSuperannuationType = '';
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Superannuation Data.Are you ensure you entered your full name?',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  addSuperannuation2(): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    const userId = +(localStorage.getItem('userid') || '');
    const date = new Date().toISOString();
    const selectedSuperannuation = this.superannuations.find(
      (superannuation) =>
        superannuation.fundName === this.selectedSuperannuationType
    );
    const payload = {
      ...this.newSuperannuation,
      employee: this.personalData,
      superannuationFund: selectedSuperannuation,
      entityId,
      userId,
      date,
    };

    this.employeeService.saveSuperannuation(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Superannuation Data added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayTemplateSuperannuation();
          this.selectedSuperannuationType = '';
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Superannuation Data.Are you ensure you entered your full name?',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  updateSuperannuation() {
    this.updateTotalSuperannuation();
  }

  removeSuperannuation(index: number) {
    if (this.newSuperannuations && this.newSuperannuations.length > index) {
      this.newSuperannuations.splice(index, 1);
    } else {
      console.log("Invalid index or empty array");
    }
  }
  
  updateTotalSuperannuation() {
    this.totalSuperannuationAmount = this.payTemplateSuperannuations.reduce(
      (sum, superannuation) => sum + (superannuation.amount || 0),
      0
    );
  }

  DeleteForm() {
    throw new Error('Method not implemented.');
  }
  onAmountChange(): void {
    if (this.employmentData.salaryType === 'Annual') {
      this.calculateOrdinaryEarningsRate();
    }
  }

  calculateOrdinaryEarningsRate(): void {
    const { amount, hoursperWeek } = this.employmentData;
    if (amount > 0 && hoursperWeek > 0) {
      this.employmentData.ordinaryEarningsRate = parseFloat(
        (amount / 52 / hoursperWeek).toFixed(4)
      );
    } else {
      this.employmentData.ordinaryEarningsRate = 0.0;
    }
  }

  states = [
    { name: 'New South Wales', code: 'NSW' },
    { name: 'Victoria', code: 'VIC' },
    { name: 'Queensland', code: 'QLD' },
    { name: 'South Australia', code: 'SA' },
    { name: 'Western Australia', code: 'WA' },
    { name: 'Tasmania', code: 'TAS' },
    { name: 'Australian Capital Territory', code: 'ACT' },
    { name: 'Northern Territory', code: 'NT' },
  ];

  incomeTypes = [
    { name: 'Salary and Wages', code: 'SAW' },
    { name: 'Voluntary Agreement', code: 'VOL' },
    { name: 'Labour Hire', code: 'LAB' },
    { name: 'Working Holiday Maker', code: 'WHM' },
    { name: 'Foreign Employment Income', code: 'FEI' },
    { name: 'Inbound Assignees to Australia', code: 'IPS' },
    { name: 'Employment Termination Payment', code: 'ETP' }
  ];
  

  countryCodes = [
    { name: 'Afghanistan', code: '+93' },
    { name: 'Albania', code: '+355' },
    { name: 'Algeria', code: '+213' },
    { name: 'Andorra', code: '+376' },
    { name: 'Angola', code: '+244' },
    { name: 'Argentina', code: '+54' },
    { name: 'Armenia', code: '+374' },
    { name: 'Australia', code: '+61' },
    { name: 'Austria', code: '+43' },
    { name: 'Azerbaijan', code: '+994' },
    { name: 'Bahamas', code: '+1-242' },
    { name: 'Bahrain', code: '+973' },
    { name: 'Bangladesh', code: '+880' },
    { name: 'Barbados', code: '+1-246' },
    { name: 'Belarus', code: '+375' },
    { name: 'Belgium', code: '+32' },
    { name: 'Belize', code: '+501' },
    { name: 'Benin', code: '+229' },
    { name: 'Bhutan', code: '+975' },
    { name: 'Bolivia', code: '+591' },
    { name: 'Bosnia and Herzegovina', code: '+387' },
    { name: 'Botswana', code: '+267' },
    { name: 'Brazil', code: '+55' },
    { name: 'Brunei', code: '+673' },
    { name: 'Bulgaria', code: '+359' },
    { name: 'Burkina Faso', code: '+226' },
    { name: 'Burundi', code: '+257' },
    { name: 'Cambodia', code: '+855' },
    { name: 'Cameroon', code: '+237' },
    { name: 'Canada', code: '+1' },
    { name: 'Cape Verde', code: '+238' },
    { name: 'Central African Republic', code: '+236' },
    { name: 'Chad', code: '+235' },
    { name: 'Chile', code: '+56' },
    { name: 'China', code: '+86' },
    { name: 'Colombia', code: '+57' },
    { name: 'Comoros', code: '+269' },
    { name: 'Congo (Congo-Brazzaville)', code: '+242' },
    { name: 'Congo (DRC)', code: '+243' },
    { name: 'Costa Rica', code: '+506' },
    { name: 'Croatia', code: '+385' },
    { name: 'Cuba', code: '+53' },
    { name: 'Cyprus', code: '+357' },
    { name: 'Czech Republic', code: '+420' },
    { name: 'Denmark', code: '+45' },
    { name: 'Djibouti', code: '+253' },
    { name: 'Dominica', code: '+1-767' },
    { name: 'Dominican Republic', code: '+1-809, +1-829, +1-849' },
    { name: 'Ecuador', code: '+593' },
    { name: 'Egypt', code: '+20' },
    { name: 'El Salvador', code: '+503' },
    { name: 'Equatorial Guinea', code: '+240' },
    { name: 'Eritrea', code: '+291' },
    { name: 'Estonia', code: '+372' },
    { name: 'Eswatini', code: '+268' },
    { name: 'Ethiopia', code: '+251' },
    { name: 'Fiji', code: '+679' },
    { name: 'Finland', code: '+358' },
    { name: 'France', code: '+33' },
    { name: 'Gabon', code: '+241' },
    { name: 'Gambia', code: '+220' },
    { name: 'Georgia', code: '+995' },
    { name: 'Germany', code: '+49' },
    { name: 'Ghana', code: '+233' },
    { name: 'Greece', code: '+30' },
    { name: 'Grenada', code: '+1-473' },
    { name: 'Guatemala', code: '+502' },
    { name: 'Guinea', code: '+224' },
    { name: 'Guyana', code: '+592' },
    { name: 'Haiti', code: '+509' },
    { name: 'Honduras', code: '+504' },
    { name: 'Hungary', code: '+36' },
    { name: 'Iceland', code: '+354' },
    { name: 'India', code: '+91' },
    { name: 'Indonesia', code: '+62' },
    { name: 'Iran', code: '+98' },
    { name: 'Iraq', code: '+964' },
    { name: 'Ireland', code: '+353' },
    { name: 'Israel', code: '+972' },
    { name: 'Italy', code: '+39' },
    { name: 'Jamaica', code: '+1-876' },
    { name: 'Japan', code: '+81' },
    { name: 'Jordan', code: '+962' },
    { name: 'Kazakhstan', code: '+7' },
    { name: 'Kenya', code: '+254' },
    { name: 'Kiribati', code: '+686' },
    { name: 'Kuwait', code: '+965' },
    { name: 'Kyrgyzstan', code: '+996' },
    { name: 'Laos', code: '+856' },
    { name: 'Latvia', code: '+371' },
    { name: 'Lebanon', code: '+961' },
    { name: 'Lesotho', code: '+266' },
    { name: 'Liberia', code: '+231' },
    { name: 'Libya', code: '+218' },
    { name: 'Liechtenstein', code: '+423' },
    { name: 'Lithuania', code: '+370' },
    { name: 'Luxembourg', code: '+352' },
    { name: 'Madagascar', code: '+261' },
    { name: 'Malawi', code: '+265' },
    { name: 'Malaysia', code: '+60' },
    { name: 'Maldives', code: '+960' },
    { name: 'Mali', code: '+223' },
    { name: 'Malta', code: '+356' },
    { name: 'Marshall Islands', code: '+692' },
    { name: 'Mauritania', code: '+222' },
    { name: 'Mauritius', code: '+230' },
    { name: 'Mexico', code: '+52' },
    { name: 'Micronesia', code: '+691' },
    { name: 'Moldova', code: '+373' },
    { name: 'Monaco', code: '+377' },
    { name: 'Mongolia', code: '+976' },
    { name: 'Montenegro', code: '+382' },
    { name: 'Morocco', code: '+212' },
    { name: 'Mozambique', code: '+258' },
    { name: 'Myanmar (Burma)', code: '+95' },
    { name: 'Namibia', code: '+264' },
    { name: 'Nauru', code: '+674' },
    { name: 'Nepal', code: '+977' },
    { name: 'Netherlands', code: '+31' },
    { name: 'New Zealand', code: '+64' },
    { name: 'Nicaragua', code: '+505' },
    { name: 'Niger', code: '+227' },
    { name: 'Nigeria', code: '+234' },
    { name: 'North Korea', code: '+850' },
    { name: 'North Macedonia', code: '+389' },
    { name: 'Norway', code: '+47' },
    { name: 'Oman', code: '+968' },
    { name: 'Pakistan', code: '+92' },
    { name: 'Palau', code: '+680' },
    { name: 'Palestine', code: '+970' },
    { name: 'Panama', code: '+507' },
    { name: 'Papua New Guinea', code: '+675' },
    { name: 'Paraguay', code: '+595' },
    { name: 'Peru', code: '+51' },
    { name: 'Philippines', code: '+63' },
    { name: 'Poland', code: '+48' },
    { name: 'Portugal', code: '+351' },
    { name: 'Qatar', code: '+974' },
    { name: 'Romania', code: '+40' },
    { name: 'Russia', code: '+7' },
    { name: 'Rwanda', code: '+250' },
    { name: 'Saint Kitts and Nevis', code: '+1-869' },
    { name: 'Saint Lucia', code: '+1-758' },
    { name: 'Saint Vincent and the Grenadines', code: '+1-784' },
    { name: 'Samoa', code: '+685' },
    { name: 'San Marino', code: '+378' },
    { name: 'Saudi Arabia', code: '+966' },
    { name: 'Senegal', code: '+221' },
    { name: 'Serbia', code: '+381' },
    { name: 'Seychelles', code: '+248' },
    { name: 'Sierra Leone', code: '+232' },
    { name: 'Singapore', code: '+65' },
    { name: 'Slovakia', code: '+421' },
    { name: 'Slovenia', code: '+386' },
    { name: 'Solomon Islands', code: '+677' },
    { name: 'Somalia', code: '+252' },
    { name: 'South Africa', code: '+27' },
    { name: 'South Korea', code: '+82' },
    { name: 'Spain', code: '+34' },
    { name: 'Sri Lanka', code: '+94' },
    { name: 'Sudan', code: '+249' },
    { name: 'Suriname', code: '+597' },
    { name: 'Sweden', code: '+46' },
    { name: 'Switzerland', code: '+41' },
    { name: 'Syria', code: '+963' },
    { name: 'Taiwan', code: '+886' },
    { name: 'Tajikistan', code: '+992' },
    { name: 'Tanzania', code: '+255' },
    { name: 'Thailand', code: '+66' },
    { name: 'Timor-Leste', code: '+670' },
    { name: 'Togo', code: '+228' },
    { name: 'Tonga', code: '+676' },
    { name: 'Trinidad and Tobago', code: '+1-868' },
    { name: 'Tunisia', code: '+216' },
    { name: 'Turkey', code: '+90' },
    { name: 'Turkmenistan', code: '+993' },
    { name: 'Tuvalu', code: '+688' },
    { name: 'Uganda', code: '+256' },
    { name: 'Ukraine', code: '+380' },
    { name: 'United Arab Emirates', code: '+971' },
    { name: 'United Kingdom', code: '+44' },
    { name: 'United States', code: '+1' },
    { name: 'Uruguay', code: '+598' },
    { name: 'Uzbekistan', code: '+998' },
    { name: 'Vanuatu', code: '+678' },
    { name: 'Vatican City', code: '+379' },
    { name: 'Venezuela', code: '+58' },
    { name: 'Vietnam', code: '+84' },
    { name: 'Yemen', code: '+967' },
    { name: 'Zambia', code: '+260' },
    { name: 'Zimbabwe', code: '+263' },
  ];

  filteredCountries = [...this.countryCodes];
  selectedCountryCode: string = '+61';
  selectCode: string = '+61';
  countrySearch: string = '';
  emergencyPhoneNumber: string = '';
  phoneNumber: string = '';
  dropdownVisible: boolean = false;
  dropdownVisible2: boolean = false;

  showDropdown(): void {
    this.dropdownVisible = true;
  }

  hideDropdown(): void {
    this.dropdownVisible = false;
  }
  showDropdown2(): void {
    this.dropdownVisible2 = true;
  }

  hideDropdown2(): void {
    this.dropdownVisible2 = false;
  }

  selectCountryCode(country: { name: string; code: string }): void {
    this.selectedCountryCode = country.code;
    this.updateEmergencyPhoneNumber();
    this.hideDropdown();
  }

  selectedCode(country: { name: string; code: string }): void {
    this.selectCode = country.code;
    this.updatePhoneNumber();
    this.hideDropdown2();
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.dropdown')) {
      this.hideDropdown();
      this.hideDropdown2();
    }
  }

  @HostListener('document:keydown', ['$event'])
  onType(event: KeyboardEvent): void {
    if (this.dropdownVisible) {
      const searchTerm = event.key.toLowerCase();
      this.filteredCountries = this.countryCodes.filter((country) =>
        country.name.toLowerCase().startsWith(searchTerm)
      );
    }
  }

  updateEmergencyPhoneNumber(): void {
    const countryCode = this.selectedCountryCode || '';
    const phone = this.emergencyPhoneNumber || '';
    this.personalData.emergencyPhoneNumber = `${countryCode}-${phone}`;
  }

  updatePhoneNumber(): void {
    const countryCode = this.selectCode || '';
    const phone = this.phoneNumber || '';
    this.personalData.phoneNumber = `${countryCode}-${phone}`;
  }

  isAddOrUpdateDisabled(): boolean {
    if (!this.employmentData.salaryType) {
      return true;
    }

    if (this.employmentData.salaryType === 'Hourly Rate') {
      return !this.employmentData.ordinaryEarningsRate;
    }

    if (this.employmentData.salaryType === 'Annual Salary') {
      return !this.employmentData.amount || !this.employmentData.hoursperWeek;
    }

    return false;
  }

  restrictInput(event: KeyboardEvent): void {
    const allowedKeys = [
      'Backspace',
      'Tab',
      'ArrowLeft',
      'ArrowRight',
      'Delete',
      'Home',
      'End',
    ];
    const isNumber = /[0-9]/.test(event.key);
    const isDecimal = event.key === '.';

    const inputElement = event.target as HTMLInputElement;
    const value = inputElement.value;

    if (
      !isNumber &&
      !allowedKeys.includes(event.key) &&
      !(isDecimal && !value.includes('.'))
    ) {
      event.preventDefault();
    }

    if (event.key === '-') {
      event.preventDefault();
    }
  }

  onHoursAccruedAnnuallyChange(): void {
    if (this.newLeave.leaveMethods === 'Fixed Sum Per Period') {
      this.onHoursPerWeekChange();
    }
  }

  onHoursPerWeekChange(): void {
    const { hoursAccruedAnnually, hoursPerWeek } = this.newLeave;
    if (hoursAccruedAnnually > 0 && hoursPerWeek > 0) {
      this.newLeave.accruedHoursPerWeek = parseFloat(
        (hoursAccruedAnnually / 52 / hoursPerWeek).toFixed(4)
      );
    } else {
      this.newLeave.accruedHoursPerWeek = 0.0;
    }
  }

  checkEligibility() {
    if (this.employmentData.eligibleForLeaveLoading === false) {
      this.employmentData.eligibleForLeaveLoadingSuper = null;
      this.cdr.detectChanges();
    }
  }

  isAddDisabled(): boolean {
    if (!this.newSuperannuation.membershipNumber?.trim()) {
      return true; 
    }
  
    if (!this.newSuperannuation.type) {
      return true; 
    }
  
    return !this.newSuperannuation.value; 
  }

  clearSalaryInputs(): void {
    if (this.employmentData.salaryType === 'Annual Salary') {
      this.employmentData.amount = 0;
      this.employmentData.hoursperWeek = 0;
      this.employmentData.ordinaryEarningsRate = 0;
    } else if (this.employmentData.salaryType === 'Hourly Rate') {
      this.employmentData.ordinaryEarningsRate = 0;
    }
  }

  onLeaveMethodChange(): void {
    this.newLeave.hoursAccruedAnnually = 0;
    this.newLeave.accruedHoursPerWeek = 0;
    this.newLeave.openingBalance = 0;
  }

  clearSuperannuationTypeChange(index: number): void {
    if (this.newSuperannuations[index]) {
      this.newSuperannuations[index].value = 0;
    }
  }
  
  
  clearSuperannuationTypeChange2(): void {
    if (this.newSuperannuation) {
      this.newSuperannuation.value = 0;
    }
  }
  
  
  clearEmployeeData(): void {
    window.location.assign("/payroll-settings-employee");
  }
  
  checkBusinessAddress() {
      const query = this.personalData.address
        ? this.personalData.address.trim()
        : '';
      const apiUrl = `${environment.addressfinderBaseUrl}?key=${
        environment.addressFinderApiKey
      }&q=${encodeURIComponent(query)}&format=json&source=gnaf%2Cpaf`;
  
      if (!query) {
        this.isAddressValid = false;
        this.addressValidationMessage = 'Please enter a business address.';
        this.suggestedAddresses = [];
        return;
      }
  
      this.http.get(apiUrl).subscribe(
        (response: any) => {
          if (response && response.completions) {
            this.suggestedAddresses = response.completions;
          } else {
            this.suggestedAddresses = [];
          }
          this.isAddressValid = true;
          this.addressValidationMessage = '';
        },
        (error) => {
          // this.isAddressValid = false;
          // this.addressValidationMessage = 'Error occurred. Try again';
          console.error('Error fetching address suggestions:', error);
        }
      );
    }
  
    selectAddress(address: any) {
      this.personalData.address = address.full_address;
      this.suggestedAddresses = [];
      this.isAddressValid = true;
      this.addressValidationMessage = '';
    }
    
  addAnotherSuperannuation(): void {
    this.selectedSuperannuationType = '';
    this.newSuperannuation.usi = '';	
    this.newSuperannuations.push(new NewSuperannuation());
  }

  hasSGCContribution(): boolean {
    return this.payTemplateSuperannuations.some(
      (superannuation) => superannuation.contributionType === 'SGC'
    );
  }
  toggleBalanceSalary() {
    this.isBalanceSalary = !this.isBalanceSalary;
    
    if (this.isBalanceSalary) {
      this.isAmount = true;  
    } else {
      this.isAmount = false;  
    }
  }

  toggleBalanceSalary2(account: BankAccountNew) {
    account.isBalanceSalary2 = !account.isBalanceSalary2;
  
    if (account.isBalanceSalary2) {
      account.isAmount2 = true;  
    } else {
      account.isAmount2 = false;  
    }
  }
  
  
}
function elseIf(additionalAccounts: BankAccountNew[]) {
  throw new Error('Function not implemented.');
}

