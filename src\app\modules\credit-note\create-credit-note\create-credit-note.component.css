/* General Styles */
body {
  font-family: Arial, sans-serif;
  background-color: transparent;
  margin: 0;
  padding: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: transparent;
}

#creditNoteNumber{
  background-color: #fff;
}

/* Header Styles */
.header {
  display: flex;
  background-color: transparent;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: none;
  border: none;
  gap: 5px;
}

.header h3 {
  flex: 1;
  margin-bottom: 0;
  font-family: Inter;
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

/* Border Container */
.bd {
  border: 1px solid #cec9c980;
  border-radius: 12px;
}

/* Form Section Styles */
.form-section,
.form-section_2 {
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #f7f7f7;
}

.form-row {
  display: flex;
  gap: 32px;
  /* padding-right: 20px; */
  margin-bottom: 20px;
}

.form-group,
.form-group_2 {
  display: flex;
  flex-direction: row;
  width: 100%;
}


.form-group label,
.form-group_2 label {
  display: block;
  margin-bottom: 5px;
  /* font-weight: bold; */
  width: 20%;
}

.form-group input,
.form-group select,
.form-group_2 input,
.form-group_2 textarea {
  width: 40%;
  padding: 8px 10px;
  border: 1px solid #c7c7c7;
  border-radius: 8px;
  /* font-size: 14px; */
}

.form-group_2 label {
  width: 30%;
}

.form-group_2 input,
.form-group_2 textarea {
  width: 70%;
}

.form-group_2 input {
  font-weight: bold;
}

/* Input and Textarea Styles */
.form-control,
.form-control1 {
  border-radius: 5px;
  border: 1px solid #ced4da;
  padding: 10px;
}

.form-control {
  text-align: right;
}

/* Search Bar Styles */
.search-bar {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-left: 20px;
  margin-bottom: 10px;
  margin-top: 10px;
  align-items: center;
  position: relative;
}

/* Button Styles */
.btn-primary,
.add_invoice {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 5px 40px;
  cursor: pointer;
  border-radius: 13px;
  border: none;
  margin-left: 10px;
  font-size: 17px;
}

.btn-primary:hover,
.add_invoice:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

.btn-secondary {
  background: transparent;
  color: #4262ff;
  border: 2px solid #4262ff;
  padding: 5px 40px;
  margin-right: 10px;
  cursor: pointer;
  border-radius: 13px;
  font-weight: bold;
  font-size: 17px;
}

.btn-secondary:hover {
  background-color: #4262ff;
  color: white;
}

/* Table Styles */
.table-section {
  background-color: transparent;
  overflow: hidden;
  margin-bottom: 20px;
  padding-left: 20px;
  padding-right: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: linear-gradient(
    90deg,
    rgba(66, 98, 255, 0.06) 0%,
    rgba(63, 20, 153, 0.06) 100%
  );
  color: black;
  text-align: left;
  padding: 12px;
  font-weight: normal;
}

td {
  padding: 12px;
  border-bottom: 1px solid #ddd;
  background-color: white;
  vertical-align: middle;
}

/* Responsive Table */
.table-responsive {
  overflow-x: auto;
}

@media (max-width: 768px) {
  .form-group select {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 8px;
    font-size: 14px;
  }

  .form-group {
    display: flex;
    flex-direction: column;
  }

  .form-group label {
    display: block;
    margin-bottom: 5px;
    /* font-weight: bold; */
    width: 100%;
  }

  .form-group input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #c7c7c7;
    border-radius: 8px;
    font-size: 14px;
  }

  .form-row {
    display: flex;
    gap: 32px;
    padding-right: 20px;
    margin-bottom: 20px;
  }

  .form-group_2 {
    flex-direction: column;
    display: flex;
    margin-left: 0%;
  }

  .form-group_2 label {
    display: block;
    margin-bottom: 5px;
    /* font-weight: bold; */
    width: 100%;
  }

  .form-group_2 input {
    width: 100%;
    padding: 8px 10px;
    border: 2px solid #c7c7c7;
    border-radius: 8px;
    font-size: 14px;
  }

  .form-group_2 textarea {
    width: 100%;
    padding: 8px 10px;
    border: 2px solid #c7c7c7;
    border-radius: 8px;
    font-size: 14px;
  }

  .btns {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .btn-primary {
    background: linear-gradient(to right, #4262ff, #512ca2);
    color: white;
    font-weight: bold;
    padding: 5px 30px;
    cursor: pointer;
    border-radius: 15px;
    border: none;
    margin-left: 0px;
    font-size: 17px;
  }

  .table-section {
    padding-left: 0;
    padding-right: 0;
    overflow-x: auto;
  }

  .table-responsive {
    overflow-x: scroll;
  }

  .table {
    /* width: 100%; */
    min-width: 800px;
    /* Minimum width to allow horizontal scroll */
  }

  .table th,
  .table td {
    padding: 8px;
    font-size: 14px;
  }

  .table input.form-control {
    min-width: 110px; /* Adjust based on expected input length */
    width: 100%;
  }

  .header {
    padding: 0;
  }

  .header h3 {
    font-size: 28px;
    text-align: center;
  }
}
