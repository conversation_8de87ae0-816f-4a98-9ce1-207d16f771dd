<app-admin-navigation></app-admin-navigation>
<app-logo-header></app-logo-header>

<div class="upper-content modern-subscription-header">
  <h2>Upgrade or change subscription</h2>
  <p>
    Choose the best plan for your business. You can upgrade, downgrade, or renew
    your subscription at any time. Select a plan below to continue.
  </p>
</div>
<div class="plans modern-plans" *ngIf="subscriptionPlans.length > 0">
  <div class="plan modern-plan-card" *ngFor="let plan of subscriptionPlans">
    <h2 class="modern-plan-title">{{ plan.subscriptionPlanId.subscriptionPlan }}</h2>
    <p class="modern-plan-desc">
      Enjoy all the features and benefits included in the {{
      plan.subscriptionPlanId.subscriptionPlan
      }} plan. Get started today and take your business to the next level.
    </p>
    <h4 class="modern-plan-price">${{ isOnGracePeriod ? plan.graceAmount : plan.amount }}/Month</h4>
    <div class="button-container modern-plan-btns">
      <button
        class="change-plan modern-btn"
        [disabled]="plan.subscriptionFeeId === selectedSubscriptionPlan"
        [ngClass]="{
          'current-plan': plan.subscriptionFeeId === selectedSubscriptionPlan
        }"
        (click)="changePlan(plan.subscriptionFeeId)"
      >
        {{
          plan.subscriptionFeeId === selectedSubscriptionPlan
            ? "Current Plan"
            : "Select"
        }}
      </button>
      <button
        class="renew-plan modern-btn-outline"
        *ngIf="plan.subscriptionFeeId === selectedSubscriptionPlan"
        (click)="navigateToPayment(plan.subscriptionFeeId)"
      >
        Renew the Plan
      </button>
    </div>
  </div>
</div>
