import { Component, ElementRef, ViewChild } from '@angular/core';
import { forkJoin } from 'rxjs';
import Swal from 'sweetalert2';
import { GlPostingHead ,GlPostingDetails } from '../../journal-voucher/journal-voucher';
import { JournalVoucherService } from '../../journal-voucher/journal-voucher.service';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { SupplierStatementService } from '../../supplier-statement/supplier-statement.service';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';

@Component({
  selector: 'app-gl-posting-list-by-suppliers',
  templateUrl: './gl-posting-list-by-suppliers.component.html',
  styleUrls: ['./gl-posting-list-by-suppliers.component.css']
})
export class GlPostingListBySuppliersComponent {
    quotes:  GlPostingHead[] = []; 
    quotesDetails: GlPostingDetails[] = []; 
    selectedQuotesDetails: GlPostingDetails[]=[];
    businessPartners: BusinessPartner[] = [];
    processedData: any[] = []; 
    filteredRecords: any[] = [];
    accountCode: string | null = null; 
    accountName: string | null = null; 
    startDate: string | null = null; 
    endDate: string | null = null; 
    searchTerm: string = ''; 
    activeTab = 'all';
    isLoading = false;

    @ViewChild('supplierStatementPreviewFrame') supplierStatementPreviewFrame!: ElementRef;

    constructor(
      private journalVoucherService: JournalVoucherService,
      private businessPartnerService: BusinessPartnerService,
      private supplierStatementService: SupplierStatementService,
      public sanitizer: DomSanitizer,
      private route: ActivatedRoute, 
      private router: Router,
    ) {}
  
     /**  ngOnInit() {
        this.route.queryParams.subscribe(params => {
          this.accountCode = JSON.parse(params['selectedAccountCode']);
        });
      
        forkJoin({
          businessPartners: this.businessPartnerService.getBusinessPartnerList(),
          quotesDetails: this.journalVoucherService.getGlPostingDetailsList(
            parseInt(localStorage.getItem('entityId') || '0', 10)
          ),
          glPostingHead: this.journalVoucherService.getGlPostingHeadList(
            parseInt(localStorage.getItem('entityId') || '0', 10)
          ),
        }).subscribe(
          ({ businessPartners, quotesDetails, glPostingHead }) => {
            this.businessPartners = businessPartners;
            this.quotesDetails = quotesDetails;
            this.quotes = glPostingHead;
      
            if (this.accountCode) {
              this.selectedQuotesDetails = this.quotesDetails.filter(detail =>
                detail.coaLedgerAccount?.ledgerAccountCode == this.accountCode
              );
              this.accountName =
                this.selectedQuotesDetails[0]?.coaLedgerAccount?.ledgerAccountName ??
                null;
            }
      
            this.processedData = this.aggregateData(this.quotes, this.selectedQuotesDetails);
            this.filteredRecords = [...this.processedData]; 
          },
          error => console.error('Error fetching data:', error)
        );
      }**/



        ngOnInit() {
  this.route.queryParams.subscribe(params => {
    const selectedAccountCodeParam = params['selectedAccountCode'];
    const accountNameParam = params['accountName'];
    const fromParam = params['from'];
    const toParam = params['to'];

    // Decode query params safely
    this.accountCode = selectedAccountCodeParam ? JSON.parse(selectedAccountCodeParam) : null;
    this.accountName = accountNameParam ? decodeURIComponent(accountNameParam) : null;
    this.startDate = fromParam ? decodeURIComponent(fromParam) : null;
    this.endDate = toParam ? decodeURIComponent(toParam) : null;
  });

  // Load all required data
  forkJoin({
    businessPartners: this.businessPartnerService.getBusinessPartnerList(),
    quotesDetails: this.journalVoucherService.getGlPostingDetailsList(
      parseInt(localStorage.getItem('entityId') || '0', 10)
    ),
    glPostingHead: this.journalVoucherService.getGlPostingHeadList(
      parseInt(localStorage.getItem('entityId') || '0', 10)
    ),
  }).subscribe(
    ({ businessPartners, quotesDetails, glPostingHead }) => {
      this.businessPartners = businessPartners;
      this.quotesDetails = quotesDetails;
      this.quotes = glPostingHead;

      // Case 1: From accountCode
      if (this.accountCode) {
        this.selectedQuotesDetails = this.quotesDetails.filter(detail =>
          detail.coaLedgerAccount?.ledgerAccountCode === this.accountCode
        );
        this.accountName = this.selectedQuotesDetails[0]?.coaLedgerAccount?.ledgerAccountName ?? null;
      }

      // Case 2: From accountName only
      else if (this.accountName) {
        const matchedDetail = this.quotesDetails.find(detail =>
          detail.coaLedgerAccount?.ledgerAccountName === this.accountName
        );

        if (matchedDetail) {
          this.accountCode = matchedDetail.coaLedgerAccount?.ledgerAccountCode ?? null;
          this.selectedQuotesDetails = this.quotesDetails.filter(detail =>
            detail.coaLedgerAccount?.ledgerAccountCode === this.accountCode
          );
        } else {
          Swal.fire({
            title: 'No Matching Account',
            text: `Could not find an account matching name: ${this.accountName}`,
            icon: 'warning',
            confirmButtonText: 'OK'
          });
        }
      }

      this.processedData = this.aggregateData(this.quotes, this.selectedQuotesDetails);
      this.filteredRecords = [...this.processedData];
    },
    error => console.error('Error loading data:', error)
  );
}

  
    aggregateData(glPostingHead: GlPostingHead[], glPostingDetails: GlPostingDetails[])  {

      const mergedData = glPostingHead.map(head => {
        const filteredDetails = glPostingDetails.filter(detail => 
          detail.glTransactionId?.glTransactionId === head.glTransactionId
        );
      
        return {
          businessPartnerId: head.businessPartnerId,
          details: filteredDetails
        };
      });
      
      console.log("mergedData:", mergedData);

      const resultMap = new Map<string, { dr_amount: number; cr_amount: number }>();
    
      mergedData.forEach(entry => {
        const { businessPartnerId, details } = entry;
        let drTotal = 0;
        let crTotal = 0;
    
        details.forEach(detail => {
          drTotal += detail.drAmount?? 0;
          crTotal += detail.crAmount?? 0;
        });
    
        if (businessPartnerId !== null) {
        const key = String(businessPartnerId);

        if (resultMap.has(key)) {
          resultMap.get(key)!.dr_amount += drTotal;
          resultMap.get(key)!.cr_amount += crTotal;
        } else {
        resultMap.set(key, { dr_amount: drTotal, cr_amount: crTotal });
      }
    }
  });

  const results = Array.from(resultMap, ([businessPartnerId, totals]) => {
    if (totals.dr_amount === 0 && totals.cr_amount === 0) {
      return null; // Skip adding this entry
    }
  
    const businessPartnerDetails = this.businessPartners.find(partner => 
      partner.businessPartnerId === Number(businessPartnerId)
    );
    
    console.log("Partner ID:", businessPartnerId);
  
    return {
      businessPartnerId,  
      dr_amount: totals.dr_amount,
      cr_amount: totals.cr_amount,
      businessPartnerDetails: businessPartnerDetails || null 
    };
  }).filter(entry => entry !== null); // Remove null values
  
      console.log("results :" , results);   
    
      return results;

    }

    onSearchChange() {
      this.filterQuotes();
    }
  
    setActiveTab(tab: string) {
      this.activeTab = tab;
      this.filterQuotes();
    }
  
    filterQuotes(): void {
      this.filteredRecords = this.processedData.filter(record => {
        const matchesSearch = this.searchTerm
          ? (record.businessPartnerDetails.bpFirstName && 
             record.businessPartnerDetails.bpFirstName.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
            (record.businessPartnerDetails.bpName && 
             record.businessPartnerDetails.bpName.toLowerCase().includes(this.searchTerm.toLowerCase()))
          : true; 
        
        return matchesSearch;
      });
    }
    
    resetFilters() {
      this.searchTerm = '';
      this.activeTab = 'all';
      this.startDate = null;
      this.endDate = null;
      this.filteredRecords = [...this.processedData]
    }
    
   /**  openSupplierRecordsPage(accountCode: string | null, partnerId: number | null) {
      if (accountCode) {
        this.router.navigate(['/gl-account-details'], {
          queryParams: { selectedAccountCode: JSON.stringify(accountCode), selectedBusinessPartner: JSON.stringify(partnerId) }
        });
      }
    }**/



      openSupplierRecordsPage(accountCode: string | null, partnerId: number | null) {
  const queryParams: any = {
    selectedAccountCode: JSON.stringify(accountCode),
    selectedBusinessPartner: JSON.stringify(partnerId)
  };

  // Pass from/to only if they exist
  if (this.startDate && this.endDate) {
    queryParams.from = this.startDate;
    queryParams.to = this.endDate;
  }

  this.router.navigate(['/gl-account-details'], {
    queryParams
  });
}

  generateGLReport(businessPartnerId: string | null) {
  const entityId = +(localStorage.getItem('entityId') || '0');
  const entityUUID = localStorage.getItem('entityUuid')!;

  const fromDate = this.startDate || '2025-01-01';
  const toDate = this.endDate || new Date().toISOString().split('T')[0];

  if (!businessPartnerId) {
    Swal.fire({
      title: 'Warning',
      text: 'Business Partner ID is required.',
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f'
    });
    return;
  }

  if (!entityUUID) {
    Swal.fire({
      title: 'Missing UUID',
      text: 'Entity UUID is missing. Please try logging in again.',
      icon: 'error',
      confirmButtonText: 'OK'
    });
    return;
  }

  const requestData = {
    fromDate,
    toDate,
    entityId,
    businessPartnerId: Number(businessPartnerId),
    entityUUID
  };

  this.isLoading = true;
  console.log('Fetching GL Report with:', requestData);

  this.supplierStatementService.getSupplierStatementReport(requestData).subscribe(
    data => {
      const base64String = data.response;

      if (base64String && base64String.trim().length >= 50) {
        this.loadPdfIntoIframe(base64String);
      } else {
        this.isLoading = false;
        Swal.fire({
          title: 'No Data',
          text: 'No General Ledger data available for the selected period.',
          icon: 'info',
          confirmButtonText: 'OK'
        });
      }
    },
    error => {
      console.error('API Error:', error);
      this.isLoading = false;
      Swal.fire({
        title: 'Error',
        text: 'Failed to load General Ledger report.',
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }
  );
}

  
    private loadPdfIntoIframe(base64String: string) {
      console.log('Loading PDF into iframe');
  
      let dataLoaded = false;
  
      if (!this.supplierStatementPreviewFrame) {
          console.error('supplierStatementPreviewFrame is undefined');
          return;
      }
  
      const iframe = this.supplierStatementPreviewFrame.nativeElement;
      if (!iframe) {
          console.error('Iframe element is not available');
          return;
      }
  
      if (base64String && base64String.trim().length >= 50) {
          const pdfData = 'data:application/pdf;base64,' + base64String;
          console.log('Sanitizing URL');
          const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
          
          console.log('Setting src on iframe');
          iframe.onload = () => {
              console.log('PDF Loaded into iframe');
              this.isLoading = false;
              dataLoaded = true;
          };
  
          iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
      } else {
          console.log('Invalid Base64 string');
      }
  
      setTimeout(() => {
          if (!dataLoaded) {
              this.isLoading = false;
              console.log('PDF did not load within 2 seconds');
              Swal.fire({
                  title: 'No Data',
                  text: 'No Supplier Statement data for preview.',
                  icon: 'info',
                  confirmButtonText: 'OK',
                  confirmButtonColor: '#007bff'
              });
          }
      }, 2000);
    }
  
}