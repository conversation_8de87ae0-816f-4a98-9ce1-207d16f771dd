<app-admin-navigation></app-admin-navigation>
<div class="container">
  <div class="faq-content">
    <!-- Ledger Chipm Application Help Guide -->
    <h2 id="overview">1. Ledger Chimp Application Overview</h2>
    <p>
      The Ledger Chimp application is designed to streamline business operations, offering comprehensive tools for
      managing business entities, user registrations, credit notes, and payments. This help guide addresses common issues users may
      encounter while using the application and provides solutions to ensure a seamless experience.
    </p>
    <!-- Adding a local video below the paragraph -->
    <div class="responsive-video">
      <video controls playsinline>
        <source src="assets/videos/Video01-LG.mp4" type="video/mp4">
        Your browser does not support the video tag.
      </video>
    </div>
    <!-- Common Issues Section -->
    <h2 id="common-issues">2. Common Issues and Resolutions</h2>
    <div class="accordion" id="accordionExample">

     

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingTwo">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
            Entity Creation: ABN Lookup Failure
          </button>
        </h2>
        <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>How it works:</strong> When you enter a valid ABN (Australian Business Number) and click "Lookup ABN," the system automatically retrieves the registered entity name associated with the ABN. This helps streamline entity creation.<br /><br />
            <strong>Scenarios:</strong>
            <ul>
                <li>
                    <strong>Valid ABN:</strong> If the ABN is correct, the system displays the registered entity name below the ABN field.
                </li>
                <li>
                    <strong>Invalid ABN:</strong> If the ABN is invalid, an error message appears: "Invalid ABN."
                </li>
            </ul>
            <strong>Resolution for Invalid ABN:</strong>
            Ensure that the ABN is correct and properly formatted. Verify the ABN on the official [ABR website](https://abr.gov.au/) if you are unsure.
        </div>
        <br/>
        <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
          <a href="https://www.youtube.com/watch?v=6e6lXNwd2RE" target="_blank" class="btn btn-primary mt-2">
              Watch on YouTube
          </a>
        </div>
      </div>

    
      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
            User Agreement Acceptance
          </button>
        </h2>
        <div id="collapseFive" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Issue:</strong> Users are unable to proceed without accepting the terms and conditions.<br />
            <strong>Error Message:</strong> "Please agree to the terms and conditions to continue."<br />
            <strong>Resolution:</strong> Ensure that the user is required to accept the terms and conditions before
            advancing to subsequent steps in the registration process, thereby preventing any unintended progression.
          </div>
         
        </div>
      </div>

     

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse9"
            aria-expanded="false" aria-controls="collapseFive">
            Payment Processing: Stripe Transaction Issue
          </button>
        </h2>
        <div id="collapse9" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Issue:</strong> Payment processing fails during Stripe transactions, potentially due to invalid input 
            such as incorrect card number, ZIP code, or card expiry date.<br />
            <strong>Error Message:</strong> "Payment failed. Please check your card details or ZIP code and try again."<br />
            <strong>Resolution:</strong>
            <ul>
                <li>Ensure that the card number is entered correctly, without spaces or incorrect digits.</li>
                <li>The ZIP code must be at least 5 digits long </li>
                <li>Double-check the card expiry date and ensure it is valid (not expired).</li>
                <li>If the error persists, confirm the card details with the issuing bank or try a different card.</li>
            </ul>
        </div>
       
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse10" aria-expanded="false" aria-controls="collapseFive">
            Validate Email Format
          </button>
        </h2>
        <div id="collapse10" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Issue:</strong> The email address format does not conform to the standard structure.<br />
            <strong>Error Message:</strong> "Invalid email format."<br />
            <strong>Resolution:</strong>
            <ul>
                <li>Ensure the email address includes an <code>@</code> symbol and a valid domain, such as 
                    <code>name&#64;example.com</code>.</li>
                <li>Check for common mistakes, such as:
                    <ul>
                        <li>Spaces within the email address (e.g., <code>name @example.com</code>).</li>
                        <li>Missing domain name (e.g., <code>name&#64;</code>).</li>
                        <li>Typos in the domain (e.g., <code>name&#64;examplle.com</code>).</li>
                    </ul>
                </li>
                <li>Use a valid top-level domain (e.g., <code>.com</code>, <code>.org</code>, <code>.net</code>).</li>
                <li>If you continue to experience issues, try using an alternative email address.</li>
            </ul>
            The system ensures that only valid email formats are accepted to avoid errors in communication and processing.
        </div>
        <br/>
        <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
          <a href="https://www.youtube.com/watch?v=80pQ8olYDN8" target="_blank" class="btn btn-primary mt-2">
              Watch on YouTube
          </a>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse11" aria-expanded="false" aria-controls="collapseFive">
            Passwords Do Not Match
          </button>
        </h2>
        <div id="collapse11" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Issue:</strong> The passwords entered in the fields do not match.<br />
            <strong>Error Message:</strong> "Passwords Do Not Match."<br />
            <strong>Resolution:</strong>
            <ul>
                <li>Ensure that you enter the exact same password in both the "Password" and "Confirm Password" fields.</li>
                <li>Check for common errors such as:
                    <ul>
                        <li>Typos or missing characters in one of the fields.</li>
                        <li>Unintended spaces before or after the password.</li>
                        <li>Inconsistent capitalization (e.g., "Password123" vs. "password123").</li>
                    </ul>
                </li>
                <li>If the issue persists:
                    <ul>
                        <li>Clear both fields and carefully re-enter the password.</li>
                        <li>Ensure the password meets system requirements (e.g., minimum length, special characters).</li>
                    </ul>
                </li>
            </ul>
             </div>
             <br/>
             <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
               <a href="https://www.youtube.com/watch?v=NCGwccKd_HM" target="_blank" class="btn btn-primary mt-2">
                   Watch on YouTube
               </a>  
        </div>
      </div>


      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse13" aria-expanded="false" aria-controls="collapseFive">
            Credit Note Creation Error
          </button>
        </h2>
        <div id="collapse13" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Error Message:</strong> "Credit Note Creation Error."<br />
            <strong>Resolution:</strong> Ensure that all required information is correctly entered before attempting to
            create the credit note again.
          </div>
          <br/>
          <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
            <a href="https://www.youtube.com/watch?v=lH_VmtFMj2w" target="_blank" class="btn btn-primary mt-2">
                Watch on YouTube
            </a>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse14" aria-expanded="false" aria-controls="collapseFive">
            Missing Due Date
          </button>
        </h2>
        <div id="collapse14" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <!-- <strong>Issue:</strong> "Due date is required."<br /> -->
            <strong>Error Message:</strong> "Due date is required."<br />
            <strong>Resolution:</strong> Enter a valid due date to proceed.
          </div>
        
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse15" aria-expanded="false" aria-controls="collapseFive">
            Required Fields Missing
          </button>
        </h2>
        <div id="collapse15" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <!-- <strong>Issue:</strong> The application fails to generate a valid credit note number due to incorrect formatting or missing data.<br /> -->
            <strong>Error Message:</strong> "Required Fields Missing."<br />
            <strong>Resolution:</strong> Complete all required fields before proceeding with the submission.
          </div>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse16" aria-expanded="false" aria-controls="collapseFive">
            Username Already Taken
          </button>
        </h2>
        <div id="collapse16" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Error Message:</strong> "Username Already Taken."<br />
            <strong>Resolution:</strong>
            <ul>
                <li>Ensure that the username entered is unique and not already registered in the system.</li>
                <li>If you are trying to register an email address, verify that it hasn’t been used for another account.</li>
                <li>Try the following alternatives:
                    <ul>
                        <li>Modify the username slightly (e.g., add a number or special characters).</li>
                        <li>Use a different email address if your email is the username.</li>
                    </ul>
                </li>
                <li>If you believe this is an error, contact support for further assistance.</li>
            </ul>
            <br />
            <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
            <a href="https://www.youtube.com/watch?v=b_uPE7v2xSY" target="_blank" class="btn btn-primary mt-2">
                Watch on YouTube
            </a>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse17" aria-expanded="false" aria-controls="collapseFive">
            Login Failed
          </button>
        </h2>
        <div id="collapse17" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Issue:</strong> The system was unable to log in the user due to incorrect credentials or
            a non-existent account.<br />
            <strong>Error Message:</strong> "Login Failed"<br />
            <strong>Resolution:</strong>
            <ul>
                <li>Ensure the username entered is a valid email address (e.g., <code>name&#64;example.com</code>).</li>
                <li>Verify that the password is entered correctly, considering:
                    <ul>
                        <li>No extra spaces before or after the password.</li>
                        <li>Correct capitalization, as passwords are case-sensitive.</li>
                    </ul>
                </li>
                <li>If you’ve forgotten your password, use the "Forgot Password" option to reset it.</li>
                <li>Ensure your account is active. If not, contact support for assistance.</li>
            </ul>
            <br />
            <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
            <a href="https://www.youtube.com/watch?v=v-LzJ6b2yiA" target="_blank" class="btn btn-primary mt-2">
                Watch on YouTube
            </a>
        </div>
      </div>


      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse20" aria-expanded="false" aria-controls="collapseFive">
            Zero Quantity and Discount (in Sales Quote / Invoice )
          </button>
        </h2>
        <div id="collapse20" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Issue:</strong> The system cannot process a discount without specifying a quantity.<br />
            <strong>Error Message:</strong> "Zero Quantity and Discount"<br />
            <strong>Resolution:</strong> Enter a valid quantity to enable discount calculation.
          </div>
         
        </div>
      </div>

   
      <div class="accordion-item">
        <h2 class="accordion-header" id="headingFive">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse22" aria-expanded="false" aria-controls="collapseFive">
            Business Partner Required Fields
          </button>
        </h2>
        <div id="collapse22" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Issue:</strong> Required fields for the business partner have not been completed.<br />
            <strong>Error Message:</strong> "Business Partner Required Fields"<br />
            <strong>Resolution:</strong> Ensure that all mandatory fields related to the business partner are filled
            out.
          </div>
          <br />
          <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
          <a href="https://www.youtube.com/watch?v=QVjyN0-5I3U" target="_blank" class="btn btn-primary mt-2">
              Watch on YouTube
          </a>
        </div>
      </div>

    </div>

    <!-- Sales Quote Section -->
    <h2 id="sales-quote-overview">3. Sales Quote Overview</h2>
    <p>
      The Sales Quote feature in the Ledger Chimp system is a powerful tool designed to help businesses efficiently
      create, manage, and distribute sales quotations to customers. This feature enhances your business's quoting process by ensuring
      that all quotes are generated with accuracy and efficiency, reducing the chances of errors and miscommunication.
    </p>
    <div class="accordion" id="accordionExample">

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingTwo">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse30" aria-expanded="false" aria-controls="collapseOne">
            Creating a New Sales Quote
          </button>
        </h2>
        <div id="collapse30" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Steps to Create a New Sales Quote:</strong><br /><br />
            <ol>
                <li>
                    <strong>Navigate to Sales Quote:</strong> 
                    After logging in, locate and click the <strong>"Sales Quote"</strong> tab in the navigation bar.
                </li>
                <li>
                    <strong>Open the Create Sales Quote Page:</strong> 
                    Click the <strong>"Create Sales Quote"</strong> button.
                </li>
                <li>
                    <strong>Fill in Quotation Details:</strong> 
                    <ul>
                        <li>Set the date and validity period.</li>
                        <li>The quotation number will be automatically generated.</li>
                        <li>Select an existing business partner or create a new one by clicking <strong>"Create Business Partner"</strong>.</li>
                    </ul>
                </li>
                <li>
                    <strong>Add Items or Services to the Quote:</strong> 
                    <ul>
                        <li>For existing items, select them from the list.</li>
                        <li>To add new items, click <strong>"Add New Item"</strong>.</li>
                        <li>For services, directly input a description, price, and tax in the table.</li>
                        <li>Specify the item code, description, quantity, and pricing details as needed.</li>
                    </ul>
                </li>
                <li>
                    <strong>Review and Save:</strong> 
                    Verify all the details and click <strong>"Save"</strong> to finalize the quote.
                </li>
            </ol>
            <br />
          <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
          <a href="https://www.youtube.com/watch?v=xgRThLGLx_s" target="_blank" class="btn btn-primary mt-2">
              Watch on YouTube
          </a>
        </div>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingTwo">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse31" aria-expanded="false" aria-controls="collapseOne">
            Common Errors in Sales Quotes
          </button>
        </h2>
        <div id="collapse31" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Issue:</strong> The system cannot process a discount without specifying a quantity.<br />
            <strong>Error Message:</strong> "Zero Quantity and Discount"<br />
            <strong>Resolution:</strong> Enter a valid quantity to enable discount calculation.
          </div>
        </div>
      </div>

    </div>

    <!-- Invoice Management Section -->
    <h2 id="invoice-overview">
      4. Invoice Management Overview
    </h2>
    <p>
      The "Invoice" feature within the Ledger Chimp system is designed to facilitate the seamless creation, management,
      and updating of invoices. This tool ensures that all financial transactions are accurately documented, enhancing the efficiency
      and reliability of your business operations.
    </p>
    <div class="accordion" id="accordionExample">

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingTwo">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse40" aria-expanded="false" aria-controls="collapseOne">
            Creating a New Invoice
          </button>
        </h2>
        <div id="collapse40" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Steps to Create a New Invoice:</strong><br /><br />
            <strong>Step 01</strong> Access the Invoice Section: After logging in, click on the "Invoice" tab in the
            navigation bar and select "Create New Invoice".<br />
            <strong>Step 02</strong> Enter Invoice Details: Input invoice date, due date, invoice number (The quotation number will be automatically generated.), and select an existing business partner or create a new one by clicking <strong>"Create Business Partner"</strong><br />
            <strong>Step 03</strong> Add Items to the Quote: Enter item details like Item Code, Description, Quantity,
            and Pricing.
            <br />
            <ul>
              <li>For existing items, select them from the list.</li>
              <li>To add new items, click <strong>"Add New Item"</strong>.</li>
              <li>For services, directly input a description, price, and tax in the table.</li>
              <li>Specify the item code, description, quantity, and pricing details as needed.</li>
           </ul>
            <strong>Step 04</strong> Review and Save: Verify the details and save the invoice.<br />
          </div>
          <br />
          <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
          <a href="https://www.youtube.com/watch?v=heiDxWmEwDM" target="_blank" class="btn btn-primary mt-2">
              Watch on YouTube
          </a>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingTwo">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse41" aria-expanded="false" aria-controls="collapseOne">
            Managing the Invoice List
          </button>
        </h2>
        <div id="collapse41" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>The Invoice List provides users with an overview of all existing invoices, allowing for efficient
              management and review. Users can view, search, filter, and download invoices.</strong><br />
          </div>
          <br />
          <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
          <a href="https://www.youtube.com/watch?v=RSdaSiWuqEw" target="_blank" class="btn btn-primary mt-2">
              Watch on YouTube
          </a>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingTwo">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse42" aria-expanded="false" aria-controls="collapseOne">
            Reviewing an Invoice
          </button>
        </h2>
        <div id="collapse42" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Access the invoice list and review the details. Take appropriate action such as sending the invoice
              to the customer or marking it as paid.</strong><br />
          </div>
          <br />
          <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
          <a href="" target="_blank" class="btn btn-primary mt-2">
              Watch on YouTube
          </a>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingTwo">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse43" aria-expanded="false" aria-controls="collapseOne">
            Updating an Invoice
          </button>
        </h2>
        <div id="collapse43" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>To update an invoice, locate the invoice from the list, make necessary changes, and save the updated
              invoice.</strong><br />
          </div>
          <br />
          <strong>Watch Video Guide:</strong> For a detailed walkthrough, watch the video tutorial below:<br />
          <a href="https://www.youtube.com/watch?v=VnduSlT-3cw" target="_blank" class="btn btn-primary mt-2">
              Watch on YouTube
          </a>
        </div>
      </div>



      <div class="accordion-item">
        <h2 class="accordion-header" id="headingTwo">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse44" aria-expanded="false" aria-controls="collapseOne">
            Common Errors in Invoices
          </button>
        </h2>
        <div id="collapse44" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <strong>Issue:</strong> The system cannot process a discount without specifying a quantity.<br />
            <strong>Error Message:</strong> "Zero Quantity and Discount"<br />
            <strong>Resolution:</strong> Enter a valid quantity to enable discount calculation.
          </div>
        </div>
      </div>

    </div>
  </div>
</div>