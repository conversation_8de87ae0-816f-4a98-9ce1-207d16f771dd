/* Center modal and control width */
.custom-modal {
  max-width: 1000px;
}

/* Modal content spacing and border styling */
.modal-content {
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Modal Header */
.modal-header {
  background-color: #007bff;
  color: black;
  font-size: 1.5rem;
  font-weight: bold;
  padding: 15px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

/* Close button styling */
.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem; 
  color: black; 
  outline: none;
  display: flex;
  align-items: center;
  margin-right: -8px;
}

.btn-close:hover {
  color: #4262FF;
}

/* Spinner container */
.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

/* Modal body spacing */
.modal-body {
  padding: 20px;
  text-align: center;
}

/* IFrame container */
.iframe-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 10px;
}

/* IFrame styling */
iframe {
  width: 100%;
  height: 700px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-modal {
    max-width: 95%;
  }

  iframe {
    height: 500px;
  }
}
