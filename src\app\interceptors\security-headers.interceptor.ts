import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class SecurityHeadersInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          event = event.clone({
            headers: event.headers
              .set('Content-Security-Policy', "default-src 'self'; script-src 'self'; style-src 'self'; img-src 'self' data:; object-src 'none'; frame-ancestors 'none'; form-action 'self';")
              .set('X-Content-Type-Options', 'nosniff')
              .set('X-Frame-Options', 'DENY')
              .set('Referrer-Policy', 'strict-origin-when-cross-origin')
              .set('Permissions-Policy', "geolocation=(), microphone=(), camera=()") // Restrict APIs
              .set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload') // HSTS
              .set('X-Powered-By', '') // Remove X-Powered-By header
              .set('Server', '') // Hide server type
          });
        }
      })
    );
  }
}
