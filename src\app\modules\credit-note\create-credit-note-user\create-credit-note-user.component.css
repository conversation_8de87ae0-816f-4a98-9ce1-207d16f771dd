/* General Styles */
body {
  font-family: Arial, sans-serif;
  background-color: transparent;
  margin: 0;
  padding: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: transparent;
}

/* Header Styles */
.header {
  display: flex;
  background-color: transparent;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: none;
  border: none;
  gap: 5px;
}

.header h3 {
  flex: 1;
  margin-bottom: 0;
  font-family: Inter;
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

/* Border Container */
.bd {
  border: 2px solid #cec9c980;
  border-radius: 12px;
}

/* Form Section Styles */
.form-section,
.form-section_2 {
  display: flex;
  font-family: Arial, sans-serif;
  flex-direction: column;
  padding: 20px;
  background-color: #f7f7f7;
}

.form-row {
  display: flex;
  gap: 32px;
  padding-right: 20px;
  margin-bottom: 5px;
}

#creditNoteNumber{
  background-color: #fff;
}

.form-group,
.form-group_2 {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.form-group_2{
  margin-left: 50%;
}

.form-group label,
.form-group_2 label {
  display: block;
  margin-bottom: 5px;
  width: 20%;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group_2 input,
.form-group_2 textarea {
  width: 40%;
  padding: 8px 10px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  font-size: 14px;
}

.form-group_2 label {
  width: 30%;
}

.form-group_2 input,
.form-group_2 textarea {
  width: 70%;
}

/* Input and Textarea Styles */
.form-control,
.form-control1 {
  border-radius: 5px;
  border: 1px solid #ced4da;
  background-color: #fff;
  padding: 10px;
}

.form-control {
  text-align: right;
}

/* Search Bar Styles */
.search-bar {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-left: 20px;
  margin-bottom: 10px;
  margin-top: 10px;
  align-items: center;
  position: relative;
}

/* Button Styles */
.btn-primary,
.add_invoice {
  background-color: #4262ff;
  color: white;
  font-weight: bold;
  padding: 5px 40px;
  cursor: pointer;
  /* border-radius: 25px; */
  border: none;
  margin-left: 10px;
  font-size: 17px;
}

.btn-primary:hover,
.add_invoice:hover {
  background: #4262ff;
}

.btn-secondary {
  background: transparent;
  color: #4262ff;
  border: 2px solid #4262ff;
  padding: 5px 40px;
  margin-right: 10px;
  cursor: pointer;
  border-radius: 25px;
  font-weight: bold;
  font-size: 17px;
}

.btn-secondary:hover {
  background-color: #4262ff;
  color: white;
}

/* Table Styles */
.table-section {
  background-color: transparent;
  overflow: hidden;
  margin-bottom: 20px;
  padding-left: 20px;
  padding-right: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: linear-gradient(
    90deg,
    rgba(66, 98, 255, 0.06) 0%,
    rgba(63, 20, 153, 0.06) 100%
  );
  color: black;
  text-align: left;
  padding: 12px;
  font-weight: normal;
}

td {
  padding: 12px;
  border-bottom: 1px solid #ddd;
  background-color: white;
  vertical-align: middle;
}

/* Responsive Table */
.table-responsive {
  overflow-x: auto;
}

.btn {
  padding: 8px 16px;
  /* border: none; */
  cursor: pointer;
  font-size: 14px;
  text-transform: capitalize;
}

.new-item {
  background-color: #4262ff;
  color: white;
}

.new-item:hover {
  background-color: #512ca2;
}

@media (max-width: 390px) {
  .form-group{
    display: flex;
    flex-direction: column;
  }
}
/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .table-responsive {
    overflow-x: scroll;
  }

  .table {
    width: 100%;
    min-width: 800px;
  }

  .form-group,
  .form-group_2 {
    display: flex;
    flex-direction: column;
  }
  .form-group_2 {
    margin-left: 0;
  }

  .form-group label,
  .form-group_2 label {
    width: 100%;
  }

  .form-group input,
  .form-group select,
  .form-group_2 input,
  .form-group_2 textarea {
    width: 100%;
  }

  .form-row {
    flex-direction: column;
    gap: 10px;
  }

  .form-group_2 {
    flex-direction: column;
  }

  .header h3 {
    font-size: 24px;
  }

  .btn-primary,
  .btn-secondary,
  .add_invoice {
    padding: 5px 20px;
    font-size: 14px;
  }
  
}