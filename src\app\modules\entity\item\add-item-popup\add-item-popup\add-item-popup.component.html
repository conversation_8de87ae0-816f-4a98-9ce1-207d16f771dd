<!-- Item Popup -->
<div class="modal fade" id="itemPopupModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" style="max-width: 740px;">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="simpleModalLabel">Add New Item</h5>
        <div class="icon-group ms-auto">
          <i class="bi bi-x-circle close-icon" #closeItemPopUp data-bs-dismiss="modal" aria-label="Close"
            title="Close"></i>
        </div>
      </div>

      <div class="modal-body">

        <form #addItemForm="ngForm" name="addItemForm" (ngSubmit)="addItemForm.form.valid && onSubmit(addItemForm)"
          novalidate="feedback-form" (keydown)="preventSubmit($event)">
          <!-- Form Group: Item Name -->
          <div class="form-group">
            <label for="itemName">Item Name <span style="color: red;">*</span></label>
            <input type="text" id="itemName" name="itemName" [(ngModel)]="newItem.itemName" required />
            <div *ngIf="addItemForm.submitted && addItemForm.controls['itemName'].invalid" class="text-danger">
              <div *ngIf="addItemForm.controls['itemName'].errors?.['required']">Item Name is required.</div>
            </div>
          </div>

          <!-- Form Group: Description -->
          <div class="form-group">
            <label for="description">Description<span style="color: red;">*</span></label>
            <input type="text" id="description" name="description" [(ngModel)]="newItem.description" required />
            <div *ngIf="addItemForm.submitted && addItemForm.controls['description'].invalid" class="text-danger">
              <div *ngIf="addItemForm.controls['description'].errors?.['required']">Description is required.</div>
            </div>
          </div>
          
          <!-- Form Group: Item Code 
          <div class="form-group">
            <label for="itemCode">Item Code<span style="color: red;">*</span></label>
            <input type="text" id="itemCode" name="itemCode" [(ngModel)]="newItem.itemCode" required
              (keyup)="checkItemCodeExistenceKeyup()" />
            <div *ngIf="addItemForm.submitted && addItemForm.controls['itemCode'].invalid" class="text-danger">
              <div *ngIf="addItemForm.controls['itemCode'].errors?.['required']">Item Code is required.</div>
            </div>
          
            <div *ngIf="itemCodeExists" class="text-danger">This item code already exists.</div>
          </div>-->

          <!-- Form Group: Unit Price -->
          <div class="form-group">
            <label for="unitPrice">Unit Price (Excluding GST)<span style="color: red;">*</span></label>
            <input type="number"
             id="unitPrice"
             name="unitPrice" 
             [(ngModel)]="newItem.unitPrice" 
              min="0.01"
              step="0.01"
             required />

            <div *ngIf="addItemForm.submitted && addItemForm.controls['unitPrice'].invalid" class="text-danger">
              <div *ngIf="addItemForm.controls['unitPrice'].errors?.['required']">Unit Price is required.</div>
              <div *ngIf="addItemForm.controls['unitPrice'].errors?.['min']">Unit price cannot be zero or negative.</div>
            </div>
          </div>

          <div class="form-group" *ngIf="showTaxApplicabilityDropdown">
            <label for="taxApplicability">GST Applicability</label>
            <select class="form-select" id="taxApplicability" [(ngModel)]="newItem.taxApplicability"
              name="taxApplicability" required>
              <option value="" disabled selected>Select</option>
              <option value="yes">Yes</option>
              <option value="no">No</option>
            </select>
            <div *ngIf="addItemForm.submitted && addItemForm.controls['taxApplicability'].invalid" class="text-danger">
              <div *ngIf="addItemForm.controls['taxApplicability'].errors?.['required']">GST Applicable is required.
              </div>
            </div>
          </div>

          <!-- Footer Buttons -->
          <div class="popup-footer">
            <div class="left-section">
              <a  *ngIf="showUpdateLink" href="/item" class="update-link">Update Item</a>
            </div>
            <div class="right-section">
              <button type="button" class="cancel-btn" #closeItemPopUp data-bs-dismiss="modal">Cancel</button>
              <button type="submit" class="add-btn">Add</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>