<app-custom-header></app-custom-header>

<body>
  <div class="form-container">
    <div>
      <h2>Ledger Chimp Partner Registration</h2>
      <form [formGroup]="userForm" (ngSubmit)="onSubmitRegister()">
        <div class="user-name">
          <div class="form-group">
            <label for="first-name">First Name</label>
            <input
              type="text"
              id="first-name"
              formControlName="firstName"
              [(ngModel)]="user.firstName"
              required
            />
            <div
              *ngIf="
                userForm.get('firstName')?.invalid &&
                (userForm.get('firstName')?.dirty ||
                  userForm.get('firstName')?.touched)
              "
            >
              <small
                class="text-danger"
                *ngIf="userForm.get('firstName')?.errors?.['required']"
                >First Name is required.</small
              >
            </div>
          </div>
          <div class="form-group">
            <label for="last-name">Last Name</label>
            <input
              type="text"
              id="last-name"
              formControlName="lastName"
              [(ngModel)]="user.lastName"
              required
            />
            <div
              *ngIf="
                userForm.get('lastName')?.invalid &&
                (userForm.get('lastName')?.dirty ||
                  userForm.get('lastName')?.touched)
              "
            >
              <small
                class="text-danger"
                *ngIf="userForm.get('lastName')?.errors?.['required']"
                >Last Name is required.</small
              >
            </div>
          </div>
        </div>
        <div class="form-group">
          <label for="email">Email</label>
          <input
            type="email"
            id="email"
            formControlName="email"
            [(ngModel)]="user.username"
            (keyup)="checkUser()"
            required
            readonly
          />
          <div
            *ngIf="
              userForm.get('email')?.invalid &&
              (userForm.get('email')?.dirty || userForm.get('email')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="userForm.get('email')?.errors?.['required']"
            >
              Email is required.
            </small>
            <small
              class="text-danger"
              *ngIf="userForm.get('email')?.errors?.['customEmail']"
            >
              Invalid email format.
            </small>
          </div>
          <small *ngIf="isUsernameExists" class="text-danger">
            {{ usernameExistsMessage }}
          </small>
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <div class="input-group d-flex align-items-center">
            <input
              [type]="isPasswordVisible ? 'text' : 'password'"
              id="password"
              formControlName="password"
              [(ngModel)]="user.password"
              style="flex: 1;"
              required
            />
            <button
              type="button"
              class="btn btn-outline-secondary input-group-text d-flex align-items-center"
              (click)="isPasswordVisible = !isPasswordVisible"
              tabindex="-1"
              style="padding: 1; width: 40px; height: 48px;"
            >
              <i [classList]="isPasswordVisible ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
            </button>
          </div>
          <div
            *ngIf="
              userForm.get('password')?.invalid &&
              (userForm.get('password')?.dirty ||
                userForm.get('password')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="userForm.get('password')?.errors?.['required']"
              >Password is required.</small
            >
            <small
              class="text-danger"
              *ngIf="userForm.get('password')?.errors?.['pattern']"
              >Password must be at least 8 characters long and include an
              uppercase letter, a lowercase letter, a number, and a special
              character.</small
            >
          </div>
        </div>

        <div class="form-group">
          <label for="re-password">Re-enter Password</label>
          <div class="input-group d-flex align-items-center">
            <input
              [type]="isPasswordVisible ? 'text' : 'password'"
              id="re-password"
              formControlName="rePassword"
              style="flex: 1;"
              required
            />
            <button
              type="button"
              class="btn btn-outline-secondary input-group-text d-flex align-items-center"
              (click)="isPasswordVisible = !isPasswordVisible"
              tabindex="-1"
              style="padding: 1; width: 40px; height: 48px;"
            >
              <i [class]="isPasswordVisible ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
            </button>
          </div>
          <div
            *ngIf="
              userForm.hasError('mismatch') &&
              userForm.get('rePassword')?.touched
            "
          >
            <small class="text-danger"> Passwords do not match. </small>
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="invite-button" [disabled]="isSubmitting">
            <span *ngIf="isSubmitting">Registering...</span>
            <span *ngIf="!isSubmitting">Register</span>
          </button>

        </div>
      </form>
    </div>
  </div>
</body>
