<app-sales-navigation></app-sales-navigation>

<div class="container">
  <h1>Dashboard</h1>
  <!-- <div class="card-wrapper">
    <div class="card read-only-card">
      <h3>Bank</h3>
      <div class="divider"></div>
      <table class="table-responsive">
        <tbody>
          <tr>
            <td>Business Bank Account</td>
            <td>Balance in Bank</td>
          </tr>
          <tr>
            <td><strong>303000 - 1518656</strong></td>
            <td style="color: #4262ff;">$3000</td>
          </tr>
          <br>
          <tr>
            <td></td>
            <td>Balance in Ledger Chimp</td>
          </tr>
          <tr>
            <td></td>
            <td style="color: #4262ff;">$3000</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="card read-only-card">
      <h3>GST Summary</h3>
      <div class="divider"></div>
      <table class="table-responsive">
        <tbody>
          <tr>
            <td>GST Payable</td>
            <td>Period to Date</td>
          </tr>
          <tr>
            <td>Claimable</td>
            <td></td>
          </tr>
          <br>
          <tr>
            <td><strong>Total GST Payable</strong></td>
          </tr>
          <tr>
            <td style="color: #4262ff;">$3000.00</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div> -->

  <div class="card-wrapper">
    <div class="card">
      <h3>Invoices Owed To</h3>
      <div class="divider"></div>
      <table class="table-responsive">
        <tbody>
          <tr>
            <td>{{ invoiceData.awaiting.count }}</td>
            <td>Awaiting payment</td>
            <td style="text-align: right;">{{ invoiceData.awaiting.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.overdue.count }}</td>
            <td>Overdue</td>
            <td style="text-align: right;">{{ invoiceData.overdue.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.paid.count }}</td>
            <td>Paid invoices</td>
            <td style="text-align: right;">{{ invoiceData.paid.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.pending.count }}</td>
            <td>Pending</td>
            <td style="text-align: right;">{{ invoiceData.pending.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.revised.count }}</td>
            <td>Revised</td>
            <td style="text-align: right;">{{ invoiceData.revised.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.canceled.count }}</td>
            <td>Canceled</td>
            <td style="text-align: right;">{{ invoiceData.canceled.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ invoiceData.sent.count }}</td>
            <td>Sent</td>
            <td style="text-align: right;">{{ invoiceData.sent.amount | number: '1.2-2' }}</td>
          </tr>
          
        </tbody>
      </table>

  
    <div ></div>
    <!--<table class="table-responsive">
        <h3 class="mt-3">Invoices Owed To</h3>
    <thead>
        <tr>
        <th>Current</th>
        <th>30 Days +</th>
        <th>90 Days +</th>
        <th>120 Days +</th>
        </tr>
    </thead>
    <tbody>
        <tr>
        <td>$ {{ agedDebtors.current | number: '1.0-2' }}</td>
        <td>$ {{ agedDebtors.days30 | number: '1.0-2' }}</td>
        <td>$ {{ agedDebtors.days90 | number: '1.0-2' }}</td>
        <td>$ {{ agedDebtors.days120 | number: '1.0-2' }}</td>
        </tr>
    </tbody>
    </table>-->

    </div>

    <div class="card">
      <h3>Quotes</h3>
      <div class="divider"></div>
      <table class="table-responsive">
        <tbody>
          <tr>
            <td>{{ quoteData.pending.count }}</td>
            <td>Pending</td>
            <td style="text-align: right;">{{ quoteData.pending.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ quoteData.sent.count }}</td>
            <td>Sent</td>
            <td style="text-align: right;">{{ quoteData.sent.amount | number: '1.2-2' }}</td>
          </tr>
          <tr>
            <td>{{ quoteData.revised.count }}</td>
            <td>Revised</td>
            <td style="text-align: right;">{{ quoteData.revised.amount | number: '1.2-2' }}</td>
          </tr>
          <!--<tr>
            <td>{{ quoteData.accepted.count }}</td>
            <td>Accepted</td>
            <td style="text-align: right;">{{ quoteData.accepted.amount | number: '1.2-2' }}</td>
          </tr>-->
          <tr>
            <td>{{ quoteData.expired.count }}</td>
            <td>Expired</td>
            <td style="text-align: right;">{{ quoteData.expired.amount | number: '1.2-2' }}</td>
          </tr>
          <!--<tr>
            <td>{{ quoteData.declined.count }}</td>
            <td>Declined</td>
            <td style="text-align: right;">{{ quoteData.declined.amount | number: '1.2-2' }}</td>
          </tr>-->
          <tr>
            <td>{{ quoteData.toInvoice.count }}</td>
            <td>To Invoice</td>
            <td style="text-align: right;">{{ quoteData.toInvoice.amount | number: '1.2-2' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- <div class="card">
    <h3>Income & Expense Tracker</h3>
    <div class="divider"></div>
    <table class="table-responsive">
      <thead>
        <tr>
          <th></th>
          <th>Year to Date</th>
          <th>Quarter to Date</th>
          <th>Month to Date</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Income</td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
        <tr>
          <td>Top 6 Expenses</td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
        <tr>
          <td>Top 6 Expenses</td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
        <tr>
          <td>Top 6 Expenses</td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
        <tr>
          <td>Top 6 Expenses</td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
        <tr>
          <td>Top 6 Expenses</td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
        <tr>
          <td>Top 6 Expenses</td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div> -->
</div>
