import { Component, OnInit } from '@angular/core';
import { Disable, MultiplePayCalendar, PayCalendar, PayCycle, PayPeriod } from '../payroll-settings/payroll-setting';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { PayCalendarService } from '../payroll-settings/services/pay-calendar.service';
import { EmployeeService } from '../payroll-settings/services/employee.service';
import * as bootstrap from 'bootstrap';
import { EmploymentData } from '../payroll-settings/empolyee/employee';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-payroll-calendar',
  templateUrl: './payroll-calendar.component.html',
  styleUrls: ['./payroll-calendar.component.css']
})
export class PayrollCalendarComponent implements OnInit {
disableCalendar(arg0: number) {
throw new Error('Method not implemented.');
}

  payCalendars: PayCalendar[] = []; 
  disable: Disable = new Disable;
  page: number = 1;
  multiplePayCalendarData: MultiplePayCalendar = new MultiplePayCalendar();
  payCycles: PayCycle[] = [];
  employmentData: EmploymentData[] = [];
  calendarName: string = '';
  payCycleName: string = '';
  days: number[] = Array.from({ length: 31 }, (_, i) => i + 1);
  fromDay: number | null = null;
  toDay: number | null = null;
  payDay: number | null = null;
  selectedOption: string = 'fromTo';
  isDisabled: boolean = false;
  buttonStates: Map<number, boolean> = new Map<number, boolean>();

  constructor(private payCalendarService: PayCalendarService, private employeeService: EmployeeService, private router: Router, private datePipe: DatePipe) {}

  ngOnInit(): void {
    this.getAllPayCalendars()
    this.getAllPayCycles();
    
  }

  clearDateInputs() {
    this.multiplePayCalendarData.fromDay = null;
    this.multiplePayCalendarData.payDay = null;
  }

  getAllPayCalendars(): void {
    const entityId = +(localStorage.getItem('entityId') || '0');
    this.payCalendarService.getAllPayCalendars(entityId).subscribe({
      next: (data: PayCalendar[]) => {
        this.payCalendars = data.reverse();;
        this.loadCalendarStatuses();
      },
      error: (err) => {
        console.error('Error fetching payCalendars:', err);
      }
    });
  }

  getAllPayCycles(): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.payCalendarService.getAllPayCycles(entityId).subscribe({
      next: (data: PayCycle[]) => {
        this.payCycles = data;
      },
      error: (err) => {
        console.error('Error fetching PayCycles:', err);
      }
    });
  }

  updateCalendar(id: number): void {
    this.router.navigate(['/update-calendar', id]);
  }

  deleteCalendar(id: number) {
        this.payCalendarService.deletePayCalendar(id).subscribe(
          (_response: any) => {
            Swal.fire({
              title: 'Success!',
              text: 'Pay calendar delete successfully!',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            }).then(() => {
              this.getAllPayCalendars();
            });
          },
          (_error: any) => {
            Swal.fire({
              title: 'Error!',
              text: 'Failed to delete Pay Calendar. Please try again.',
              icon: 'error',
              confirmButtonText: 'OK',
              confirmButtonColor: '#be0032',
            });
          }
        );
      }

  disabledCalendar(id: number) {
    const updatedStatus = { status: this.disable.status };
        this.payCalendarService.disabledCalendar(id, updatedStatus).subscribe(
          (_response: any) => {
            Swal.fire({
              title: 'Success!',
              text: this.disable.status
                          ? 'Pay calendar disable successfully!'
                          : 'Pay calendar enable successfully!',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            }).then(() => {
              this.getAllPayCalendars();
            });
          },
          (_error: any) => {
            this.disable.status = !this.disable.status;
            Swal.fire({
              title: 'Error!',
              text: 'Failed to disable Pay Calendar. Please try again.',
              icon: 'error',
              confirmButtonText: 'OK',
              confirmButtonColor: '#be0032',
            });
          }
        );
      }
      
      loadCalendarStatuses(): void {
        const entityId = +(localStorage.getItem('entityId') || '0');
    
        this.payCalendars.forEach((calendar) => {
          this.employeeService.previewEmployeeById(calendar.payCalendarId, entityId).subscribe({
            next: (response: EmploymentData[]) => {
             
              const isDisabled = response.length > 0 && !!response[0].payCalendar ;
              this.buttonStates.set(calendar.payCalendarId, isDisabled);
            },
            error: (error: any) => {
              console.error(`Error fetching data for calendar ID ${calendar.payCalendarId}:`, error);
              this.buttonStates.set(calendar.payCalendarId, false); 
            }
          });
        });
      }

      getButtonState(calendarId: number): boolean {
        return this.buttonStates.get(calendarId) ?? false; 
      }

      previewEmployees(id: number) {
        const entityId = +((localStorage.getItem('entityId')) + "");
      
        this.employeeService.previewEmployeeById(id, entityId).subscribe(
          (response: EmploymentData[]) => {
            this.employmentData = response.map((data) => {
              return {
                ...data, // Spread all existing properties
                personal: data.employee ? { ...data.employee } : null, // Map employee to personal
              };
            });
      
            // Set modal header if data is available
            if (this.employmentData.length > 0 && this.employmentData[0].payCalendar) {
              this.calendarName = this.employmentData[0].payCalendar.calendarName || '';
              this.payCycleName = this.employmentData[0].payCalendar.payCycleName || '';
            }
      
            // Open the modal
            const modalElement = document.getElementById('employeePreviewModal');
            if (modalElement) {
              const modalInstance = new bootstrap.Modal(modalElement);
              modalInstance.show();
            }
          },
          (_error: any) => {
            console.error('Error loading employee data.');
          }
        );
      }
      
      // formatPayStartDate(): string {
      //   let payStartDate = this.multiplePayCalendarData.payStartDate;

      //   if (payStartDate && payStartDate.length === 2) {
      //     return `${payStartDate}-00-00`;
      //   }

      //   return this.datePipe.transform(payStartDate, 'dd-MM-yyyy')!;
      // }

      // formatNextPayDate(): string {
      //   let nextPayDate = this.multiplePayCalendarData.nextPayDate;
        
      //   if (nextPayDate && nextPayDate.length === 2) {
      //     return `${nextPayDate}-00-00`;
      //   }
      //   return this.datePipe.transform(nextPayDate, 'dd-MM-yyyy')!;
      // }

      addMultiplePayCalendar(): void {
          const entityId = +((localStorage.getItem('entityId')) + "");
          const userId = +((localStorage.getItem('userid')) + "");
          const date = new Date().toISOString(); 
          const formattedPayStartDate = this.datePipe.transform(this.multiplePayCalendarData.payStartDate, 'dd-MM-yyyy');
          const formattedNextPayDate = this.datePipe.transform(this.multiplePayCalendarData.nextPayDate, 'dd-MM-yyyy');


          const payload = {
            ...this.multiplePayCalendarData,
            entityId,
            userId,
            date,
            payStartDate: formattedPayStartDate,
            nextPayDate: formattedNextPayDate,
          };
        
          this.payCalendarService.saveMultiplePayCalendar(payload).subscribe(
            (_response: any) => {
              Swal.fire({
                title: 'Success!',
                text: 'Pay calendar Data added successfully!',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
              }).then(() => {
                 window.location.assign("/payroll-calendar");
              });
            },
            (_error: any) => {
              Swal.fire({
                title: 'Error!',
                text: 'Failed to save Pay Calendar Data. Please try again.',
                icon: 'error',
                confirmButtonText: 'OK',
                confirmButtonColor: '#be0032',
              });
            }
          );
        }

        onPayCycleChange(): void {
          const selectedCycle = this.payCycles.find(
            (cycle) => cycle.cycleName === this.multiplePayCalendarData.payCycle?.cycleName
          );
      
          if (selectedCycle) {
            this.multiplePayCalendarData.payCycle = selectedCycle;
          }
      
          this.updateNextPayDate();
        }

        navigateToPayrollSettings(): void {
          window.location.assign("/payroll-calendar");
        }

        onPayStartDateChange(): void {
          this.updateNextPayDate();
        }

        validateDateRange(): void {
          const fromDay = this.multiplePayCalendarData.fromDay;
          const payDay = this.multiplePayCalendarData.payDay;
        
          if (fromDay != null && payDay != null && (payDay - fromDay) > 1) {
            Swal.fire({
              title: 'Warning!',
              text: 'Invalid date range selected, Please select again',
              icon: 'warning',
              confirmButtonText: 'OK',
              confirmButtonColor: '#ff7e5f',
            }).then(() => {
              this.multiplePayCalendarData.payDay = '';
           });
          }
        }
        

        private updateNextPayDate(): void {
          const selectedCycle = this.multiplePayCalendarData.payCycle;
          if (selectedCycle && this.multiplePayCalendarData.payStartDate) {
            const payStartDate = new Date(this.multiplePayCalendarData.payStartDate);
            const nextPayDate = new Date(
              payStartDate.setDate(payStartDate.getDate() + selectedCycle.numberOfDate)
            );
            this.multiplePayCalendarData.nextPayDate = nextPayDate.toISOString().split('T')[0]; 
          }
        }

}
