* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Inter", sans-serif;
  background-color: #1a1a1a;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  color: #fff;
  background-image: url("/assets/images/MacBook Air - 1.png");
  background-size: cover;
  background-position: center;
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 20px;
}

.selection {
  margin-top: 20px;
  background: #fff;
  border-radius: 20px;
  padding: 2rem;
  position: relative;
  width: 700px;
  text-align: center;
  margin-bottom: 150px;
  box-shadow: 0px 10px 30px rgba(66, 98, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

h1 {
  font-size: 2.5rem;
  color: #1a1a1a;
  font-weight: 700;
  margin-bottom: 3rem;
  text-transform: uppercase;
}

h2 {
  font-size: 1.4rem;
  color: #4c4c4c;
  font-weight: 600;
  margin-bottom: 0.3rem;
  background-color: #f9f9f9;
  padding: 10px 15px;
  border-radius: 10px;
}

.entity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.entity {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 15px;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.entity:hover {
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.2);
}

.entity-details {
  text-align: left;
}

.entity-details h3 {
  font-size: 1rem;
  color: #8e8e8e;
  font-weight: 600;
  margin-bottom: 0.5rem;
  padding: 0px 15px;
}

.select-entity-button {
  padding: 10px 20px;
  background-color: #4262ff;
  color: #fff;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.5rem;
  font-weight: bold;
  transition: background-color 0.3s ease, transform 0.3s ease;
  width: 45px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.select-entity-button:hover {
  background-color: #2a49e0;
  transform: translateY(-3px);
}

.select-entity-button:active {
  transform: translateY(0);
}


@media screen and (max-width: 600px) {
  .selection {
    width: 100%;
    padding: 1.5rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  button {
    width: 100%;
  }

  .entity {
    flex-direction: column;
    align-items: flex-start;
  }

  .entity h2 {
    margin-bottom: 10px;
  }
}


.no-entity {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 15px;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  margin-top: 20px;
}

.no-entity p {
  font-size: 1.2rem;
  color: #4c4c4c;
  margin-bottom: 20px;
  font-weight: 600;
}


@media screen and (max-width: 600px) {
  .no-entity {
    padding: 15px;
  }

  .no-entity p {
    font-size: 1rem;
  }

}

.request-entity-button {
  padding: 12px 25px;
  background-color: #4262ff;
  color: #fff;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  text-transform: uppercase;
  transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
}

.request-entity-button:hover {
  background-color: #2a49e0;
  transform: translateY(-3px);
  box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.2);
}

.request-entity-button:active {
  transform: translateY(0);
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.15);
}

.request-entity-button:focus {
  outline: none;
  box-shadow: 0px 0px 0px 3px rgba(66, 98, 255, 0.4);
}

@media screen and (max-width: 600px) {
  .request-entity-button {
    font-size: 0.9rem;
    padding: 10px 20px;
  }
}


