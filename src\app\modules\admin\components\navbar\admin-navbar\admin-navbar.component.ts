import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Entity, EntityTradingName } from 'src/app/modules/entity/entity';
import { EntityService } from 'src/app/modules/entity/entity.service';

@Component({
  selector: 'app-admin-navbar',
  templateUrl: './admin-navbar.component.html',
  styleUrls: ['./admin-navbar.component.css'],
})
export class AdminNavbarComponent implements OnInit {
  isDropdownOpen: boolean = false;
  isDropdownOpen1: boolean = false;
  entity: Entity = new Entity();
  entityTradingName: EntityTradingName = new EntityTradingName();
  entityId: number = 0;
  logoUrl: string | null = null;

  constructor(private router: Router, private entityService: EntityService) {}

  ngOnInit(): void {
    this.entityId = JSON.parse(localStorage.getItem('entityId') || '[]');
    this.getEntity();
    // this.getEntityTradingName();
  }

  private getEntity(): void {
    this.entityService.getBusinessEntityByIdCached(this.entityId).subscribe((data) => {
      this.entity = data;
      this.logoUrl = this.entity.logo ? `data:image/png;base64,${this.entity.logo}` : null;
    });
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
    if (this.isDropdownOpen) {
      this.isDropdownOpen1 = false; // Close Reports dropdown if Subscription is opened
    }
  }

  toggleDropdown1(): void {
    this.isDropdownOpen1 = !this.isDropdownOpen1;
    if (this.isDropdownOpen1) {
      this.isDropdownOpen = false; // Close Subscription dropdown if Reports is opened
    }
  }

  onStatementChange(event: any): void {
    const selectedStatement = event.target.value;
    if (selectedStatement === 'customer') {
      this.navigateCustomerStatementReport();
    } else if (selectedStatement === 'account') {
      this.navigateAccountsStatementReport();
    }
    this.isDropdownOpen1 = false;
  }

  navigateDashboard(): void {
    this.router.navigate(['/dashboard']);
  }

  navigateCountry(): void {
    this.router.navigate(['/country']);
  }

  navigateInvoice(): void {
    this.router.navigate(['/invoice']);
  }

  navigateCreditNote(): void {
    this.router.navigate(['/credit-note']);
  }

  navigateRecordPayment(): void {
    this.router.navigate(['/payment-receipt']);
  }

  navigateManageSubscription(): void {
    this.router.navigate(['/manage-subscription']);
  }

  navigateUserInvitation(): void {
    this.router.navigate(['/invite-user']);
    this.isDropdownOpen = false;
  }

  navigateUserRegistration(): void {
    this.router.navigate(['/user-registration']);
    this.isDropdownOpen = false;
  }

  navigateQuotationReport(): void {
    this.router.navigate(['/quotation-report']);
    this.isDropdownOpen1 = false;
  }

  navigateInvoiceReport(): void {
    this.router.navigate(['/invoice-report']);
    this.isDropdownOpen1 = false;
  }

  navigateCreditNoteReport(): void {
    this.router.navigate(['/credit-note-report']);
    this.isDropdownOpen1 = false;
  }

  navigatePaymentReport(): void {
    this.router.navigate(['/payment-receipt-report']);
    this.isDropdownOpen1 = false;
  }

  navigateCustomerStatementReport(): void {
    this.router.navigate(['/customer-statement']);
  }

  navigateAccountsStatementReport(): void {
    this.router.navigate(['/account-statement']);
  }

  navigateBusinessEntityEdit(): void {
    this.router.navigate(['/business-entity-edit']);
  }
}
