/* Global styles */
body {
  font-family: Arial, sans-serif;
  background-color: transparent;
  margin: 0;
  padding: 0;
}

.bill-container {
  width: 100%;
  max-width: 1200px; /* You can change this value to suit your desired width */
  margin: 0 auto;
  padding: 10px;
  background-color: transparent;
}

/* Heading styles */
.form-dataInput {
  display: flex;
  flex-direction: row;
}

.heading {
  display: flex;
  background-color: transparent;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: none;
  border: none;
  gap: 5px;
  /* Adds space between buttons */
}

.heading h3 {
  flex: 1;
  margin-bottom: 0;
  font-family: Inter;
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
  top: 264px;
}

.heading .transparent-button {
  top: 256px;
  border: 1px solid #4262ff;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  background-color: white;
  color: #4262ff;
  font-family: Inter;
  margin-left: 20px;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

.heading .transparent-button:hover {
  background-color: #4262ff;
  color: white;
}

/* Header styles */

.header {
  display: flex;
  background-color: transparent;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: none;
  border: none;
  gap: 5px;
  /* Adds space between buttons */
}

.header h3 {
  flex: 1;
  margin-bottom: 0;
  font-family: Inter;
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
  top: 264px;
}

.header .transparent-button {
  top: 256px;
  border: 1px solid #4262ff;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  background-color: white;
  color: #4262ff;
  font-family: Inter;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

.header .transparent-button:hover {
  background-color: #4262ff;
  color: white;
}

.bd {
  border: 2px solid #cec9c980;
  border-radius: 12px;
  margin: 0;
  width: 100%;
}
/* Form styles */

.form-section {
  display: flex;
  font-family: Arial, sans-serif;
  flex-direction: column;
  padding: 20px;
  background-color: #f7f7f7; /* If you want a solid background color */
}

.form-row {
  display: flex;
  gap: 32px;
  padding-right: 20px;
  margin-bottom: 5px;
}

.form-group {
  flex: 1;
}

.input-style {
  height: 49px;
  top: 656px;
  left: 111px;
  padding: 10px;
  font-size: 14px;
}

.create-customer-container {
  display: flex;
  justify-content: flex-end;
}

label {
  display: block;
  /* margin-bottom: 5px; */
  font-weight: bold;
  color: #333;
}

.create-customer-btn {
  float: right;
  color: #4a4ae2;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-style: italic;
  font-weight: bold;
}

#quotationNo {
  background-color: white;
  width: 100%;
  height: 40px;
  border-radius: 5px; /* Rounds the corners */
  border: 0.1px solid #c7c7c7;
}
#validityUntil {
  background-color: white;
  width: 100%;
  height: 95%;
  border-radius: 5px; /* Rounds the corners */
  border: 0.1px solid #c7c7c7;
}
#quotationDate {
  background-color: white;
  width: 100%;
  height: 95%;
  border-radius: 5px; /* Rounds the corners */
  border: 0.1px solid #c7c7c7;
}
/* #quotation,
            #quotationNo {
                margin-left: 25px;
            } */

#customer-add {
  width: 100%;
  height: 40px;
  border-radius: 5px; /* Rounds the corners */
  border: 0.1px solid #c7c7c7;
}
/* Customer and Search Item sections */

.customer-section,
.search-item-section {
  flex-basis: 50%;
}

/* Search bar styles */

.search-bar {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-left: 20px;
  margin-bottom: 20px;
  align-items: center;
  position: relative;
}
.reff {
  width: 100%;
  display: flex;
  flex-direction: row;
}

.search-bar input.input-style {
  flex-grow: 1;
  border: 1px solid #ffffff;
  border-radius: 8px;
  padding-right: 40px;
  height: 49px;
  /* Add padding to make space for the button */
}

.search-bar button.btn.btn-link {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.search-bar button.btn.btn-link1 {
  background: linear-gradient(180deg, #4262ff 0%, #283b99 100%);
  color: white;
  border-radius: 8px;
  opacity: 0px;
}

/* Table styles */

.table-section {
  background-color: transparent;
  overflow: hidden;
  margin-bottom: 20px;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: -20px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: linear-gradient(
    90deg,
    rgba(66, 98, 255, 0.06) 0%,
    rgba(63, 20, 153, 0.06) 100%
  );
  color: black;
  text-align: left;
  padding: 12px;
  font-weight: normal;
}

td {
  padding: 12px 5px;
  border-bottom: 1px solid #ddd;
  background-color: white;
  vertical-align: middle;
  margin-left: auto;
}

.notes-totals-container {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  padding-bottom: 20%;
}

.notes-totals-section {
  display: flex;
  flex-direction: row;
}

.notes-section,
.totals-section {
  flex: 1;
  width: 50%;
  background-color: transparent;
  border-radius: 8px;
  padding: 30px 10px;
  box-shadow: none;
  border: none;
  margin-left: 14px;
  margin-right: 14px;
  margin-bottom: 20px;
  /* height: 255px; */
  box-sizing: border-box;
}

#notes {
  border: 1px solid #e6e6e6;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  opacity: 1;
  resize: none;
}

.totals-row {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  margin-left: auto;
}

.totals-row1 {
  flex-grow: 1;
  width: 60%;
  margin-right: 10px;
  text-align: right;
}

.totals-row2 {
  width: 40%;
  text-align: right;
  padding: 5px 10px;
  border: 1px solid #cec9c980;
  border-radius: 10px;
  background-color: #d9d9d94d;
  box-sizing: border-box;
}
/* Utility classes */

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}
/* Icon button */

.icon-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 24px;
  color: #4a4ae2;
}

.update-link {
  font-size: 15px;
  font-family: Segoe UI, sans-serif;
  font-weight: 600;
  color: #4a4ae2;
  margin-top: 20px;
  margin-left: 10px;
  display: inline-block; /* Allow custom width */
  white-space: nowrap; /* Prevent wrapping to the next line */
  text-align: left;
}

.accounts-dropdown {
  border: 1px solid #c7c7c7;
  border-radius: 12px;
  font-weight: 400;
  font-size: 20px;
  color: black;
  height: 49px;
  width: 100%;
  padding-left: 10px;
  padding-right: -15px;
  /* min-width: 540px; */
  appearance: none; /* Hides default dropdown arrow */
  -webkit-appearance: none; /* Safari support */
  -moz-appearance: none; /* Firefox support */
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16"><path fill="gray" d="M7 10l5 5 5-5z"/></svg>')
    no-repeat right 10px center;
  background-size: 30px 30px; /* Adjust the size of the arrow */
}

.accounts-dropdown option:disabled {
  color: #5e5e5e;
}

.form-group {
  margin-bottom: 10px;
  text-align: left;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 25.41px;
  color: #444343;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: normal;
}

.form-group input {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  font-size: 14px;
}

.popup-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.transparent-button {
  top: 256px;
  border: 2px solid #4262ff;
  padding: 10px 40px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  background-color: white;
  color: #4262ff;
  font-family: Inter;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

.transparent-button:hover {
  background-color: #4262ff;
  color: white;
}

.cancel-btn {
  padding: 10px 70px;
  border-radius: 12px;
  cursor: pointer;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  background: #ffffff;
  color: #4262ff;
  border: 2px solid #4262ff;
}

.cancel-btn:hover {
  background: #4262ff;
  color: white;
}

.add-btn {
  padding: 10px 40px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  background-color: #4262ff;
  color: white;
}

.add-btn:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}
.btn-primarys {
  /* border-radius: 5px; */
  background: #4262ff;
  color: white;
  border: none;
  padding-inline: 10px;
  padding-block: 8px;
}

.btns {
  border-radius: 5px;
}
.form-check {
  display: inline-flex;
  align-items: center;
}

.form-check-input {
  margin-right: 5px;
  margin-left: 5px;
}

.form-check-label {
  margin-right: 10px;
}

#quotationNo {
  background-color: white;
}

.btn {
  padding: 8px 16px;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  text-transform: capitalize;
}

.form-check-tax {
  display: inline-flex;
  align-items: center;
}

.form-check-tax-input {
  margin-right: 5px;
}

.form-check-tax-label {
  margin-right: 2px;
}

.close-icon {
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.close-icon:hover {
  color: #000;
}

.table-responsive {
  overflow-x: auto;
}

.main-row-note {
  display: flex;
  flex-direction: row;
  width: 100%;
  /* justify-content: end; */
  /* background-color: #000000; */
}

/* General styles for the Notes and Totals Section */
.notes-totals-section {
  display: flex;
  width: 100%;
  /* margin-right: auto; */
  /* background-color: #c79191; */
  flex-direction: row;
  justify-content: space-between;
  /* align-items: flex-start; */
  gap: 20px;
}

/* Notes section (if re-enabled in future) */
.notes-section {
  flex: 1;
  /* min-width: 0; */
  /* margin-right: 20px; */
}

/* Totals section */
.totals-section {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  /* width: 250px; */
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
}

/* Row styles in totals */
.totals-row {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 16px;
  color: #333333;
}

/* Special styling for the total amount */
.totals-row strong {
  font-size: 18px;
  font-weight: bold;
}

/* Styling for labels in totals rows */
.totals-row1 {
  font-weight: 500;
}

/* Styling for values in totals rows */
.totals-row2 {
  font-weight: 600;
  text-align: right;
}

/* Footer Section Styles */
.footer-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  margin-left: 20px;
  flex-wrap: wrap;
  /* Allows wrapping of buttons */
  gap: 20px;
  /* Adds space between buttons */
}

/* Footer Button Section */
.footer-btn-section {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  /* Allows buttons to wrap when needed */
  width: 100%;
  /* Ensure full width for flexibility */
}

.footer-btn-section button {
  padding: 10px 20px;
  border-radius: 12px;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  cursor: pointer;
  margin-bottom: 10px;
}

.bill-file input[type="file"] {
  display: none;
}

.bi {
  font-size: 16px;
}

/* Customer Popup */

.customer-popup .popup-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  overflow-y: auto;
  padding-top: 20px;
}

.customer-popup .popup {
  background: white;
  width: 750px;
  max-height: 95vh;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 40px;
  position: relative;
  overflow-y: auto;
}

.customer-popup .class-name {
  display: flex;
  gap: 20px;
}

.img-preview {
  margin-left: 20px;
  margin-bottom: 10px;
}

.customer-popup h3 {
  text-align: left;
  color: #000000;
  font-family: Segoe UI, sans-serif;
  font-size: 20px;
  font-weight: 600;
  text-align: left;
  margin-bottom: 20px;
}

.customer-popup small {
  font-family: Segoe UI, sans-serif;
  font-size: 12px;
  font-weight: 300;
  text-align: left;
  color: #444343;
}

.customer-popup select {
  width: 100%;
  padding: 10px;
  border: 2px solid #c7c7c7;
  border-radius: 8px;
  box-sizing: border-box;
  font-size: 1em;
  font-family: Arial, sans-serif;
}

.cancel-btn1 {
  padding: 10px 60px;
  border-radius: 17px;
  cursor: pointer;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  background: #ffffff;
  color: #6822ff;
  border: 2px solid #4262ff;
  margin-top: 15px;
  margin-left: 220px;
  margin-right: 10px;
}
.cancel-btn1:hover {
  background: #4262ff;
  color: white;
}
.add-btn1 {
  padding: 10px 70px;
  border: none;
  border-radius: 17px;
  cursor: pointer;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  margin-top: 15px;
  margin-right: 32px;
}

.add-btn1:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

.update-link {
  font-size: 15px;
  font-family: Segoe UI, sans-serif;
  font-weight: 600;
  color: #4a4ae2;
  margin-top: 20px;
  margin-left: 10px;
  display: inline-block; /* Allow custom width */
  white-space: nowrap; /* Prevent wrapping to the next line */
  text-align: left;
}

input::file-selector-button {
  font-weight: bold;
  background: #4262ff;
  color: white;
  border: none;
  border-radius: 3px;
}

#checkBox {
  display: inline-block;
  vertical-align: middle;
  margin-left: 20px;
  /* Reduce the gap between checkbox and label */
}

#markas-1 {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0px;
  /* Reset any left margin/padding */
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-top: 4px solid #007bff; /* Blue color */
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

.border-danger {
  border: 2px solid red !important;
}

/* Large Screen (Default layout - three buttons in one row with fixed width) */
.btn-rows {
  width: 100%;
}

.gradient-btn {
  background: linear-gradient(to right, #512ca2, #4262ff);
  border: none; /* Remove border if desired */
  color: white; /* Ensure text/icon contrast */
  border-radius: 6px;
}

.gradient-btn:hover {
  background: linear-gradient(
    to right,
    #4262ff,
    #512ca2
  ); /* Optional hover effect */
  color: white;
}



.text-pending {
  color: #debe15;
  text-align: center;
}

.text-canceled {
  color: #dc3545;
  text-align: center;
  /* Danger (red) for Canceled */
}



.text-sent {
  color: #455cff;
  text-align: center;
  /* Info (cyan) for Sent */
}

.text-paid {
  color: #28a745;
  text-align: center;
  /* Success (green) for Paid */
}

.text-overdue{
  color: #FF6610;
  text-align: center;
}

.text-awaiting-payment {
  color: #17a2b8; /* Teal-ish green */
  text-align: center;
  /* Info-like green/blue for Awaiting Payment */
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 680px) {
  .reff {
    flex-direction: column;
    display: flex;
    width: 100%;
  }

  #supplierReferenceNumber {
    width: 100%;
  }
}

@media (min-width: 680px) {
  .form-rows {
    width: 48%;
  }
}

@media (max-width: 680px) {
  .heading h3 {
    flex: 1;
    margin-bottom: 0;
    font-family: Inter;
    font-size: 26px;
    font-weight: 700;
    text-align: left;
    color: #4262ff;
    top: 264px;
  }

  .form-row {
    display: flex;
    flex-direction: column;
    gap: 32px;
    padding-right: 20px;
    margin-bottom: 5px;
  }

  .form-group {
    margin-bottom: 10px;
    text-align: left;
    font-family: Segoe UI, sans-serif;
    font-size: 15px;
    font-weight: 600;
    line-height: 25.41px;
    color: #444343;
    display: flex;
    flex-direction: column;
  }

  .InputBoxes {
    flex-direction: column;
    display: flex;
  }

  .table-responsive {
    overflow-x: scroll;
  }

  .table {
    width: 100%;
    min-width: 800px;
    /* Minimum width to allow horizontal scroll */
  }

  .table-section {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* For smooth scrolling on iOS */
  }

  .notes-totals-section {
    width: 100%;
  }

  .popup-footer {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    margin-top: 20px;
    padding-inline: 20px;
  }

  .cancel-btn1 {
    padding: 10px 60px;
    border-radius: 17px;
    cursor: pointer;
    font-family: Segoe UI, sans-serif;
    font-size: 15px;
    font-weight: 700;
    text-align: center;
    background: #ffffff;
    color: #6822ff;
    border: 2px solid #4262ff;
    margin-top: 15px;
    margin-left: 0px;
    margin-right: 10px;
  }

  .add-btn1 {
    padding: 10px 70px;
    border: none;
    border-radius: 17px;
    cursor: pointer;
    font-family: Segoe UI, sans-serif;
    font-size: 15px;
    font-weight: 700;
    text-align: center;
    background: linear-gradient(to right, #4262ff, #512ca2);
    color: white;
    margin-top: 15px;
    margin-right: 0px;
  }
}

@media screen and (max-width: 558px) {
  .heading {
    overflow-x: auto;
    /* Enable horizontal scrolling */
    white-space: nowrap;
    /* Prevent content from wrapping */
    -webkit-overflow-scrolling: touch;
    /* For smooth scrolling on iOS devices */
  }

  .button-group {
    flex-wrap: nowrap;
    /* Ensure buttons don't wrap */
  }
}

@media screen and (max-width: 685px) {
  .footer-section {
    flex-direction: row;
    align-items: flex-start;
    margin-bottom: 20px;
    /* Adjust as needed */
  }

  .footer-btn-section {
    margin-bottom: 10px;
    justify-content: space-between;
  }

  .footer-btn-section button {
    margin-right: 10px;
    width: 100%;
    /* Make buttons full-width on small screens */
    margin-bottom: 10px;
    /* Add space between buttons */
  }

  .bill-file {
    margin-bottom: 15px;
    /* Add margin between file upload and buttons */
  }

  .transparent-button {
    top: 256px;
    border: 1px solid #4262ff;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 12px;
    font-weight: bold;
    background-color: white;
    color: #4262ff;
    font-family: Inter;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    /* Vertically center */
    align-items: center;
    /* Horizontally center */
    height: 100%;
    /* Ensure it has a height to center within */
  }

  .notes-totals-section {
    display: flex;
    flex-direction: column;
  }

  .totals-row {
    display: flex;
    width: 100%;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin-left: auto;
  }

  .totals-row1 {
    flex-grow: 1;
    width: 100%;
    margin-right: 10px;
    text-align: right;
  }

  .totals-row2 {
    width: 100%;
    text-align: right;
    padding: 5px 10px;
    border: 1px solid #cec9c980;
    border-radius: 10px;
    background-color: #d9d9d94d;
    box-sizing: border-box;
  }

  .notes-section,
  .totals-section {
    flex: 1;
    width: 100%;
    background-color: transparent;
    border-radius: 8px;
    padding: 30px 10px;
    box-shadow: none;
    border: none;
    margin-left: 14px;
    margin-right: 14px;
    padding-left: 40px;
    margin-bottom: 20px;
    height: 255px;
    box-sizing: border-box;
  }
}

@media (min-width: 1024px) {
  .footer-btn-section button {
    width: 180px;
    /* Fixed width for buttons on larger screens */
  }

  .footer-section {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }

  .btn-rows {
    width: 100%;
    justify-content: space-between;
    display: flex;
    flex-direction: row;
  }

  .footer-btn-section {
    justify-content: space-between;
  }

  .footer-Btn {
    display: flex;
    width: 100%;
    justify-content: flex-end;
  }
}

/* Medium Screen (Buttons arranged in two rows) */
@media (max-width: 1024px) {
  .footer-btn-section {
    flex-direction: column;
    /* Stack buttons in two rows */
  }

  .footer-btn-section button {
    width: 100%;
    /* Full width for each button */
  }

  .transparent-button {
    width: 100%;
    margin-bottom: 30px;
  }
}

/* Small Screen (Buttons arranged in column) */
@media (max-width: 680px) {
  .footer-btn-section {
    flex-direction: column;
    /* Stack buttons vertically */
    gap: 10px;
  }

  .footer-btn-section button {
    width: 100%;
    /* Full width for each button */
  }

  .form-dataInput {
    display: flex;
    flex-direction: column;
  }
}
