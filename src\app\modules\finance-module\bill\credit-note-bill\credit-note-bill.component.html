<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
    <div class="header">
      <h3>Supplier Credit Note</h3>
    </div>
  
    <form #f="ngForm"   class="styled-form" novalidate>
  
      <div class="bd">
  
        <div class="form-section">
  
          <div class="form-row"
            style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
            <div class="form-group">
              <label for="creditNoteNumberBill">Credit Note Number:</label>
              <input id="creditNoteNumberBill" type="text" required [(ngModel)]="creditNoteBillHead.creditNoteNumberBill"
                name="creditNoteNumberBill"  #creditNoteNumberBill="ngModel" disabled />
            </div>
          </div>
  
  
          <div class="form-row">
            <div class="form-group">
              <label for="documentDate">Document Date:</label>
              <div class="input-container">
                <input 
                  matInput
                  [matDatepicker]="documentDatePicker"
                  id="documentDate" 
                  required 
                  [(ngModel)]="creditNoteBillHead.documentDate"
                  name="documentDate" 
                />
                <mat-datepicker-toggle matSuffix [for]="documentDatePicker"></mat-datepicker-toggle>
              </div>
              <mat-datepicker #documentDatePicker></mat-datepicker>
              <div *ngIf="f.controls['documentDate']?.touched && f.controls['documentDate']?.invalid" class="text-danger">Date is required.</div>
            </div>
          </div>

          <div class="form-row"
              *ngIf="selectedApInvoices.length > 0 && selectedApInvoices[0]?.referenceNo"
              style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
            <div class="form-group">
              <label for="billReferanceNumber">Reference Number:</label>
              <input id="billReferanceNumber" type="text" required [(ngModel)]="selectedApInvoices[0].referenceNo"
                    name="billReferanceNumber" #creditNoteNumberBill="ngModel" disabled />
            </div>
          </div>


          <div
            class="form-row"
            *ngIf="selectedApInvoices.length > 0 && selectedApInvoices[0].apInvoiceHeadId"
            style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;"
          >
            <div class="form-group" style="display: flex; align-items: center; flex-grow: 1">
              <label for="postingDate">Posting Date:</label>
              <input
                type="text"
                [value]="selectedApInvoices[0].apInvoiceHeadId.postingDate | date: 'dd/MM/yyyy'"
                disabled
              />
            </div>
          </div>
        </div>

        <div class="table-section">
          <div class="table-responsive">
            <table class="table">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Item Description</th>
                  <th>Unit price</th>
                  <th>Quantity</th>
                  <th style="text-align: right;">Amount</th>
                  <th style="text-align: right;">Due Amount</th>
                  <th>Account</th>
                  <th style="text-align: right;">Credit Amount</th>
                  <th style="text-align: right;">New Balance</th>
                </tr>
              </thead>
              <tbody>
                <!-- Loop through selected  -->
                <tr *ngFor="let expense of selectedApInvoices; let i = index">
                  <td>{{ i + 1 }}</td>
    
                  <!-- item Description -->
                  <td>
                    <input type="text" class="form-control1" required [(ngModel)]="expense.itemDescription"
                      name="itemDescription{{i}}" disabled />
                  </td>
    
                  <!--  Unit Price -->
                  <td>
                    <input type="text" class="form-control" [ngModel]="expense.unitPrice"
                      name="unitPrice{{i}}" disabled />
                  </td>

                  <!--  Quantity -->
                  <td>
                    <input type="text" class="form-control" [ngModel]="expense.quantity"
                      name="quantity{{i}}" disabled />
                  </td>
    
                  <!-- Amount -->
                  <td>
                    <input type="text" class="form-control" [value]="(expense.amount + expense.tax) | currency:'USD':'symbol':'1.2-2'"
                      disabled />
                    <input type="hidden" [(ngModel)]="expense.amount" name="netAmount{{i}}" />
                  </td>
    
                  <!-- Balance -->
                  <td>
                    <input type="text" class="form-control"
                      [value]="expense.dueAmount ? (expense.dueAmount + expense.tax | currency:'USD':'symbol':'1.2-2') : '0.00'"
                      disabled />
                    <input type="hidden" [(ngModel)]="expense.dueAmount" name="dueAmount{{i}}" />
                  </td>

                  <!--<td>
                    <select
                      class="form-control"
                      name="coaLedgerAccountId-{{ i }}"
                      required
                      [(ngModel)]="expense.coaLedgerAccountId"
                      (change)="onGLChange($event, i)"
                    >
                      <option value="" disabled selected>GL Account</option>
                      <option *ngFor="let account of glAccounts" [value]="account.coaLedgerAccountId">
                      {{ account.ledgerAccountName }}
                      </option>
                    </select>
                  </td>--->

                  <td>
                    <select
                      class="form-control"
                      name="coaLedgerAccountId-{{ i }}"
                      required
                      [(ngModel)]="expense.coaLedgerAccountId"
                      (change)="onGLChange($event, i)"
                    >
                      <option value="" disabled selected>GL Account</option>
                      <option 
                        *ngFor="let account of glAccountsMap[expense.apInvoiceHeadId.apInvoiceHeadId] || []" 
                        [value]="account.coaLedgerAccountId"
                      >
                        {{ account.ledgerAccountName }}
                      </option>
                    </select>
                  </td>
                  
    
                  <!-- Credit Amount 
                  <td  [ngClass]="{ 'empty-input': !expense.creditAmount , 'filled-input': expense.creditAmount || expense.creditAmount == 0 }">
                    <input type="number" class="form-control" required [(ngModel)]="expense.creditAmount"
                      name="creditAmount{{i}}" (keydown)="preventEnter($event)" min="0"
                      [ngClass]="{ 'is-invalid': f.submitted && f.controls['creditAmount' + i].invalid }" />
                    <div *ngIf=" f.controls['creditAmount' + i]?.invalid"
                      class="text-danger">
                      Credit Amount is required.
                    </div>
                  </td>-->

                  <!-- Credit Amount -->
                <td  >
                  <input 
                    type="number" 
                    class="form-control" 
                    required 
                    [(ngModel)]="expense.creditAmount"
                    name="creditAmount{{i}}" 
                    (keydown)="preventEnter($event)" 
                    min="0"
                    #creditAmount="ngModel"
                    [ngClass]="{ 'is-invalid': f.submitted && creditAmount.invalid }" 
                  />
                  <div *ngIf="f.submitted && creditAmount.invalid" class="text-danger">
                    Credit Amount is required.
                  </div>
                </td>



    
                  <!-- New expense Balance -->
                  <td>
                    <input id="ExpenseBalance" type="text" class="form-control"
                      [value]="getExpenseNewBalance(expense) | currency:'USD':'symbol':'1.2-2'" disabled />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
  
        <div class="form-section_2">
          <div class="form-row">
            <div class="form-group_2">
              <label for="totalCreditAmount">Total Credit Amount</label>
              <input id="totalCreditAmount" type="text" class="form-control"
                [value]="getTotalPaidAmount() | currency:'USD':'symbol':'1.2-2'" disabled />
            </div>
  
          </div>
  
          <div class="form-row"
            style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px; ">
            <div class="form-group_2">
              <label for="remarks">Remarks:</label>
              <textarea id="remarks" required [(ngModel)]="creditNoteBillHead.remarks" name="remarks"
                rows="3"></textarea>
            </div>
          </div>
  
  
          <div class="d-flex justify-content-end mt-5 mb-4 btns" style="background-color: transparent;">
            <button type="button" class="btn btn-secondary me-2" (click)="onCancel()">Cancel</button>
            <button type="submit" class="btn btn-primary" (click)="onSubmit(f)">Save Credit Note</button>
          </div>
        </div>
      </div>
        
    </form>
  </div>