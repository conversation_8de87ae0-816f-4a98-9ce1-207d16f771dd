* {
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.heading-section h1 {
  font-family: Inter;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
  margin-bottom: 40px;
}

.button-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 16px;
}

.custom-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border: 1px solid #4262ff;
  color: #4262ff;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  /* font-size: 16px; */
  /* font-weight: 500; */
  transition: background-color 0.2s ease;
}

.custom-button:hover {
  background-color: #f2f4ff;
}

.arrow {
  /* font-weight: bold; */
  /* font-size: 18px; */
  color: #4262ff;
}

.button-header {
  font-size: 25px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.button-arrow {
  color: #333;
  margin-right: 15px;
}