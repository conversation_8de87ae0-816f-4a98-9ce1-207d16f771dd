import { Entity } from "../entity/entity";

export class BusinessPartner {
    businessPartnerId: number = 0;
    businessPartnerTypeId: BusinessPartnerType = new BusinessPartnerType();
    entityId: Entity = new Entity();
    financialDetails: string = '';
    filesUpload: string = '';
    bpName: string = '';
    bpFirstName: string = '';
    email: string = '';
    contactPhoneNumber: string = '';
    companyReferenceName: string = '';
    abnNumber: string = '';
    acnNumber: string = '';
    defaultPaymentTerms: string = '';
    businessAddress: string = '';
    deliveryAddress: string = '';
    bankAccountName: string = '';
    bsb: string = '';
    accountNumber: string = '';
    partnerStatus: string = '';
  }

  export class BusinessPartnerType {
    businessPartnerTypeId: any = 0;
    businessPartnerType: string = '';
  }

  