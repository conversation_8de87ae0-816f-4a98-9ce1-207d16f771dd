import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { GlPostingHead, GlPostingDetails } from '../journal-voucher';
import { JournalVoucherService } from '../journal-voucher.service';
import { Entity } from 'src/app/modules/entity/entity';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { BillService } from '../../bill/bill.service';
import { CoaLedgerAccount } from '../../gl-account/gl-account';
import { BusinessPartner, BusinessPartnerType } from 'src/app/modules/business-partner/business-partner';
import { HttpErrorResponse } from '@angular/common/http';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { NgForm } from '@angular/forms';
import { PeriodClosingService } from '../../period-closing/period-closing.service';
import { DateAdapter } from '@angular/material/core';

@Component({
  selector: 'app-create-jv',
  templateUrl: './create-jv.component.html',
  styleUrls: ['./create-jv.component.css'],
})
export class CreateJvComponent implements OnInit {

    @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
    @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;

  rows: Array<{
    description: string;
    bankAccount: number | null;
    debit: number | null;
    credit: number | null;
    coaLedgerAccountId: number | null;
  }> = [
    {
      description: '',
      bankAccount: null,
      debit: null,
      credit: null,
      coaLedgerAccountId: null,
    },
  ];

  totalDebit: number = 0;
  totalCredit: number = 0;
  uploadedFiles: File[] = [];
  jvNumber: string = '';
  narration: string = '';
  date: string = '';
  businessEntity: Entity = new Entity();
  glPostingHead: GlPostingHead = new GlPostingHead();
  glPostingDetails: GlPostingDetails[] = [];
  coaLedgerAccounts: CoaLedgerAccount[] = [];
  selectedCoaLedgerAccount: CoaLedgerAccount = new CoaLedgerAccount();
  customers: BusinessPartner[] = [];
  businessPartner: BusinessPartner = new BusinessPartner();
  private audio!: HTMLAudioElement;
  businessPartnerType: BusinessPartnerType[] = [];
  isAmountEqual: boolean = false;

  areAllRowsValid(): boolean {
   const rowsWithAmounts = this.rows.filter(row => row.debit !== null || row.credit !== null);
  
  return rowsWithAmounts.every(row => {
    const hasDescription = !!row.description?.trim();
    const hasAccount = row.coaLedgerAccountId !== null;
    const hasValidAmount = (row.debit !== null || row.credit !== null) && 
                         !(row.debit !== null && row.credit !== null);
    
    return hasDescription && hasAccount && hasValidAmount;
  });
  }

  constructor(
    private router: Router,
    private entityService: EntityService,
    private jornalVoucherService: JournalVoucherService,
    private billService: BillService,
    private businessPartnerService: BusinessPartnerService,
    private periodClosingService: PeriodClosingService,
    private dateAdapter: DateAdapter<Date>,
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }

  ngOnInit(): void {
    this.setTodayDate();
    this.loadCustomers();
    this.getBusinessEntityById();
    this.fetchGlAccounts();
  }

  loadCustomers() {
      const entityId = +(localStorage.getItem('entityId') + '');
  
      this.businessPartnerService
        .getSupplierListByEntity(entityId)
        .subscribe(
          (customers: BusinessPartner[]) => {
            // Filter customers where businessPartnerType is 'Supplier'
            this.customers = customers.filter(
              (customer) =>
                customer.businessPartnerTypeId?.businessPartnerType === 'Supplier'
            );
          },
          (error: HttpErrorResponse) => {
            console.error('Error fetching Suppliers', error);
            Swal.fire({
              title: 'Error!',
              text: 'Failed to load Supplier.',
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#be0032',
              cancelButtonColor: '#007bff',
              showCancelButton: true,
            }).then((result) => {
              if (
                result.isDismissed &&
                result.dismiss === Swal.DismissReason.cancel
              ) {
                if (this.chatBotComponent) {
                  Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while Chimp processes your request.',
                    allowOutsideClick: false,
                    didOpen: () => {
                      Swal.showLoading();
                      this.chatBotComponent.setInputData(
                        'Failed to load customers.'
                      );
                      this.chatBotComponent.responseReceived.subscribe(
                        (response) => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound();
                        }
                      );
                    },
                  });
                } else {
                  console.error('ChatBotComponent is not available.');
                }
              }
            });
          }
        );
  
      // Reset the selected business partner
      this.glPostingHead.businessPartnerId = '';
    }

  addRow(){
    this.rows.push({
      description: '',
      bankAccount: null,
      debit: null,
      credit: null,
      coaLedgerAccountId: null,
    });
    this.updateTotals(); 
  }

  onDescriptionInput(index: number) {
    if (index === this.rows.length - 1 && this.rows[index].description.trim()) {
      this.addRow();
    }
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  removeRow(index: number) {
    this.rows.splice(index, 1);
    this.updateTotals();
  }

  updateTotals() {
    this.totalDebit = this.rows.reduce((sum, row) => sum + (row.debit || 0), 0);
    this.totalCredit = this.rows.reduce((sum, row) => sum + (row.credit || 0), 0);
    this.isAmountEqual = this.totalDebit === this.totalCredit && this.totalDebit !== 0;
  }

  mapTableToGlPostingDetails(): void {
    this.glPostingDetails = this.rows.map((row) => {
      const selectedAccount = this.coaLedgerAccounts.find(
        (account) =>
          row.coaLedgerAccountId !== null &&
          account.coaLedgerAccountId === +row.coaLedgerAccountId
      );
      return new GlPostingDetails(
        null,
        this.glPostingHead,
        selectedAccount || null,
        null,
        row.debit,
        row.credit
      );
    });
  }

  navigateJVList(): void {
    this.router.navigate(['/journal-voucher-list']);
  }

  getBusinessEntityById() {
    this.entityService
      .getBusinessEntityById(Number(localStorage.getItem('entityId')))
      .subscribe(
        (data) => {
          this.businessEntity = data;
          this.glPostingHead.jvNumber = this.incrementJVNumber(
            this.businessEntity.jvNumber
          );
        },
        (error) => {
          console.error('Error fetching business entity:', error);
        }
      );
  }

  incrementJVNumber(jvNumber: string): string {
    if (jvNumber == null || !jvNumber) {
      return 'J000001';
    }

    const prefix = jvNumber.charAt(0);
    const numericPart = jvNumber.slice(1);

    const incrementedNumber = (Number(numericPart) + 1)
      .toString()
      .padStart(numericPart.length, '0');

    this.businessEntity.jvNumber = prefix + incrementedNumber;
    return this.businessEntity.jvNumber;
  }

  setTodayDate() {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const dd = String(today.getDate()).padStart(2, '0');

    this.glPostingHead.date = today.toISOString().split('T')[0];
  }


  
    onJvDateChange() {  
          const entityId = +localStorage.getItem('entityId')!;
          const date = this.glPostingHead.date;
        
          if (date) {
            this.periodClosingService.isDateLocked(entityId, date).subscribe({
              next: (isLocked: boolean) => {
                if (isLocked) {
                  Swal.fire({
                    icon: 'error',
                    title: 'Posting Date is Locked',
                    text: 'The selected date falls within a closed accounting period. Please choose another date.',
                    confirmButtonColor: '#ff7e5f'
                  });
        
                  // Reset the posting date
                this.setTodayDate();
                }
              },
              error: (err) => {
                console.error('Error validating posting date lock', err);
              }
            });
          }
        }
        

  fetchGlAccounts(): void {
    const entityId = +localStorage.getItem('entityId')!;
    this.billService.getAllCoaLedgerAccounts(entityId).subscribe((data) => {
      this.coaLedgerAccounts = data;
    });
  }
  
  saveJV(): void {
    if (
      this.selectedCoaLedgerAccount == null ||
      this.totalCredit == 0 ||
      this.totalCredit == 0
    ) {
      Swal.fire({
        title: 'Error!',
        text: 'Fill all the required fields.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      });
      return;
    }
    if (this.totalDebit !== this.totalCredit) {
      Swal.fire({
        title: 'Error!',
        text: 'Total Debit and Credit must be equal.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      });
      return;
    }
    this.glPostingHead.entityId = +(localStorage.getItem('entityId') + '');
    this.glPostingHead.userId = +(localStorage.getItem('userid') + '');
    this.glPostingHead.status = 'Posted';
    this.glPostingHead.totalDr = this.totalDebit;
    this.glPostingHead.totalCr = this.totalCredit;

    if (this.glPostingHead.jvNumber) {
      this.businessEntity.jvNumber = this.glPostingHead.jvNumber;
    }

    this.jornalVoucherService
      .addGlPostingHead(this.glPostingHead)
      .subscribe((response) => {
        this.glPostingHead = response;
        this.mapTableToGlPostingDetails();
        this.jornalVoucherService
          .addGlPostingDetailsList(this.glPostingDetails)
          .subscribe((response) => {
            this.glPostingDetails = response;
            this.entityService
              .updateJvNumber(
                Number(localStorage.getItem('entityId')),
                this.businessEntity.jvNumber
              )
              .subscribe((data) => {
                this.businessEntity = data;
              });
            Swal.fire({
              title: 'Posted!',
              text: 'Journal voucher has been saved successfully.',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            }).then(() => {
              this.router.navigate(['/journal-voucher-list']);
            });
          });
      });

    this.rows = this.rows.filter(row => 
      (row.debit !== null || row.credit !== null) && 
      row.coaLedgerAccountId !== null
    );
  }

  saveDraftJV(): void {
    if (
      this.selectedCoaLedgerAccount == null ||
      this.totalCredit == 0 ||
      this.totalCredit == 0
    ) {
      Swal.fire({
        title: 'Error!',
        text: 'Fill all the required fields.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      });
      return;
    }
    if (this.totalDebit !== this.totalCredit) {
      Swal.fire({
        title: 'Error!',
        text: 'Total Debit and Credit must be equal.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      });
      return;
    }
    this.glPostingHead.entityId = +(localStorage.getItem('entityId') + '');
    this.glPostingHead.userId = +(localStorage.getItem('userid') + '');
    this.glPostingHead.status = 'Draft';
    this.glPostingHead.totalDr = this.totalDebit;
    this.glPostingHead.totalCr = this.totalCredit;

    if (this.glPostingHead.jvNumber) {
      this.businessEntity.jvNumber = this.glPostingHead.jvNumber;
    }

    this.jornalVoucherService
      .addGlPostingHead(this.glPostingHead)
      .subscribe((response) => {
        this.glPostingHead = response;
        this.mapTableToGlPostingDetails();
        this.jornalVoucherService
          .addGlPostingDetailsList(this.glPostingDetails)
          .subscribe((response) => {
            this.glPostingDetails = response;
            this.entityService
              .updateJvNumber(
                Number(localStorage.getItem('entityId')),
                this.businessEntity.jvNumber
              )
              .subscribe((data) => {
                this.businessEntity = data;
              });
            Swal.fire({
              title: 'Draft saved!',
              text: 'Journal voucher has been saved successfully.',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            }).then(() => {
              this.router.navigate(['/journal-voucher-list']);
            });
          });
      });
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

  @ViewChild('cuspop') cuspop!: NgForm;
    @ViewChild('closeCustomerPopUp') closeCustomerPopUp: any;
  
    onSubmitCustomerForm() {
      this.saveCustomerForm(this.businessPartner);
    }
  
    saveCustomerForm(businessPartner: BusinessPartner) {
      this.businessPartner.entityId.entityId = +(
        localStorage.getItem('entityId') + ''
      );
      this.businessPartnerService.saveBusinessPartner(businessPartner).subscribe(
        (response) => {
          Swal.fire({
            title: 'Success!',
            text: 'Supplier added successfully.',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          });
          this.loadCustomers();
          this.cuspop.resetForm();
          this.closeCustomerPopUp.nativeElement.click();
        },
        (error) => {
          console.error('Error adding Supplier', error);
          Swal.fire({
            title: 'Error!',
            text: 'Error adding Supplier.',
            icon: 'error',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#be0032',
            cancelButtonColor: '#007bff',
            showCancelButton: true,
          }).then((result) => {
            if (
              result.isDismissed &&
              result.dismiss === Swal.DismissReason.cancel
            ) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData('Error adding Supplier.');
                    this.chatBotComponent.responseReceived.subscribe(
                      (response) => {
                        Swal.close();
                        this.chatResponseComponent.showPopup = true;
                        this.chatResponseComponent.responseData = response;
                        this.playLoadingSound();
                        this.stopLoadingSound();
                      }
                    );
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
          return;
        }
      );
    }

    loadBusinessPartnerTypes() {
        this.businessPartnerService.getBusinessPartnerTypesList().subscribe(
          (businessPartnerType: BusinessPartnerType[]) => {
            // Find the "Customer" type from the list
            const customerType = businessPartnerType.find(
              (type) => type.businessPartnerType.toLowerCase() === 'supplier'
            );
    
            if (customerType) {
              // Assign the customer type to the businessPartner object
              this.businessPartner.businessPartnerTypeId.businessPartnerTypeId =
                customerType.businessPartnerTypeId;
            }
    
            // Optionally store the filtered list if needed
            this.businessPartnerType = businessPartnerType;
          },
          (error: HttpErrorResponse) => {
            console.error('Error fetching Business Partner Type', error);
            Swal.fire({
              title: 'Error!',
              text: 'Failed to load Business Partner Type.',
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
              confirmButtonColor: '#be0032',
              cancelButtonColor: '#007bff',
              showCancelButton: true,
            }).then((result) => {
              if (
                result.isDismissed &&
                result.dismiss === Swal.DismissReason.cancel
              ) {
                if (this.chatBotComponent) {
                  Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while Chimp processes your request.',
                    allowOutsideClick: false,
                    didOpen: () => {
                      Swal.showLoading();
                      this.chatBotComponent.setInputData(
                        'Failed to load Business Partner Type.'
                      );
                      this.chatBotComponent.responseReceived.subscribe(
                        (response) => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound();
                        }
                      );
                    },
                  });
                } else {
                  console.error('ChatBotComponent is not available.');
                }
              }
            });
            return;
          }
        );
      }
}
