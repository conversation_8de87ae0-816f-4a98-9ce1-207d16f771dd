import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import {
  InvoiceDetail,
  InvoiceHead,
  InvoiceLog,
  PaymentReceiptsHead,
  SaveInvoiceHeadDTO,
} from './invoice';
import { HttpService } from 'src/app/http.service';

@Injectable({
  providedIn: 'root',
})
export class InvoiceService {

  private readonly baseURL = environment.salesApiUrl;

  constructor(private http: HttpClient, private httpService: HttpService) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  saveInvoice(saveInvoiceHeadDTO: SaveInvoiceHeadDTO): Observable<any> {
    return this.request('POST', '/saveInvoice', saveInvoiceHeadDTO);
  }

  saveInvoiceHeadFile(
    invoiceHeadId: number,
    file: File
  ): Observable<InvoiceHead> {
    const formData: FormData = new FormData();
    formData.append('invoiceHeadId', invoiceHeadId.toString());
    formData.append('file', file);
    return this.request('PUT', `/invoiceHead/save-file`, formData, null);
  }

  getInvoiceHeadList(): Observable<InvoiceHead[]> {
    return this.request('GET', '/invoiceHeadList', {});
  }

  getAllInvoiceLogsByInvoiceHeadId(id: number): Observable<InvoiceLog[]> {
    return this.request('GET', `/getAllInvoiceLogsByInvoiceHeadId/${id}`, {});
  }

  getInvoiceHeadListByEntity(entityId: any): Observable<InvoiceHead[]> {
    return this.request(
      'GET',
      '/invoiceHeadListByEntity',
      {},
      { entityId: entityId }
    );
  }

  getInvoiceHeadListByEntityPaginated(entityId: number, page: number, size: number): Observable<any> {
  return this.request(
    'GET',
    '/invoiceHeadListByEntityPaginated',
    {},
    {
      entityId: entityId,
      page: page,
      size: size
    }
  );
}
  getInvoiceHeadById(id: number): Observable<InvoiceHead> {
    return this.request('GET', `/getInvoiceHeadById/${id}`, {});
  }

  getInvoiceDetailsByInvoiceHeadId(id: number): Observable<InvoiceDetail[]> {
    return this.request('GET', `/getInvoiceDetailsByInvoiceHeadId/${id}`, {});
  }

  getInvoicesByDateRangeAndCustomer(
    fromDate: string,
    toDate: string,
    businessPartnerId: any
  ): Observable<InvoiceHead[]> {
    return this.request(
      'GET',
      '/getInvoicesByDateRangeAndCustomer',
      {},
      {
        fromDate: fromDate,
        toDate: toDate,
        businessPartnerId: businessPartnerId,
      }
    );
  }

  updateInvoice(id: number, invoiceHead: InvoiceHead): Observable<object> {
    return this.request('PUT', `/updateInvoiceHead/${id}`, invoiceHead);
  }

  cancelSelectedInvoices(invoices: InvoiceHead[]): Observable<any> {
    return this.request('POST', '/cancel-invoices', invoices);
  }

 sendInvoicesWithEmail(
  emailData: {
    subject: string;
    content: string;
    TempRecipientEmail: string;
    invoices: InvoiceHead[];
  },
  entityUuid: string,
  entityId: number
): Observable<any> {
  return this.request('POST', `/sendInvoicesWithEmail/${entityUuid}/${entityId}`, emailData);
}

  sendCancellationEmail(emailData: {
    recipients: string[];
    subject: string;
    content: string;
  }, entityUuid: string,
    entityId: number
  ): Observable<any> {
    return this.request('POST', `/sendCancellationEmail/${entityUuid}/${entityId}`, emailData);
  }



  getRecipientEmail(invoiceId: number): Observable<string> {
    return this.request('GET', `/getRecipientEmail/${invoiceId}`, {});
  }

  markAsPaid(invoices: InvoiceHead[]): Observable<any> {
    return this.request('POST', '/mark-as-paid', invoices);
  }

  deleteInvoice(invoiceId: number): Observable<any> {
    return this.request('DELETE', `/deleteInvoiceHead/${invoiceId}`, {});
  }

  deleteInvoiceItem(invoiceDetailId: number): Observable<any> {
    return this.request('DELETE', `/deleteInvoiceItem/${invoiceDetailId}`, {});
  }

  reviseInvoice(id: number, createdBy:String): Observable<object> {
    return this.request('PUT', `/reviseInvoice/${id}/${createdBy}`, {});
  }

  getInvoiceReport(invoiceId: number, entityId: any, entityUuid: string): Observable<any> {
    return this.request(
      'GET',
      `/getInvoiceReport/${invoiceId}/${entityId}/${entityUuid}`,
      {}
    );
  }


    getInvoiceCashListReport(requestData: {
    fromDate: string,
    toDate: string,
    entityId: number,
    businessPartnerId: number | null,
    entityUUID: string
  }): Observable<any> {
     return this.request('POST',`/getInvoiceCashListReport`, requestData);
  }
  
  
    getInvoiceListReport(requestData: {
    fromDate: string,
    toDate: string,
    status: string,
    entityId: number,
    businessPartnerId: number | null,
    minAmount: number,
    maxAmount: number,
    entityUUID: string
  }): Observable<any> {
     return this.request('POST',`/getInvoiceListReport`, requestData);
  }


     getPaymentListReport(requestData: {
    fromDate: string,
    toDate: string,
    status: string,
    entityId: number,
    businessPartnerId: number | null,
    entityUUID: string
  }): Observable<any> {
     return this.request('POST',`/getPaymentListReport`, requestData);
  }
  
  getItemDetails(itemCode: string): Observable<any> {
    return this.request('GET', `/getSalesItemByItemCode/${itemCode}`, {});
  }

  updateInvoiceBalance(
    invoiceHeadId: number,
    newBalanceAmount: number
  ): Observable<any> {
    const requestBody = {
      balanceAmount: newBalanceAmount,
    };
    return this.request('PUT', `/updateBalance/${invoiceHeadId}`, requestBody);
  }

  updateInvoiceHeadAndDetailsBalance(
    invoiceHeadId: number,
    requestBody:
    {
      newBalanceHeadAmount: number;
      updates: {
          invoiceDetailId: number;
          newBalanceDetailAmount: number;
        }[];

    }
  ): Observable<any> {
    return this.request('PUT', `/updateHeadAndDetailsBalance/${invoiceHeadId}`, requestBody);
  }

  getPaymentReceiptsHeadList(): Observable<PaymentReceiptsHead[]> {
    return this.request('GET', `/paymentReceiptsHeadList`, {});
  }

  savePaymentReceiptsHead(
    paymentReceiptsHead: PaymentReceiptsHead
  ): Observable<any> {
    return this.request(
      'POST',
      `/savePaymentReceiptsHead`,
      paymentReceiptsHead
    );
  }

  getAllPaymentReceiptsHeadList(
    entityId: number
  ): Observable<PaymentReceiptsHead[]> {
    return this.request('GET', `/paymentReceiptsHeadList/${entityId}`, {}, {});
  }

  getPaymentReceiptsHeadById(id: number): Observable<PaymentReceiptsHead> {
    return this.request('GET', `/getPaymentReceiptsHeadById/${id}`, {});
  }

  getPaymentReceiptsHeadByInvoiceIdEntityId(invoiceNumber: string,entityId:number): Observable<PaymentReceiptsHead> {
    return this.request('GET', `/getPaymentReceiptsHeadByInvoiceIdEntityId/${entityId}/${invoiceNumber}`, {});
  }

  deletePaymentReceiptsHead(creditNoteId: number): Observable<any> {
    return this.request(
      'DELETE',
      `/deletePaymentReceiptsHead/${creditNoteId}`,
      {}
    );
  }
  getInvoiceDetailsByNumber(invoiceNumber: string): Observable<any> {
    return this.request('GET', `/getInvoiceByNumber/${invoiceNumber}`, {});
  }

  getInvoiceDetailsByNumberEn(
    invoiceNumber: string,
    entityId: any
  ): Observable<any> {
    return this.request(
      'GET',
      `/getInvoiceByNumberEn/${invoiceNumber}/${entityId}`,
      {}
    );
  }

  getInvoiceHeadListByStatus(entityId: number): Observable<InvoiceHead[]> {
    return this.request('GET', `/invoiceHeadListByStatus/${entityId}`, {}, {});
  }

   getInvoiceSummary(entityId: number): Observable<any> {
    return this.request('GET', `/dashboard/invoice-summary`, null, { entityId });
  }

  getAgedDebtorsSummary(entityId: number): Observable<any> {
    return this.request('GET', `/dashboard/aged-debtors-summary`, null, { entityId });
  }

}
