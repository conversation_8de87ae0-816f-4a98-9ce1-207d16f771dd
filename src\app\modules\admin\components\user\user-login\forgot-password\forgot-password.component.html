<app-home-header></app-home-header>
<body>
  <div class="container">
    <div class="form">
      <h2>Forgot Password</h2>
      <p>
        Remember your password?
        <a (click)="navigateToUserLogin()">Login here</a>
      </p>
      <form (ngSubmit)="onSubmit()">
        <div class="input-group">
          <input
            type="text"
            name="email"
            [(ngModel)]="email"
            (change)="onEmailChange()"
            placeholder="Enter your user email"
            required
          />
          <!-- Validation message directly below the input -->
          <div *ngIf="emailError" class="error-message">{{ emailError }}</div>
        </div>
        <button type="submit">Reset Password</button>
      </form>
    </div>
  </div>
</body>
