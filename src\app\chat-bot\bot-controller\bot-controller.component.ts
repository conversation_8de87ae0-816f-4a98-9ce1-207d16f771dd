import { Component, OnInit, EventEmitter, Output } from '@angular/core';
import { BotService } from '../bot.service';


@Component({
  selector: 'app-bot-controller',
  templateUrl: './bot-controller.component.html',
  styleUrls: ['./bot-controller.component.css']
})
export class BotControllerComponent implements OnInit {
  @Output() responseReceived = new EventEmitter<string>();

  inputData: string = '';
  responseData: string = '';

  constructor(private botService: BotService) {}

  ngOnInit(): void {}

  setInputData(data: string): void {
    this.inputData = data;
    this.sendData(); 
  }

  sendData(): void {
    if (this.inputData) {
      const params = { prompt: this.inputData };
      this.botService.request('GET', '/chat', null, params).subscribe(
        (response: any) => {
          this.responseData = response;
          this.responseReceived.emit(response);
        },
        error => {
          console.error('Error:', error);
          this.responseData = 'ChatBot is not available!';
          this.responseReceived.emit('ChatBot is not available!');
        }
      );
    }
  }
}
