import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Entity } from 'src/app/modules/entity/entity';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { UserService } from '../user.service';
import { UserType } from '../user';
import Swal from 'sweetalert2';
import { SubscriptionService } from 'src/app/modules/subscription/subscription.service';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';

@Component({
  selector: 'app-user-invitation',
  templateUrl: './user-invitation.component.html',
  styleUrls: ['./user-invitation.component.css'],
})
export class UserInvitationComponent implements OnInit {
  entity: Entity = new Entity();
  firstName: string = '';
  lastName: string = '';
  entityId: number = 0;
  email: string = '';
  subscriptionPlanId: number = 0;
  isAccountant: boolean = false;
  isSystemAdmin: boolean = false;
  selectedUserType: UserType = new UserType();
  accountantUser: UserType = new UserType();
  userTypes: UserType[] = [];
  isUsernameExists: boolean = false;
  usernameExistsMessage: string = '';
  inviteUserForm!: FormGroup;
  isInviting: boolean = false;
  
  constructor(
    private router: Router,
    private entityService: EntityService,
    private userService: UserService,
    private subscriptionService: SubscriptionService,
    private fb: FormBuilder
  ) {
     this.inviteUserForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, this.customEmailValidator()]],
      userType: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.entityId = Number(localStorage.getItem('entityId'));
    this.getEntity();
    this.getUserTypes();
    this.loadUserType();
    this.getSubscriptionPlan();

    
  }

customEmailValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    if (!value) {
      return null; // Let required validator handle empty value
    }

    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
    const isValid = emailRegex.test(value);
    return isValid ? null : { customEmail: true };
  };
}
  
  getEntity() {
    this.entityService.getBusinessEntityById(this.entityId).subscribe(
      (data) => {
        this.entity = data;
        

      },
      (error) => {
        console.error('Error fetching entity data:', error);
        this.showErrorSwal('Failed to load entity information.');
      }
    );
  }

  getSubscriptionPlan() {
    this.subscriptionService.getSubscriptionByEntityId(this.entityId).subscribe(
      (response) => {
        this.subscriptionPlanId = response.subscriptionFeeId?.subscriptionPlanId?.subscriptionPlanId ?? 0;
      },
      (error) => {
        console.error('Error fetching subscription plan:', error);
        this.showErrorSwal('Failed to load subscription plan.');
      }
    );
  }
  
  getUserTypes() {
    // Retrieve the user object from localStorage and parse it
    const user = localStorage.getItem('user')
      ? JSON.parse(localStorage.getItem('user')!)
      : null;
    const userType = user?.userType || null;

    this.userService.getUserTypesList().subscribe(
      (response) => {
        // Filter the response to exclude 'Admin' and 'System Admin'
        this.userTypes = response.filter(
          (userType) =>
            userType.userType !== 'Admin' &&
            userType.userType !== 'System Admin'
        );

        // If the local storage userType is 'System Admin', find 'Accountant' and set its ID
        if (userType === 'System Admin') {
          const accountantType = response.find(
            (type) => type.userType === 'Accountant'
          );
          if (accountantType) {
            this.selectedUserType.userTypeId = accountantType.userTypeId;
          }
        } else {
          this.selectedUserType.userTypeId = '';
        }
      },
      (error) => {
        console.error('Error fetching user types:', error);
        this.showErrorSwal('Failed to load user types.');
      }
    );
  }

  loadUserType() {
    const userTypeId = Number(localStorage.getItem('userTypeId'));

    this.userService.getUserTypeById(userTypeId).subscribe(
      (response) => {
        if (response.userType === 'Accountant') {
          this.isAccountant = true;
          this.selectedUserType = response;
        }
        if (response.userType === 'System Admin') {
          this.isSystemAdmin = true;
          this.userService
            .getUserTypeByUserType('Accountant')
            .subscribe((response) => {
              this.selectedUserType = response;
            });
          this.entityService
            .getBusinessEntityByName('Default Entity')
            .subscribe((response) => {
              this.entityId = response.entityId;
            });
        }
      },
      (error) => {
        console.error('Error fetching user type data:', error);
      }
    );
  }

  checkEmailExistenceKeyup(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.email) {
        this.userService.checkUser(this.email).subscribe(
          (exists) => {
            this.isUsernameExists = exists;
            if (this.isUsernameExists) {
              this.usernameExistsMessage =
                'This email is already registered. Please use a different email.';
            } else {
              this.usernameExistsMessage = '';
            }
            resolve(this.isUsernameExists);
          },
          (error) => {
            console.error('Error checking email existence', error);
            this.isUsernameExists = false;
            this.usernameExistsMessage = '';
            resolve(false);
          }
        );
      } else {
        this.isUsernameExists = false;
        this.usernameExistsMessage = '';
        resolve(false);
      }
    });
  }

  async inviteUser() {
    if (this.inviteUserForm.invalid) {
          this.inviteUserForm.markAllAsTouched();
          return;
        }

    this.isInviting = true; 
    const emailExists = await this.checkEmailExistenceKeyup();

    if (emailExists) {
      this.isInviting = false;
      return;
    }

    const payload = {
      firstName: this.firstName,
      lastName: this.lastName,
      email: this.email,
      entityId: this.entityId,
      subscriptionPlanId: Number(this.subscriptionPlanId),
      userTypeId: this.selectedUserType.userTypeId,
    };
 
    this.userService.inviteUser(payload).subscribe(
      (response) => {
        this.isInviting = false;
        Swal.fire({
          title: 'Invitation Sent!',
          text: 'User invitation has been sent successfully.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
         this.router.navigate(['/manage-users']);
        });
      },
      (error) => {
        this.isInviting = false;
        console.error('Error sending invitation:', error);
        this.showErrorSwal('Failed to send the invitation.');
      }
    );
  }

  navigateToDashboard() {
    this.router.navigate(['/manage-users']);
  }

  showErrorSwal(message: string): void {
    Swal.fire({
      title: 'Error',
      text: message,
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#be0032',
    });
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
}
