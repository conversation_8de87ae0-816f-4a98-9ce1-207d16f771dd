import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of, tap } from 'rxjs';
import {
  Subscription,
  SubscriptionCycle,
  SubscriptionFee,
  SubscriptionPlan,
} from 'src/app/modules/subscription/subscription';

@Injectable({
  providedIn: 'root',
})
export class SubscriptionService {
  private readonly baseURL = environment.apiUrl;
  private subscriptionCache: Map<number, any> = new Map();
   private subscriptionExpiryCache: Map<number, boolean> = new Map();

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

const authToken = this.getAuthToken();

  if (authToken) {
    headers = headers.set('Authorization', 'Bearer ' + authToken);
  } else {
    // Add secure API key for protected-but-public endpoints
    headers = headers.set('X-API-KEY', environment.secureApiKey);
  }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  private selectedSubscriptionFeeId: number | null = null;

  setSelectedSubscriptionFeeId(id: number) {
    this.selectedSubscriptionFeeId = id;
  }

  getSelectedSubscriptionFeeId(): number | null {
    return this.selectedSubscriptionFeeId;
  }

  // Subscription

  saveSubscription(subscription: Subscription): Observable<any> {
    return this.request('POST', `/saveSubscription`, subscription);
  }

  getAllSubscription(): Observable<Subscription[]> {
    return this.request('GET', '/subscriptionList', {});
  }

  getSubscriptionById(id: number): Observable<Subscription> {
    return this.request('GET', `/getSubscriptionById/${id}`, {});
  }

  getSubscriptionByEntityId(id: number): Observable<any> {
    return this.request('GET', `/subscriptionByEntityId/${id}`, {});
  }

  getSubscriptionByEntityIdCached(id: number): Observable<any> {
    const cached = this.subscriptionCache.get(id);
    if (cached) {
      return of(cached);
    }

    return this.getSubscriptionByEntityId(id).pipe(
      tap((data) => this.subscriptionCache.set(id, data))
    );
  }

  clearSubscriptionCache(): void {
    this.subscriptionCache.clear();
  }

  clearSubscriptionFromCache(id: number): void {
    this.subscriptionCache.delete(id);
  }

  checkSubscriptionExpiry(entityId: number): Observable<boolean> {
    return this.request(
      'GET',
      `/subscription/check-expiry/${entityId}`,
      null,
      null
    );
  }

  checkSubscriptionExpiryCached(entityId: number): Observable<boolean> {
    const cachedResult = this.subscriptionExpiryCache.get(entityId);
    if (cachedResult !== undefined) {
      return of(cachedResult);
    }

    return this.checkSubscriptionExpiry(entityId).pipe(
      tap((isExpired) => this.subscriptionExpiryCache.set(entityId, isExpired))
    );
  }

  clearSubscriptionExpiryCache(): void {
    this.subscriptionExpiryCache.clear();
  }

  clearSubscriptionExpiryFromCache(entityId: number): void {
    this.subscriptionExpiryCache.delete(entityId);
  }

  isSubscriptionExistsForEntity(entitId: number): Observable<boolean> {
    return this.request(
      'GET',
      `/subscription/check-exists/${entitId}`,
      null,
      null
    );
  }

  updateSubscription(
    entitId: number,
    subscriptionFeeId: number
  ): Observable<Subscription> {
    return this.request(
      'PUT',
      `/subscription/update/${entitId}/${subscriptionFeeId}`,
      null,
      null
    );
  }

  //Subscription Fee

  saveSubscriptionFee(subscriptionFee: SubscriptionFee): Observable<any> {
    return this.request('POST', `/saveSubscriptionFee`, subscriptionFee);
  }

  getAllSubscriptionFee(): Observable<SubscriptionFee[]> {
    return this.request('GET', '/subscriptionFeeList', {});
  }

  getSubscriptionFeeById(id: number): Observable<SubscriptionFee> {
    return this.request('GET', `/getSubscriptionFeeById/${id}`, {});
  }

  updateSubscriptionFeeById(
    id: number,
    subscriptionFee: SubscriptionFee
  ): Observable<object> {
    return this.request('PUT', `/updateSubscriptionFee/${id}`, subscriptionFee);
  }

  //Subscription Plan

  saveSubscriptionPlan(subscriptionPlan: SubscriptionPlan): Observable<any> {
    return this.request('POST', `/saveSubscriptionPlan`, subscriptionPlan);
  }

  getAllSubscriptionPlan(): Observable<SubscriptionPlan[]> {
    return this.request('GET', '/subscriptionPlanList', {});
  }

  getSubscriptionPlanById(id: number): Observable<SubscriptionPlan> {
    return this.request('GET', `/getSubscriptionPlanById/${id}`, {});
  }

  updateSubscriptionPlanById(
    id: number,
    subscriptionPlan: SubscriptionPlan
  ): Observable<object> {
    return this.request(
      'PUT',
      `/updateSubscriptionPlan/${id}`,
      subscriptionPlan
    );
  }

  //Subscription Cycle

  saveSubscriptionCycle(subscriptionCycle: SubscriptionCycle): Observable<any> {
    return this.request('POST', `/saveSubscriptionCycle`, subscriptionCycle);
  }

  getAllSubscriptionCycle(): Observable<SubscriptionCycle[]> {
    return this.request('GET', '/subscriptionCycleList', {});
  }

  getSubscriptionCycleById(id: number): Observable<SubscriptionCycle> {
    return this.request('GET', `/getSubscriptionCycleById/${id}`, {});
  }

  updateSubscriptionCycleById(
    id: number,
    subscriptionCycle: SubscriptionCycle
  ): Observable<object> {
    return this.request(
      'PUT',
      `/updateSubscriptionCycle/${id}`,
      subscriptionCycle
    );
  }
}
