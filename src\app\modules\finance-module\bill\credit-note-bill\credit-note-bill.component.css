body {
  font-family: Arial, sans-serif;
  background-color: transparent;
  margin: 0;
  padding: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: transparent;
}
/* Header styles */

.header {
  display: flex;
  background-color: transparent;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: none;
  border: none;
  gap: 5px;
  /* Adds space between buttons */
}

.header h3 {
  flex: 1;
  margin-bottom: 0;
  font-family: Inter;
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
  top: 264px;
}

.bd {
  border: 2px solid #cec9c980; /* Sets the border width, style, and color */
  border-radius: 12px; /* Rounds the corners */
}

.form-section {
  display: flex;
  font-family: Arial, sans-serif;
  flex-direction: column;
  padding: 20px;
  background-color: #f7f7f7; /* If you want a solid background color */
}

.form-row {
  display: flex;
  gap: 32px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  align-items: center;
  width: 100%;
}

.form-group label {
  width: 20%;
  min-width: 20%;
}

.input-container {
  position: relative;
  width: 40%; /* Fixed 40% width for input container */
  min-width: 40%; /* Prevent shrinking */
}

.input-container input {
  width: 100% !important; /* Input takes full width of container */
  padding: 8px 10px; 
  border: 1px solid #c7c7c7;
  border-radius: 8px;
}

.mat-datepicker-toggle {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  margin: 0; /* Remove default margins */
}

.form-group input {
  width: 40%;
  padding: 8px 10px;
  border: 1px solid #c7c7c7;
  border-radius: 8px;
}

.form-group select {
  width: 40%;
  padding: 8px 10px;
  border: 1px solid #c7c7c7;
  border-radius: 8px;
  /* font-size: 14px; */
}

.form-section_2{
  display: flex;
  font-family: Arial, sans-serif;
  flex-direction: column;
  padding: 20px;
  background-color: #f7f7f7; /* If you want a solid background color */
}

.form-group_2{
  display: flex;
    align-items: center;
    flex-grow: 1;
    margin-left: 40%;
}

.form-group_2 label {
  display: block;
  margin-bottom: 5px;
  /* font-weight: bold; */
  width: 30%;
}

.form-group_2 input {
  width: 70%;
  padding: 8px 10px;
  border: 1px solid #c7c7c7;
  border-radius: 8px;
  /* font-size: 14px; */
  font-weight: bold;
}

.form-group_2 textarea {
  width: 70%;
  padding: 8px 10px;
  border: 1px solid #c7c7c7;
  border-radius: 8px;
  /* font-size: 14px; */
}

.form-control {
  border-radius: 5px;
  border: 1px solid #ced4da;
  padding: 10px;
  text-align: right;
}

.form-control1{
  border-radius: 5px;
  border: 1px solid #ced4da;
  padding: 10px;
}

/* .credit-amount-td{
  padding-top: 57px;
} */

.search-bar {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-left: 20px;
  margin-bottom: 10px;
  margin-top: 10px;
  align-items: center;
  position: relative;
}

.btn-primary {
  background: linear-gradient(to right, #4262FF, #512CA2);
  color: white;
  font-weight: bold;
  padding: 10px 40px;
  cursor: pointer;
  border-radius: 12px;
  border: none;
  margin-left: 10px;
  font-size: 17px;
}

.btn-primary:hover {
  background: linear-gradient(to right, #512CA2, #4262FF);
}
.btns{
  display: flex;
  flex-direction: row;
}

.btn-secondary {
  background: transparent;
  color: #4262FF;
  border: 2px solid #4262FF;
  padding: 10px 40px;
  margin-right: 10px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  font-size: 17px;
}

.btn-secondary:hover {
  background-color: #4262FF;
  color: white;
}

.exceeds-balance {
  color: red;
}

.table-section {
  background-color: transparent;
  overflow: hidden;
  margin-bottom: 20px;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: -20px;
}

.table-responsive {
  overflow-x: auto;
  width: 100%;
}

.table-responsive table {
  width: 100%;
  min-width: 600px;
  /* or whatever minimum width you need */
  white-space: nowrap;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: linear-gradient(
    90deg,
    rgba(66, 98, 255, 0.06) 0%,
    rgba(63, 20, 153, 0.06) 100%
  );
  color: black;
  text-align: left;
  padding: 12px;
  font-weight: normal;
}

td {
  padding: 12px;
  border-bottom: 1px solid #ddd;
  background-color: white;
  vertical-align: middle;
}

.empty-input{
  padding-top: 57px;
}

.filled-input{
  padding-top:12px;
}

@media (max-width:668px) {
  .form-group {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      flex-grow: 1;
    }
  
    .form-group label {
      display: block;
      margin-bottom: 5px;
      /* font-weight: bold; */
      width: 100%;
    }
  
    .form-group input {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid #c7c7c7;
      border-radius: 8px;
      font-size: 14px;
    }
  
    .form-group select {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid #c7c7c7;
      border-radius: 8px;
      font-size: 14px;
    }


        .form-group_2 {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex-grow: 1;
          margin-left: 0%;
        }
    
        .form-group_2 label {
          display: block;
          margin-bottom: 5px;
          /* font-weight: bold; */
          width: 100%;
        }
    
        .form-group_2 input {
          width: 100%;
          padding: 8px 10px;
          border: 1px solid #c7c7c7;
          border-radius: 8px;
          font-size: 14px;
          font-weight: bold;
        }
    
        .form-group_2 textarea {
          width: 100%;
          padding: 8px 10px;
          border: 1px solid #c7c7c7;
          border-radius: 8px;
          font-size: 14px;
        }
        .form-row {
        display: flex;
        align-items: center;
        gap: 32px;
        padding-right: 2px;
  }

                .btns{
                  display: flex;
                  flex-direction: column-reverse;
                  gap: 15px;
                  align-items: center;
                }

  .btn-primary {
    background: linear-gradient(to right, #4262ff, #512ca2);
    color: white;
    font-weight: bold;
    padding: 5px 30px;
    cursor: pointer;
    border-radius: 12px;
    border: none;
    margin:auto;
    font-size: 16px;
    text-align: center;
    width: 100%;
   }

  .btn-secondary {
  background: transparent;
  color: #4262ff;
  border: 1px solid #4262ff;
  padding: 5px 30px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  font-size: 16px;
  margin: auto;
  text-align: center;
  width: 100%;
  }


  .table-section {
    padding-left: 0;
    padding-right: 0;
    overflow-x: auto;
  }

  .table-responsive {
    overflow-x: scroll;
  }

  .input-container {
    width: 100%; 
  }
}

@media(max-width:912px){
  .empty-input{
    padding-top: 80px;
  }
  .filled-input{
    padding-top:12px;
  }
}