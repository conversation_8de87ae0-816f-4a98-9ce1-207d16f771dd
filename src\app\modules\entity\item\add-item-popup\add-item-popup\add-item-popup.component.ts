import { Component, EventEmitter, Input, Output, ViewChild, ElementRef } from '@angular/core';
import { NgForm } from '@angular/forms';
import { SalesItem } from 'src/app/modules/quotation/quotation';
import { QuotationService } from 'src/app/modules/quotation/quotation.service';

import { HttpErrorResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';
import { EntityService } from '../../../entity.service';
import { Entity } from '../../../entity';

@Component({
  selector: 'app-add-item-popup',
  templateUrl: './add-item-popup.component.html',
  styleUrls: ['./add-item-popup.component.css']
})
export class AddItemPopupComponent {
  @Output() itemAdded = new EventEmitter<SalesItem>();
  @Input() entityId!: number;
  newItem: SalesItem = {
    salesItemId: 0,
    entityId: 1,
    userId: 1,
    itemTypeId: 1,
    itemCode: '',
    itemName: '',
    sellingPrice: 0,
    standardDiscount: 0,
    salesAccount: '',
    taxApplicability: '',
    itemStatus: 'active',
    description: '',
    unitPrice: 0,
    amount: 0,
    transactionDate: '',
  };


  @ViewChild('closeItemPopUp') closeItemPopUp!: ElementRef;
  @ViewChild('addItemForm') addItemForm!: NgForm;
  itemCode: string = '';
  itemCodeExists: boolean = false;
  showTaxApplicabilityDropdown: boolean = true; 
  showUpdateLink = true;

  constructor(private quotationService: QuotationService, private entityService: EntityService, private swalAlertsService: SwalAlertsService) { }


  ngOnInit() {
    if (!this.entityId) {
      this.showTaxApplicabilityDropdown = false;
      this.newItem.taxApplicability = 'no';
      return;
    }

    this.entityService.getBusinessEntityById(this.entityId).subscribe(
      (entity: Entity) => {
        this.showTaxApplicabilityDropdown = entity.taxApplicability === 'yes';
        this.newItem.taxApplicability = entity.taxApplicability || 'no';
      },
      error => {
        console.error('Error fetching entity:', error);
        this.showTaxApplicabilityDropdown = false;
        this.newItem.taxApplicability = 'no';
      }
    );


    const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      const userType = user.userType; 

      if (userType === 'General user' || userType === 'Accountant') {
        this.showUpdateLink = false;
      }
    }
  }

  saveItem() {
    this.newItem.amount = this.newItem.unitPrice;
    this.newItem.entityId = this.entityId;

    this.quotationService.saveSalesItem(this.newItem).subscribe(
      (savedItem: SalesItem) => {
        this.swalAlertsService.showSuccessDialog('Success!', 'Item saved successfully.', () => {
          this.itemAdded.emit(savedItem); // <--- Emit to parent
          this.itemCode = this.newItem.itemCode;
          this.addItemForm.resetForm();
          this.closeItemPopUp.nativeElement.click();
        });
      },
      (error: HttpErrorResponse) => {
        this.handleApiError('Error saving item. Please try again.', error);
      }
    );
  }


  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  onSubmit(addItemForm: NgForm) {
    if (!addItemForm.valid) return;

    this.checkItemCodeExistence().subscribe((itemCodeExists) => {
      if (itemCodeExists) {
        this.swalAlertsService.showWarning('This item code already exists. Please choose a different code.', () => { });
      } else {
        this.saveItem();
      }
    });
  }

  checkItemCodeExistence(): Observable<boolean> {

    if (this.newItem.itemCode) {
      return this.quotationService.getSalesItemByCodeAndEntityId(this.newItem.itemCode, this.entityId)
        .pipe(
          map(response => {
            this.itemCodeExists = !!response; // Update the itemCodeExists flag
            return this.itemCodeExists; // Return the boolean value for item code existence
          }),
          catchError(() => {
            console.error('Error checking item code');
            this.itemCodeExists = false;
            return of(false);
          })
        );
    } else {
      this.itemCodeExists = false; 
      return of(false); 
    }
  }
  onItemAdded(savedItem: SalesItem) {

    this.itemCode = savedItem.itemCode;
  }

  checkItemCodeExistenceKeyup(): void {
    if (this.newItem.itemCode) {
      this.quotationService.getSalesItemByCodeAndEntityId(this.newItem.itemCode, this.entityId)
        .subscribe(
          (response) => {
            this.itemCodeExists = !!response; // Set itemCodeExists flag
          },
          (error) => {
            console.error('Error checking item code');
            this.itemCodeExists = false; // Reset in case of error
          }
        );
    } else {
      this.itemCodeExists = false; // Reset if no item code is entered
    }
  }

  handleApiError(errorMessage: string, error: any = null) {
    console.error(errorMessage, error);
  }
}

