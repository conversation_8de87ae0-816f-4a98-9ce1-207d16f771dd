import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpService } from 'src/app/http.service';
import { environment } from 'src/environments/environment';
import { Earning, Leave, PayItemType } from '../payroll-setting';
import { GlAccount } from 'src/app/modules/finance-module/gl-account/gl-account';
import { LeaveCategory, LeaveType } from '../empolyee/employee';

@Injectable({
  providedIn: 'root'
})
export class LeaveService {

  private readonly baseURL = environment.payrollApiUrl;


  constructor(private http: HttpClient, private httpService: HttpService) { }

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  getLeaveList(entityId: number): Observable<LeaveType[]> {
    return this.request('GET', `/leaveType/getAll?entityId=${entityId}`, {});
  }

  updateLeaveList(leaveTypeId: number, payload: any): Observable<LeaveType[]> {
    return this.request('PUT', `/leaveType/update/${leaveTypeId}`, payload);
  }

  // getEarningList(): Observable<Earning[]> {
  //   return this.request('GET', '/earning/getAll', {});
  // }
  // getPayitemType(): Observable<PayItemType[]> {
  //   return this.request('GET', '/payItemType/getAll', {});
  // }

  // saveEarning(earning: Earning): Observable<any> {
  //   return this.request('POST', '/earning/save', earning);
  // }

  // deleteEarning(entityId: number): Observable<any> {
  //   return this.request('DELETE', `/earning/deleteById/${entityId}`, {});
  // }

  // updatePayCalendarById(id: number): Observable<any> {
  //   return this.request('PUT', `/earning/update/${id}`,{});
  // }

  // getAllEarnings(id: number): Observable<Earning> {
  //   return this.request('GET', `/earning/getById/${id}`, null);
  // }
  
  // updateEarning(id: number, payload: Earning ): Observable<Earning> {
  //   return this.request('PUT', `/earning/update/${id}`, payload);
  // }

 
}



