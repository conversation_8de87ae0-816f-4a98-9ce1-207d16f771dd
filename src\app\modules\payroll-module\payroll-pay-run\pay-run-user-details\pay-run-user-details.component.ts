import { PayPeriod, PayRunDetailsSummary, PayRunMaster } from './../../payroll-settings/payroll-setting';
import { Component, ElementRef, OnInit, ViewChild, AfterViewInit, } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PayRunService } from '../../payroll-settings/services/pay-run.service';
import Swal from 'sweetalert2';
import { PayProcessDTO } from '../../payroll-settings/payroll-setting';
import { DomSanitizer } from '@angular/platform-browser';
import { PayRunDetailService } from '../../payroll-settings/services/pay-run-detail.service';
import { switchMap } from 'rxjs';
import { EmployeeEmployment } from '../../payroll-settings/empolyee/employee';

@Component({
  selector: 'app-pay-run-user-details',
  templateUrl: './pay-run-user-details.component.html',
  styleUrls: ['./pay-run-user-details.component.css'],
})
export class PayRunUserDetailsComponent implements OnInit, AfterViewInit {
  leaveTwoPage = 1;
  payCalendarId!: number;
  payPeriodId!: number;
  payRunId!: number;
  employeeId!: number;
  totalEarnings: number = 0;
  totalEmployees: string = '';
  totalDeduction: number = 0;
  totalReimbursements: number = 0;
  totalSuper: number = 0;
  totalTax: number = 0;
  totalNetpay: number = 0;

  payProcesses: PayProcessDTO[] = [];
  payRunDetailsSummaryList: PayRunDetailsSummary[] = [];
  payPeriod: PayPeriod | null = null;
  selectedEmployeeId: number = 0;

  payCalendarName: String = '';
  payStartDate: String = '';
  payEndDate: String = '';
  payRunDetailsSummary: PayRunDetailsSummary = new PayRunDetailsSummary();
  payRun: PayRunMaster = new PayRunMaster();

  payRunStatus: String = '';
  isLoading: boolean = false;
  isSubmitting: boolean = false;
  @ViewChild('payRunSummaryPreviewFrame') payRunSummaryPreviewFrame!: ElementRef;
  @ViewChild('reportPreviewFrame') reportPreviewFrame!: ElementRef;
  @ViewChild('bankReportPreviewFrame') bankReportPreviewFrame!: ElementRef;
  @ViewChild('AddEmployeeModal') modalRef!: ElementRef;

  entityId: number = +(localStorage.getItem('entityId') + '');

  constructor(
    private router: Router,
    private payRunService: PayRunService,
    private payRunDetailService: PayRunDetailService,
    private route: ActivatedRoute,
    public sanitizer: DomSanitizer
  ) { }

  ngOnInit(): void {
    this.getAllPayProcessData();
    this.getPayPeriodById();
    this.getPayRun();
    this.getUnscheduledEmployees()
  }

  ngAfterViewInit() {
    const modalElement = this.modalRef.nativeElement;
    modalElement.addEventListener('hidden.bs.modal', () => {
      this.selectedEmployeeId = 0; // Clear selected employee
    });
  }

  getPayPeriodById(): void {
    this.route.params.subscribe((params) => {
      this.payPeriodId = +params['payPeriodId'];
    });

    this.payRunService.getPayPeriodById(this.payPeriodId).subscribe({
      next: (data: PayPeriod) => {
        this.payPeriod = data;
        this.payCalendarName = this.payPeriod.payCalendar.calendarName;
        this.payStartDate = this.payPeriod.payStartDate;
        this.payEndDate = this.payPeriod.nextPayDate;
        this.payRunStatus = this.payPeriod.status;

      },
      error: (err) => {
        console.error('Error fetching PayPeriod by Id:', err);
      },

    })
  }

  navigateToPaySlip(employeeId: number): void {
    this.route.params.subscribe((params) => {
      this.payCalendarId = +params['payCalendarId'];
      this.payPeriodId = +params['payPeriodId'];
    });
    this.router.navigate(['/pay-slip', employeeId, this.payCalendarId, this.payPeriodId]);
  }

  navigateToPayTemplate(employeeId: number): void {
    this.route.params.subscribe((params) => {
      this.payRunId = +params['payRunId'];
    });
    this.router.navigate([
      '/payRun-pay-template/',
      employeeId,
      this.payPeriodId,
      this.payRunId,
    ]);
  }
  
  getAllPayProcessData(): void {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.route.params.subscribe((params) => {
      this.payCalendarId = +params['payCalendarId'];
      this.payPeriodId = +params['payPeriodId'];
    });

    this.payRunService
      .getPayprocessDetails(entityId, this.payCalendarId, this.payPeriodId)
      .subscribe({
        next: (data: PayProcessDTO[]) => {
          this.payProcesses = data;
          console.log(" pay process:", data);
          

          // this.payProcesses = data.map((process) => {
          //   process.netPay =
          //     (process.totalEarnings || 0) -
          //     (process.totalDeductions || 0) +
          //     (process.totalReimbursements || 0) -
          //     (process.superannuation || 0) -
          //     (process.tax || 0);

          //   process.netPay = parseFloat(process.netPay.toFixed(4));
          //   return process;
          // });

          const totals = this.payProcesses.reduce(
            (acc, process) => {
              acc.totalEarnings += process.totalEarnings || 0;
              acc.totalDeductions += process.totalDeductions || 0;
              acc.totalReimbursements += process.totalReimbursements || 0;
              acc.totalSuper += process.superannuation || 0;
              acc.totalTax += process.tax || 0;
              acc.totalNetpay += process.netPay || 0;
              return acc;
            },
            {
              totalEarnings: 0,
              totalDeductions: 0,
              totalReimbursements: 0,
              totalSuper: 0,
              totalTax: 0,
              totalNetpay: 0,
            }
          );

          this.totalEarnings = parseFloat(totals.totalEarnings.toFixed(4));
          this.totalDeduction = parseFloat(totals.totalDeductions.toFixed(4));
          this.totalReimbursements = parseFloat(
            totals.totalReimbursements.toFixed(4)
          );
          this.totalSuper = parseFloat(totals.totalSuper.toFixed(4));
          this.totalTax = parseFloat(totals.totalTax.toFixed(4));
          this.totalNetpay = parseFloat(totals.totalNetpay.toFixed(4));

          this.totalEmployees = this.payProcesses.length
            .toString()
            .padStart(2, '0');
        },
        error: (err) => {
          console.error('Error fetching PayCycles:', err);
        },
      });
  }

  getUnscheduledEmployees(): void {
    this.route.params.subscribe((params) => {
      this.payRunId = +params['payRunId'];
    });

    this.payRunService
      .getUnscheduledPayRunEmployees(this.payRunId)
      .subscribe({
        next: (data: PayRunDetailsSummary[]) => {
          this.payRunDetailsSummaryList = data;
          console.log("summary details: ", this.payRunDetailsSummaryList);
        },
        error: (err) => {
          console.error('Error fetching Unscheduled employees:', err);
        },
      });
  }

  deletePayRun() {
  Swal.fire({
    title: 'Are you sure?',
    text: 'This will delete the Pay Run permanently.',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Yes, delete it!',
    cancelButtonText: 'Cancel',
    confirmButtonColor: '#d33',
    cancelButtonColor: '#3085d6'
  }).then((result) => {
    if (result.isConfirmed) {
      // Proceed with deletion
      this.payRunService.deleteDraftPayRun(this.route.snapshot.params['payPeriodId']).subscribe({
        next: (data: any) => {
          console.log('pay process data', data);
          Swal.fire('Deleted!', 'Pay Run deleted successfully.', 'success');
          this.router.navigate(['/payroll-pay-run']);
        },
        error: (err) => {
          console.error('Error deleting Pay Run:', err);
          Swal.fire('Error', 'Something went wrong while deleting.', 'error');
        }
      });
    }
  });
}


  postPayrun() {
    this.isSubmitting = true;
    this.route.params.subscribe((params) => {
      this.payPeriodId = +params['payPeriodId'];
    });
  
    this.payRunService.updatePayRunPost(this.payPeriodId).pipe(
      switchMap(() => this.payRunService.updatePayRunPostDate(this.payPeriodId)),
      switchMap(() => this.payRunService.updatePayRunBankJournal(this.payPeriodId)) 
    ).subscribe({
      next: () => {
        Swal.fire({
          title: 'Success',
          text: 'Pay Run posted successfully',
          icon: 'success',
          timer: 2000,
          timerProgressBar: true,
        }).then(() => {
          this.isSubmitting = false;
          this.router.navigate(['/payroll-pay-run']);
        });
      },
      error: (err) => {
        console.error('Error:', err);
        Swal.fire({
          title: 'Error',
          text: 'Failed to post Pay Run. Please try again.',
          icon: 'error',
        });
      },
    });
  }

  getPayRunDetailsSummaryData() {
    this.route.params.subscribe((params) => {
      this.employeeId = +params['employeeId'];
    });
    this.payRunDetailService
      .getPayRunDetailsSummary(this.employeeId, this.payRunId)
      .subscribe({
        next: (data: any) => {
          this.payRunDetailsSummary = data;
          console.log(' pay run details summary : ', this.payRunDetailsSummary);
        },
        error: (err) => {
          console.error('Error fetching pay run detail summary data:', err);
        },
      });
  }

  
  generateSummary(): void {
    this.payPeriodId // pay period id passsed from the previous page
    this.payPeriod // pay period object extracted using the pay period id

    console.log("Pay period Id", this.payPeriodId);

  }

  previewPayRunSummary(): void {

    const payPeriodId = this.payPeriodId;

    this.isLoading = true;

    const entityId = +(localStorage.getItem('entityId') || '1040');

    this.payRunService.getPayRunSummaryReport(payPeriodId, entityId).subscribe(
      (data: any) => {
        const base64String = data.response;

        if (base64String) {
          //alert(base64String)
          this.loadPdfIntoIframe(base64String);
        } else {
          this.isLoading = false;
          alert('No summary data available for preview.');
        }
      },
      (error) => {
        this.isLoading = false;
        alert('Error loading summary report preview.');
      }
    );
  }

  private loadPdfIntoIframe(base64String: string): void {

    if (!this.payRunSummaryPreviewFrame || !this.payRunSummaryPreviewFrame.nativeElement) {
      console.error('Iframe not initialized.');
      this.isLoading = false;
      return;

    }
    let dataLoaded = false; // Flag to track if data was loaded successfully

    // Check if base64String is valid
    if (base64String && base64String.trim().length >= 50) { // Adjust the length check as needed
      const pdfData = 'data:application/pdf;base64,' + base64String;
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
      const iframe = this.payRunSummaryPreviewFrame.nativeElement;

      iframe.onload = () => {
        this.isLoading = false;
        dataLoaded = true; // Set flag to true when data loads
      };

      iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
    }

    // Check load status after a short delay
    setTimeout(() => {
      if (!dataLoaded) {
        this.isLoading = false;
        Swal.fire({
          title: 'No Data',
          text: 'No summary report data for preview.',
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff'
        });
      }
    }, 2000);

  }


  //payroll employee details report
  previewPayrollSummary(): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    const payPeriodId = this.payPeriodId;
    const payCalendarId = this.payCalendarId;

    this.isLoading = true;

    this.payRunDetailService.getPayrollSummaryReport(entityId, payCalendarId, payPeriodId).subscribe(
      (data: any) => {
        const base64String = data.response;

        if (base64String) {
          this.loadPdfIntoIframe1(base64String, this.reportPreviewFrame);

        } else {
          this.isLoading = false;
          Swal.fire('No Data', 'No payroll employee data available.', 'info');
        }
      },
      (error) => {
        this.isLoading = false;
        Swal.fire('Error', 'Failed to generate payroll summary report.', 'error');
      }
    );
  }

  private loadPdfIntoIframe1(base64String: string, iframeRef: ElementRef): void {
    if (!iframeRef || !iframeRef.nativeElement) {
      console.error('Iframe not initialized.');
      this.isLoading = false;
      return;
    }
  
    let dataLoaded = false;
  
    if (base64String && base64String.trim().length >= 50) {
      const pdfData = 'data:application/pdf;base64,' + base64String;
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
      const iframe = iframeRef.nativeElement;
  
      iframe.onload = () => {
        this.isLoading = false;
        dataLoaded = true;
      };
  
      iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
    }
  
    setTimeout(() => {
      if (!dataLoaded) {
        this.isLoading = false;
        Swal.fire('No Data', 'No employee data available for preview.', 'info');
      }
    }, 2000);
  }
  
  // employee exclude from pay run

  onCheckboxClick( employeeId: number, isExcluded: boolean): void {
    this.updateExclusion(employeeId, isExcluded); 
    console.log(" state : ", isExcluded);
    
  }
  
  updateExclusion(employeeId: number, isExcluded: boolean): void {
    this.route.params.subscribe((params) => {
      this.payRunId = +params['payRunId'];
    });
    this.payRunDetailService.updateEmployeeExclution(employeeId, this.payRunId, isExcluded).subscribe({
      next: (data: PayRunDetailsSummary) => {
        Swal.fire({
          title: 'Success',
          text: 'Employee status updated successfully',
          icon: 'success',
          timer: 2000,
          timerProgressBar: true,
        });
        this.getAllPayProcessData();
        this.getPayRun();
      },
      error: (err) => {
        console.error('Error posting Pay Run:', err);
      },
    });
    console.log(`Employee ${employeeId} exclusion status: ${isExcluded}`);
  }



//Bank report
previewBankPaymentsReport(): void {
  const entityId = +(localStorage.getItem('entityId') || '');
  const payPeriodId = this.payPeriodId;

  this.isLoading = true; 

 
  this.payRunDetailService.getBankPaymentsReport(payPeriodId, entityId).subscribe(
    (data: any) => {
      const base64String = data.response;

      if (base64String) {
        this.loadBankReportIntoIframe(base64String, this.bankReportPreviewFrame);
      } else {
        this.isLoading = false;
        Swal.fire('No Data', 'No bank payment data available.', 'info');
      }
    },
    (error) => {
      this.isLoading = false;
      Swal.fire('Error', 'Failed to generate bank payments report.', 'error');
    }
  );
}




private loadBankReportIntoIframe(base64String: string, iframeRef: ElementRef): void {
  if (!iframeRef || !iframeRef.nativeElement) {
    console.error('Iframe not initialized.');
    this.isLoading = false;
    return;
  }

  let dataLoaded = false;

  if (base64String && base64String.trim().length >= 50) {
    const pdfData = 'data:application/pdf;base64,' + base64String;
    const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
    const iframe = iframeRef.nativeElement;

    iframe.onload = () => {
      this.isLoading = false;
      dataLoaded = true;
    };

    iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
  }

  setTimeout(() => {
    if (!dataLoaded) {
      this.isLoading = false;
      Swal.fire('No Data', 'No bank payments data available for preview.', 'info');
    }
  }, 2000);
}

//Bank Journal report

previewBankJournalReport(): void {
  const entityId = +(localStorage.getItem('entityId') || '');
  const payPeriodId = this.payPeriodId;

  this.isLoading = true; 

 
  this.payRunDetailService.getBankJournalReport(payPeriodId, entityId).subscribe(
    (data: any) => {
      const base64String = data.response;

      if (base64String) {
        this.loadBankJournalIntoIframe(base64String, this.bankReportPreviewFrame);
      } else {
        this.isLoading = false;
        Swal.fire('No Data', 'No bank Journal data available.', 'info');
      }
    },
    (error) => {
      this.isLoading = false;
      Swal.fire('Error', 'Failed to generate bank Journal report.', 'error');
    }
  );
}

private loadBankJournalIntoIframe(base64String: string, iframeRef: ElementRef): void {
  if (!iframeRef || !iframeRef.nativeElement) {
    console.error('Iframe not initialized.');
    this.isLoading = false;
    return;
  }

  let dataLoaded = false;

  if (base64String && base64String.trim().length >= 50) {
    const pdfData = 'data:application/pdf;base64,' + base64String;
    const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
    const iframe = iframeRef.nativeElement;

    iframe.onload = () => {
      this.isLoading = false;
      dataLoaded = true;
    };

    iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
  }

  setTimeout(() => {
    if (!dataLoaded) {
      this.isLoading = false;
      Swal.fire('No Data', 'No bank Journal data available for preview.', 'info');
    }
  }, 2000);
}

// get pay run master
getPayRun() {
  this.route.params.subscribe((params) => {
    this.payRunId = +params['payRunId'];
  });
  this.payRunService.getPayRunById(this.payRunId).subscribe({
    next: (data: PayRunMaster) => {
      this.payRun = data;
    }
  })
}

unScheduledEmployeeDropDownList: EmployeeEmployment[] =[]; 

fetchUnscheduledEmployees() : void {
  this.payRunService.getEmployeesByPayCalendar(this.entityId, this.payCalendarId).subscribe({
      next: (data: EmployeeEmployment[]) => {
        this.unScheduledEmployeeDropDownList = data.filter(emp => {
        return !this.payRunDetailsSummaryList.some(
          summary => summary.employee?.employeeId === emp.employee.employeeId
        );
      });
        
      },
      error: (err) => {
        console.error('Error fetching PayPeriods:', err);
      },
    });
}

onAddEmployee(): void {
  const selectedEmployee = this.unScheduledEmployeeDropDownList.find(
    emp => emp.employee.employeeId === +this.selectedEmployeeId
  );

  if (selectedEmployee) {
    this.payRunDetailService.addUnscheduledEmployee(selectedEmployee, this.payRunId).subscribe({
      next: (data: PayRunDetailsSummary) => {

        Swal.fire({
          icon: 'success',
          title: 'Employee Added',
          text: `${selectedEmployee.employee.firstName} ${selectedEmployee.employee.lastName} was added successfully.`,
          timer: 2000,
          showConfirmButton: true
        });

        this.selectedEmployeeId = 0;
        this.fetchUnscheduledEmployees();
        this.getUnscheduledEmployees();
        this.getPayRun();
      },
      error: (err) => {
        console.error('Error adding employee:', err);
        Swal.fire({
          icon: 'error',
          title: 'Failed to Add Employee',
          text: 'An error occurred while adding the employee.',
        });
      },
    });
  } else {
    Swal.fire({
      icon: 'warning',
      title: 'No Employee Selected',
      text: 'Please select an employee to add.',
    });
  }
}


clearSelection() {
    this.selectedEmployeeId = 0;
  }

  viewDetails(
    payCalendarId: number,
    payPeriodId: number,
    payRunId: number
  ): void {
    window.location.assign(
      `/payRun-user-details/${payCalendarId}/${payPeriodId}/${payRunId}`
    );
  }


}
