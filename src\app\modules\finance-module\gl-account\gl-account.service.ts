import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { CoaHeaders, CoaLedgerAccount } from './gl-account';

@Injectable({
  providedIn: 'root',
})
export class GlAccountService {
  private readonly baseURL = environment.financeApiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  getCoaHeaders(): Observable<CoaHeaders[]> {
    return this.request('GET', '/coa-headers/list', {}, {});
  }

  getCoaLedgerAccount(): Observable<CoaLedgerAccount[]> {
    return this.request('GET', '/coa-ledger-accounts/list', {}, {});
  }

  addCoaLedgerAccount(
    glAccount: CoaLedgerAccount
  ): Observable<CoaLedgerAccount> {
    return this.request('POST', '/coa-ledger-accounts/save', glAccount);
  }

  updateGlAccount(
    id: number,
    glAccount: CoaLedgerAccount
  ): Observable<CoaLedgerAccount> {
    return this.request('PUT', `/coa-ledger-accounts/update/${id}`, glAccount);
  }

  deleteGlAccount(id: number): Observable<void> {
    return this.request('DELETE', `/coa-ledger-accounts/delete/${id}`, null);
  }

  getCoaLedgerAccountListByEntity(entityId: any): Observable<CoaLedgerAccount[]> {
      return this.request('GET', '/coa-ledger-accounts/getCoaLedgerAccountListByEntity', {}, { entityId: entityId });
    }

    getCoaHeaderListByEntity(entityId: any): Observable<CoaHeaders[]> {
      return this.request('GET', '/coa-headers/getCoaHeaderListByEntity', {}, { entityId: entityId });
    }

}
