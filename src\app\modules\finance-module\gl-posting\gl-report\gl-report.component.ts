import { Component } from '@angular/core';
import { JournalVoucherService } from '../../journal-voucher/journal-voucher.service';
import { Router } from '@angular/router';
import { GlPostingHead, GlPostingDetails } from '../../journal-voucher/journal-voucher';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
@Component({
  selector: 'app-gl-report',
  templateUrl: './gl-report.component.html',
  styleUrls: ['./gl-report.component.css']
})
export class GlReportComponent {
  searchTerm: string = ''; 
  quotes: GlPostingHead[] = []; 
  quotesDetails: GlPostingDetails[] = []; 
  processedData: any[] = []; 
  filteredRecords: any[] = [];
  selectedQuotesDetails: GlPostingDetails[]=[];
  activeTab = 'all';
  startDate: string | null = null; 
  endDate: string | null = null; 
  expandedRowId: number | null = null;
  isLoading = false;

  constructor(
    private journalVoucherService: JournalVoucherService,
    private router: Router,
  ) {}

  ngOnInit() {
    this.fetchQuotesDetails();
  }

  fetchQuotesDetails() {
    
   const entityId = +((localStorage.getItem('entityId')) + "");
    this.journalVoucherService.getGlPostingDetailsList(entityId).subscribe(
      (data: GlPostingDetails[]) => {
        this.quotesDetails = data;
        console.log('Gl Posting Details:', this.quotesDetails);
        this.processedData = this.aggregateData(this.quotesDetails);
        this.filteredRecords = [...this.processedData]; 
      },
      (error) => console.error('Error fetching quotations:', error)
    );
  }

  aggregateData(data: any[]): any[]  {
    const summary = new Map<string, { accountName: string, accountCode: string, totalDr: number, totalCr: number }>();
    this.quotesDetails.forEach((detail) => {
      const accountCode = detail.coaLedgerAccount?.ledgerAccountCode;
      const accountName = detail.coaLedgerAccount?.ledgerAccountName || "Unknown Account";
      if (!accountCode) return;

      const dr = detail.drAmount || 0; 
      const cr = detail.crAmount || 0;

      if (!summary.has(accountCode)) {
        summary.set(accountCode, { accountCode, accountName, totalDr: 0, totalCr: 0 });
      }

      const entry = summary.get(accountCode);
      if (entry) {
        entry.totalDr += dr;
        entry.totalCr += cr;
      }
    });

    return Array.from(summary.values());
  }

  onSearchChange() {
    this.filterQuotes();
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.filterQuotes();
  }

  filterQuotes(): void {
    this.isLoading = true;
    // Step 1: Filter records based on the search term and date range
    this.filteredRecords = this.processedData.filter(record => {
      const matchesSearch = this.searchTerm
        ? record.accountCode.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          record.accountName.toLowerCase().includes(this.searchTerm.toLowerCase())
        : true;
  
        const matchesDates = this.startDate 
        ? (new Date(record.date) >= new Date(this.startDate) && (this.endDate ? new Date(record.date) <= new Date(this.endDate) : true))
        : true;
          
      return matchesSearch && matchesDates;
    });
  
    // Step 2: Recalculate the aggregate data based on the filtered records
    this.processedData = this.aggregateData(this.filteredRecords);
  
    console.log("Filtered Records:", this.filteredRecords);
    console.log("Aggregated Data after filtering by date:", this.processedData);
    this.isLoading = false;
  }
  
  resetFilters() {
    this.searchTerm = '';
    this.activeTab = 'all';
    this.startDate = null;
    this.endDate = null;
    this.filteredRecords = [...this.processedData]
  }

  openDetailsPage(accountCode: string | null) {
    if (accountCode) {
      this.router.navigate(['/gl-account-details-by-supplier'], {
        queryParams: { selectedAccountCode: JSON.stringify(accountCode) }
      });
    }
  }
  exportToExcel(): void {
    if (!this.filteredRecords || this.filteredRecords.length === 0) {
      alert('No data available to export.');
      return;
    }

    const exportData = this.filteredRecords.map(data => ({
      'Account Code': data.accountCode,
      'Account Name': data.accountName,
      'Total Debit ($)': `$${parseFloat(data.totalDr).toFixed(2)}`, // Add dollar sign and format to 2 decimal places
      'Total Credit ($)': `$${parseFloat(data.totalCr).toFixed(2)}`  // Add dollar sign and format to 2 decimal places
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'GL Postings');

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    this.saveAsExcelFile(excelBuffer, `GL_Postings_${timestamp}`);
}

private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    });

    saveAs(data, `${fileName}.xlsx`);
}

}
