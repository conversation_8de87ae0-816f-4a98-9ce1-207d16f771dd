.popup-message {
    position: fixed;
    bottom: 100px;
    right: 50px;
    background-color: white;
    border: 1px solid #ccc;
    padding: 12px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex-direction: column;
    align-items: flex-start;
    display: flex;
    max-width: 250px;
    align-items: center;
  }
  
  .close-btn {
    position: absolute;
    top: -4px;
    right: -2px;
    background: transparent;
    border: none;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    color: #151313;
  }
  .close-btn:hover {
    color: #b91b1b;
  }
  
  
  .chat-container {
    position: fixed;
    bottom: 100px;
    right: 20px;
    z-index: 1000;
  }
  
  .chat-box {
    width: 400px;
    height: 500px;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    position: relative;
  }
  
  .chat-header {
    display: flex;
    align-items: center;
    padding: 5px;
    background-color: hsl(201, 95%, 85%);
    border-bottom: 2px solid #0e0d0d;
    cursor: move; 
  }
  
  .chat-header-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
    margin-left: 10px;
  }
  
  .chat-title {
    font-weight: bold;
    font-size: 1.35em;
    color: #371fd9;
  }
  .close-btn-2 {
    position: absolute;
    top: -2px;
    right: 2px;
    background: transparent;
    border: none;
    font-size: 15px;
    font-weight: bold;
    cursor: pointer;
    margin-left: auto;
    color: #151313;
  }
  .text {
    background-color: #bde5e0;
    font-weight: bold;
    color: rgb(12, 12, 13);
    padding: 10px;
    border-radius: 10px;;
    margin: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  }
  .text2 {
    font-weight: 100;
    color: rgb(12, 12, 13);
    background-color: #bde5e0;
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    margin-bottom: 10px;
  }
  .text3 {
    font-weight: 500;
    color: rgb(12, 12, 13);
    margin-left: 20px;
    margin-right: 50px;
  }
  .text4 {
    font-weight: 500;
    color: rgb(12, 12, 13);
    margin-left: 20px;
    margin-right: 50px;
    margin-bottom: 20px;
  }
  .img-assistant {
    width: 25px; 
    height: 25px; 
    vertical-align: middle;
    margin-right: 5px; 
    margin-bottom: 5px;
  }
  
  .assistant-label {
    font-weight: bold;
    color: black;
  }
  .sent strong {
    font-weight: bold;
  }
  
  .conversation {
    flex-grow: 1;
    overflow-y: auto;
    margin: 10px;
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    scroll-behavior: smooth;
  }
  
  .conversation p {
    position: relative;
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    margin-bottom: 10px;
    max-width: 100%;
    word-wrap: break-word;
    background-color: #b9eff8;
  }
  .conversation p.sent {
    background-color: #86dcbb; 
    color: black;
    align-self: flex-end;
  }
  .conversation p.sent::before {
    content: "";
    position: absolute;
    top: 10px;
    right: -10px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-left: 10px solid #86dcbb;
    border-bottom: 10px solid transparent;
    left: auto;
  }
  
  input {
    width: calc(100% - 53px);
    padding: 10px;
    margin: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  
  .send-icon {
    background-color: rgb(239, 234, 234);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  
  .img-send {
    width: 15px;
    height: 15px;
    position: absolute;
    bottom: 18px;
    right: 6px;
    width: 30px;
    height: 30px;
    mix-blend-mode: multiply;
  }
  
  .chat-icon {
    position: fixed;
    bottom: 40px;
    right: 50px;
    width: 20px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    mix-blend-mode: multiply;
  }
  
  .img {
    width: 80px;
    height: 80px;
  }
  
  .loading-dots {
    display: flex;
    justify-content: center;
    margin: 20px;
  }
  
  .loading-dots span {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin: 0 4px;
    background-color: #a917e8;
    border-radius: 50%;
    animation: bounce 1.2s infinite ease-in-out;
  }
  
  .loading-dots span:nth-child(2) {
    animation-delay: -0.4s;
  }
  
  .loading-dots span:nth-child(3) {
    animation-delay: -0.8s;
  }
  
  @keyframes bounce {
    0%, 80%, 100% {
      transform: scale(0);
    }
    40% {
      transform: scale(1);
    }
  }
  
  
  