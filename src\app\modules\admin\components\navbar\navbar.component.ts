import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Entity, EntityTradingName } from 'src/app/modules/entity/entity';
import { EntityService } from 'src/app/modules/entity/entity.service';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css'],
})
export class NavbarComponent implements OnInit {
  isDropdownOpen = false;
  isDropdownOpen1 = false;
  entity: Entity = new Entity();
  entityTradingName: EntityTradingName = new EntityTradingName();
  entityId: number = 0;
  logoUrl: string | null = null;
  userRole: string = '';
  constructor(private router: Router, private entityService: EntityService) {}

  ngOnInit(): void {
    this.entityId = JSON.parse(localStorage.getItem('entityId') || '[]');
    this.loadEntityData();

   const userStr = localStorage.getItem('user');
    if (userStr) {
    const user = JSON.parse(userStr);
    this.userRole = user.roleName; // e.g., "Free", "Premium"
  }
  }

  @ViewChild('dropdownRef') dropdownRef!: ElementRef;

  closeDropdown() {
    this.isDropdownOpen1 = false;
  }

  // Listen for clicks on the whole document
  @HostListener('document:click', ['$event'])
  handleClickOutside(event: MouseEvent) {
    if (
      this.dropdownRef && !this.dropdownRef.nativeElement.contains(event.target)
    ) {
      this.closeDropdown();
    }
  }

  private loadEntityData(): void {
    this.getEntity();
    // this.getEntityTradingName();
  }

  private getEntity(): void {
    this.entityService.getBusinessEntityByIdCached(this.entityId).subscribe((data) => {
      this.entity = data;
      this.logoUrl = this.entity.logo ? `data:image/png;base64,${this.entity.logo}` : null;
    });
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
    if (this.isDropdownOpen) {
      this.isDropdownOpen1 = false; // Close Reports dropdown if Subscription is opened
    }
  }

  toggleDropdown1(): void {
    this.isDropdownOpen1 = !this.isDropdownOpen1;

    if (this.isDropdownOpen1) {
      this.isDropdownOpen = false; // Close Subscription dropdown if Reports is opened
    }
  }

  onStatementChange(event: Event): void {
    const selectedStatement = (event.target as HTMLSelectElement).value;
    if (selectedStatement === 'customer') {
      this.navigateCustomerStatementReport();
    } else if (selectedStatement === 'account') {
      this.navigateAccountsStatementReport();
    }
    this.isDropdownOpen1 = false;
  }

  navigateDashboard(): void {
    this.router.navigate(['/dashboard']);
  }

  navigateQuotation(): void {
    this.router.navigate(['/quotation']);
  }

  navigateInvoice(): void {
    this.router.navigate(['/invoice']);
  }

  navigateCreditNote(): void {
    this.router.navigate(['/credit-note']);
  }

  navigateRecordPayment(): void {
    this.router.navigate(['/payment-receipt']);
  }

  navigateSalesReports(): void {
    this.router.navigate(['/sales-reports']);
  }

  navigateBankReconciliation(): void {
    this.router.navigate(['/bank-reconciliation']);
  }

  navigateManageSubscription(): void {
    this.router.navigate(['/manage-subscription']);
    this.isDropdownOpen = false;
  }

  navigateUserInvitation(): void {
    this.router.navigate(['/invite-user']);
    this.isDropdownOpen = false;
  }

  navigateUserRegistration(): void {
    this.router.navigate(['/user-registration']);
    this.isDropdownOpen = false;
  }

  navigateQuotationReport(): void {
    this.router.navigate(['/quotation-report']);
    this.isDropdownOpen1 = false;
  }

  navigateInvoiceReport(): void {
    this.router.navigate(['/invoice-report']);
    this.isDropdownOpen1 = false;
  }

  navigateCreditNoteReport(): void {
    this.router.navigate(['/credit-note-report']);
    this.isDropdownOpen1 = false;
  }

  navigatePaymentReport(): void {
    this.router.navigate(['/payment-receipt-report']);
    this.isDropdownOpen1 = false;
  }

  navigateInvoiceCashReport(): void {
    this.router.navigate(['/invoice-cash-report']);
    this.isDropdownOpen1 = false;
  }

  navigateCustomerStatementReport(): void {
    this.router.navigate(['/customer-statement']);
  }

  navigateAccountsStatementReport(): void {
    this.router.navigate(['/account-statement']);
  }

  navigateBusinessEntityEdit(): void {
    this.router.navigate(['/business-entity-edit']);
  }
}
