<div class="container vh-100 justify-content-center align-content-center">
  <div class="row">
    <div class="col-12 bottom-border g-0">
      <div class="float-start">
        <button
          class="tab-view-button"
          [ngClass]="{ 'active-button': activeTab === 'spend' }"
          (click)="activeTab = 'spend'"
        >
          Spend Money Rule
        </button>
        <button
          class="tab-view-button"
          [ngClass]="{ 'active-button': activeTab === 'receive' }"
          (click)="activeTab = 'receive'"
        >
          Receive Money Rule
        </button>
      </div>
    </div>
    <div
      class="col-12 bg-white p-5"
      style="border: 1px solid lightgray; border-top: 0"
      [ngSwitch]="activeTab"
    >
      <div *ngSwitchCase="'spend'">
        <ng-container>
          <ng-container *ngTemplateOutlet="spendRule"></ng-container>
        </ng-container>
      </div>
      <div *ngSwitchCase="'receive'">
        <ng-container>
          <ng-container *ngTemplateOutlet="receiveRule"></ng-container>
        </ng-container>
      </div>
      <div *ngSwitchCase="'transfer'">
        <ng-container>
          <ng-container
            *ngTemplateOutlet="noRules; context: { $implicit: 'Transfer' }"
          ></ng-container>
        </ng-container>
      </div>
    </div>
  </div>
</div>

<ng-template #noRules let-type>
  <p>
    There are no <span style="font-weight: 600">{{ type }} Money Rules</span> to
    display.
    <a href="#">Click here to add one.</a>
  </p>
</ng-template>

<ng-template #spendRule>
  <form
    [formGroup]="spendMoneyRuleForm"
    (submit)="spendMoneyFormSubmit()"
    (reset)="spendMoneyRuleForm.reset()"
  >
    <div class="col-12">
      <div class="row mb-4">
        <div class="col-5">
          <h4 class="px-2 fw-bold">
            Create
            <span class="bg-danger px-2 py-1 rounded-2 text-white">Spend</span>
            Money Rule
          </h4>
        </div>
        <div class="col-7">
          <div class="row">
            <div class="col-6">
              <p class="q-head px-2">Bank Details</p>
              <div class="px-2 mt-2">
                <input
                  disabled
                  type="text"
                  value="{{ selectedBankAccount.bankName }} - {{
                    selectedBankAccount.accountNumber
                  }}"
                  class="custom-input"
                />
              </div>
            </div>
            <div class="col-6">
              <p class="q-head px-2">Bank Rule Name</p>
              <div class="px-2 mt-2">
                <input
                  type="text"
                  placeholder="Specify name for the rule"
                  class="custom-input"
                  formControlName="spendRuleName"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <p class="q-head px-2">Define the FilterTerms</p>
        </div>
        <div
          class="col-12 mt-1 gx-0"
          style="margin-left: 8px"
          formArrayName="spendConditions"
        >
          <table>
            <colgroup>
              <col width="25%" />
              <col width="25%" />
              <col width="40%" />
              <col width="10%" />
            </colgroup>

            <tr
              *ngFor="let group of spendConditions.controls; let i = index"
              [formGroupName]="i"
            >
              <td>
                <select
                  class="custom-select"
                  formControlName="spendMatchField"
                  [ngClass]="{
                    'placeholder-style':
                      group.get('spendMatchField')?.value === null
                  }"
                >
                  <option
                    disabled
                    [value]="null"
                    class="fst-italic text-black-50"
                  >
                    Match with
                  </option>
                  <option *ngFor="let field of matchFields" [value]="field">
                    {{ field }}
                  </option>
                </select>
              </td>

              <td>
                <select
                  class="custom-select"
                  formControlName="spendFilterBy"
                  [ngClass]="{
                    'placeholder-style':
                      group.get('spendFilterBy')?.value === null
                  }"
                >
                  <option
                    disabled
                    [value]="null"
                    class="fst-italic text-black-50"
                  >
                    Filter By
                  </option>
                  <option
                    *ngFor="let option of filterByOptions"
                    [value]="option"
                  >
                    {{ option }}
                  </option>
                </select>
              </td>

              <td>
                <input
                  type="text"
                  class="custom-input"
                  formControlName="spendFilterText"
                />
              </td>

              <td>
                <button
                  type="button"
                  class="btn btn-sm bg-danger-subtle"
                  (click)="removeSpendCondition(i)"
                  [disabled]="spendConditions.length === 1"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </table>

          <button
            class="primary-button fw-normal mt-2"
            style="margin-left: 12px"
            type="button"
            (click)="addSpendCondition()"
          >
            <i class="fa fa-plus fw-normal"></i>&nbsp;&nbsp;Add New Condition
          </button>
        </div>
      </div>
      <hr class="p-0 my-4" />
      <div class="row px-2">
        <div class="col-12">
          <div class="row">
            <div class="col-12 d-flex">
              <p class="q-head">Set GL Accounts</p>
            </div>
            <div class="col-12">
              <table>
                <colgroup>
                  <col width="40%" />
                  <col width="30%" />
                  <col width="20%" />
                  <col width="10%" />
                </colgroup>
                <tbody formArrayName="spendGLAccounts">
                  <tr
                    *ngFor="
                      let group of spendGLAccounts.controls;
                      let i = index
                    "
                    [formGroupName]="i"
                  >
                    <td class="ps-0">
                      <input
                        type="text"
                        formControlName="glAccountDescription"
                        [ngClass]="{
                          'placeholder-style':
                            spendMoneyRuleForm.get('glAccountDescription')
                              ?.value === ''
                        }"
                        placeholder="Supplier or Description"
                        [matAutocomplete]="auto"
                        class="custom-input"
                      />
                      <mat-autocomplete #auto="matAutocomplete">
                        <mat-option
                          *ngFor="let supplier of supplierList"
                          [value]="supplier.bpName"
                          >{{ supplier.bpName }}</mat-option
                        >
                      </mat-autocomplete>
                    </td>
                    <td>
                      <select
                        class="custom-select"
                        formControlName="glAccountId"
                        [ngClass]="{
                          'placeholder-style':
                            group.get('glAccountId')?.value === null
                        }"
                      >
                        <option
                          class="fst-italic text-black-50"
                          [value]="null"
                          disabled
                        >
                          Select account
                        </option>
                        <option
                          *ngFor="let glAccount of expenseLedgerAccountsList"
                          [value]="glAccount.coaLedgerAccountId"
                        >
                          {{ glAccount.ledgerAccountName }}
                        </option>
                      </select>
                    </td>
                    <td class="d-inline-flex align-items-center">
                      <input
                        min="0.1"
                        type="number"
                        formControlName="percentage"
                        class="custom-input text-end"
                      />
                      &nbsp;&nbsp;%
                    </td>
                    <td>
                      <button
                        type="button"
                        class="btn btn-sm bg-danger-subtle"
                        (click)="removeSpendGLAccount(i)"
                        [disabled]="spendGLAccounts.length === 1"
                      >
                        <i class="fa fa-trash"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr>
                    <td class="py-0 px-0">
                      <button
                        class="primary-button fw-normal"
                        style="margin-left: 0px"
                        type="button"
                        (click)="addSpendGLAccount()"
                        [disabled]="totalSpendGLPercentage >= 100"
                      >
                        <i class="fa fa-plus fw-normal"></i>&nbsp;&nbsp;Add New
                        Condition
                      </button>
                    </td>
                    <td></td>
                    <td class="text-end py-0">
                      <p class="total-percentage-cell py-2">
                        {{ totalSpendGLPercentage }} &nbsp;&nbsp;&nbsp;%
                      </p>
                    </td>
                    <td></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>
      <hr class="p-0 my-4" />
      <div class="row">
        <div class="d-flex justify-content-end">
          <button class="secondary-button" type="reset">Reset</button>
          <button
            class="primary-button"
            [disabled]="
              spendMoneyRuleForm.invalid ||
              totalSpendGLPercentage > 100 ||
              totalSpendGLPercentage <= 0
            "
            type="submit"
          >
            Save Spend Rule
          </button>
        </div>
      </div>
    </div>
  </form>
</ng-template>

<ng-template #receiveRule>
  <form
    [formGroup]="receiveMoneyRuleForm"
    (submit)="receiveMoneyFormSubmit()"
    (reset)="receiveMoneyRuleForm.reset()"
  >
    <div class="col-12">
      <div class="row mb-4">
        <div class="col-12">
          <div class="row">
            <div class="col-5">
              <h4 class="px-2 fw-bold">
                Create
                <span class="bg-success px-2 py-1 rounded-2 text-white"
                  >Receive</span
                >
                Money Rule
              </h4>
            </div>
            <div class="col-7">
              <div class="row">
                <div class="col-6">
                  <p class="q-head px-2">Bank Details</p>
                  <div class="px-2 mt-2">
                    <input
                      disabled
                      type="text"
                      value="{{ selectedBankAccount.bankName }} - {{
                        selectedBankAccount.accountNumber
                      }}"
                      class="custom-input"
                    />
                  </div>
                </div>
                <div class="col-6">
                  <p class="q-head px-2">Bank Rule Name</p>
                  <div class="px-2 mt-2">
                    <input
                      type="text"
                      placeholder="Specify name for the rule"
                      class="custom-input"
                      formControlName="receiveRuleName"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <p class="q-head px-2">Define the FilterTerms</p>
        </div>
        <div
          class="col-12 mt-1 gx-0"
          style="margin-left: 8px"
          formArrayName="receiveConditions"
        >
          <table>
            <colgroup>
              <col width="25%" />
              <col width="25%" />
              <col width="40%" />
              <col width="10%" />
            </colgroup>
            <tr
              *ngFor="let group of receiveConditions.controls; let i = index"
              [formGroupName]="i"
            >
              <td>
                <select
                  class="custom-select"
                  formControlName="receiveMatchField"
                  [ngClass]="{
                    'placeholder-style':
                      group.get('receiveMatchField')?.value === null
                  }"
                >
                  <option
                    disabled
                    [value]="null"
                    class="fst-italic text-black-50"
                  >
                    Match with
                  </option>
                  <option *ngFor="let field of matchFields" [value]="field">
                    {{ field }}
                  </option>
                </select>
              </td>
              <td>
                <select
                  class="custom-select"
                  formControlName="receiveFilterBy"
                  [ngClass]="{
                    'placeholder-style':
                      group.get('receiveFilterBy')?.value === null
                  }"
                >
                  <option
                    disabled
                    [value]="null"
                    class="fst-italic text-black-50"
                  >
                    Filter By
                  </option>
                  <option
                    *ngFor="let option of filterByOptions"
                    [value]="option"
                  >
                    {{ option }}
                  </option>
                </select>
              </td>
              <td>
                <input
                  type="text"
                  class="custom-input"
                  formControlName="receiveFilterText"
                />
              </td>
              <td>
                <button
                  type="button"
                  class="btn btn-sm bg-danger-subtle"
                  (click)="removeReceiveCondition(i)"
                  [disabled]="receiveConditions.length === 1"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </table>
          <button
            class="primary-button fw-normal mt-2"
            style="margin-left: 12px"
            type="button"
            (click)="addReceiveCondition()"
          >
            <i class="fa fa-plus fw-normal"></i>&nbsp;&nbsp;Add New Condition
          </button>
        </div>
      </div>

      <hr class="p-0 my-4" />

      <div class="row px-2">
        <div class="col-12">
          <div class="row">
            <div class="col-12 d-flex">
              <p class="q-head">Set GL Accounts</p>
            </div>
            <div class="col-12">
              <table>
                <colgroup>
                  <col width="40%" />
                  <col width="30%" />
                  <col width="20%" />
                  <col width="10%" />
                </colgroup>
                <tbody formArrayName="receiveGLAccounts">
                  <tr
                    *ngFor="
                      let group of receiveGLAccounts.controls;
                      let i = index
                    "
                    [formGroupName]="i"
                  >
                    <td class="ps-0">
                      <input
                        type="text"
                        formControlName="glAccountDescription"
                        [ngClass]="{
                          'placeholder-style':
                            receiveMoneyRuleForm.get('glAccountDescription')
                              ?.value === null
                        }"
                        placeholder="Customer or Description"
                        [matAutocomplete]="auto"
                        class="custom-input"
                      />
                      <mat-autocomplete #auto="matAutocomplete">
                        <mat-option
                          *ngFor="let customer of customerList"
                          [value]="customer.bpName"
                          >{{ customer.bpName }}</mat-option
                        >
                      </mat-autocomplete>
                    </td>
                    <td>
                      <select
                        class="custom-select"
                        formControlName="glAccountId"
                        [ngClass]="{
                          'placeholder-style':
                            group.get('glAccountId')?.value === null
                        }"
                      >
                        <option
                          class="fst-italic text-black-50"
                          [value]="null"
                          disabled
                        >
                          Select account
                        </option>
                        <option
                          *ngFor="let glAccount of incomeLedgerAccountsList"
                          [value]="glAccount.coaLedgerAccountId"
                        >
                          {{ glAccount.ledgerAccountName }}
                        </option>
                      </select>
                    </td>
                    <td class="d-inline-flex align-items-center">
                      <input
                        min="0.1"
                        type="number"
                        formControlName="percentage"
                        class="custom-input text-end"
                      />
                      &nbsp;&nbsp;%
                    </td>
                    <td>
                      <button
                        type="button"
                        class="btn btn-sm bg-danger-subtle"
                        (click)="removeReceiveGLAccount(i)"
                        [disabled]="receiveGLAccounts.length === 1"
                      >
                        <i class="fa fa-trash"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr>
                    <td class="py-0 px-0">
                      <button
                        class="primary-button fw-normal"
                        style="margin-left: 0px"
                        type="button"
                        (click)="addReceiveGLAccount()"
                        [disabled]="totalReceiveGLPercentage >= 100"
                      >
                        <i class="fa fa-plus fw-normal"></i>&nbsp;&nbsp;Add New
                        Condition
                      </button>
                    </td>
                    <td></td>
                    <td class="text-end py-0">
                      <p class="total-percentage-cell py-2">
                        {{ totalReceiveGLPercentage }} &nbsp;&nbsp;&nbsp;%
                      </p>
                    </td>
                    <td></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>

      <hr class="p-0 my-4" />
      <div class="row">
        <div class="d-flex justify-content-end">
          <button class="secondary-button" type="reset">Reset</button>
          <button
            class="primary-button"
            [disabled]="
              receiveMoneyRuleForm.invalid ||
              totalReceiveGLPercentage > 100 ||
              totalReceiveGLPercentage <= 0
            "
            type="submit"
          >
            Save Receive Rule
          </button>
        </div>
      </div>
    </div>
  </form>
</ng-template>
