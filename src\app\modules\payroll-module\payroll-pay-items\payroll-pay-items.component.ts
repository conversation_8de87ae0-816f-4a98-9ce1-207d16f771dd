import { Component, OnInit } from '@angular/core';
import { start } from '@popperjs/core';
import {
  Deduction,
  Earning,
  PayItemType,
  Reimbursement,
  EarningCategory,
  PayrollConfig
} from '../payroll-settings/payroll-setting';
import { EarningService } from '../payroll-settings/services/earning.service';
import { ReimbursementsService } from '../payroll-settings/services/reimbursements.service';
import { DeductionService } from '../payroll-settings/services/deduction.service';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { GlAccount } from '../../finance-module/gl-account/gl-account';
import { LeaveCategory, LeaveType, NewDeduction, NewEarning, NewReimbursement } from '../payroll-settings/empolyee/employee';
import { LeaveService } from '../payroll-settings/services/leave.service';
import { GlAccountService } from '../payroll-settings/services/gl-account.service';
import { BeamServiceService } from '../payroll-settings/services/beam-service.service';
import { PayrollPayrollSettingsService } from '../payroll-pay-run/payroll-payroll-settings/payroll-payroll-settings.service';
import { EmployeeService } from '../payroll-settings/services/employee.service';


@Component({
  selector: 'app-payroll-pay-items',
  templateUrl: './payroll-pay-items.component.html',
  styleUrls: ['./payroll-pay-items.component.css'],
})
export class PayrollPayItemsComponent implements OnInit {
  earnings: Earning[] = [];
  PayitemTypes: PayItemType[] = [];
  earning: Earning = new Earning();
  deductions: Deduction[] = [];
  glAccounts: GlAccount[] = [];
  payrollConfig: PayrollConfig | null = null;
  deduction: Deduction = new Deduction();
  reimbursements: Reimbursement[] = [];
  reimbursement: Reimbursement = new Reimbursement();
  earningsPage = 1;
  deductionsPage = 1;
  reimbursementsPage = 1;
  leavesPage = 1;
  leaveTwoPage = 1;
  leave: LeaveType[] =[];
  editingLeave: LeaveType = new LeaveType();
  earningCategories: EarningCategory[] = [];
  buttonStates: Map<number, boolean> = new Map<number, boolean>();
  buttonStates2: Map<number, boolean> = new Map<number, boolean>();
  buttonStates3: Map<number, boolean> = new Map<number, boolean>();
  paymentTypes: string[] = [ 'Rate', 'Fixed Amount'];
  ReimbursementsType: string[] = ['Office Supplies', 'Tarking Expenses', 'Travelling Expenses', 'Other'];
  deductionCategory: string[] = ['Pre Tax', 'Post Tax'];
  earningTypes = ['Ordinary Hours', 'Overtime', 'Allowance', 'Bonus'];

  emp_earnings: { description: string; amount: number }[] = [];
  totalEarningAmount = 0;
  selectedEarningType: string | null = null;

  emp_leave: { description: string; amount: number }[] = [];
  totalLeaveAmount = 0;
  selectedLeaveType: string | null = null;

  emp_reimbursement: { description: string; amount: number }[] = [];
  totalReimbursemenAmount = 0;
  selectedReimbursemenType: string | null = null;

  emp_deduction: { description: string; amount: number }[] = [
    { description: 'Ordinary Hours', amount: 40000 },
  ];
  totalDeduction = 40000;
  // earningCategory = [
  //   'Regular Earnings',
  //   'Overtime Earnings',
  //   'Commission',
  //   'Bonus',
  //   'Tips',
  //   'Back Pay',
  //   'Other Earnings',
  //   'Lump sums',
  // ];

  
  
  constructor(
    private earningService: EarningService,
    private router: Router,
    private deductionService: DeductionService,
    private reimbursementsService: ReimbursementsService,
    private leaveService: LeaveService,
    private glAccountService: GlAccountService,
    private beamServiceService: BeamServiceService,
    private payrollpayrollSettingService: PayrollPayrollSettingsService,
    private employeeService: EmployeeService
   
  ) {}

  ngOnInit(): void {
    this.getEarnings();
    this.getDeductions();
    this.getReimbursement();
    this.getPayrollConfig();
    this.getGlAccounts();
    this.getLeave();
    this.getEarningCategories();
  }

  navigateToPayrollSettings(): void {
    window.location.assign("/payroll-settings");
  }
  
  navigateToPayrollItems() : void{
    this.router.navigate(['/payroll-pay-items']);
  }

  getPayrollConfig(): void {
    const entityId = +(localStorage.getItem('entityId') || '0');
    this.payrollpayrollSettingService.getPayrollConfigByEntity(entityId).subscribe({
      next: (data: PayrollConfig) => {
        this.payrollConfig = data;
        this.setDefaultGlAccount(); 
      },
      error: (err) => {
        console.error('Error fetching PayrollConfig:', err);
      },
    });
  }
  

  setDefaultGlAccount(): void {
    if (this.payrollConfig && this.glAccounts && !this.earning.glAccount) {
      const defaultAccount = this.glAccounts.find(
        account => 
          account.ledgerAccountCode === this.payrollConfig!.wagesExpenseAccount &&
          account.ledgerAccountName === this.payrollConfig!.wagesExpenseAccountName
      );
  
      if (defaultAccount) {
        this.earning.glAccount = defaultAccount.ledgerAccountCode;
        this.earning.glAccountName = defaultAccount.ledgerAccountName;
      }
    }
  }
  
  private getEarnings() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.earningService.getEarningList(entityId).subscribe((data) => {
      this.earnings = data.reverse();;
      this.loadEarningStatuses();
    });
  }
  private getGlAccounts(): void {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.glAccountService.getGlAccountList(entityId).subscribe((data) => {
      this.glAccounts = data;
      this.setDefaultGlAccount();
    });
  }
  
  private getLeave() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.leaveService.getLeaveList(entityId).subscribe((data) => {
      this.leave = data;
      console.log(data)
    });
  }

  private getEarningCategories() {
    this.earningService.getAllEarningCategories().subscribe((data) => {
      this.earningCategories = data;
    });
  }

  editLeave(leaveTypeId: number) {
    if (this.leave !== null) {
      this.editingLeave = this.leave.find(
        (leave) => leave.leaveTypeId === leaveTypeId
      ) || new LeaveType(); // Provide a default instance
      console.log(this.editLeave);
    }
  }

  updateLeaveList(updatedLeave: LeaveType): void {
    const entityId = +((localStorage.getItem('entityId')) + "");
    const userId = +((localStorage.getItem('userid')) + "");
    // const date = new Date().toISOString();

    console.log("Clickdata", updatedLeave);
    this.leaveService.updateLeaveList(updatedLeave.leaveTypeId, updatedLeave).subscribe(
      
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Ordinary Time Earnings updated successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745'
        }).then(() => {
        });
        console.log("Respond",_response)
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to update Ordinary Time Earnings. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032'
        });
      }
    );
  }


  // Getter and Setter for ngModel binding
  get leaveCategoryName(): string {
    return this.editingLeave.leaveCategory?.leaveCategory || '';
  }

  set leaveCategoryName(value: string) {
    if (this.editingLeave.leaveCategory) {
      this.editingLeave.leaveCategory.leaveCategory = value;
    }
  }


  // private getPayitemType() {
  //   this.earningService.getPayitemType().subscribe(data => {
  //     this.PayitemTypes = data;
  //   });
  // }

  private getReimbursement() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.reimbursementsService.getReimbursementsList(entityId).subscribe((data) => {
      this.reimbursements = data.reverse();;
      this.loadReimbursementStatuses();
    });
  }

  get rateLabel(): string {
    if (!this.earning.typeOfUnits) {
      return '';
    }
    return this.earning.typeOfUnits === 'Rate' ? 'Rate' : 'Fixed Amount';
  }

  activeSide: string = 'Earnings';

  isActiveSide(side: string): boolean {
    return this.activeSide === side;
  }

  setActiveSide(side: string): void {
    this.activeSide = side;
  }

  private getDeductions() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.deductionService.getDeductionList(entityId).subscribe((data) => {
      this.deductions = data.reverse();;
      this.loadDeductionStatuses();
    });
  }

  loadDeductionStatuses(): void {
    const entityId = +(localStorage.getItem('entityId') || '0');

    this.deductions.forEach((deduction) => {
      this.employeeService.previewDeductionById(deduction.deductionId, entityId).subscribe({
        next: (response: NewDeduction[]) => {
         
          const isDisabled = response.length > 0 && !!response[0].deduction ;
          this.buttonStates2.set(deduction.deductionId, isDisabled);
        },
        error: (error: any) => {
          console.error(`Error fetching data for deduction ID ${deduction.deductionId}:`, error);
          this.buttonStates2.set(deduction.deductionId, false); 
        }
      });
    });
  }

  getButtonState2(deductionId: number): boolean {
  return this.buttonStates2.get(deductionId) ?? false; 
  }

  updateEarnings(id: number): void {
    this.router.navigate(['/update-earning', id]);
  }

  updateDeductions(id: number): void {
    this.router.navigate(['/update-deduction', id]);
  }

  openEarningModal() {
    this.selectedEarningType = null;
  }

  addEarningLine() {
    if (this.selectedEarningType) {
      this.emp_earnings.push({
        description: this.selectedEarningType,
        amount: 0,
      });
      this.updateEarning();
    }
  }

  removeEarning(index: number) {
    this.emp_earnings.splice(index, 1);
    this.updateEarning();
  }

  updateEarning() {
    this.totalEarningAmount = this.emp_earnings.reduce(
      (sum, earning) => sum + +earning.amount,
      0
    );
  }

  openLeaveModal() {
    this.selectedLeaveType = null;
  }

  addLeaveLine() {
    if (this.selectedLeaveType) {
      this.emp_leave.push({ description: this.selectedLeaveType, amount: 0 });
      this.updateLeave();
    }
  }

  removeLeave(index: number) {
    this.emp_leave.splice(index, 1);
    this.updateLeave();
  }

  updateLeave() {
    this.totalLeaveAmount = this.emp_leave.reduce(
      (sum, leaves) => sum + +leaves.amount,
      0
    );
  }

  openReimbursementModal() {
    this.selectedReimbursemenType = null;
  }
  addReimbursementLine() {
    if (this.selectedReimbursemenType) {
      this.emp_reimbursement.push({
        description: this.selectedReimbursemenType,
        amount: 0,
      });
      this.updateReimbursement();
    }
  }
  removeReimbursement(index: number) {
    this.emp_reimbursement.splice(index, 1);
    this.updateReimbursement();
  }
  updateReimbursement() {
    this.totalReimbursemenAmount = this.emp_reimbursement.reduce(
      (sum, reimbursement) => sum + +reimbursement.amount,
      0
    );
  }

  removeDeduction(index: number) {
    if (this.emp_deduction.length === 1) {
      this.emp_deduction.splice(index, 1);
      this.updateDeduction();
    }
  }
  updateDeduction() {
    this.totalDeduction = this.emp_deduction.reduce(
      (sum, deduction) => sum + +deduction.amount,
      0
    );
  }


  onEarningAdd(): void {
    const entityId = +(localStorage.getItem('entityId') + '');
    const userId = +(localStorage.getItem('userid') + '');
    const date = new Date().toISOString();

    const payload = {
      ...this.earning,
      entityId,
      userId,
      date,
    };

    this.earningService.saveEarning(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Ordinary Time Earnings added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.earning = new Earning();
          this.getEarnings();
          this.getPayrollConfig();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Ordinary Time Earnings. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  // Add Reimbursement
  onReimbursementAdd(): void {
    const entityId = +(localStorage.getItem('entityId') + '');
    const userId = +(localStorage.getItem('userid') + '');
    const date = new Date().toISOString();

    const payload = {
      ...this.reimbursement,
      entityId,
      userId,
      date,
    };

    this.reimbursementsService.saveReimbursement(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Reimbursement added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.reimbursement = new Reimbursement();
          this.getReimbursement();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Reimbursement. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  loadReimbursementStatuses(): void {
    const entityId = +(localStorage.getItem('entityId') || '0');

    this.reimbursements.forEach((reimbursement) => {
      this.employeeService.previewReimbursementById(reimbursement.reimbursementId, entityId).subscribe({
        next: (response: any[]) => {
         
          const isDisabled = response.some(item => !!item.payRun);
          this.buttonStates3.set(reimbursement.reimbursementId, isDisabled);
        },
        error: (error: any) => {
          console.error(`Error fetching data for reimbursement ID ${reimbursement.reimbursementId}:`, error);
          this.buttonStates3.set(reimbursement.reimbursementId, false); 
        }
      });
    });
  }

  getButtonState3(reimbursementId: number): boolean {
  return this.buttonStates3.get(reimbursementId) ?? false; 
  }

  deleteReimbursement(id: number) {
    this.reimbursementsService.deleteReimbursement(id).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Reimbursement delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getReimbursement();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Reimbursement. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  addDeduction(): void {
    const entityId = +(localStorage.getItem('entityId') + '');
    const userId = +(localStorage.getItem('userid') + '');
    const date = new Date().toISOString();

    const payload = {
      ...this.deduction,
      entityId,
      userId,
      date,
    };

    this.deductionService.saveDeduction(payload).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Deduction added successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.deduction = new Deduction();
          this.getDeductions();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save Deduction. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  loadEarningStatuses(): void {
          const entityId = +(localStorage.getItem('entityId') || '0');
      
          this.earnings.forEach((earning) => {
            this.employeeService.previewEarningById(earning.earningId, entityId).subscribe({
              next: (response: NewEarning[]) => {
               
                const isDisabled = response.length > 0 && !!response[0].earning ;
                this.buttonStates.set(earning.earningId, isDisabled);
              },
              error: (error: any) => {
                console.error(`Error fetching data for Earning ID ${earning.earningId}:`, error);
                this.buttonStates.set(earning.earningId, false); 
              }
            });
          });
        }

  getButtonState1(earningId: number): boolean {
    return this.buttonStates.get(earningId) ?? false; 
  }

  deleteEarning(id: number) {
    this.earningService.deleteEarning(id).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Earning delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getEarnings();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Earning. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  deleteDeduction(id: number) {
    this.deductionService.deleteDeduction(id).subscribe(
      (_response: any) => {
        Swal.fire({
          title: 'Success!',
          text: 'Deduction delete successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getDeductions();
          
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete Deduction. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }

  leaves = [
    {
      name: 'Annual Leave',
      category: 'Paid Leave',
      units: 'Hours',
      entitlement: '102.00',
      loadingRate: 'No',
      showOnPayslip: 'Yes',
    },
    {
      name: 'Personal Leave',
      category: 'Paid Leave',
      units: 'Hours',
      entitlement: '190.00',
      loadingRate: 'No',
      showOnPayslip: 'No',
    },
    {
      name: 'Long Service Leave',
      category: 'Unpaid Leave',
      units: 'Days',
      entitlement: '150.00',
      loadingRate: 'No',
      showOnPayslip: 'Yes',
    },
    {
      name: 'Other Leave',
      category: 'Paid Leave',
      units: 'Hours',
      entitlement: '130.00',
      loadingRate: 'No',
      showOnPayslip: 'Yes',
    },
    {
      name: 'Paid Family and Domestic Violence Leave',
      category: 'Paid Leave',
      units: 'Days',
      entitlement: '192.00',
      loadingRate: 'Yes',
      showOnPayslip: 'No',
    },
    {
      name: 'Other Leave',
      category: 'Unpaid Leave',
      units: 'Hours',
      entitlement: '102.00',
      loadingRate: 'Yes',
      showOnPayslip: 'Yes',
    },
  ];

  leaveTwo = [
    {
      name: 'Other Leave',
      category: 'Unpaid Leave',
      units: 'Hours',
      entitlement: '132.00',
      loadingRate: 'Yes',
      showOnPayslip: 'Yes',
      exemptSuperannuation: 'No',
      showBalance: 'No',
    },
    {
      name: 'Personal Leave',
      category: 'Unpaid Leave',
      units: 'Hours',
      entitlement: '92.00',
      loadingRate: 'Yes',
      showOnPayslip: 'No',
      exemptSuperannuation: 'No',
      showBalance: 'No',
    },
    {
      name: 'Paid Family and Domestic Violence Leave',
      category: 'Paid Leave',
      units: 'Days',
      entitlement: '107.00',
      loadingRate: 'No',
      showOnPayslip: 'No',
      exemptSuperannuation: 'Yes',
      showBalance: 'No',
    },
    {
      name: 'Annual Leave',
      category: 'Paid Leave',
      units: 'Hours',
      entitlement: '100.00',
      loadingRate: 'Yes',
      showOnPayslip: 'No',
      exemptSuperannuation: 'No',
      showBalance: 'Yes',
    },
    {
      name: 'Annual Leave',
      category: 'Paid Leave',
      units: 'Hours',
      entitlement: '102.00',
      loadingRate: 'No',
      showOnPayslip: 'Yes',
      exemptSuperannuation: 'No',
      showBalance: 'No',
    },
    {
      name: 'Other Leave',
      category: 'Unpaid Leave',
      units: 'Hours',
      entitlement: '103.00',
      loadingRate: 'No',
      showOnPayslip: 'Yes',
      exemptSuperannuation: 'No',
      showBalance: 'Yes',
    },
  ];

  onGlAccountChange() {
    const selectedAccount = this.glAccounts.find(account => account.ledgerAccountCode === this.earning.glAccount);
    if (selectedAccount) {
      this.earning.glAccountName = selectedAccount.ledgerAccountName;
    }
  }
  
  onGlAccountChangeDeduction(){
    const selectedAccount = this.glAccounts.find((account) => account.ledgerAccountCode === this.deduction.glAccount);
    if(selectedAccount){
      this.deduction.glAccountName = selectedAccount.ledgerAccountName;
    }
  }
  onGlAccountChangeRem(){
    const selectedAccount = this.glAccounts.find((account) => account.ledgerAccountCode === this.reimbursement.glAccount);
    if(selectedAccount){
      this.reimbursement.glAccountName = selectedAccount.ledgerAccountName;
    }
  }

}






