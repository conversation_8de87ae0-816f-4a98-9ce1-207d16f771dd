<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<form class="form" onsubmit={submit}>
    <div class="text-header">Bank Account</div>
    <div >
        <div class="primary-acc-text">Opening Balances and the Fiscal Year</div>
    </div>
    <div class="hr-div">
        <hr class="hr-line"/>
    </div>
    <div class="date-div">
        <span class="dates">01 Nov 2024 - 01 Nov 2024</span>
    </div>
   
        <div class="account-table-wrapper">
            <table class="account-table">
                <thead>
                  <tr>
                    <th style="width: 60%;">Account Name</th>
                    <th style="width: 20%;">Debit</th>
                    <th style="width: 20%;">Credit</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Assets Section -->
                   <td colspan="4" style="padding: 1px;"></td>
                  <tr class="section-header">
                    <td colspan="4">Assets</td>
                  </tr>
                  <tr *ngFor="let asset of assets">
                    <td>{{ asset.name }}</td>
                    <td>{{ asset.debit }}</td>
                    <td>{{ asset.credit }}</td>
                    <td><button class="delete-btn" *ngIf="asset.name != null">✖</button></td>
                  </tr>
                  <tr class="section-header">
                    <td colspan="4" style="padding: 20px;"></td>
                  </tr>
            
                  <!-- Liabilities Section -->
                  <tr class="section-header">
                    <td colspan="4">Liabilities</td>
                  </tr>
                  <tr *ngFor="let liability of liabilities">
                    <td>{{ liability.name }}</td>
                    <td>{{ liability.debit }}</td>
                    <td>{{ liability.credit }}</td>
                    <td><button class="delete-btn" *ngIf="liability.name != null">✖</button></td>
                  </tr>
                  <tr class="section-header">
                    <td colspan="4" style="padding: 20px;"></td>
                  </tr>
            
                  <!-- Equity Section -->
                  <tr class="section-header">
                    <td colspan="4">Equity</td>
                  </tr>
                  <tr *ngFor="let equityItem of equity" class="equity-row">
                    <td>{{ equityItem.name }}</td>
                    <td>{{ equityItem.debit }}</td>
                    <td>{{ equityItem.credit }}</td>
                    <td><button class="delete-btn" *ngIf="equityItem.name != null">✖</button></td>
                  </tr>
                  <!-- Total Row -->
                  <tr class="total-row" >
                    <td class="total">Total</td>
                    <td>{{ totalDebit }}</td>
                    <td colspan="4">{{ totalCredit }}</td>
                  </tr>
                  <td colspan="4" style="border-bottom: 2px solid #444343; padding: 4px;"></td>
                </tbody>
              </table>
             

              <div class="button-container">
                <button class="cancel-btn">Cancel</button>
                <button class="save-btn">Save</button>
              </div>
              <div class="maginn" style="padding-top: -15px;"></div>
          </div>
</form>