import { PaymentVoucherHeader } from '../bill/bill';
import { BankAccount } from '../bank/bank';
import { CoaLedgerAccount } from '../expence-claims/expence-claims';
export class BankReconciliation {
  id: number;
  statementDate: string; // Keep as string or convert to Date if needed
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  openingBalance: number;
  closingBalance: number;
  transactions: BankStatementDetail[];
  // Array of transactions

  constructor(
    id: number,
    statementDate: string,
    bankName: string,
    accountNumber: string,
    accountHolderName: string,
    openingBalance: number | null,
    closingBalance: number | null,
    transactions: BankStatementDetail[]
  ) {
    this.id = id;
    this.statementDate = statementDate;
    this.bankName = bankName;
    this.accountNumber = accountNumber;
    this.accountHolderName = accountHolderName;
    this.openingBalance = openingBalance ?? 0; // Assign 0 if null
    this.closingBalance = closingBalance ?? 0; // Assign 0 if null
    this.transactions = transactions || []; // Ensure it is always an array
  }
}

export class BankStatementHeader {
  bankName?: string;
  bankAccount?: BankAccount;
  bankStatementId?: string;
  accountHolderName?: string;
  accountNumber?: string;
  statementDate?: string;
  entityId?: number;
  statementList?: BankStatementDetail[];
  statementStatus?: string;
  statementTotal?: number;

  constructor(
    bankName?: string,
    bankAccount?: BankAccount,
    bankStatementId?: string,
    accountHolderName?: string,
    accountNumber?: string,
    statementDate?: string,
    statementStatus?: string,
    statementTotal?: number
  ) {
    this.bankName = bankName;
    this.bankAccount = bankAccount;
    this.bankStatementId = bankStatementId;
    this.accountHolderName = accountHolderName;
    this.accountNumber = accountNumber;
    this.statementDate = statementDate;
    this.statementStatus = statementStatus;
    this.statementTotal = statementTotal;
  }
}

export class BankStatementDetail {
  id: number;
  date: string;
  description: string;
  credit: number;
  debit: number;
  balance: number;
  reference: string;
  matchStatus: string;
  reconcileStatus: string;
  pendingBalance: number;
  bankStatementHeader: BankStatementHeader;

  constructor(data?: Partial<BankStatementDetail>) {
    this.id = data?.id ?? 0;
    this.date = data?.date ?? '';
    this.description = data?.description ?? '';
    this.credit = data?.credit ?? 0;
    this.debit = data?.debit ?? 0;
    this.balance = data?.balance ?? 0;
    this.reference = data?.reference ?? '';
    this.matchStatus = data?.matchStatus ?? '';
    this.reconcileStatus = data?.reconcileStatus ?? '';
    this.pendingBalance = data?.pendingBalance ?? 0;
    this.bankStatementHeader =
      data?.bankStatementHeader ?? new BankStatementHeader();
  }
}

export class BankStatementMatchedDetails {
  id?: number;
  groupId?: string;
  transactionId?: BankStatementDetail;
  transactionDescription?: string;
  transactionType?: string;
  transactionAmount?: number;
  transactionDate?: string;
  recordDate?: string;
  bankStatementId?: number;
  entityId?: number;
  bankRecDocumentType?: BankRecDocumentType;
  documentId?: number;
  gstApplicability?: boolean | null;
  glAccountId?: number | null;
  bankRecMatchStatus?: BankRecMatchStatus;
  bankRecMatchMode?: BankRecMatchMode;

  constructor(data: Partial<BankStatementMatchedDetails> = {}) {
    if (data) {
      this.id = data.id;
      this.bankStatementId = data.bankStatementId;
      this.entityId = data.entityId;
      this.transactionId = data.transactionId;
      this.transactionDescription = data.transactionDescription;
      this.transactionType = data.transactionType;
      this.transactionAmount = data.transactionAmount;
      this.transactionDate = data.transactionDate;
      this.recordDate = data.recordDate;
      this.bankRecDocumentType = data.bankRecDocumentType;
      this.documentId = data.documentId;
      this.gstApplicability = data.gstApplicability ?? null;
      this.glAccountId = data.glAccountId ?? null;
      this.bankRecMatchStatus = data.bankRecMatchStatus;
      this.bankRecMatchMode = data.bankRecMatchMode;
    }
  }
}

export interface TransactionRequestCreditListDTO {
  selectedType: BankRecTransactionType;
  paymentType: BankRecDocumentType;
  matchedMode: BankRecMatchMode;
  selectedTransaction: BankStatementDetail;
  transactionAmount: number;
  paymentTypeId: number;
  paymentTypeAmount: number;
  paymentTypeTotalRecBalanceAmount: number;
  paymentTypeReference: string;
  paymentTypeDate: string;
  entityId: number;
  userId: number;
}

export interface TransactionRequestIncomeOrExpense {
  selectedType: BankRecTransactionType;
  matchedMode: BankRecMatchMode;
  bankRecDocumentType: BankRecDocumentType;
  selectedTransaction: BankStatementDetail;
  transactionReference: string;
  transactionAmount: number;
  ledgerAccountId: CoaLedgerAccount;
  gstApplicability: boolean;
  entityId: number;
  userId: number;
}

export interface BankRuleTransactionMatchDTO {
  entityId: number;
  userId: number;
  selectedType: BankRecTransactionType;
  matchedMode: BankRecMatchMode;
  transaction: BankStatementDetail;
  bankRuleHead: BankRuleHead;
  bankRuleDetail: BankRuleDetail[];
  totalAllocationPercentage: number;
}

export enum BankRecDocumentType {
  PAYMENT_RECEIPT,
  PAYMENT_VOUCHER,
  PAYMENT_EXPENSE,
  GL_EXPENSE,
  GL_INCOME,
  INVOICE,
  BILL,
  BANK_TRANSFER,
  BANK_RULE,
}

export interface TransactionRequestBankTransfer {
  selectedType: BankRecTransactionType;
  matchedMode: BankRecMatchMode;
  bankRecDocumentType: BankRecDocumentType;
  selectedTransaction: BankStatementDetail;
  transactionReference: string;
  transactionAmount: number;
  fromBankAccountId: number;
  toBankAccountId: number;
  entityId: number;
  userId: number;
}

export function bankRecDocumentList(): {
  value: BankRecDocumentType;
  label: string;
}[] {
  return [
    { value: BankRecDocumentType.PAYMENT_RECEIPT, label: 'Payment Receipt' },
    { value: BankRecDocumentType.PAYMENT_VOUCHER, label: 'Payment Voucher' },
    { value: BankRecDocumentType.PAYMENT_EXPENSE, label: 'Payment Expense' },
    { value: BankRecDocumentType.GL_EXPENSE, label: 'GL Expense' },
    { value: BankRecDocumentType.GL_INCOME, label: 'GL Income' },
    { value: BankRecDocumentType.INVOICE, label: 'Invoice' },
    { value: BankRecDocumentType.BILL, label: 'Bill' },
    { value: BankRecDocumentType.BANK_TRANSFER, label: 'Bank Transfer' },
    { value: BankRecDocumentType.BANK_RULE, label: 'Bank Rule' },
  ];
}

export enum BankRecMatchMode {
  SYSTEM_MATCH,
  FIND,
  CREATE,
  TRANSFER,
  BANK_RULE,
}

export enum BankRecMatchStatus {
  PENDING,
  COMPLETE,
}

export enum BankRecTransactionType {
  CREDIT,
  DEBIT,
}

export enum BankRuleType {
  SPEND = 'SPEND',
  RECEIVE = 'RECEIVE',
  TRANSFER = 'TRANSFER',
}

export interface BankRuleHead {
  bankRuleHeadId?: number;
  entityId: number;
  userId: number;
  bankRuleType: BankRuleType;
  selectedBusinessPartner: string;
  bankRuleName: string;
  bankAccount: BankAccount;
  createdAt?: string;
}

export interface BankRuleDetail {
  bankRuleDetailId?: number;
  ruleMatchField?: string;
  ruleFilterBy?: string;
  ruleFilterText?: string;
  bankRuleHead?: BankRuleHead;
  ruleDetailType: string;
  ruleDetailGlAccountId?: number;
  ruleDetailGlAccountDescription?: string;
  ruleDetailGlAccountPercentage?: number;
}
export interface BankRuleDTO {
  bankRuleHead: BankRuleHead;
  bankRuleDetailList: BankRuleDetail[];
}

export interface BankStatementMatchDetailView {
  id?: number;
  groupId?: string;
  transactionId?: BankStatementDetail;
  transactionType?: string;
  recordDate?: string;
  bankRecDocumentType?: BankRecDocumentType;
  bankRecMatchStatus?: BankRecMatchStatus;
  bankRecMatchMode?: BankRecMatchMode;
  documentDetailList: BankStatementMatchDocumentDetail[];
}

export interface BankStatementMatchDocumentDetail {
  transactionDescription?: string;
  transactionAmount?: number;
  documentId?: number;
  gstApplicability?: boolean | null;
  glAccountId?: number | null;
}
