import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2'; // Import SweetAlert2
import { CreditNote, CreditNoteDetail } from '../credit-note';
import { Entity } from '../../entity/entity';
import { CreditNoteService } from '../credit-note.service';
import { EntityService } from '../../entity/entity.service';
import { InvoiceHead } from '../../invoice/invoice';
import { InvoiceService } from '../../invoice/invoice.service';
import { HttpErrorResponse } from '@angular/common/http';
import { NgForm } from '@angular/forms';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';

@Component({
  selector: 'app-create-credit-note',
  templateUrl: './create-credit-note.component.html',
  styleUrls: ['./create-credit-note.component.css'],
})
export class CreateCreditNoteComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  invoiceHead: InvoiceHead = new InvoiceHead();
  creditNote: CreditNote = new CreditNote();
  details: CreditNoteDetail = new CreditNoteDetail();
  newCreditNoteNumber: string = '';
  businessEntityId: number = 0;
  lastCreditNoteNumber: string = '';
  businessEntity: Entity = new Entity();
  selectedInvoices: any[] = [];
  totalCreditAmount: number = 0;
  validInvoices: any[]= [];
  private debounceTimer: any = null;

  quotationData = {
    "creditNoteId": 1,
    "businessPartnerId": 1,
    "userId": 1,
    "entityId": 1,
    "creditNoteNumber": "CN123",
    "documentType": "Type",
    "documentDate": "2024-09-12",
    "contactNumber": "1234567890",
    "paidAmount": 100.0,
    "balanceDue": 50.0,
    "totalCreditAmount": 150.0,
    "documentStatus": "Pending",
    "transactionDate": "2024-09-12",
    "customerName": "Customer Name",
    "details": [
      {
        "invoiceNumber": "INV123",
        "grandTotal": 200.0,
        "balanceAmount": 100.0,
        "creditAmount": 50.0
      }
    ]
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private creditNoteService: CreditNoteService,
    private entityService: EntityService,
    private invoiceService: InvoiceService
  ) { }


  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      const invoiceIds = JSON.parse(params['invoiceIds'] || '[]');
      if (invoiceIds.length > 0) {
        this.loadInvoices(invoiceIds);
      } else {
        Swal.fire({
          title: 'No Invoices Found',
          text: 'No invoice IDs were provided.',
          icon: 'warning',
          confirmButtonText: 'OK',
          confirmButtonColor: '#ff7e5f'
        });
      }
    });
    this.getBusinessEntityById();

    if (!this.creditNote.documentDate) {
      this.creditNote.documentDate = this.getTodayDate();
    }
  }

  // Utility function to get today's date in 'YYYY-MM-DD' format
  getTodayDate(): string {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const dd = String(today.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  loadInvoices(invoiceIds: number[]): void {
    const invoiceRequests = invoiceIds.map(id => this.invoiceService.getInvoiceDetailsByInvoiceHeadId(id).toPromise());
    Promise.all(invoiceRequests)
      .then(invoices => {
        this.selectedInvoices = invoices.flat();
        this.creditNote.invoiceNumber = this.selectedInvoices[0].invoiceNumber;
      })
      .catch(error => {
        Swal.fire({
          title: 'Error Loading Invoices',
          text: 'There was an error fetching the invoice details.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032'
        });
      });

  }

  getBusinessEntityById() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '');
    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
      (data) => {
        this.businessEntity = data;
        this.lastCreditNoteNumber = this.incrementCreditNoteNumber(
          this.businessEntity.creditNoteNumber
        );
        this.creditNote.creditNoteNumber = this.lastCreditNoteNumber;
      },
    );
  }

  incrementCreditNoteNumber(creditNoteNumber: string): string {
    if (!creditNoteNumber) {
      return 'C000001';
    }
    const prefix = creditNoteNumber.charAt(0);
    const numericPart = creditNoteNumber.slice(1);
    const incrementedNumber = (Number(numericPart) + 1).toString().padStart(numericPart.length, '0');
    return prefix + incrementedNumber;
  }

  getTotalCreditAmount(): number {
    return this.selectedInvoices.reduce((total, invoice) => total + (invoice.creditAmount || 0), 0);
  }

  getTotalCreditAmountWithGST(): number {
    return this.selectedInvoices.reduce((total, invoice) => total + (invoice.creditAmount || 0) + (invoice.totalGst || 0), 0);
  }

  getInvoiceNewBalance(invoice: any): number {
    const amount = invoice.amount || 0;
    const tax = invoice.tax || 0;
    const totalInvoiceAmount = amount + tax;
    const creditAmount = invoice.creditAmount || 0;
    return totalInvoiceAmount - creditAmount;
  }

  onCancel() {
    this.router.navigate(['/credit-note']);
  }

  preventEnter(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault(); // Prevent form submission on "Enter"
    }
  }

  /**onSubmit() {
    // Pass the invoices to the saveQuotation method
    this.creditNote.totalCreditAmount = this.getTotalCreditAmount();
    this.creditNote.details = this.selectedInvoices.map(invoice => {
      let detail = new CreditNoteDetail();
      detail.invoiceHead = invoice.invoiceHeadId;
      detail.invoiceNumber = invoice.invoiceNumber;
      detail.grandTotal = invoice.grandTotal;
      detail.balanceAmount = invoice.balanceAmount;
      detail.creditAmount = invoice.creditAmount;
      return detail;
  });

    this.saveCreditNote(this.selectedInvoices);
  }**/


  onSubmit(f: NgForm) {
  // Filter only invoices that have a valid creditAmount
  this.validInvoices = this.selectedInvoices.filter(invoice => invoice.creditAmount && invoice.creditAmount > 0);

  if (this.validInvoices.length === 0) {
    Swal.fire({
      title: 'Warning!',
      text: 'Please enter Credit Amount for at least one invoice (greater than 0).',
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f'
    });
  } else {
    // Proceed with only the valid invoices
    this.creditNote.totalCreditAmount = this.validInvoices.reduce((sum, invoice) => sum + invoice.creditAmount, 0);
    this.creditNote.details = this.validInvoices.map(invoice => {
      let detail = new CreditNoteDetail();
      detail.invoiceHead = invoice.invoiceHead.invoiceHeadId;
      detail.invoiceNumber = invoice.invoiceNumber;
      detail.grandTotal = invoice.invoiceHead.grandTotal;
      detail.balanceAmount = invoice.balance;
      detail.creditAmount = invoice.creditAmount;
      return detail;
    });



    // Proceed to saving
    this.saveCreditNote(this.validInvoices);
  }
}



  saveCreditNote(invoices: any[]) {

    // Ensure document date is set, default to today if not provided
    if (!this.creditNote.documentDate) {
      this.creditNote.documentDate = this.getTodayDate();
    }
    // Iterate over each invoice to validate its credit amount
    const invalidInvoices = invoices.filter(invoice => invoice.creditAmount > invoice.balanceAmount);

    if (invalidInvoices.length > 0) {
      // If any invoice has a credit amount greater than its balance, show the SweetAlert warning
      Swal.fire({
        title: 'Credit Amount Exceeds Balance!',
        text: 'One or more invoices have credit amounts greater than their balances. Do you want to continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, continue',
        cancelButtonText: 'No, revert to balance',
      }).then((result) => {
        if (result.isConfirmed) {
          // If user confirms, set flag and continue
          invalidInvoices.forEach(invoice => {
            invoice.isCreditExceeded = true;
          });
          this.proceedToSave();
        } else {
          // If user cancels, reset the credit amount to the balance and mark as not exceeded
          invalidInvoices.forEach(invoice => {
            invoice.creditAmount = invoice.balanceAmount;
            invoice.isCreditExceeded = false;
          });
        }
      });
    } else {
      // If all credit amounts are valid, proceed to save
      this.proceedToSave();
    }
  }



  proceedToSave() {
    // Assuming the selectedInvoices array is already populated and validated
    if (this.selectedInvoices.length > 0) {
      // Set the businessPartnerId from the first invoice, as all invoices have the same customer
      this.creditNote.businessPartnerId = this.selectedInvoices[0].invoiceHead.businessPartnerId;
      this.creditNote.customerName = this.selectedInvoices[0].invoiceHead.reference;
      this.creditNote.invoiceNumber = this.selectedInvoices[0].invoiceNumber;
    }

    // Set other credit note properties
    this.creditNote.documentStatus = 'Open';
    this.creditNote.userId = +(localStorage.getItem('userid') + "");
    this.creditNote.entityId = +(localStorage.getItem('entityId') + "");


    // Save the credit note
    this.creditNoteService.saveCreditNote(this.creditNote).subscribe(
      (response: any) => {
        this.updateBusinessEntityCreditNoteNumber();

        const creditAmount = this.validInvoices.reduce((sum, invoice) => sum + invoice.creditAmount, 0);
        const newBalanceHeadAmount = this.validInvoices[0].invoiceHead.balanceAmount - creditAmount;

        const updateRequest = {
          newBalanceHeadAmount: newBalanceHeadAmount,
          updates: this.validInvoices.map(invoice => ({
          invoiceDetailId: invoice.invoiceDetailId,
          newBalanceDetailAmount: invoice.balance - invoice.creditAmount
          }))
        };

        const invoiceHeadId = this.validInvoices[0].invoiceHead.invoiceHeadId;

        this.updateInvoiceBalance(invoiceHeadId, updateRequest);

        // Update the balance amount for each selected invoice detail
        // this.validInvoices.forEach(invoice => {
        //   const newBalanceHeadAmount = invoice.invoiceHead.amount - invoice.invoiceHead.creditAmount;
        //   const newBalanceDetailAmount = invoice.amount - invoice.creditAmount;


          // this.updateInvoiceBalance(invoice.invoiceHead.invoiceHeadId, );
        // });



        Swal.fire({
          title: 'Success!',
          text: 'The Credit Note has been successfully saved.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/credit-note']);
          }
        });
      },
      (error: HttpErrorResponse) => {
        console.error('Error saving Credit Note', error);
        Swal.fire({
          title: 'Error!',
          text: 'Unable to save the Credit Note. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Unable to save the Credit Note. Please try again.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
  }

  // Method to update invoice balance in the backend
  updateInvoiceBalance(invoiceHeadId: number, update:
    {
      newBalanceHeadAmount: number;
      updates: {
          invoiceDetailId: number;
          newBalanceDetailAmount: number;
        }[];

    }) {

    this.invoiceService.updateInvoiceHeadAndDetailsBalance(invoiceHeadId, update).subscribe(
      (response: any) => {
      },
      (error: HttpErrorResponse) => {
        console.error(`Error updating balance for Invoice ${invoiceHeadId}:`, error);
      }
    );
  }

  updateBusinessEntityCreditNoteNumber() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '0');
    this.businessEntity.creditNoteNumber = this.creditNote.creditNoteNumber;
    this.entityService.updateCreditNoteNumber(this.businessEntity, this.businessEntityId).subscribe(
      (data) => {
      },
      (error) => {
        console.error(error);
      }
    );
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}
