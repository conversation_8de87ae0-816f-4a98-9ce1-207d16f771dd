import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2'; // Import SweetAlert2
import { CreditNote, CreditNoteDetail } from '../credit-note';
import { Entity } from '../../entity/entity';
import { CreditNoteService } from '../credit-note.service';
import { EntityService } from '../../entity/entity.service';
import { InvoiceHead } from '../../invoice/invoice';
import { InvoiceService } from '../../invoice/invoice.service';
import { HttpErrorResponse } from '@angular/common/http';
import { NgForm } from '@angular/forms';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { BusinessPartner } from '../../business-partner/business-partner';
@Component({
  selector: 'app-create-credit-note-user',
  templateUrl: './create-credit-note-user.component.html',
  styleUrls: ['./create-credit-note-user.component.css'],
})
export class CreateCreditNoteUserComponent {
  invoiceHead: InvoiceHead = new InvoiceHead();
  creditNote: CreditNote = new CreditNote();
  details: CreditNoteDetail = new CreditNoteDetail();
  newCreditNoteNumber: string = '';
  businessEntityId: number = 0;
  lastCreditNoteNumber: string = '';
  businessEntity: Entity = new Entity();
  selectedInvoices: any[] = [];
  totalCreditAmount: number = 0;
  private debounceTimer: any = null;
  customers: BusinessPartner[] = [];
  creditNoteData = {
    creditNoteId: 1,
    businessPartnerId: 1,
    userId: 1,
    entityId: 1,
    creditNoteNumber: 'CN123',
    documentType: 'Type',
    documentDate: '2024-09-12',
    contactNumber: '1234567890',
    paidAmount: 100.0,
    balanceDue: 50.0,
    totalCreditAmount: 150.0,
    documentStatus: 'Pending',
    transactionDate: '2024-09-12',
    customerName: 'Customer Name',
    details: [
      {
        invoiceNumber: 'INV123',
        grandTotal: 200.0,
        balanceAmount: 100.0,
        creditAmount: 50.0,
      },
    ],
  };
  invoiceNumberInput: any;
  enteredInvoices: any[] = [];
  isCustomerSelected = false;

  activeRowIndex: number | null = null;
  searchCriteria = {
    fromDate: '',
    toDate: '',
  };

  filteredInvoices: any[] = [];
  isInvalidInvoice: boolean = true; // Initially disable the save credit note button

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private creditNoteService: CreditNoteService,
    private entityService: EntityService,
    private invoiceService: InvoiceService,
    private businessPartnerService: BusinessPartnerService
  ) {}

  ngOnInit(): void {
    this.loadCustomers();

    this.initializeEmptyInvoiceRows();
    this.getBusinessEntityById();

    if (!this.creditNote.documentDate) {
      this.creditNote.documentDate = this.getTodayDate();
    }
  }

  initializeEmptyInvoiceRows(): void {
    this.enteredInvoices = [
      {
        invoiceNumber: '',
        postingDate: '',
        grandTotal: 0,
        balanceAmount: 0,
        creditAmount: 0,
      },
    ];
  }

  // Utility function to get today's date in 'YYYY-MM-DD' format
  getTodayDate(): string {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const dd = String(today.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  loadCustomers() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.businessPartnerService
      .getCustomerListByEntity(entityId)
      .subscribe(
        (customers: BusinessPartner[]) => {
          this.customers = customers;
        },
        (error: HttpErrorResponse) => {}
      );
  }

  onCustomerChange(event: any) {
    const selectedCustomerId = +event.target.value; // Convert to number using the unary + operator
    const selectedCustomer = this.customers.find(
      (cust) => cust.businessPartnerId === selectedCustomerId
    );

    if (selectedCustomer) {
      this.creditNote.customerName = selectedCustomer.bpName || '';
      this.creditNote.businessPartnerId = selectedCustomer.businessPartnerId; // Correctly set the businessPartnerId here
      this.isCustomerSelected = this.creditNote.businessPartnerId !== null;
    } else {
      // Handle case when no customer is found
      this.creditNote.customerName = '';
      this.creditNote.businessPartnerId = 0;
    }
  }

  loadInvoices(invoiceIds: number[]): void {
    const invoiceRequests = invoiceIds.map((id) =>
      this.invoiceService.getInvoiceHeadById(id).toPromise()
    );
    Promise.all(invoiceRequests)
      .then((invoices) => {
        this.selectedInvoices = invoices;
      })
      .catch((error) => {
        Swal.fire({
          title: 'Error Loading Invoices',
          text: 'There was an error fetching the invoice details.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      });
  }

  getBusinessEntityById() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '');
    this.entityService
      .getBusinessEntityById(this.businessEntityId)
      .subscribe((data) => {
        this.businessEntity = data;
        this.lastCreditNoteNumber = this.incrementCreditNoteNumber(
          this.businessEntity.creditNoteNumber
        );
        this.creditNote.creditNoteNumber = this.lastCreditNoteNumber;
      });
  }

  incrementCreditNoteNumber(creditNoteNumber: string): string {
    if (!creditNoteNumber) {
      return 'C000001';
    }
    const prefix = creditNoteNumber.charAt(0);
    const numericPart = creditNoteNumber.slice(1);
    const incrementedNumber = (Number(numericPart) + 1)
      .toString()
      .padStart(numericPart.length, '0');
    return prefix + incrementedNumber;
  }

  getInvoiceNewBalance(invoice: any): number {
    const balanceAmount = invoice.balanceAmount || 0;
    const creditAmount = invoice.creditAmount || 0;
    return balanceAmount - creditAmount;
  }

  onCancel() {
    this.router.navigate(['/credit-note']);
  }

  preventEnter(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault(); // Prevent form submission on "Enter"
    }
  }

  onSubmit() {
    // Check if all required fields are filled
    if (
      !this.creditNote.documentDate ||
      !this.creditNote.customerName ||
      this.enteredInvoices.length === 0 ||
      this.enteredInvoices.some((invoice) => !invoice.invoiceNumber)
    ) {
      Swal.fire({
        title: 'Incomplete Form',
        text: 'Please fill in all required fields before submitting.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f',
      });
      return; // Stop the submission if validation fails
    }
    // First, ensure that the total credit amount is calculated from entered invoices
    this.creditNote.totalCreditAmount = this.getTotalCreditAmountEntered();

    // Proceed if no mismatches
    this.creditNote.details = this.enteredInvoices.map((invoice) => {
      let detail = new CreditNoteDetail();

      detail.invoiceHead = invoice.invoiceHeadId; // Assuming invoiceHeadId comes from fetched invoice details
      detail.invoiceNumber = invoice.invoiceNumber;
      detail.grandTotal = invoice.grandTotal;
      detail.balanceAmount = invoice.balanceAmount;
      detail.creditAmount = invoice.creditAmount || 0;
      return detail;
    });

    this.saveCreditNote(this.enteredInvoices);
  }

  saveCreditNote(invoices: any[]) {
    // Ensure document date is set, default to today if not provided
    if (!this.creditNote.documentDate) {
      this.creditNote.documentDate = this.getTodayDate();
    }

    // Validate credit amount vs. balance for entered invoices
    const invalidInvoices = invoices.filter(
      (invoice) => invoice.creditAmount > invoice.balanceAmount
    );

    if (invalidInvoices.length > 0) {
      // If any invoice has a credit amount greater than its balance, show the SweetAlert warning
      Swal.fire({
        title: 'Credit Amount Exceeds Balance!',
        text: 'One or more invoices have credit amounts greater than their balances. Do you want to continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, continue',
        cancelButtonText: 'No, revert to balance',
      }).then((result) => {
        if (result.isConfirmed) {
          // If user confirms, set flag and continue
          invalidInvoices.forEach((invoice) => {
            invoice.isCreditExceeded = true;
          });
          this.checkZeroCreditAmount(invoices);
        } else {
          // If user cancels, reset the credit amount to the balance and mark as not exceeded
          invalidInvoices.forEach((invoice) => {
            invoice.creditAmount = invoice.balanceAmount;
            invoice.isCreditExceeded = false;
          });
        }
      });
    } else {
      this.checkZeroCreditAmount(invoices);
    }
  }

  checkZeroCreditAmount(invoices: any[]) {
    // Check for invoices where creditAmount is 0
    const zeroCreditInvoices = invoices.filter(
      (invoice) => invoice.creditAmount === 0
    );

    if (zeroCreditInvoices.length > 0) {
      // Show SweetAlert for creditAmount === 0
      Swal.fire({
        title: 'Credit Amount is Zero',
        text: 'One or more invoices have a Credit Amount of 0. Do you want to proceed?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, proceed',
        cancelButtonText: 'No, edit',
      }).then((result) => {
        if (result.isConfirmed) {
          this.proceedToSave(); // Proceed to save
        } else {
          // Allow user to edit credit amounts
          return;
        }
      });
    } else {
      // Proceed to save if no issues
      this.proceedToSave();
    }
  }

  validateCreditAmount(invoice: any) {
    if (invoice.creditAmount < 0) {
      Swal.fire({
        title: 'Invalid Input',
        text: 'Credit Amount cannot be negative. It has been reset to 0.',
        icon: 'warning',
        confirmButtonText: 'OK',
      });

      // Reset the credit amount to 0
      invoice.creditAmount = 0;
    }
  }

  proceedToSave() {
    if (this.enteredInvoices.length > 0) {
      // Set the businessPartnerId and customerName from the first invoice, assuming all belong to the same customer

      // this.creditNote.invoiceNumber = this.enteredInvoices.map(
      //   (invoice) => invoice.invoiceNumber
      // );
    }

    // Set other credit note properties
    this.creditNote.documentStatus = 'Open';
    this.creditNote.userId = +(localStorage.getItem('userid') + '');
    this.creditNote.entityId = +(localStorage.getItem('entityId') + '');

    this.creditNoteService.saveCreditNote(this.creditNote).subscribe(
      (response: any) => {
        this.updateBusinessEntityCreditNoteNumber();
        const updatePromises = this.enteredInvoices.map((invoice) => {
          const newBalanceAmount = invoice.balanceAmount - invoice.creditAmount;
          return this.updateInvoiceBalancePromise(
            invoice.invoiceHeadId,
            newBalanceAmount
          );
        });

        Promise.all(updatePromises)
          .then(() => {
            Swal.fire({
              title: 'Success!',
              text: 'The Credit Note has been successfully saved and invoices updated.',
              icon: 'success',
              confirmButtonText: 'OK',
            }).then(() => this.router.navigate(['/credit-note']));
          })
          .catch((error) => {
            Swal.fire({
              title: 'Error!',
              text: 'Failed to update invoice balances.',
              icon: 'error',
              confirmButtonText: 'OK',
            });
          });
      },
      (error: HttpErrorResponse) => {
        Swal.fire({
          title: 'Error!',
          text: 'Unable to save the Credit Note. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
        });
      }
    );
  }


updateInvoiceBalancePromise(invoiceHeadId: number, newBalanceAmount: number): Promise<any> {
  return new Promise((resolve, reject) => {
    this.invoiceService.updateInvoiceBalance(invoiceHeadId, newBalanceAmount).subscribe(
      (response) => {
        console.log(`Invoice ${invoiceHeadId} balance updated successfully.`);
        resolve(response);
      },
      (error) => {
        // Check if it's a parse error but response status is 200
        if (error.status === 200 && error.message?.includes('Http failure during parsing')) {
          console.warn(`Parsing error ignored for invoice ${invoiceHeadId}:`, error);
          resolve({ message: 'Parsed manually, assuming success.' });
        } else {
          console.error(`Failed to update balance for invoice ${invoiceHeadId}`, error);
          reject(error);
        }
      }
    );
  });
}


  updateBusinessEntityCreditNoteNumber() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '0');
    this.businessEntity.creditNoteNumber = this.creditNote.creditNoteNumber;
    this.entityService
      .updateCreditNoteNumber(this.businessEntity, this.businessEntityId)
      .subscribe(
        (data) => {},
        (error) => {
          console.error(error);
        }
      );
  }

  onInvoiceNumberChange(invoiceNumber: string, index: number): void {
    console.log('Invoice No Changed:', invoiceNumber, 'Index:', index); // Debugging

    // Clear any existing debounce timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // Set a new debounce timer to delay the API call
    this.debounceTimer = setTimeout(() => {
      console.log('Calling fetchInvoiceDetailsOnEnter with:', invoiceNumber, index);
 
      // Call the method to fetch the invoice details
      this.fetchInvoiceDetailsOnEnter(invoiceNumber, index);
    }, 300); // 300ms delay
  }

  fetchInvoiceDetailsOnEnter(invoiceNumber: string, index: number): void {
    console.log('Fetching Invoice:', invoiceNumber, 'For index:', index);
    if (!invoiceNumber) return;

    if (!this.creditNote.customerName) {
      Swal.fire({
        title: 'Customer Not Selected',
        text: 'Please select a customer first.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f',
      });
      return; // Exit the function if no customer is selected
    }

    // Check if the invoice number already exists in the enteredInvoices array
    const duplicateInvoice = this.enteredInvoices.some(
      (invoice, i) => invoice.invoiceNumber === invoiceNumber && i !== index
    );

    if (duplicateInvoice) {
      Swal.fire({
        title: 'Duplicate Invoice',
        text: 'This invoice number has already been entered.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f',
      });
      return; // Exit the function to prevent adding the duplicate invoice
    }

    // const entityId = +localStorage.getItem('entityId')!;
    const entityId = +(localStorage.getItem('entityId') + '');
    console.log('Entity ID:', entityId); // Debugging
    this.invoiceService.getInvoiceDetailsByNumberEn(invoiceNumber, entityId).subscribe(
      (invoiceData: any) => {
        console.log('Received invoice data:', invoiceData);
        if (invoiceData) {
          console.log('Fetched Invoice Data:', invoiceData); // Debugging
          
          // Check if the customer name of the invoice matches the selected customer name
          if (invoiceData.reference === this.creditNote.customerName) {
            // Update the entered invoice data with the fetched details, including invoiceHeadId
            this.enteredInvoices[index] = {
              ...this.enteredInvoices[index],
              invoiceHeadId: invoiceData.invoiceHeadId, // Make sure to capture the invoiceHeadId
              postingDate: invoiceData.postingDate,
              grandTotal: invoiceData.grandTotal,
              balanceAmount: invoiceData.balanceAmount,
              creditAmount: 0, // Initialize with 0, can be changed later by user
            };
            this.isInvalidInvoice = false; // Enable button when invoice is valid
          } else {
            // Show a SweetAlert warning if customer names do not match
            Swal.fire({
              title: 'Customer Mismatch',
              text: 'This invoice does not belong to the selected customer.',
              icon: 'warning',
              confirmButtonText: 'OK',
              confirmButtonColor: '#ff7e5f',
            });
            this.isInvalidInvoice = true; // Disable button if invoice doesn't belong to customer
          }
        } else {
          console.error('Invoice not found');
          this.isInvalidInvoice = true; // Disable button if invoice not found
        }
      },
      (error) => {
        console.error('Error fetching invoice details:', error);
        this.isInvalidInvoice = true; // Disable button on error
      }
    );
  }

  removeItem(index: number) {
    this.enteredInvoices.splice(index, 1); // Remove the item at the specified index

    this.getTotalCreditAmountEntered();
  }

  getTotalCreditAmountEntered(): number {
    return this.enteredInvoices.reduce(
      (total, invoice) => total + (invoice.creditAmount || 0),
      0
    );
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  addInvoiceRow(): void {
    this.enteredInvoices.push({
      invoiceNumber: '',
      postingDate: '',
      grandTotal: 0,
      balanceAmount: 0,
      creditAmount: 0,
    });
  }

  searchInvoices() {
    if (!this.creditNote.customerName) {
      // this.closeSearchPopup();
      Swal.fire({
        title: 'Customer Not Selected',
        text: 'Please select a customer first.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f',
      });
      this.isInvalidInvoice = true; // Disable button if no customer
      return; // Exit the function if no customer is selected
    }
    if (!this.searchCriteria.fromDate || !this.searchCriteria.toDate) {
      Swal.fire({
        title: 'Invalid Search',
        text: 'Please select Date range.',
        icon: 'warning',
        confirmButtonText: 'OK',
      });
      this.isInvalidInvoice = true; // Disable button if invalid range
      return;
    }

    this.invoiceService
      .getInvoicesByDateRangeAndCustomer(
        this.searchCriteria.fromDate,
        this.searchCriteria.toDate,
        this.creditNote.businessPartnerId
      )
      .subscribe(
        (invoices: any[]) => {
          this.filteredInvoices = invoices;

          // Check if there are no results and display a message
          if (this.filteredInvoices.length === 0) {
            Swal.fire({
              title: 'No Invoices Found',
              text: 'No invoices found within the selected date range.',
              icon: 'info',
              confirmButtonText: 'OK',
              confirmButtonColor: '#007bff',
            });
            this.isInvalidInvoice = true; // Disable button if invalid no invoices found
          }
        },
        (error: HttpErrorResponse) => {
          console.error('Error fetching invoices:', error);
          this.isInvalidInvoice = true; // Disable button on error
        }
      );
  }

  // This method is triggered when the search button is clicked
  onSearchInvoiceClick(index: number): void {
    this.activeRowIndex = index; // Store the index of the row
  }

  selectInvoice(selectedInvoice: any): void {
    // Check if the invoice number already exists in enteredInvoices
    const duplicateInvoice = this.enteredInvoices.some(
      (invoice) => invoice.invoiceNumber === selectedInvoice.invoiceNumber
    );

    if (duplicateInvoice) {
      Swal.fire({
        title: 'Duplicate Invoice',
        text: 'This invoice number has already been entered.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f',
      });
      this.isInvalidInvoice = true; // Disable button if duplicate
      return; // Exit the function to prevent adding the duplicate invoice
    }

    if (this.activeRowIndex !== null) {
      this.enteredInvoices[this.activeRowIndex].invoiceNumber =
        selectedInvoice.invoiceNumber;
      this.enteredInvoices[this.activeRowIndex].grandTotal =
        selectedInvoice.grandTotal;
      this.enteredInvoices[this.activeRowIndex].balanceAmount =
        selectedInvoice.balanceAmount;
      this.enteredInvoices[this.activeRowIndex].invoiceHeadId =
        selectedInvoice.invoiceHeadId; // Ensure this line is present
      this.enteredInvoices[this.activeRowIndex].creditAmount = 0; // Reset the credit amount for the new entry

      this.activeRowIndex = null;

      // Clear the filteredInvoices list
      this.filteredInvoices = [];

      // this.closeSearchPopup();
      this.isInvalidInvoice = false; // Enable button when selection is valid
    }
  }

  isModalOpen = false;

  openModal(): void {
    this.isModalOpen = true; // Show the modal
    setTimeout(() => {
      const modalElement = document.getElementById('invoiceSearchModal');
      if (modalElement) {
        const bootstrapModal = new window.bootstrap.Modal(modalElement);
        bootstrapModal.show(); // Bootstrap modal JavaScript API to open modal
      }
    }, 0);
  }

  closeSearchPopup(): void {
    this.isModalOpen = false; // Hide the modal via Angular

    // Also, close the modal using Bootstrap's JavaScript API
    const modalElement = document.getElementById('invoiceSearchModal');
    if (modalElement) {
      const bootstrapModal = window.bootstrap.Modal.getInstance(modalElement); // Get the existing modal instance
      if (bootstrapModal) {
        bootstrapModal.hide(); // Close the modal programmatically
      }
    }
  }
}
