import { Component, OnInit } from '@angular/core';
import { EmployeeService } from '../../payroll-settings/services/employee.service';
import { PayRunService } from '../../payroll-settings/services/pay-run.service';
import { ActivatedRoute, Router } from '@angular/router';
import {
  EmployeeLeave,
  LeaveType,
  NewEarning,
  NewLeave,
  Personal,
} from '../../payroll-settings/empolyee/employee';
import Swal from 'sweetalert2';
import {
  Deduction,
  Earning,
  PayRunDetail,
  PayRunDetailsSummary,
  Reimbursement,
} from '../../payroll-settings/payroll-setting';
import { ReimbursementsService } from '../../payroll-settings/services/reimbursements.service';
import { DeductionService } from '../../payroll-settings/services/deduction.service';
import { EarningService } from '../../payroll-settings/services/earning.service';
import { PayRunDetailService } from '../../payroll-settings/services/pay-run-detail.service';

@Component({
  selector: 'app-pay-run-pay-template',
  templateUrl: './pay-run-pay-template.component.html',
  styleUrls: ['./pay-run-pay-template.component.css'],
})
export class PayRunPayTemplateComponent implements OnInit {
  employeeId!: number;
  payPeriodId!: number;
  payRunId!: number;
  personal: Personal[] = [];
  payRunDetails: PayRunDetail[] = [];
  payTemplateEarnings: NewEarning[] = [];
  newEarning: NewEarning = new NewEarning();
  newPayRunDetail: PayRunDetail = new PayRunDetail();
  earnings: Earning[] = [];
  deductions: Deduction[] = [];
  reimbursements: Reimbursement[] = [];
  totalEarningAmount = 0;
  totalDeductionAmount = 0;
  totalReimbursementAmount = 0;
  selectedEarningType: string | null = null;
  selectedEarningsName: any;
  selectedRate: number = 0.0;
  selectedDeduction: Deduction | null = null;
  selectedReimbursement: Reimbursement | null = null;
  leaves: EmployeeLeave[] = [];
  leaveList: any[] = [];
  earningDropdown: any[] = [];
  payRunDetailsSummary: PayRunDetailsSummary = new PayRunDetailsSummary();

  SUPERANNUATION: number = 0;

  totalEarnings: number = 0.0;
  totalEarningsForSuper: number = 0.0;
  totalDeductions: number = 0.0;
  totalDeductionsForSuper: number = 0.0;
  totalReimbursements: number = 0.0;
  superAmount: number = 0.0;
  super: number = 0.0;

  isEditing: PayRunDetail | null = null;

  constructor(
    private employeeService: EmployeeService,
    private payRunService: PayRunService,
    private route: ActivatedRoute,
    private router: Router,
    private earningService: EarningService,
    private deductionService: DeductionService,
    private reimbursementsService: ReimbursementsService,
    private payRunDetailService: PayRunDetailService
  ) {}

  ngOnInit(): void {
    this.getAllPayTemplateData();
    this.getEarnings();
    this.getDeductions();
    this.getReimbursement();
    this.getPayRunDetail();
    this.getLeaveTypes();
    this.getPayRunDetailsSummaryData();
  }
  private getEarnings() {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.earningService.getEarningList(entityId).subscribe((data) => {
      this.earnings = data;
      console.log('Earning data', data);

      this.updateDropdownOptions();
      this.calculateSuperannuation();
    });
  }

  private getLeaveTypes() {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.earningService
      .getLeaveList(entityId, this.employeeId)
      .subscribe((data) => {
        this.leaves = data;
        this.leaveList = data.map((item) => ({
          type: 'leave',
          name: item.leaveType.leaveType,
        }));
        this.updateDropdownOptions();
      });
  }

  // create the drop down list for category in earning
  private updateDropdownOptions() {
    const payRunNames = new Set(
      this.payRunDetails?.map((item) => item.codeType) || []
    );

    const formattedEarnings = this.earnings
      .filter((item) => !payRunNames.has(item.earningsName)) // Remove existing earnings
      .map((item) => ({
        type: 'earning',
        name: item.earningsName,
      }));

    const formattedLeaveList = (this.leaveList || [])
      .filter((item) => payRunNames.has(item.name)) // Remove existing leave types
      .map((item) => ({
        type: 'leave',
        name: item.name,
      }));
    this.earningDropdown = [...formattedEarnings, ...formattedLeaveList];
    console.log('Dropdown options:', this.earningDropdown);
  }

  openEarningModal() {
    this.selectedEarningsName = '';
    this.newEarning = {
      earning: new Earning(),
      amount: 0.0,
      hours: 0.0,
      rate: 0.0,
      typeOfUnits: '',
      employeeEarningId: 0,
    };
  }

  onEarningTypeChange(value: any): void {
    this.newEarning = {
      earning: new Earning(),
      amount: 0.0,
      hours: 0.0,
      rate: 0.0,
      typeOfUnits: '',
      employeeEarningId: 0,
    };

    if (value.type === 'earning') {
      const selectedEarning = this.earnings.find(
        (earning) => earning.earningsName === value.name
      );

      if (selectedEarning) {
        this.newEarning.rate = selectedEarning.rate;
        this.newEarning.typeOfUnits = selectedEarning.typeOfUnits;

        if (this.newEarning.typeOfUnits === 'Fixed Amount') {
          this.newEarning.amount = this.newEarning.rate;
          this.newEarning.rate = 0.0;
        }
      } else {
        this.newEarning.amount = 0.0;
      }
      this.calculateAmount();
    } else if (value.type === 'leave') {
      const selectedLeave = this.leaves.find(
        (leave) => leave.leaveType.leaveType === value.name
      );

      const ordinaryEarning = this.payRunDetails.find(
        (detail) => detail.code == 0 && detail.type == 'EARNING'
      );

      if (selectedLeave && ordinaryEarning) {
        this.newEarning.rate = ordinaryEarning.rate;
        this.newEarning.typeOfUnits = 'Rate';
      }
    }
  }

  private getDeductions() {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.deductionService.getDeductionList(entityId).subscribe((data) => {
      this.deductions = data;
      console.log('Deductions: ', this.deductions);
    });
  }

  private getReimbursement() {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.reimbursementsService
      .getReimbursementsList(entityId)
      .subscribe((data) => {
        this.reimbursements = data;
      });
  }

  getAllPayTemplateData(): void {
    this.route.params.subscribe((params) => {
      this.employeeId = +params['employeeId'];
    });

    this.employeeService.getEmployeeById(this.employeeId).subscribe({
      next: (data: Personal) => {
        this.personal = [data];
      },
      error: (err) => {
        console.error('Error fetching Pay Template data:', err);
      },
    });
  }

  private getPayRunDetail() {
    const entityId = +(localStorage.getItem('entityId') || '');
    this.route.params.subscribe((params) => {
      this.payPeriodId = +params['payPeriodId'];
      this.payRunService
        .getPayRunDetails(entityId, this.employeeId, this.payPeriodId)
        .subscribe((data) => {
          this.payRunDetails = data;
          this.calculateSuperannuation();
          this.updateTotalEarnings();

          console.log('PayRun details: ', this.payRunDetails);
          this.updateDropdownOptions();

          if (Array.isArray(this.payRunDetails)) {
            for (let payRun of this.payRunDetails) {
              if (payRun.type === 'REIMBURSEMENT') {
                this.totalReimbursements += payRun.amount || 0;
              }
            }
          }
        });
    });
  }

  calculateAmount(): void {
    if (this.newEarning.typeOfUnits === 'Rate') {
      this.newEarning.amount = this.newEarning.hours
        ? this.newEarning.rate * this.newEarning.hours
        : 0.0;
    }
  }

  addPayRunDetail(type: any): void {
    const entityId = +(localStorage.getItem('entityId') || '');
    const userId = +(localStorage.getItem('userid') || '');
    const date = new Date().toISOString();
    let selectedPayRun: any;

    this.newPayRunDetail.type = type;
    this.newPayRunDetail.entityId = entityId;
    this.newPayRunDetail.employee = this.personal[0];
    this.newPayRunDetail.payRun = this.payRunDetails[0].payRun;

    if (this.selectedEarningsName) {
      if (this.selectedEarningsName.type === 'earning') {
        selectedPayRun = this.earnings.find(
          (earning) => earning.earningsName === this.selectedEarningsName.name
        );

        this.newPayRunDetail.type = 'EARNING';
        this.newPayRunDetail.amount = this.newEarning.amount;
        this.newPayRunDetail.hours = this.newEarning.hours;
        this.newPayRunDetail.rate = this.newEarning.rate;
        this.newPayRunDetail.code = selectedPayRun.earningId;
        this.newPayRunDetail.codeType = selectedPayRun.earningsName;
        this.newPayRunDetail.glAccount = selectedPayRun.glAccount;
        this.newPayRunDetail.glAccountName = selectedPayRun.glAccountName;
      } else if (this.selectedEarningsName.type === 'leave') {
        selectedPayRun = this.leaves.find(
          (leave) =>
            leave.leaveType.leaveType === this.selectedEarningsName.name
        );

        this.newPayRunDetail.type = 'LEAVE';
        this.newPayRunDetail.amount = this.newEarning.amount;
        this.newPayRunDetail.hours = this.newEarning.hours;
        this.newPayRunDetail.rate = this.newEarning.rate;
        this.newPayRunDetail.code = selectedPayRun.leaveType.leaveTypeId;
        this.newPayRunDetail.codeType = selectedPayRun.leaveType.leaveType;
      }
    } else if (this.selectedDeduction) {
      this.newPayRunDetail.code = this.selectedDeduction.deductionId;
      this.newPayRunDetail.glAccount = this.selectedDeduction.glAccount;
      this.newPayRunDetail.glAccountName = this.selectedDeduction.glAccountName;
      this.newPayRunDetail.codeType = this.selectedDeduction.deductionName;
    } else if (this.selectedReimbursement) {
      this.newPayRunDetail.code = this.selectedReimbursement.reimbursementId;
      this.newPayRunDetail.codeType =
        this.selectedReimbursement.reimbursementType;
      this.newPayRunDetail.glAccount = this.selectedReimbursement.glAccount;
      this.newPayRunDetail.glAccountName = this.selectedReimbursement.glAccountName;
    }

    this.payRunDetailService.addPayRunDetail(this.newPayRunDetail).subscribe(
      (_response: any) => {
        const successMessage =
          type === 'DEDUCTION'
            ? 'Deduction Data added successfully!'
            : type === 'EARNING'
            ? 'Earning Data added successfully!'
            : 'Reimbursement Data added successfully!';

        Swal.fire({
          title: 'Success!',
          text: successMessage,
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.newEarning = new NewEarning();
          this.selectedEarningsName = '';
          this.selectedDeduction = null;
          this.selectedReimbursement = null;
          this.newPayRunDetail = new PayRunDetail();
          this.getPayRunDetail();
          this.selectedEarningsName = '';
          this.newEarning = new NewEarning();
          this.getPayRunDetailsSummaryData();
          this.getLeaveTypes();
        });
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save PayRun Detail. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
    this.selectedEarningsName = '';
    this.newEarning = new NewEarning();
  }

  deletePayRun(index: number) {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you really want to delete this item? This action cannot be undone.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Confirm',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (result.isConfirmed) {
        this.payRunService.deletePayRun(index).subscribe(
          (_response: any) => {
            console.log('response:', _response);

            Swal.fire({
              title: 'Success!',
              text: 'Delete successfully!',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            }).then(() => {
              this.getPayRunDetail();
              this.updateTotalEarnings();
              this.getPayRunDetailsSummaryData()
            });
          },
          (_error: any) => {
            Swal.fire({
              title: 'Error!',
              text: 'Failed to delete. Please try again.',
              icon: 'error',
              confirmButtonText: 'OK',
              confirmButtonColor: '#be0032',
            });
          }
        );
      }
    });
  }

  // Update earnings total
  updateEarning() {
    this.updateTotalEarnings();
  }

  // Calculate total earnings
  updateTotalEarnings() {
    this.totalEarningAmount = this.payTemplateEarnings.reduce(
      (sum, earning) => sum + (earning.amount || 0),
      0
    );
  }
  closeEarningModal() {
    this.selectedEarningsName = '';
    this.newEarning = new NewEarning();
  }

  editEarning(earning: PayRunDetail): void {
    this.isEditing = earning;
  }

  updateAmount(earning: PayRunDetail): void {
    earning.amount = (earning.hours || 0) * (earning.rate || 0);
  }

  // saving editted earning
  saveEarning(earning: PayRunDetail): void {
    this.isEditing = null;
    this.payRunDetailService
      .updatePayRunDetail(earning.payRunDetailId, earning.entityId, earning)
      .subscribe(
        (data) => {
          this.getPayRunDetail();
          Swal.fire({
            title: 'Success!',
            text: 'Update successfully!',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
            // confirmButtonColor: '#007bff',
          }).then(() => {
            this.getLeaveTypes();
          });
        },
        (error) => {
          Swal.fire({
            title: 'Error!',
            text: 'Failed to update. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
  }

  calculateSuperannuation() {
    // Reset values
    this.totalEarnings = 0;
    this.totalDeductions = 0;
    this.totalEarningsForSuper = 0;
    this.totalDeductionsForSuper = 0;
    this.superAmount = 0;

    if (Array.isArray(this.payRunDetails)) {
      for (let payRun of this.payRunDetails) {
        if (payRun.type === 'EARNING' || payRun.type === 'LEAVE') {
          this.totalEarnings += payRun.amount || 0;
          const matchingEarning = this.earnings.find(
            (e) => e.earningId === payRun.code
          );
          if (
            matchingEarning &&
            matchingEarning.exemptFromSuperannuation === false
          ) {
            this.totalEarningsForSuper += payRun.amount || 0;
          }
        } else if (
          payRun.type === 'DEDUCTION' &&
          payRun.amount &&
          !payRun.rate
        ) {
          this.totalDeductions += payRun.amount || 0;
          const matchingDeduction = this.deductions.find(
            (d) => d.deductionId === payRun.code
          );
          if (
            matchingDeduction &&
            matchingDeduction.exemptFromSuperannuation === 'false'
          ) {
            this.totalDeductionsForSuper += payRun.amount || 0;
          }
        }
      }

      for (let payRun of this.payRunDetails) {
        if (payRun.type === 'DEDUCTION' && !payRun.amount && payRun.rate) {
          payRun.amount = (payRun.rate * this.totalEarningsForSuper) / 100;
          this.totalDeductionsForSuper += payRun.amount || 0;
          this.totalDeductions += payRun.amount || 0;
        }
      }

      for (let payRun of this.payRunDetails) {
        if (
          payRun.type === 'SUPERANNUATION' &&
          !payRun.amount &&
          payRun.rate &&
          this.totalEarningsForSuper
        ) {
          // Calculate net super amount
          this.superAmount =
            this.totalEarningsForSuper - this.totalDeductionsForSuper;
          // Apply superannuation rate
          this.super = (this.superAmount * payRun.rate) / 100;
          payRun.amount = this.super;
        }
      }
    }
  }

  getPayRunDetailsSummaryData() {
    this.route.params.subscribe((params) => {
      this.payRunId = +params['payRunId'];
    });
    this.payRunDetailService
      .getPayRunDetailsSummary(this.employeeId, this.payRunId)
      .subscribe({
        next: (data: any) => {
          this.payRunDetailsSummary = data;
          console.log(' pay run details summary : ', this.payRunDetailsSummary);
        },
        error: (err) => {
          console.error('Error fetching pay run detail summary data:', err);
        },
      });
  }
}
