<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<form class="form-container" #bankForm="ngForm" (ngSubmit)="submit(bankForm)">
  <div class="text-header">Bank Accounts</div>

  <div class="box-div">
    <div class="primary-acc-text">Primary Account</div>

    <!-- 🔽 Basiq Account Dropdown -->
    <div class="form-group full-width">
      <label class="input-labels">Link to Basiq Account</label>
      <select
        class="form-input"
        [(ngModel)]="selectedBasiqAccountId"
        name="basiqAccountId"
        #basiqAcc="ngModel"
        (change)="onBasiqAccountSelected()"
      >
        <option [ngValue]="null">-- Select Basiq Account --</option>
        <option *ngFor="let acc of basiqAccounts" [value]="acc.id">
          {{ acc.name }} ({{ acc.accountNo }}) - {{ acc.balance | currency }}
        </option>
      </select>
      <div class="error-message" *ngIf="basiqAcc.invalid && basiqAcc.touched">
        Please select a Basiq Account.
      </div>
    </div>

    <!-- 🏦 Manual Bank Details -->
    <div class="form-row">
      <div class="form-group">
        <label class="input-labels">Bank Name</label>
        <input
          type="text"
          class="form-input"
          placeholder="Bank of Australia"
          required
          [(ngModel)]="bankAccount.bankName"
          name="bankName"
          #bankName="ngModel"
        />
        <div class="error-message" *ngIf="bankName.invalid && bankName.touched">
          Bank Name is required.
        </div>
      </div>

      <div class="form-group">
        <label class="input-labels">BSB No</label>
        <input
          type="text"
          class="form-input"
          placeholder="000 000"
          [(ngModel)]="bankAccount.bsbNo"
          name="bsbNo"
          #bsbNo="ngModel"
        />
        <div class="error-message" *ngIf="bsbNo.invalid && bsbNo.touched">
          BSB No is required.
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label class="input-labels">Account Name</label>
        <input
          type="text"
          class="form-input"
          placeholder="Samson Peera"
          required
          [(ngModel)]="bankAccount.accountName"
          name="accountName"
          #accountName="ngModel"
        />
        <div class="error-message" *ngIf="accountName.invalid && accountName.touched">
          Account Name is required.
        </div>
      </div>

      <div class="form-group">
        <label class="input-labels">Account No</label>
        <input
          type="text"
          class="form-input"
          placeholder="*********"
          required
          [(ngModel)]="bankAccount.accountNumber"
          name="accountNumber"
          #accountNumber="ngModel"
        />
        <div class="error-message" *ngIf="accountNumber.invalid && accountNumber.touched">
          Account Number is required.
        </div>
      </div>
    </div>
  </div>

  <hr class="hr-line" />

  <div class="button-div">
    <button class="cancel-btn" type="reset" (click)="onCancel()">Cancel</button>
    <button class="approve-btn" type="submit">Create</button>
  </div>
</form>
