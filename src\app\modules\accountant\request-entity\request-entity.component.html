<app-admin-navigation></app-admin-navigation>
<div class="container">
  <div class="add-accountant-form">
    <h2>Request to the Entity</h2>
    <form
      #entityRequestForm="ngForm"
      (ngSubmit)="entityRequestForm.form.valid && requestEntity()"
      novalidate
      (keydown)="preventSubmit($event)"
    >
      <div class="form-group">
        <label for="business-entity">Business Entity</label>
        <select
          id="businessEntity"
          name="businessEntity"
          [(ngModel)]="businessEntityRequest.entityId.entityId"
          required
          class="form-select"
        >
          <option value="" disabled selected>Select Business Entity</option>
          <option *ngFor="let entity of entities" [value]="entity.entityId">
            {{ entity.entityName }}
          </option>
        </select>
        <div
          *ngIf="
            entityRequestForm.submitted &&
            entityRequestForm.controls['businessEntity']?.invalid
          "
          class="text-danger"
        >
          <div
            *ngIf="
              entityRequestForm.controls['businessEntity']?.errors?.['required']
            "
          >
            Business Entity is required.
          </div>
        </div>
      </div>

      <div class="form-actions">
        <!-- <button type="button" class="cancel" (click)="navigateToDashboard()">
          Cancel
        </button> -->
        <button type="submit" class="add-entity">Request</button>
      </div>
    </form>
  </div>
</div>
