import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import Swal from 'sweetalert2';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { BillService } from '../../bill/bill.service';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-pnl-report',
  templateUrl: './pnl-report.component.html',
  styleUrls: ['./pnl-report.component.css']
})
export class PnlReportComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  @ViewChild('pnlreportPreviewFrame') pnlreportPreviewFrame!: ElementRef;

  fromDate: string = '';
  toDate: string = '';
  isLoading: boolean = false;
  pnlType: string = 'accrual';
  private audio!: HTMLAudioElement;
  isCustom: boolean = false;
  comparisonOptions: { value: string, label: string }[] = [];
  selectedComparison: string = '';
  showComparisonDropdown: boolean = false;
  divideByMonths: boolean = false; // Checkbox state
  showDivideByMonths: boolean = false; // Determines if checkbox should be visible

  constructor(

    public sanitizer: DomSanitizer,
    private billService: BillService,
     private swalAlerts: SwalAlertsService,
        
  ) { }

  ngOnInit() {
  }


  updateDateRange(event: Event) {
    const selectedValue = (event.target as HTMLSelectElement).value;
    const today = new Date();
    let fromDate = new Date();
    let toDate = new Date();
    this.showComparisonDropdown = false;
    this.comparisonOptions = [];
    this.showDivideByMonths = false; // Hide checkbox by default
    this.divideByMonths = false; // Reset checkbox state

    switch (selectedValue) {
      case 'endOfThisMonth':
        fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
        toDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        this.showComparisonDropdown = true;
        this.comparisonOptions = [
          { value: 'monthly', label: 'Last Month' }, // Update to 'monthly'

        ];
        break;

      case 'endOfThisQuarter':
        const quarter = Math.floor(today.getMonth() / 3);
        fromDate = new Date(today.getFullYear(), quarter * 3, 1);
        toDate = new Date(today.getFullYear(), quarter * 3 + 3, 0);
        this.showComparisonDropdown = true;
        this.comparisonOptions = [
          { value: 'quarterly', label: 'Last Quarter' } // Update to 'quarterly'
        ];
        this.showDivideByMonths = true;
        break;

      case 'endOfThisYear':
        fromDate = new Date(today.getFullYear(), 0, 1);
        toDate = new Date(today.getFullYear(), 11, 31);
        this.showComparisonDropdown = true;
        this.comparisonOptions = [
          { value: 'yearly', label: 'Last Year' }, // Update to 'yearly'

        ];
        this.showDivideByMonths = true;
        break;

      case 'endOfLastMonth':
        fromDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        toDate = new Date(today.getFullYear(), today.getMonth(), 0);
        this.showComparisonDropdown = false; // No dropdown for last month, last quarter, last year
        break;

      case 'endOfLastQuarter':
        const quarter1 = Math.floor(today.getMonth() / 3);
        fromDate = new Date(today.getFullYear(), quarter1 * 3 - 3, 1);
        toDate = new Date(today.getFullYear(), quarter1 * 3, 0);
        this.showComparisonDropdown = false; // No dropdown for last month, last quarter, last year
        break;

      case 'endOfLastYear':
        fromDate = new Date(today.getFullYear() - 1, 0, 1);
        toDate = new Date(today.getFullYear() - 1, 11, 31);
        this.showComparisonDropdown = false; // No dropdown for last month, last quarter, last year
        break;

      case 'custom':
        this.isCustom = true;
        this.fromDate = '';
        this.toDate = '';
        return;

      default:
        return;
    }

    this.fromDate = fromDate.toISOString().split('T')[0];
    this.toDate = toDate.toISOString().split('T')[0];
  }


  onComparisonChange() {
    if (this.selectedComparison) {
      this.divideByMonths = false; // Uncheck the checkbox when comparison is selected
    }
  }

  /**previewPnlReport(fromDate: string, toDate: string, selectedComparison: string, divideByMonths: boolean) {
    if (!fromDate || !toDate) {
      Swal.fire({
        title: 'Warning!',
        text: 'Please select Date Range',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f'
      });
      return;
    }

    this.isLoading = true;
    const entityId = +(localStorage.getItem('entityId') || '0');

    let reportObservable;

    if (this.pnlType === 'accrual') {
      if (this.showComparisonDropdown && selectedComparison) {
        reportObservable = this.billService.getPnlListReportWithComparison(fromDate, toDate, entityId, selectedComparison);
      } else if (divideByMonths) {
        // Call with divideByMonths when it's true
        reportObservable = this.billService.getPnlListReportDivideByMonths(fromDate, toDate, entityId);
      } else {
        // Default call without divideByMonths
        reportObservable = this.billService.getPnlListReport(fromDate, toDate, entityId);
      }
    } else {
      if (this.showComparisonDropdown && selectedComparison) {
        reportObservable = this.billService.getPnlListReportWithComparison(fromDate, toDate, entityId, selectedComparison);
      } else {
        reportObservable = this.billService.getCashPnlListReport(fromDate, toDate, entityId);
      }
    }

    reportObservable.subscribe(
      data => {
        const base64String = data.response;
        if (base64String) {
          this.loadPdfIntoIframe(base64String);
        } else {
          this.isLoading = false;
          alert('No data for preview.');
        }
      },
      error => {
        this.isLoading = false;
        alert('Error loading preview.');
      }
    );
  }**/


    previewPnlReport(fromDate: string, toDate: string, selectedComparison: string, divideByMonths: boolean) {
  if (!fromDate || !toDate) {
    this.swalAlerts.showSwalWarning('Warning!', 'Please select Date Range', 'How to select a valid date range in P&L report?');
    return;
  }

  this.isLoading = true;
  const entityId = +(localStorage.getItem('entityId') || '0');
  const entityUUID = localStorage.getItem('entityUUID') || '';

  const baseRequest = { fromDate, toDate, entityId, entityUUID };
  let reportObservable;

  if (this.pnlType === 'accrual') {
    if (this.showComparisonDropdown && selectedComparison) {
      reportObservable = this.billService.getPnlListReportWithComparison({
        ...baseRequest,
        selectedComparison
      });
    } else if (divideByMonths) {
      reportObservable = this.billService.getPnlListReportDivideByMonths(baseRequest);
    } else {
      reportObservable = this.billService.getPnlListReport(baseRequest);
    }
  } else {
    // Cash based
    if (this.showComparisonDropdown && selectedComparison) {
      reportObservable = this.billService.getPnlListReportWithComparison({
        ...baseRequest,
        selectedComparison
      });
    } else {
      reportObservable = this.billService.getCashPnlListReport(baseRequest);
    }
  }

  reportObservable.subscribe(
    data => {
      const base64String = data.response;
      if (base64String) {
        this.loadPdfIntoIframe(base64String);
      } else {
        this.isLoading = false;
        this.swalAlerts.showSwalWarning('No Data', 'No P&L report data found for the selected range.', 'Why is my P&L report showing no data?');
      }
    },
    error => {
      this.isLoading = false;
      this.swalAlerts.showErrorWithChimpSupport('Error loading preview.', 'Why did the P&L report fail to load?');
    }
  );
}

          
            private loadPdfIntoIframe(base64String: string) {
              if (base64String && base64String.trim().length >= 50) {
                const pdfData = 'data:application/pdf;base64,' + base64String;
                const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
                const iframe = this.pnlreportPreviewFrame.nativeElement;
            
                iframe.onload = () => {
                  this.isLoading = false;
                };
            
                iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
            
                // Open modal manually using Bootstrap JS
                const modalElement = document.getElementById('simpleModal');
                const modal = new bootstrap.Modal(modalElement!);
                modal.show();
              } else {
                 this.isLoading = false;
                 this.swalAlerts.showSwalWarning('No Data', 'No Supplier Statement for preview.', 'No Supplier Statement was returned for the selected range.');
            
              }
            }
            


 /** private loadPdfIntoIframe(base64String: string) {
    let dataLoaded = false; // Flag to track if data was loaded successfully

    // Check if base64String is valid
    if (base64String && base64String.trim().length >= 50) { // Adjust the length check as needed
      const pdfData = 'data:application/pdf;base64,' + base64String;
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
      const iframe = this.pnlreportPreviewFrame.nativeElement;

      iframe.onload = () => {
        this.isLoading = false;
        dataLoaded = true; // Set flag to true when data loads
      };

      iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
    }

    // Check load status after a short delay
    setTimeout(() => {
      if (!dataLoaded) {
        this.isLoading = false;
        Swal.fire({
          title: 'No Data',
          text: 'No Bill data for preview.',
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff'
        });
      }
    }, 2000);
  }
**/

  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}

