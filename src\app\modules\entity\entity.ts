import { User } from '../admin/components/user/user';
import { Country } from './country/country';

export class Entity {
  entityId: any = 0;
  abn: string = '';
  entityName: string = '';
  gstRegistrationNumber: string = '';
  gstReturnFrequency: string = '';
  defaultCreditPeriod: string = '';
  email: string = '';
  businessStructure: string = '';
  city: string = '';
  state: string = '';
  postalCode: string = '';
  enableSubscriptionPayment: string = '';
  uniqueEntityCode: string = '';
  businessAddress: string = '';
  industryClassification: string = '';
  profitAndLossTemplate: string = '';
  invoiceNumber: string = '';
  taxApplicability: string = '';
  quoteNumber: string = '';
  referenceNo: string = '';
  jvNumber: string = '';
  creditNoteNumber: string = '';
  paymentReceiptNumber: string = '';
  expensesNumber: string = '';
  voucherNumber: string = '';
  paymentExpensesNumber: string = '';
  creditNoteNumberBill: string = '';
  stripeAccountId: string = '';
  basiqId: string = '';
  countryId: Country = new Country();
  logo: string = '';
  basPeriod: string = '';
  createdAt?:Date;
}

export class EntityTradingName {
  entityTradingNameId: number = 0;
  tradingName: string = '';
  entityId: Entity = new Entity();
  industryId: Industry = new Industry();
}

export class Industry {
  industryId: number = 0;
  coaTemplateId: number = 0;
  industryName: string = '';
}

export class BusinessEntityRequest {
  businessEntityRequestId: any = 0;
  entityId: Entity = new Entity();
  userId: User = new User();
}

export class InviteLog {
  inviteLogId: number = 0;
  userId: User = new User();
  entityId: number = 0;
  email: string = '';
  status: string = '';
}
