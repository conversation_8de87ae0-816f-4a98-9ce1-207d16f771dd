import { Component, OnInit } from '@angular/core';
import { BusinessEntityRequest, Entity } from '../../entity/entity';
import { EntityService } from '../../entity/entity.service';
import { Router } from '@angular/router';
import { AccountantService } from '../accountant.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-request-entity',
  templateUrl: './request-entity.component.html',
  styleUrls: ['./request-entity.component.css'],
})
export class RequestEntityComponent implements OnInit {
  entities: Entity[] = [];
  requestedEntities: number[] = [];
  businessEntityRequest: BusinessEntityRequest = new BusinessEntityRequest();

  constructor(
    private accountantService: AccountantService,
    private entityService: EntityService
  ) {}

  ngOnInit(): void {
    this.loadBusinessEntities();
  }

  private loadBusinessEntities() {
    this.entityService.getBusinessEntityList().subscribe((data) => {
      this.entities = data;
    });
    this.businessEntityRequest.entityId.entityId = '';
  }

  requestEntity(): void {
    this.businessEntityRequest.userId.userId = +(
      localStorage.getItem('userid') || '0'
    );

    this.accountantService
      .getBusinessEntityByuserId(this.businessEntityRequest.userId.userId)
      .subscribe((response) => {
        this.requestedEntities = response;

        if (
          this.requestedEntities.includes(
            this.businessEntityRequest.entityId.entityId
          )
        ) {
          Swal.fire({
            title: 'Warning!',
            text: 'Already requested.',
            icon: 'warning',
            showCancelButton: false,
            confirmButtonText: 'Ok',
          });
          return;
        } else {
          this.accountantService
            .saveBusinessEntityRequest(this.businessEntityRequest)
            .subscribe({
              next: () => {
                Swal.fire({
                  icon: 'success',
                  title: 'Request Submitted',
                  text: 'Your request for the business entity has been successfully submitted.',
                  confirmButtonText: 'OK',
                }).then((result) => {
                  if (result.isConfirmed) {
                    window.location.reload();
                  }
                });
              },
              error: (err) => {
                Swal.fire({
                  icon: 'error',
                  title: 'Submission Failed',
                  text: 'There was an error submitting your request. Please try again later.',
                  confirmButtonText: 'OK',
                });
              },
            });
        }
      });
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
}
