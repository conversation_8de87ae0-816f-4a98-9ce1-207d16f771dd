<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <div class="actions">
    <h1>Payroll</h1>
  </div>

  <div class="sub-container">
    <h2>Payroll Settings</h2>
  </div>

  

  <div class="payroll-info">
    <div class="payroll-tabs">
      <ul class="nav nav-tabs">
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeTab === 'calendars'"
            (click)="setActiveTab('calendars')"
            >Calendars</a
          >
        </li>
              
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="isActiveTab('pay-items')"
            (click)="setActiveTab('pay-items')"
            >Pay Items</a
          >
        </li>
        <li class="nav-item">
          <a
              class="nav-link"
              [class.active]="activeTab === 'employee'"
              (click)="setActiveTab('employee')"
          >
              Employee
          </a>
      </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeTab === 'pay-run'"
            (click)="setActiveTab('pay-run')"
            >Pay Run</a
          >
        </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeTab === 'stp'"
            (click)="setActiveTab('stp')"
            >STP</a
          >
        </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeTab === 'report'"
            (click)="setActiveTab('report')"
            >Report</a
          >
        </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeTab === 'bas'"
            (click)="setActiveTab('bas')"
            >BAS</a
          >
        </li>
      </ul>
    </div>
  </div>


  

  <!-- Conditional Display Based on Active Tab -->  <div class="pay-calendars-section" *ngIf="activeTab === 'calendars'">
    <div class="pay-calendars">
      <div class="pay-calendar-head">
        <strong>Pay Calendars</strong>
        <div class="btn-group">
          <button
            type="button"
            class="btn btn-primary "
            href="#"
            data-bs-toggle="modal"
            data-bs-target="#setup_multiple_pay_calenders"
          >
            + Add Pay Calendar
          </button>
        </div>
      </div>
      <div class="pay-calendar-table">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Name</th>
              <th>Pay Cycle </th>
              <th>Pay Start Date</th>
              <th>Next Pay Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let calendar of payCalendars | paginate: { itemsPerPage: 5, currentPage: page }">
              <td>{{ calendar.calendarName }}</td>
              <td>{{ calendar.payCycleName }}</td>
              <td>{{ calendar.payStartDate }}</td>
              <td>{{ calendar.nextPayDate }}</td>
              <td class="value">
                <button
                  (click)="updateCalendar(calendar.payCalendarId)"
                  class="btn btn-orange btn-sm"
                  style="
                    margin-right: 2px;
                    border: none;
                    background: none;
                    padding: 2px;
                    font-size: 1rem;
                  "
                  title="Edit"
                >
                  <i class="ri-edit-box-line" style="color: #4262ff"></i>
                </button>
                <button
                  class="btn btn-danger btn-sm"
                  (click)="deleteCalendar(calendar.payCalendarId)"
                  style="
                    margin-right: 2px;
                    border: none;
                    background: none;
                    padding: 2px;
                    font-size: 1rem;
                  "
                  title="Delete"
                >
                  <i class="ri-delete-bin-line" style="color: #ff0000"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <pagination-controls
          class="d-flex justify-content-end"
          (pageChange)="page = $event"
        >
        </pagination-controls>
      </div>
    </div>
  </div>

  <!-- Pay Items Section -->
  <div class="pay-items-section" *ngIf="isActiveTab('pay-items')">
    <!-- Sidebar -->
    <div class="side-bar">
      <ul class="nav nav_pay_item">
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Earnings')"
            (click)="setActiveSide('Earnings'); $event.preventDefault()"
          >
            Earnings
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Deductions')"
            (click)="setActiveSide('Deductions'); $event.preventDefault()"
          >
            Deductions
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Reimbursements')"
            (click)="setActiveSide('Reimbursements'); $event.preventDefault()"
          >
            Reimbursements
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Leave')"
            (click)="setActiveSide('Leave'); $event.preventDefault()"
          >
            Leave
          </a>
        </li>
      </ul>
      <div
        class="form-check form-switch"
        *ngIf="isActiveSide('Reimbursements')"
        style="margin-left: 20px; margin-top: 20px"
      >
        <input
          class="form-check-input"
          type="checkbox"
          role="switch"
          id="flexSwitchCheckDefault"
        />
        <label
          class="form-check-label"
          for="flexSwitchCheckDefault"
          style="font-size: 14px; font-weight: bold; color: #666666"
          >show inactive item</label
        >
      </div>
    </div>

    <!-- Main Content -->
    <div class="pay_item-content">
      <div class="Earnings" *ngIf="isActiveSide('Earnings')">
        <div class="pay_item_head">
          <h2>Earnings</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#Ordinary-Time_Earnings"
            >
              Add
            </button>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Earnings Name</th>
                <th>Earnings Category</th>
                <th>Rate</th>
                <!-- <th>Type of Units</th> -->
                <th>GL Account</th>
                <th>Reportable as WI</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let earning of earnings
                    | paginate : { itemsPerPage: 5, currentPage: earningsPage }
                "
              >
                <td>{{ earning.earningsName }}</td>
                <td>{{ earning.category }}</td>
                <td>{{ earning.rate }}</td>
                <!-- <td>{{ earning.payItemType }}</td> -->
                <td>{{ earning.glAccount }}</td>
                <td>{{ earning.wiReportable }}</td>
                <td class="value">
                  <button class="btn btn-orange btn-sm" (click)="updateEarnings(earning.earningId)" style="
                                    margin-right: 2px;
                                    border: none;
                                    background: none;
                                    padding: 2px;
                                    font-size: 1rem;
                                    visibility: visible;
                                  " title="Edit">
                    <i class="ri-edit-box-line" style="color: #4262ff"></i>
                  </button>
                  <button class="btn btn-danger btn-sm" (click)="deleteEarning(earning.earningId)" style="
                                    margin-right: 2px;
                                    border: none;
                                    background: none;
                                    padding: 2px;
                                    font-size: 1rem;
                                  " title="Delete">
                    <i class="ri-delete-bin-line" style="color: #ff0000"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <pagination-controls
            class="d-flex justify-content-end"
            (pageChange)="earningsPage = $event"
          >
          </pagination-controls>
        </div>
      </div>

          <div class="Deductions"  *ngIf="isActiveSide('Deductions')">
            <div class="pay_item_head">
              <h2>Deductions</h2>
              <div class="btn-group">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="modal" data-bs-target="#add-deductions">
                  Add
                </button>
              </div>
            </div>
  
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Deductions Name</th>
                    <th>Deductions Category</th>
                    <th>Account</th>
                    <th>Reduces PAYG</th>
                    <th>Reduces SGC</th>
                    <th>Excluded From W1</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let deduction of deductions | paginate: { itemsPerPage: 5, currentPage: deductionsPage }">
                    <td>{{ deduction.deductionName }}</td>
                    <td>{{ deduction.deductionCategory }}</td>
                    <td>{{ deduction.glAccount }}</td>
                    <td>{{ deduction.exemptFromPAYG }}</td>
                    <td>{{ deduction.exemptFromSuperannuation }}</td>
                    <td>{{ deduction.wiReportable }}</td>
                    <td class="value">
                      <button class="btn btn-orange btn-sm" (click)="updateDeductions(deduction.deductionId)" style="
                                                        margin-right: 2px;
                                                        border: none;
                                                        background: none;
                                                        padding: 2px;
                                                        font-size: 1rem;
                                                        visibility: hidden;
                                                      " title="Edit">
                        <i class="ri-edit-box-line" style="color: #4262ff"></i>
                      </button>
                      <button class="btn btn-danger btn-sm" (click)="deleteDeduction(deduction.deductionId)" style="
                                                        margin-right: 2px;
                                                        border: none;
                                                        background: none;
                                                        padding: 2px;
                                                        font-size: 1rem;
                                                      " title="Delete">
                        <i class="ri-delete-bin-line" style="color: #ff0000"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            
              <pagination-controls 
                class="d-flex justify-content-end" 
                (pageChange)="deductionsPage = $event">
              </pagination-controls>
            </div>
            
          </div>

      <div class="Reimbursements" *ngIf="isActiveSide('Reimbursements')">
        <div class="pay_item_head">
          <h2>Reimbursements</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#add-reimbursements"
            >
              Add
            </button>
          </div>
        </div>

            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Reimbursements Type</th>
                    <th>Description</th>
                    <th>Account Code</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let reimbursement of reimbursements | paginate: { itemsPerPage: 5, currentPage: reimbursementsPage }">
                    <td>{{ reimbursement.reimbursementType }}</td>
                    <td>{{ reimbursement.description }}</td>
                    <td>{{ reimbursement.glAccount }}</td>
                    <td><button class="btn btn-danger btn-sm" (click)="deleteReimbursement(reimbursement.reimbursementId)" style="
                                                        margin-right: 2px;
                                                        border: none;
                                                        background: none;
                                                        padding: 2px;
                                                        font-size: 1rem;
                                                      " title="Delete">
                      <i class="ri-delete-bin-line" style="color: #ff0000"></i>
                    </button></td>
                  </tr>
                </tbody>
              </table>
            
              <pagination-controls 
                class="d-flex justify-content-end" 
                (pageChange)="reimbursementsPage = $event">
              </pagination-controls>
            </div>
            
          </div>

   

      <div class="Leave" *ngIf="isActiveSide('Leave')">
        <div class="pay_item_head">
          <h2>Leave</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#add-leave-type-01"
            >
              Add Leave Type
            </button>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Leave Name</th>
                <th>Leave Category</th>
                <th>Units</th>
                <th>Normal Entitlement</th>
                <th>Leave Loading Rate</th>
                <th>Show on Payslip</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let leaveCategory of leaveCategories
                    | paginate : { itemsPerPage: 5, currentPage: leavesPage }
                "
              >
                <td>{{ leaveCategory.leaveCategory }}</td>
                <td>{{ leaveCategory.leaveCategory }}</td>
                <td>{{ leaveCategory.units }}</td>
                <td>{{ leaveCategory.normalEntitlement }}</td>
                <td>{{ leaveCategory.leaveLoadingRate }}</td>
                <td>{{ leaveCategory.showBalances }}</td>
                <td><i class="bi bi-three-dots-vertical"></i></td>
              </tr>
            </tbody>
          </table>

          <pagination-controls
            class="d-flex justify-content-end"
            (pageChange)="leavesPage = $event"
          >
          </pagination-controls>
        </div>
      </div>

     

      <div class="Leave" *ngIf="isActiveSide('Leave_Form_2')">
        <div class="pay_item_head">
          <h2>Leave</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              style="margin-right: 10px"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#assign-leave-type"
            >
              Leave Loading
            </button>
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#add-leave-type-form-2"
            >
              Add Leave Type
            </button>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Leave Name</th>
                <th>Leave Category</th>
                <th>Units</th>
                <th>Normal Entitlement</th>
                <th>Leave Loading Rate</th>
                <th>Show on Payslip</th>
                <th>Exempt from Superannuation</th>
                <th>Show Balance on Pay Slip</th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let leave of leaveTwo
                    | paginate : { itemsPerPage: 5, currentPage: leaveTwoPage }
                "
              >
                <td>{{ leave.name }}</td>
                <td>{{ leave.category }}</td>
                <td>{{ leave.units }}</td>
                <td>{{ leave.entitlement }}</td>
                <td>{{ leave.loadingRate }}</td>
                <td>{{ leave.showOnPayslip }}</td>
                <td>{{ leave.exemptSuperannuation }}</td>
                <td>{{ leave.showBalance }}</td>
              </tr>
            </tbody>
          </table>

          <pagination-controls
            class="d-flex justify-content-end"
            (pageChange)="leaveTwoPage = $event"
          >
          </pagination-controls>
        </div>
      </div>

      <div
        style="margin-top: 20px; margin-left: 20px; margin-bottom: 20px"
        *ngIf="isActiveSide('Leave_Form_2')"
      >
        <button
          type="button"
          class="btn btn-primary"
          data-bs-toggle="modal"
          data-bs-target="#add-leave-accural"
        >
          + Add Leave Accural
        </button>
      </div>

      <div
        class="Leave-form-3"
        style="background-color: transparent"
        *ngIf="isActiveSide('Leave_Form_3')"
      >
        <div class="pay_item_head">
          <h2>Leave</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#assign-leave-type"
            >
              Assign Leave Type
            </button>
          </div>
        </div>

        <div class="pay-item-2" style="margin-top: 10px">
          <div class="pay-item-one">
            <div class="pay_item_head">
              <h2>Leave Loading Balance</h2>
              <div class="btn-group">
                <button class="btn btn-primary dropdown-toggle" type="button">
                  leave loading Eligibility
                </button>
              </div>
            </div>

            <div class="pay-item-two">
              <div class="pay-item-col1">
                <span>Leave Loading Balance</span>
                <strong style="color: #4262ff; font-weight: bold"
                  >88.0245 Hours</strong
                >
              </div>
              <div class="pay-item-col2">
                <span>Personal (Sick/Carer's) Leave</span>
                <strong style="color: #4262ff; font-weight: bold"
                  >88.0245 Hours</strong
                >
              </div>
            </div>

            <div class="pay_item_head">
              <h2>Leave</h2>
              <div class="btn-group">
                <button
                  class="btn btn-primary dropdown-toggle"
                  type="button"
                  [class.active]="isActiveSide('Leave_Form_2')"
                  (click)="
                    setActiveSide('Leave_Form_2'); $event.preventDefault()
                  "
                  data-bs-dismiss="modal"
                >
                  New Request
                </button>
              </div>
            </div>
          </div>

          <div class="pay-item-one">
            <div class="pay-item-two" style="gap: 15px">
              <div class="pay-item-col3">
                <!-- <i class="bi bi-search"></i> -->
                <input
                  type="text"
                  class="form-control"
                  placeholder=" Search"
                  aria-label="Search"
                  style="width: 100%; background-color: #f3f3f3"
                />
              </div>
              <div class="filter-buttons">
                <button class="btn btn-light">
                  <i class="bi bi-funnel"></i> Search
                </button>
                <button class="btn btn-light">
                  <i class="bi bi-arrow-down-up"></i> Status
                </button>
              </div>
            </div>

            <div class="pay-item-three">
              <h2>To Review</h2>
              <div class="pay-item-four">
                <div class="pro-pic">JL</div>
                <div class="User-Details">
                  <h5>
                    James Safron <small class="text-muted">Annual Leave</small>
                  </h5>
                  <p>
                    <small style="color: #4262ff">02 Apr 2024 Day Off</small>
                  </p>
                </div>
                <div class="approve-btn">
                  <button class="btn btn-outline-primary">Approve</button>
                </div>
                <div class="app-icon">
                  <i class="bi bi-three-dots-vertical"></i>
                </div>
              </div>
            </div>

            <div class="pay-item-three">
              <h2>Upcoming</h2>
              <div class="pay-item-four">
                <div class="pro-pic">JL</div>
                <div class="User-Details">
                  <h5>
                    James Safron <small class="text-muted">Annual Leave</small>
                  </h5>
                  <p>
                    <small style="color: #4262ff">02 Apr 2024 Day Off</small>
                  </p>
                </div>
                <div class="approve-btn">
                  <button class="btn btn-outline-primary">Approve</button>
                </div>
                <div class="app-icon">
                  <i class="bi bi-three-dots-vertical"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        style="margin-top: 20px; margin-left: 20px; margin-bottom: 20px"
        *ngIf="isActiveSide('Leave_Form_3')"
      >
        <button
          type="button"
          class="btn btn-primary"
          data-bs-toggle="modal"
          data-bs-target="#draft-pay-slip"
        >
          + Adjust Draft Payslip
        </button>
      </div>

      <div
        style="margin-top: 20px; margin-left: 20px; margin-bottom: 20px"
        *ngIf="isActiveSide('Leave_Form_3')"
      >
        <button
          type="button"
          class="btn btn-primary"
          data-bs-toggle="modal"
          data-bs-target="#add-leave-loading-percentage"
        >
          + Add a leave Loading Percentage
        </button>
      </div>

      <div
        class="Leave-form-final"
        style="background-color: transparent"
        *ngIf="isActiveSide('Leave-form-final')"
      >
        <div class="pay_item_head">
          <h2>Leave</h2>
          <div class="btn-group">
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#edit-leave-type"
            >
              Edit Leave Type
            </button>
          </div>
        </div>

        <div class="leave-emp">
          <div class="emp-name">
            <label for="employee-name">Employee Name</label>
            <p class="employee-name">Elizabeth Bennison</p>
          </div>
          <div class="salary-earnings">
            <label for="annual-salary">Annual Salary</label>
            <button class="view-btn">View</button>
          </div>
          <div class="salary-earnings">
            <label for="earnings-ytd">Earnings YTD</label>
            <button class="view-btn">View</button>
          </div>
          <div class="date-next-payment">
            <label for="next-payment-date">Next Payment Date</label>
            <strong class="next-payment-date">Oct 28 2024</strong>
          </div>
        </div>

        <div class="pay-item-2" style="margin-top: 10px">
          <div class="pay-item-one">
            <div class="pay_item_head">
              <h2>Leave Loading Balance</h2>
              <div class="btn-group">
                <button class="btn btn-primary dropdown-toggle" type="button">
                  leave loading Eligibility
                </button>
              </div>
            </div>

            <div class="pay-item-two">
              <div class="pay-item-col1">
                <span>Leave Loading Balance</span>
                <strong style="color: #4262ff; font-weight: bold"
                  >88.0245 Hours</strong
                >
              </div>
              <div class="pay-item-col2">
                <span>Personal (Sick/Carer's) Leave</span>
                <strong style="color: #4262ff; font-weight: bold"
                  >88.0245 Hours</strong
                >
              </div>
            </div>

            <div class="pay_item_head">
              <h2>Leave</h2>
              <div class="btn-group">
                <button
                  class="btn btn-primary dropdown-toggle"
                  type="button"
                  data-bs-toggle="modal"
                  data-bs-target="#add-leave-type-form-2"
                >
                  New Request
                </button>
              </div>
            </div>
          </div>

          <div class="pay-item-one">
            <div class="pay-item-two" style="gap: 15px">
              <div class="pay-item-col3">
                <!-- <i class="bi bi-search"></i> -->
                <input
                  type="text"
                  class="form-control"
                  placeholder=" Search"
                  aria-label="Search"
                  style="width: 100%; background-color: #f3f3f3"
                />
              </div>
              <div class="filter-buttons">
                <button class="btn btn-light">
                  <i class="bi bi-funnel"></i> Search
                </button>
                <button class="btn btn-light">
                  <i class="bi bi-arrow-down-up"></i> Status
                </button>
              </div>
            </div>

            <div class="pay-item-three">
              <h2>To Review</h2>
              <div class="pay-item-four">
                <div class="pro-pic">JL</div>
                <div class="User-Details">
                  <h5>
                    James Safron <small class="text-muted">Annual Leave</small>
                  </h5>
                  <p>
                    <small style="color: #4262ff">02 Apr 2024 Day Off</small>
                  </p>
                </div>
                <div class="approve-btn">
                  <button class="btn btn-outline-primary">Approve</button>
                </div>
                <div class="app-icon">
                  <i class="bi bi-three-dots-vertical"></i>
                </div>
              </div>
            </div>

            <div class="pay-item-three">
              <h2>Upcoming</h2>
              <div class="pay-item-four">
                <div class="pro-pic">JL</div>
                <div class="User-Details">
                  <h5>
                    James Safron <small class="text-muted">Annual Leave</small>
                  </h5>
                  <p>
                    <small style="color: #4262ff">02 Apr 2024 Day Off</small>
                  </p>
                </div>
                <div class="approve-btn">
                  <button class="btn btn-outline-primary">Approve</button>
                </div>
                <div class="app-icon">
                  <i class="bi bi-three-dots-vertical"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      
    </div>



    
  </div>


  <div class="payroll-history" *ngIf="isActiveSide('Reimbursements')">
    <h3>
      <button
        class="btn btn-toggle"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#historyContent"
        aria-expanded="false"
        aria-controls="historyContent"
      >
        <i class="bi bi-chevron-down" style="margin-right: 50px"></i>History
      </button>
    </h3>
    <div class="collapse" id="historyContent">
      <!-- History content goes here -->
      <p>No history available yet.</p>
    </div>
  </div>
</div>

<div class="pay-items-sections-sub" *ngIf="isActiveTab('pay-run')">


  <div class="PayRun-Tabs">
    <div class="payroll-tabs">
      <ul class="nav nav-tabs">
        <li class="nav-item">
          <a class="nav-link" [class.active]="activeInnerTab === 'DraftPayRun'" (click)="setActiveInnerTab('DraftPayRun')">Draft
            PayRun</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" [class.active]="activeInnerTab === 'AllPayRuns'" (click)="setActiveInnerTab('AllPayRuns')">
            All PayRuns
          </a>
        </li>
      </ul>
    </div>
  </div>


  <!-- Main Content -->
  <div class="Leave" *ngIf="isActiveInnerTab('DraftPayRun')">


    <div class="pay_item_head">
      <h2>Draft PayRun</h2>
      <div class="btn-group">
      </div>
    </div>

    <div class="menu-row">
    
      <div class="menu-sub-col">
        <div class="menu-topic">Employers</div>
        <div class="menu-value">0</div>
      </div>
    
      <div class="line"></div>
    
      <div class="menu-sub-col">
        <div class="menu-topic">Earning</div>
        <div class="menu-value">0</div>
      </div>
    
      <div class="line"></div>
    
      <div class="menu-sub-col">
        <div class="menu-topic">PAYG</div>
        <div class="menu-value">0</div>
      </div>
    
      <div class="line"></div>
    
      <div class="menu-sub-col">
        <div class="menu-topic">Superannuation</div>
        <div class="menu-value">0</div>
      </div>
    
      <div class="line"></div>
    
      <div class="menu-sub-col">
        <div class="menu-topic">Net Pay</div>
        <div class="menu-value">0</div>
      </div>
    
      <!-- <div class="line"></div> -->
    
      <!-- <div class="menu-sub-col">
                  <div class="menu-topic">Status</div>
                  <div class="menu-value">3000</div>
              </div> -->
    </div>
    
<!-- <button>hiran</button> -->
<div class="table-responsive">
  <table class="table table-hover">
    <thead>
      <tr>
        <th>Calendar</th>
        <th>Period</th>
        <th>Payment Date</th>
        <th>wages</th>
        <th>Tax</th>
        <th>Super</th>
        <th>Net Pay</th>
        <th>STP Filling</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="
                  let payRunMasters of payRunMasters
                    | paginate : { itemsPerPage: 5, currentPage: leaveTwoPage }
                " (click)="viewDetails()">
        <td>{{ payRunMasters.date }}</td>
        <td>{{ payRunMasters.payPeriod }}</td>
        <td>{{ payRunMasters.paymentDate }}</td>
        <td>{{ payRunMasters.wages }}</td>
        <td>{{ payRunMasters.tax }}</td>
        <td>{{ payRunMasters.superAmount }}</td>
        <td>{{ payRunMasters.netPay }}</td>
        <td>{{ payRunMasters.stpFilling }}</td>
      </tr>
    </tbody>
  </table>

  <pagination-controls class="d-flex justify-content-end" (pageChange)="leaveTwoPage = $event">
  </pagination-controls>

  <button class="btn btn-primary" type="button" data-bs-toggle="modal"
              data-bs-target="#AddPayRun">Add Pay Run</button>
</div>
</div>

<!-- Main Content -->
<div class="Leave" *ngIf="isActiveInnerTab('AllPayRuns')">


  <div class="pay_item_head">
    <h2>All PayRuns</h2>
    <div class="btn-group">
    </div>
  </div>

  <div class="menu-row">

    <div class="menu-sub-col">
      <div class="menu-topic">Employers</div>
      <div class="menu-value">0</div>
    </div>

    <div class="line"></div>

    <div class="menu-sub-col">
      <div class="menu-topic">Earning</div>
      <div class="menu-value">0</div>
    </div>

    <div class="line"></div>

    <div class="menu-sub-col">
      <div class="menu-topic">PAYG</div>
      <div class="menu-value">0</div>
    </div>

    <div class="line"></div>

    <div class="menu-sub-col">
      <div class="menu-topic">Superannuation</div>
      <div class="menu-value">0</div>
    </div>

    <div class="line"></div>

    <div class="menu-sub-col">
      <div class="menu-topic">Net Pay</div>
      <div class="menu-value">0</div>
    </div>

    <!-- <div class="line"></div> -->

    <!-- <div class="menu-sub-col">
                  <div class="menu-topic">Status</div>
                  <div class="menu-value">3000</div>
              </div> -->
  </div>

  <!-- <button>hiran</button> -->
  <div class="table-responsive">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Calendar</th>
          <th>Period</th>
          <th>Payment Date</th>
          <th>wages</th>
          <th>Tax</th>
          <th>Super</th>
          <th>Net Pay</th>
          <th>STP Filling</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="
                  let payRunMasters of payRunMasters
                    | paginate : { itemsPerPage: 5, currentPage: leaveTwoPage }
                " (click)="viewDetails()" >
          <td>{{ payRunMasters.date }}</td>
          <td>{{ payRunMasters.payPeriod }}</td>
          <td>{{ payRunMasters.paymentDate }}</td>
          <td>{{ payRunMasters.wages }}</td>
          <td>{{ payRunMasters.tax }}</td>
          <td>{{ payRunMasters.superAmount }}</td>
          <td>{{ payRunMasters.netPay }}</td>
          <td>{{ payRunMasters.stpFilling }}</td>
        </tr>
      </tbody>
    </table>

    <pagination-controls class="d-flex justify-content-end" (pageChange)="leaveTwoPage = $event">
    </pagination-controls>

    <button class="btn btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#AddPayRun">Add Pay
      Run</button>
  </div>
</div>

  </div>

<!--setup_multiple_pay_calenders-->
<div
  class="modal fade"
  id="setup_multiple_pay_calenders"
  tabindex="-1"
  aria-labelledby="setup_multiple_pay_calenders"
  aria-hidden="true"
>
  <div class="modal-dialog">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="setup_multiple_pay_calenders">Setup Pay Calendars</h5>
              <button type="button" class="custom-close-btn" data-bs-dismiss="modal" aria-label="Close" (click)="navigateToPayrollSettings()">
                  <i class="bi bi-x-circle"></i>
              </button>
          </div>
          <div class="modal-body">
            <form #payMultipleCalendarForm="ngForm" (ngSubmit)="addMultiplePayCalendar()">
              <div class="mb-3">
                <label for="Calendar" class="form-label">Calendar Name</label>
                      
                    <input type="text" class="form-control" id="name" [(ngModel)]="multiplePayCalendarData.calendarName" name="name" required>
                    <div *ngIf="payMultipleCalendarForm.controls['name']?.invalid && payMultipleCalendarForm.controls['name']?.touched" class="text-danger">
                      Calendar Name is required.
                    </div> 
              </div>
            
              <div class="mb-3">
                <label for="payCycle" class="form-label">Pay Cycle</label>
                <select
                  class="form-control"
                  id="payCycle"
                  [(ngModel)]="multiplePayCalendarData.payCycle"
                  name="payCycle"
                  required
                  (change)="onPayCycleChange()"
                >
                  <option value="">Select Pay Cycle</option>
                  <option *ngFor="let cycle of payCycles" [ngValue]="cycle">
                    {{ cycle.cycleName }}
                  </option>
                </select>
                <div
                  *ngIf="payMultipleCalendarForm.controls['payCycle']?.invalid && payMultipleCalendarForm.controls['payCycle']?.touched"
                  class="text-danger"
                >
                  Pay cycle is required.
                </div>
              </div>
              
              <div class="mb-3">
                <label for="payStartDate" class="form-label">Pay Start Date</label>
                <input
                  type="date"
                  class="form-control"
                  id="payStartDate"
                  [(ngModel)]="multiplePayCalendarData.payStartDate"
                  name="payStartDate"
                  #payStartDate="ngModel"
                  required
                  (change)="onPayStartDateChange()"
                />
                <div *ngIf="payStartDate.invalid && payStartDate.touched" class="text-danger">
                  Pay Start Date is required.
                </div>
              </div>
              
              <div class="mb-3">
                <label for="nextPayDate" class="form-label">Next Pay Date</label>
                <input
                  type="date"
                  class="form-control"
                  id="nextPayDate"
                  [(ngModel)]="multiplePayCalendarData.nextPayDate"
                  name="nextPayDate"
                  readonly
                />
              </div>
              
            
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="navigateToPayrollSettings()">Cancel</button>
                <button type="submit" class="btn btn-primary" [disabled]="payMultipleCalendarForm.invalid">Add</button>
              </div>
            </form>                 
      </div>             
      </div>
  </div>
</div>

  <div
    class="modal fade"
    id="Ordinary-Time_Earnings"
    tabindex="-1"
    aria-labelledby="Ordinary-Time_Earnings"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="ot-modal-header">
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
            style="margin-left: 95%"
            (click)="navigateToPayrollSettings()"
          >
            <i class="bi bi-x-circle"></i>
          </button>
          <i
            class="bi bi-person-workspace"
            style="font-size: 70px; color: #4262ff"
          ></i>
          <div class="modal-header">
            <h5
              class="modal-title"
              id="add_pay_calendar"
              style="margin-left: 30%"
            >
              Ordinary Time Earnings
            </h5>
          </div>
        </div>
        <div class="modal-body">
          <form #earningForm="ngForm">
            <div class="mb-3">
              <label for="earnings-name" class="form-label"
                >Earning Type</label
              >
              <input
                name="earningsName"
                type="text"
                class="form-control"
                id="earnings-name"
                [(ngModel)]="earning.earningsName"
                required
                #earningsName="ngModel"
              />
                <div *ngIf="earningsName.invalid && earningsName.touched" class="text-danger">
                  Earning Type is required.
                </div>
            </div>
            <div class="mb-3">
              <label for="Category" class="form-label">Earning Category</label>
              <input
                name="earningsCategory"
                type="text"
                class="form-control"
                id="Category"
                [(ngModel)]="earning.category"
                required
                #earningsCategory="ngModel"
              />
              <div *ngIf="earningsCategory.invalid && earningsCategory.touched" class="text-danger">
                Earning Category is required.
              </div>
            </div>
            <!-- <div class="mb-3">
              <label for="dis-name" class="form-label"
                >Display Name (optional)</label
              >
              <input
                name="earningsDisplayName"
                type="text"
                class="form-control"
                id="dis-name"
                [(ngModel)]="earning.displayName"
              />
            </div> -->
            <div class="mb-3">
              <label for="unit-types" class="form-label">Payment Type</label>
              <select
                name="earningsTypeOfUnit"
                class="form-control"
                id="unit-types"
                [(ngModel)]="earning.typeOfUnits"
                required
                #earningsTypeOfUnit="ngModel"
              >
                <option value="" disabled selected>Select a payment type</option>
                <option *ngFor="let type of paymentTypes" [value]="type">{{ type }}</option>
              </select>
              <div *ngIf="earningsTypeOfUnit.invalid && earningsTypeOfUnit.touched" class="text-danger">
                Payment Type is required.
              </div>
            </div>
            <div class="mb-3">
              <label for="rate" class="form-label">{{ rateLabel }}</label>
              <input
                name="earningsRate"
                type="text"
                class="form-control"
                id="rate"
                [(ngModel)]="earning.rate"
                required
                pattern="^[0-9]+(\.[0-9]{1,2})?$"
                #earningsRate="ngModel"
              />
              <div *ngIf="earningsRate.invalid && earningsRate.touched" class="text-danger">
                Enter a valid rate (e.g., 100 or 100.50).
              </div>
            </div>
            
            
            <div class="mb-3">
              <label for="gl-account" class="form-label">Expense Account</label>
              <select
                name="earningsGlAccount"
                class="form-control"
                id="gl-account"
                [(ngModel)]="earning.glAccount"
                required
                #earningsGlAccount="ngModel"
              >
                <option value="" disabled selected>Select a Expense Account</option>
                <option *ngFor="let account of glAccounts" [value]="account">{{ account }}</option>
              </select>
              <div *ngIf="earningsGlAccount.invalid && earningsGlAccount.touched" class="text-danger">
                Expense Account is required.
              </div>
            </div>
            
            <!-- <div class="mb-3">
              <label for="wi" class="form-label">WI (Reportable)</label>
              <select
                name="earningsWIReportable"
                class="form-control"
                id="wi"
                [(ngModel)]="earning.wiReportable"
              >
                <option value="">None</option>
              </select>
            </div> -->
            <div class="mb-3" style="margin-top: 20px; margin-left: 20px">
              <input type="checkbox" [(ngModel)]="earning.exemptFromPAYG" name="exemptFromPAYG" />
              <strong style="margin-left: 10px">Exempt from PAYG withholding</strong>
              <br />
              <input type="checkbox" [(ngModel)]="earning.exemptFromSuperannuation" name="exemptFromSuperannuation" />
              <strong style="margin-left: 10px">Exempt from Superannuation Guarantee Contribution</strong>
              <br />
              <input type="checkbox" [(ngModel)]="earning.wiReportable" name="wiReportable" />
              <strong style="margin-left: 10px">WI Reportable</strong>
            </div>                 
          </form>
        
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
              (click)="navigateToPayrollSettings()"
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              (click)="onEarningAdd()"
              [disabled]="earningForm.invalid"
            >
              Add
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>


<div class="modal fade" id="AddPayRun" tabindex="-1" aria-labelledby="Ordinary-Time_Earnings"
  aria-hidden="true">
  <div class="modal-dialog" style="width: 30%">
    <div class="modal-content">
      <div class="ot-modal-header">
        <button type="button" class="custom-close-btn" data-bs-dismiss="modal" style="margin-left: 95%"
          (click)="navigateToPayrollSettings()">
          <i class="bi bi-x-circle"></i>
        </button>
        <div class="modal-header">
          <h5 class="modal-title" id="add_pay_calendar" style="margin-left: 30%">
            Add PayRun
          </h5>
        </div>
      </div>
      <div class="modal-body">
        <form>
          <div class="mb-3">
            <label for="earnings-name" class="form-label">Select Pay Period</label>
            <select name="earningsGlAccount" class="form-control" id="gl-account" [(ngModel)]="earning.glAccount">
              <option value="" disabled selected>Select a Pay Period</option>
              <option *ngFor="let period of payPeriods" [value]="period.payPeriodId">{{ period.payCalendar.calendarName }}</option>
            </select>
          </div>
        </form>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="navigateToPayrollSettings()">
            Cancel
          </button>
          <button type="button" class="btn btn-primary" (click)="onEarningAdd()">
            Add
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

  <!-- Add -->

  <div
    class="modal fade"
    id="add-deductions"
    tabindex="-1"
    aria-labelledby="add-deductions"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="add-deductions">Add Deductions</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form #deductionForm = 'ngForm' (ngSubmit)="addDeduction()">
            <div class="mb-3">
              <label for="deduction-name" class="form-label"
                >Deductions Name</label
              >
              <input type="text" class="form-control" id="deduction-name" name="deduction-name" [(ngModel)]="deduction.deductionName" required
              minlength="3"
              #deductionName="ngModel"/>
              <div *ngIf="deductionName.invalid && deductionName.touched" class="text-danger">
                Deductions Name is required and must be at least 3 characters.
              </div>
            </div>
            <div class="mb-3">
              <label for="deduction-cat" class="form-label"
                >Deductions Category</label
              >
              <select class="form-control" id="deduction-cat" name="deductionCategory" [(ngModel)]="deduction.deductionCategory" required>
                <option *ngFor="let deduction of deductionCategory" Value="deduction">{{deduction}}</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="gl-account" class="form-label">GL Account</label>
              <input type="text" class="form-control" id="gl-account" name="gl-account" [(ngModel)]="deduction.glAccount" required
              #glAccount="ngModel"/>
              <div *ngIf="glAccount.invalid && glAccount.touched" class="text-danger">
                GL Account is required.
              </div>
            </div>
            <div class="mb-3">
              <label for="wi-reportable" class="form-label"
                >WI (Reportable)</label
              >
              <input type="text" class="form-control" id="wi-reportable" name="wi-reportable" [(ngModel)]="deduction.wiReportable" pattern="^[a-zA-Z0-9]*$"
              #wiReportable="ngModel"/>
              <div *ngIf="wiReportable.invalid && wiReportable.touched" class="text-danger">
                WI Reportable should contain only alphanumeric characters.
              </div>
            </div>
            <div class="mb-3" style="margin-top: 20px; margin-left: 20px">
              <input type="checkbox" name="exemptFromPAYG" [(ngModel)]="deduction.exemptFromPAYG"/><strong style="margin-left: 10px">
                Exempt from PAYG</strong
              ><br />
              <input type="checkbox" name="exemptFromSuperannuation" [(ngModel)]="deduction.exemptFromSuperannuation"/><strong style="margin-left: 10px">
                Exempt from Superannuation</strong
              >
            </div>

            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                Cancel
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="deductionForm.invalid">Add</button>
            </div>
          </form>
        </div>
        
      </div>
    </div>
  </div>

  <div
    class="modal fade"
    id="add-reimbursements"
    tabindex="-1"
    aria-labelledby="add-reimbursements"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="add-reimbursements">
            Add Reimbursements Line
          </h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form #reimbursementForm="ngForm" (ngSubmit)="onReimbursementAdd()">
            <div class="mb-3">
              <label for="reimbursements_type" class="form-label"
                >Reimbursements Type</label
              >
              <select class="form-control" id="reimbursements_type"  name="ReimbursementsType" [(ngModel)]="reimbursement.reimbursementType" required
              #reimbursementType="ngModel">
                <option value="" disabled selected>Select a reimbursement Type</option>
                <option *ngFor="let reimbursementType of ReimbursementsType" [value]="reimbursementType">{{ reimbursementType }}</option>
              </select>
              <div *ngIf="reimbursementType.invalid && reimbursementType.touched" class="text-danger">
                Reimbursement Type is required.
              </div>
            </div>
            <div class="mb-3">
              <label for="description" class="form-label">Description</label>
              <textarea
                class="form-control"
                id="AccountCode"
                rows="3"
                name="description"
                type="text"
                [(ngModel)]="reimbursement.description"
                required
                #description="ngModel"
              ></textarea>
              <div *ngIf="description.invalid && description.touched" class="text-danger">
                Description is required.
              </div>
            </div>
            <div class="mb-3">
              <label for="ct-account" class="form-label">Account Code</label>
              <input type="text" class="form-control" id="ct-account" name="Ct-account" [(ngModel)]="reimbursement.glAccount" required
              #accountCode="ngModel"/>
            </div>
            <div *ngIf="accountCode.invalid && accountCode.touched" class="text-danger">
              Account Code is required.
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary" (click)="onReimbursementAdd()" [disabled]="reimbursementForm.invalid">Add</button>
        </div>
      </div>
    </div>
  </div>

  <!--Leave type form modal -->
  <div id="myModal" class="modal fade" role="dialog">
    <div class="modal-dialog">
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">
            &times;
          </button>
          <h4 class="modal-title">Modal Header</h4>
        </div>
        <div class="modal-body">
          <p>Some text in the modal.</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>

  <!--Leave type form modal 02-->
  <div
    class="modal fade"
    id="add-leave-type-form-2"
    tabindex="-1"
    aria-labelledby="add-leave-type-form-2"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="add-leave-type1">Add Leave Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave" class="form-label">Leave</label>
              <input type="text" class="form-control" id="leave" />
            </div>
            <div class="mb-3">
              <label for="leave-cat" class="form-label">Leave Category</label>
              <input type="text" class="form-control" id="leave-cat" />
            </div>
            <div class="mb-3">
              <label for="leave-cal-method" class="form-label"
                >Leave Calculation Method</label
              >
              <select class="form-control" id="leave-cal-method">
                <option value="">Select</option>
              </select>
            </div>
            <div class="mb-3" style="display: flex; gap: 20px">
              <div style="width: 65%">
                <label for="hours-au-annually" class="form-label"
                  >Hours Accrued Annually</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="hours-au-annually"
                />
              </div>
              <div style="width: 30%">
                <label for="open-bal" class="form-label">Opening Balance</label>
                <input type="text" class="form-control" id="open-bal" />
              </div>
            </div>
            <div class="mb-3">
              <label for="hours" class="form-label">Hours</label>
              <input type="text" class="form-control" id="hours" />
            </div>
            <div class="mb-3">
              <label for="unused-balance" class="form-label"
                >On Termination Unused Balance is</label
              >
              <select class="form-control" id="unused-balance">
                <option value="">Select</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary">Add</button>
        </div>
      </div>
    </div>
  </div>

  <!--Leave accural form modal-->
  <div
    class="modal fade"
    id="add-leave-accural"
    tabindex="-1"
    aria-labelledby="add-leave-accural"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="add-leave-accural">Add Leave Accural</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave-type" class="form-label">Leave Type</label>
              <select class="form-control" id="leave-type">
                <option value="">Annual Leave</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="units-type" class="form-label">Type of Units</label>
              <input type="text" class="form-control" id="units-type" />
            </div>
            <div class="mb-3">
              <label for="leave-load-rate" class="form-label"
                >Leave Loading Rate</label
              >
              <select class="form-control" id="leave-load-rate">
                <option value="">Select</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="nom-ent" class="form-label">Normal Entitlement</label>
              <input type="text" class="form-control" id="nom-ent" />
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary">Add</button>
        </div>
      </div>
    </div>
  </div>

 

  <!--Assign leave type modal-->
  <div
    class="modal fade"
    id="assign-leave-type"
    tabindex="-1"
    aria-labelledby="assign-leave-type"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="assign-leave-type">Add Leave Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave-type" class="form-label">Leave Type</label>
              <input type="text" class="form-control" id="leave-type" />
            </div>
            <div class="mb-3">
              <label for="leave-cat" class="form-label">Leave Category</label>
              <input type="text" class="form-control" id="leave-cat" />
            </div>
            <div class="mb-3">
              <label for="leave-cal-method" class="form-label"
                >Leave Calculation Method</label
              >
              <select class="form-control" id="leave-cal-method">
                <option value="">Select</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="hours-au-annually" class="form-label"
                >Hours Accrued Annually by a F.T Employee</label
              >
              <input type="text" class="form-control" id="hours-au-annually" />
            </div>
            <div class="mb-3" style="display: flex; gap: 20px">
              <div style="width: 65%">
                <label for="hours-au-annually" class="form-label"
                  >Hours Accrued Annually</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="hours-au-annually"
                />
              </div>
              <div style="width: 30%">
                <label for="open-bal" class="form-label">Opening Balance</label>
                <input type="text" class="form-control" id="open-bal" />
              </div>
            </div>
            <div class="mb-3">
              <label for="unused-balance" class="form-label"
                >On Termination Unused Balance is</label
              >
              <select class="form-control" id="unused-balance">
                <option value="">Select</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button
            type="button"
            class="btn btn-primary"
            [class.active]="isActiveSide('Leave_Form_3')"
            (click)="setActiveSide('Leave_Form_3'); $event.preventDefault()"
            data-bs-dismiss="modal"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  </div>

  <!--Draft pay slip modal-->
  <div
    class="modal fade"
    id="draft-pay-slip"
    tabindex="-1"
    aria-labelledby="draft-pay-slip"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="draft-pay-slip">Draft Pay Slip</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave-loading-percentage" class="form-label"
                >Leave Loading Percentage</label
              >
              <select class="form-control" id="leave-loading-percentage">
                <option value="">Select</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary">Save</button>
        </div>
      </div>
    </div>
  </div>

  <!--Leave loading percentage modal-->
  <div
    class="modal fade"
    id="add-leave-loading-percentage"
    tabindex="-1"
    aria-labelledby="add-leave-loading-percentage"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="add-leave-loading-percentage">
            Add leave Loading Percentage
          </h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave-type" class="form-label">Leave Type</label>
              <select class="form-control" id="leave-type">
                <option value="">Select</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="leave-loading-percentage" class="form-label"
                >Leave loading Percentage</label
              >
              <input
                type="text"
                class="form-control"
                id="leave-loading-percentage"
              />
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button
            type="button"
            class="btn btn-primary"
            [class.active]="isActiveSide('Leave-form-final')"
            (click)="setActiveSide('Leave-form-final'); $event.preventDefault()"
            data-bs-dismiss="modal"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  </div>

  <!--Edit leave type modal-->
  <div
    class="modal fade"
    id="edit-leave-type"
    tabindex="-1"
    aria-labelledby="edit-leave-type"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 30%">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="assign-leave-type">Edit Leave Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="leave-type" class="form-label">Leave Type</label>
              <input type="text" class="form-control" id="leave-type" />
            </div>
            <div class="mb-3">
              <label for="leave-cat" class="form-label">Leave Category</label>
              <input type="text" class="form-control" id="leave-cat" />
            </div>
            <div class="mb-3">
              <label for="leave-cal-method" class="form-label"
                >Leave Calculation Method</label
              >
              <select class="form-control" id="leave-cal-method">
                <option value="">Select</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="hours-au-annually" class="form-label"
                >Hours Accrued Annually by a F.T Employee</label
              >
              <input type="text" class="form-control" id="hours-au-annually" />
            </div>
            <div class="mb-3" style="display: flex; gap: 20px">
              <div style="width: 65%">
                <label for="hours-au-annually" class="form-label"
                  >Hours Accrued Annually</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="hours-au-annually"
                />
              </div>
              <div style="width: 30%">
                <label for="open-bal" class="form-label">Opening Balance</label>
                <input type="text" class="form-control" id="open-bal" />
              </div>
            </div>
            <div class="mb-3">
              <label for="unused-balance" class="form-label"
                >On Termination Unused Balance is</label
              >
              <select class="form-control" id="unused-balance">
                <option value="">Select</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary">Save</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Select Earning Type -->
  <div
    class="modal fade"
    id="modal-two"
    tabindex="-1"
    aria-labelledby="modalTitle"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 400px">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalTitle">Select Earning Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            &times;
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="form-check" *ngFor="let type of earningTypes">
              <input
                type="radio"
                class="form-check-input"
                [value]="type"
                name="earningType"
                [(ngModel)]="selectedEarningType"
              />
              <label class="form-check-label">{{ type }}</label>
            </div>
          </form>
        </div>
        <div
          class="modal-footer"
          style="display: flex; align-items: center; justify-content: center"
        >
          <button
            type="button"
            class="btn btn-primary"
            (click)="addEarningLine()"
            data-bs-dismiss="modal"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Select Leave Type -->
  <div
    class="modal fade"
    id="modal-leave"
    tabindex="-1"
    aria-labelledby="modalTitle"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 400px">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalTitle">Select Leave Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            &times;
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="form-check" *ngFor="let type of leaveTypes">
              <input
                type="radio"
                class="form-check-input"
                [value]="type"
                name="leaveType"
                [(ngModel)]="selectedLeaveType"
              />
              <label class="form-check-label">{{ type }}</label>
            </div>
          </form>
        </div>
        <div
          class="modal-footer"
          style="display: flex; align-items: center; justify-content: center"
        >
          <button
            type="button"
            class="btn btn-primary"
            (click)="addLeaveLine()"
            data-bs-dismiss="modal"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  </div>

  <!--Tax modal-->
  <div
    class="modal fade"
    id="modal-tax"
    tabindex="-1"
    aria-labelledby="modalTitle"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 400px">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalTitle">Select Tax Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            &times;
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="form-check" *ngFor="let type of TaxTypes">
              <input
                type="radio"
                class="form-check-input"
                [value]="type"
                name="taxType"
                [(ngModel)]="selectedTaxType"
              />
              <label class="form-check-label">{{ type }}</label>
            </div>
          </form>
        </div>
        <div
          class="modal-footer"
          style="display: flex; align-items: center; justify-content: center"
        >
          <button
            type="button"
            class="btn btn-primary"
            (click)="addTaxLine()"
            data-bs-dismiss="modal"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  </div>

  <!--Reimbursement modal-->
  <div
    class="modal fade"
    id="modal-reimbursement"
    tabindex="-1"
    aria-labelledby="modalTitle"
    aria-hidden="true"
  >
    <div class="modal-dialog" style="width: 400px">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalTitle">Select Reimbursement Type</h5>
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
          >
            &times;
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="form-check" *ngFor="let type of reimbursementTypes">
              <input
                type="radio"
                class="form-check-input"
                [value]="type"
                name="taxType"
                [(ngModel)]="selectedReimbursemenType"
              />
              <label class="form-check-label">{{ type }}</label>
            </div>
          </form>
        </div>
        <div
          class="modal-footer"
          style="display: flex; align-items: center; justify-content: center"
        >
          <button
            type="button"
            class="btn btn-primary"
            (click)="addReimbursementLine()"
            data-bs-dismiss="modal"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  </div>

