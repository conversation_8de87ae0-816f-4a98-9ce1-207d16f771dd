import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'replaceUnderscoreWithSpace'
})

export class ReplaceUnderscoreWithSpacePipe implements PipeTransform {

  transform(value: string, properCase: boolean = false): string {
    if (!value) return '';
    let result = value.replace(/_/g, ' ');
    if (properCase) {
      result = result
        .toLowerCase()
        .replace(/\b\w/g, char => char.toUpperCase());
    }
    return result;
  }

}
