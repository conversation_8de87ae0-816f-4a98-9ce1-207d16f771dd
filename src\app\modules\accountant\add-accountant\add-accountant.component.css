.container {
  font-family: "Inter";
  background-color: transparent;
  display: flex;
  justify-content: center;
  padding-block: 10px;
}

.add-accountant-form {
  background: #fff;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  min-width: 300px;
  height: 300px;
  text-align: center;
  margin-top: 20px;
}

.add-accountant-form h2 {
  margin-bottom: 30px;
  font-family: Inter;
  font-size: 30px;
  font-weight: 600;
  text-align: left;
  color: #535353;
}

.add-accountant-form .form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
}

.add-accountant-form .form-actions button {
  padding: 10px 40px;
  font-size: 16px;
  cursor: pointer;
  border-radius: 12px;
  font-family: Inter;
  font-weight: 700;
  text-align: center;
}

.add-accountant-form .form-actions .cancel {
  background: transparent;
  color: #4262ff;
  border: #4262ff 2px solid;
  margin-left: 245px;
}

.add-accountant-form .form-actions .add-entity {
  background: linear-gradient(to right, #4262ff, #512ca2);
  border: none;
  color: #fff;
}

.add-accountant-form .form-actions .add-entity:hover {
  background: linear-gradient(to left, #4262ff, #512ca2);
}

.add-accountant-form .form-actions .cancel:hover {
  background: #4262ff;
  color: #fff;
}

@media (max-width: 599px) {
  .add-accountant-form .form-actions .cancel {
    background: transparent;
    color: #4262ff;
    border: #4262ff 2px solid;
    margin-left: 24px;
  }
}
