import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, Observable, of, tap, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import {
  BusinessEntityRequest,
  Entity,
  EntityTradingName,
  Industry,
  InviteLog,
} from './entity';

export interface EmailTemplate {
  id: number | null; // Allow id to be null
  name: string;
  subject: string;
  content: string;
}
@Injectable({
  providedIn: 'root',
})
export class EntityService {
  private readonly baseURL = environment.apiUrl;
  private entityCache: Map<number, Entity> = new Map();

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any,
    responseType: 'json' | 'text' = 'json'
  ): Observable<any> {
    let headers = new HttpHeaders();

  const authToken = this.getAuthToken();

  if (authToken) {
    headers = headers.set('Authorization', 'Bearer ' + authToken);
  } else {
    // Add secure API key for protected-but-public endpoints
    headers = headers.set('X-API-KEY', environment.secureApiKey);
  }

    const options: any = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
      responseType: responseType as 'json' | 'text',
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  //entity

  saveEntity(entity: Entity, file: File): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('entity', JSON.stringify(entity));
    formData.append('logo', file);

    return this.request('POST', '/saveBusinessEntity', formData);
  }

  updateBusinessEntity(
    id: number,
    entity: Entity,
    file: File
  ): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('entity', JSON.stringify(entity));
    formData.append('logo', file);

    return this.request('PUT', `/updateBusinessEntity/${id}`, formData);
  }

  checkAbnExists(abn: string): Observable<boolean> {
    return this.request('GET', '/check-businessNumber', null, { abn });
  }

  getBusinessEntityById(businessEntityId: number): Observable<Entity> {
    return this.request(
      'GET',
      `/getBusinessEntityById/${businessEntityId}`,
      {}
    );
  }

  getBusinessEntityByIdCached(businessEntityId: number): Observable<Entity> {
    const cached = this.entityCache.get(businessEntityId);
    if (cached) {
      return of(cached);
    }

    return this.getBusinessEntityById(businessEntityId).pipe(
      tap((entity) => this.entityCache.set(businessEntityId, entity))
    );
  }

  clearEntityCache(): void {
    this.entityCache.clear();
  }

  clearEntityFromCache(entityId: number): void {
    this.entityCache.delete(entityId);
  }

  getBusinessEntityByName(entityName: string): Observable<Entity> {
    return this.request(
      'GET',
      '/entityByName',
      null,
      { entityName: entityName },
      'json'
    );
  }

  updateInvoiceNumber(businessEntity: Entity, id: number): Observable<object> {
    return this.request('PUT', `/updateInvoiceNumber/${id}`, businessEntity);
  }

  updateQuoteNumber(businessEntity: Entity, id: number): Observable<object> {
    return this.request('PUT', `/updateQuoteNumber/${id}`, businessEntity);
  }

  updateReferenceNumber(
    businessEntity: Entity,
    id: number
  ): Observable<object> {
    return this.request('PUT', `/updateReferenceNumber/${id}`, businessEntity);
  }

  updateJvNumber(entityId: number, jvNumber: string): Observable<Entity> {
    return this.request(
      'PUT',
      `/updateJvNumber/${entityId}`,
      null,
      { jvNumber },
      'json'
    );
  }

  updateCreditNoteNumber(
    businessEntity: Entity,
    id: number
  ): Observable<object> {
    return this.request('PUT', `/updateCreditNoteNumber/${id}`, businessEntity);
  }

  updatePaymentReceiptNumber(
    businessEntity: Entity,
    id: number
  ): Observable<object> {
    return this.request(
      'PUT',
      `/updatePaymentReceiptNumber/${id}`,
      businessEntity
    );
  }

  updatePaymentExpensesNumber(
    businessEntity: Entity,
    id: number
  ): Observable<object> {
    return this.request(
      'PUT',
      `/updatePaymentExpensesNumber/${id}`,
      businessEntity
    );
  }

  updateExpensesNumber(businessEntity: Entity, id: number): Observable<object> {
    return this.request('PUT', `/updateExpensesNumber/${id}`, businessEntity);
  }

  updatePaymentVoucherNumber(
    businessEntity: Entity,
    id: number
  ): Observable<object> {
    return this.request(
      'PUT',
      `/updatePaymentVoucherNumber/${id}`,
      businessEntity
    );
  }

  updateCreditNoteNumberBill(
    businessEntity: Entity,
    id: number
  ): Observable<object> {
    return this.request(
      'PUT',
      `/updateCreditNoteNumberBill/${id}`,
      businessEntity
    );
  }

  getBusinessEntityByUserId(userId: number): Observable<Entity> {
    return this.request('GET', `/getBusinessEntityByUserId/${userId}`, {});
  }

  addEmailTemplate(id: number, emailTemplate: any): Observable<any> {
    return this.request(
      'PUT',
      `/addEmailTemplate?entityId=${id}`,
      emailTemplate
    );
  }

  getTemplate(): Observable<EmailTemplate[]> {
    return this.request('GET', '/templateList', null);
  }

  getBusinessEntityList(): Observable<Entity[]> {
    return this.request('GET', '/businessEntityList', {});
  }

  getAllBusinessEntityRequestsByEntity(
    entityId: any
  ): Observable<BusinessEntityRequest[]> {
    return this.request(
      'GET',
      '/getAllBusinessEntityRequestsByEntity',
      {},
      { entityId: entityId }
    );
  }

  declineBusinessEntityRequest(id: number): Observable<void> {
    return this.request('DELETE', `/deleteBusinessEntityRequest/${id}`, null);
  }

  inviteEntity(inviteLogId: number): Observable<any> {
    return this.request('POST', `/invite-entity`, inviteLogId, null, 'text');
  }

  //entity trading name

  saveEntityTradingName(entityTradingName: EntityTradingName): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('entityTradingName', JSON.stringify(entityTradingName));
    return this.request('POST', '/saveEntityTradingName', formData);
  }

  getEntityTradingNameList(): Observable<EntityTradingName[]> {
    return this.request('GET', '/entityTradingNameList', {});
  }

  getEntityTradingName(id: number): Observable<EntityTradingName> {
    return this.request('GET', `/getEntityTradingNameById/${id}`, {});
  }

  updateEntityTradingName(
    id: number,
    entityTradingName: EntityTradingName
  ): Observable<object> {
    return this.request(
      'PUT',
      `/updateEntityTradingName/${id}`,
      entityTradingName
    );
  }

  getTradingNameByEntityId(entityId: number): Observable<EntityTradingName> {
    const headers = new HttpHeaders({
      Authorization: `Bearer ${this.getAuthToken()}`,
    });
    return this.http.get<EntityTradingName>(
      `${this.baseURL}/getTradingNameByEntityId/${entityId}`,
      { headers }
    );
  }

  getEntityTradingNamesByEntityId(
    entityId: any
  ): Observable<EntityTradingName[]> {
    return this.request(
      'GET',
      '/getEntityTradingNamesByEntityId',
      {},
      { entityId: entityId }
    );
  }

  updateEntityTradingNames(
    entityId: number,
    tradingNames: EntityTradingName[]
  ): Observable<any[]> {
    return this.request('POST', '/updateEntityTradingNames', tradingNames, {
      entityId: entityId,
    });
  }

  //Entity Invite Log

  saveEntityInviteLog(inviteLog: InviteLog): Observable<InviteLog> {
    return this.request('POST', '/saveInviteLog', inviteLog, null, 'json');
  }

  getEntityInviteLogById(id: number): Observable<InviteLog> {
    return this.request('GET', `/getInviteLogById/${id}`, null, null, 'json');
  }

  updateEntityInviteLog(
    invteLogId: number,
    inviteLog: InviteLog
  ): Observable<InviteLog> {
    return this.request(
      'PUT',
      `/updateInviteLog/${invteLogId}`,
      inviteLog,
      null,
      'json'
    );
  }

  //Industry
  getIndustryList(): Observable<Industry[]> {
    return this.request('GET', '/industry/list', null, null, 'json');
  }
}
