<app-home-header></app-home-header>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<body>
  <div class="container">
    <div class="selection">
      <h1>Select Your Entity</h1>
      <div class="entity-list" *ngIf="entities.length > 0; else noEntities">
        <ng-container *ngFor="let entity of entities">
          <div class="entity" *ngIf="entity.entityName !== 'Default Entity'">
            <div class="entity-details">
              <h2>{{ entity.entityName }}</h2>
              <h3>ABN: {{ entity.abn }}</h3>
            </div>
            <button class="select-entity-button" (click)="setEntityId(entity.entityId)">
              <i class="fas fa-angle-right"></i>
            </button>            
          </div>
        </ng-container>
      </div>
      <!-- Template for when no entities are displayed -->
      <ng-template #noEntities>
        <div class="no-entity">
          <p>No entities available.</p>
          <button class="request-entity-button" (click)="requestEntity()">Request Entity</button>
        </div>
      </ng-template>
    </div>
  </div>
</body>
