.container {
  margin: 0;
  padding: 20px;
  background: transparent;
  width: 100%; /* Increased width to occupy more space on the screen */
  max-width: 1400px; /* Increased max width for larger screens */
  margin: 0 auto;
}

.common-heading {
  text-align: center;
  margin-bottom: 20px;
}

.upper-layout {
  padding: 20px;
  margin-bottom: 20px;
  background: #ffffff;
  align-items: flex-start;
  justify-content: space-between;
  gap: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.split-layout {
  display: flex;
  padding: 20px;
  background: #ffffff;
  align-items: flex-start;
  justify-content: space-between;
  gap: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.Left-side,
.Right-side {
  flex: 1; /* Allow equal space for both sections */
}

.card,
.table1 {
  margin-top: 0; /* Ensure no extra margin is added */
}

.table-responsive {
  margin-top: 65px; /* Add space below the filters and upload buttons */
}

.table-responsive-right {
  margin-top: 20px;
}

.table-responsive table,
.table-responsive-right table {
  width: 100%;
  table-layout: auto; /* Ensure proper column sizing */
  border-collapse: collapse;
}

.table-head,
.table-head1 {
  background-color: #d7dbf5;
  text-align: left;
}
h3.h3 {
  margin-top: 0; /* Align the title with the left table */
}

.card {
  background-color: #f9f9f9;
  padding: 20px;
  margin-top: 55px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.row2_col2 {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.primary-button,
.secondary-button {
  padding: 10px 20px;
  border: none;
  border-radius: 0px;
  cursor: pointer;
}

.primary-button {
  background-color: #007bff;
  color: white;
}

.secondary-button {
  background-color: #6c757d;
  color: white;
}

.table-responsive,
.table-responsive-right {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

.table-head {
  background-color: #007bff;
  color: white;
}

progress {
  width: 100%;
}

.row2_col2 {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.row2_col2 {
  padding: 8px;
  font-size: 14px;
  color: #555;
}

.primary-button {
  background-color: #28a745;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.secondary-button {
  background-color: #dc3545;
  color: white;

  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.primary-button:hover,
.secondary-button:hover {
  opacity: 0.8;
}

.primary-button,
.secondary-button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.primary-button {
  background-color: #007bff;
  color: white;
}
.primary-button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 5px 30px;
  margin-right: 15px;
  cursor: pointer;
  border-radius: 5px;
  border: 1px solid #4262ff;
  margin-left: 10px;
  font-size: 17px;
}

.primary-button:hover {
  background-color: linear-gradient(to right, #512ca2, #4262ff);
}

.secondary-button:hover {
  background-color: #4262ff;
  color: white;
}

.secondary-button {
  background-color: #6c757d;
  color: white;
}

/* Container for filter options */
.filter-options {
  display: flex;
  gap: 30px;
  padding: 20px 40%;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Style for labels */
.filter-options label {
  display: flex;
  align-items: center;
  gap: 12px; /* Increased space between radio button and text */
  cursor: pointer;
  font-size: 16px; /* Increased font size */
  color: #333;
}

/* Style for radio buttons */
.filter-options input[type="radio"] {
  appearance: none; /* Remove default styling */
  width: 20px; /* Increased size */
  height: 20px; /* Increased size */
  border: 2px solid #007bff;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
}

/* Style for checked radio buttons */
.filter-options input[type="radio"]:checked {
  background-color: #007bff;
}

/* Add a pseudo-element for the inner dot of the radio button */
.filter-options input[type="radio"]:checked::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px; /* Increased size */
  height: 10px; /* Increased size */
  background-color: white;
  border-radius: 50%;
}

/* Hover effect for radio buttons */
.filter-options input[type="radio"]:hover {
  border-color: #0056b3;
}

/* Hover effect for labels */
.filter-options label:hover {
  color: #0056b3;
}

/* .matched {
  background-color: green;
  color: white;
} */

.matched-green {
  border: 1px solid #28a745 !important;
  border-radius: 5px;
  background-color: #0791121e !important;
  color: black;
}

.matched-yellow {
  border: 1px solid #ffc107 !important;
  color: black;
  background-color: #ffbf001e !important;
  border-radius: 5px;
}

.matched-red {
  border: 1px solid #ff0707 !important;
  color: black;
  background-color: #ff00001e !important;
  border-radius: 5px;
}

.middle-item {
  position: relative;
}

.middle-item::before {
  content: "";
  position: absolute;
  width: 1px;
  height: 40px;
  background-color: lightgray;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.middle-item::after {
  content: "";
  position: absolute;
  width: 1px;
  height: 40px;
  background-color: lightgray;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

tr.matched-green td,
tr.matched-yellow td {
  font-weight: bold;
}

* {
  box-sizing: border-box;
  font-family: "Inter", sans-serif !important;
}

p {
  margin: 0;
  padding: 0;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.custom-radio {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  position: relative;
  padding-left: 28px;
  margin-right: 20px;
  user-select: none;
}

.custom-radio input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.radio-mark {
  position: absolute;
  left: 0;
  top: 2px;
  height: 18px;
  width: 18px;
  background-color: #fff;
  border: 2px solid #4262ff;
  border-radius: 50%;
}

.custom-radio input[type="radio"]:checked ~ .radio-mark {
  background-color: #4262ff;
  box-shadow: 0 0 0 3px rgba(66, 98, 255, 0.2);
}

.radio-mark::after {
  content: "";
  position: absolute;
  display: none;
}

.custom-radio input[type="radio"]:checked ~ .radio-mark::after {
  display: block;
}

.custom-radio .radio-mark::after {
  top: 4px;
  left: 4px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
}

.main-container {
  background-color: rgb(241, 241, 241);
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left-side {
  flex: 7; /* Increased flex ratio to make the left panel bigger */
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.right-side {
  flex: 3; /* Adjusted flex ratio to make the right panel smaller */
  background-color: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.Middle {
  display: flex;
  margin-left: 5px;
  align-items: center;
  justify-content: center;
}

.actions {
  display: block;
  padding: 0 27%;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 10px;
}

.actions h1 {
  flex: 1;
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}
.h1 {
  flex: 1;
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}
.h3 {
  flex: 1;
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 30px;
  font-weight: 700;
  text-align: left;
}
.h4 {
  flex: 1;
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 25px;
  font-weight: 700;
  text-align: left;
}

.actions .transparent-button {
  background: transparent;
  color: #4262ff;
  border: 1px solid #4262ff;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 29px;
  font-weight: bold;
  margin-bottom: 20px;
  margin-left: 15px;
}

.actions .transparent-button:hover {
  background-color: #4262ff;
  color: white;
}

.search-create {
  display: flex;
  justify-content: flex-end;
  /* align-items: center; */
  margin-bottom: 20px;
  gap: 10px;
}

.search-create .input-container {
  position: relative;
  width: 600px;
}

.search-create .input-container input.form-control {
  width: 100%;
  padding: 10px 30px 10px 10px;
  font-size: 16px;
  border-radius: 13px;
  border: 1px solid #ccc;
  box-sizing: border-box;
}

.search-create .input-container i.bi-search {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
}

.search-create input {
  width: 510px;
  padding: 10px;
  font-size: 16px;
  border-radius: 13px;
  border: 1px solid #ccc;
  flex-grow: 1;
}

.search-create button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 13px;
  border: none;
  margin-left: 10px;
  width: 350px;
  top: 356px;
  left: 879px;
  border-radius: 13px;
}

.search-create button:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

/* .nav-link.active {
  background-color: #007bff;
  color: white;
} */

.InvoiceBox {
  width: 100%;
  margin: auto;
  padding: 20px;
  background-color: rgb(237, 237, 237);
}

.InvoiceSubBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-radius: 5px;
}

.InvoiceTopic p {
  font-size: 43px;
  font-weight: bold;
  margin: 0;
}

.InvoiceButtons {
  display: flex;
  flex-direction: row;
  gap: 25px;
}

.InvoiceButtons button {
  padding: 10px 15px;
  font-size: 14px;
  cursor: pointer;
  border: none;
  border-radius: 30px;
  width: 163px;
  background-color: #4262ff;
  color: white;
}

.InvoiceButtons button:hover {
  background-color: #07386e;
}

.Row1 {
  display: flex;
  padding-left: 80px;
  margin-top: 30px;
  padding-right: 80px;
  /* width: 100vw; */
  justify-content: space-between;
}

.SearchBar {
  display: flex;
  flex-direction: row;
  height: 50px;
  width: 500px;
  align-items: center;
  background-color: white;
  border-radius: 10px;
}

.input-group-text {
  cursor: pointer;
}

.SearchBar input {
  width: 100%;
  padding: 10px 40px 10px 15px;
  font-size: 16px;
  border: 1px solid white;
  background-color: white;
  box-shadow: none;
  width: 400px;
}

.SearchBar img {
  width: 80px;
  height: 20px;
  background-color: white;
}

.SubRow1 {
  justify-content: space-between;
  width: 100%;
}

.SubRow1 button {
  border-color: #4262ff;
  height: 50px;
  border-radius: 10px;
  width: 300px;
  margin-left: 20px;
  color: #4262ff;
}

.SubRow1 button:hover {
  background-color: #4262ff;
  border: none;
  color: white;
}

.button-row {
  margin-top: 40px;
  display: flex;
  flex-direction: row;
  /* width: 100vw; */
  /* padding-left: 80px;
    padding-right: 80px; */
  justify-content: space-between;
  align-items: center;
  height: 50px;
  border: none;
  background-color: white;
}

.actions .transparent-button {
  background: transparent;
  color: #4262ff;
  border: 2px solid #4262ff;
  padding: 10px 20px;
  margin-right: 10px;
  cursor: pointer;
  border-radius: 25px;
  font-weight: bold;
}

.actions .transparent-button:hover {
  background-color: #4262ff;
  color: white;
}

.search-create button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 10px;
  border: none;
  margin-left: 10px;
}

.search-create button:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

/* styles.css or app.component.css */

.flex-box {
  display: flex;
  margin-top: 5px;
  height: 50px;
  border: 2px solid;
  /* margin-right: 80px;
    margin-left: 80px; */
  flex-direction: row;
  /* padding-left: 100px;
    padding-right: 100px; */
  justify-content: space-between;
  align-items: center;
  border-top: none;
  background-color: white;
  border-left: none;
  border-right: none;
  border-color: #e6e6e6;
}

.flex-box-heading {
  font-weight: 600;
  font-size: 1rem;
  background-color: white;
}

.checkBox {
  display: flex;
  align-items: center;
  background-color: white;
}

.checkBox input {
  width: 20px;
  height: 20px;
  background-color: white;
}

.main-box {
  background-color: white;
  margin-left: 80px;
  margin-right: 80px;
}

.Box1 {
  background-color: white;
}

.flex-box-content {
  background-color: white;
  font-size: medium;
  color: #5e5e5e;
  display: flex;
}

.flex-box-content-button img {
  width: 80px;
  height: 20px;
  background-color: white;
}

.flex-box-content-button {
  display: flex;
  width: 110px;
  background-color: white;
  gap: 1;
}

.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 600px;
  /* Same height as the iframe */
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

table {
  width: 100%;
  margin-bottom: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.table-head th {
  position: relative;
  padding: 10px;
  background-color: #d7dbf5;
  text-align: left;
  color: rgb(0, 0, 0);
  font-size: 15px;
  font-weight: bold;
}

.table-head th:first-child {
  border-top-left-radius: 10px;
}

.table-head th:last-child {
  border-top-right-radius: 10px;
}

tbody tr {
  background-color: white;
  border-bottom: rgb(171, 171, 171) 1px solid;
  font-size: small;
  color: rgb(102, 102, 102);
}

tbody tr:hover {
  background-color: #f1f1f1;
  /* Light grey color on hover */
}

td,
th {
  padding: 10px;
}

.table1 {
  width: 100%;
  margin-bottom: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.table-head1 th {
  position: relative;
  padding: 10px 35px;
  background-color: #d7dbf5;
  text-align: left;
  color: rgb(0, 0, 0);
  font-size: 15px;
  font-weight: bold;
}

.table-head1 th:first-child {
  border-top-left-radius: 10px;
}

.table-head1 th:last-child {
  border-top-right-radius: 10px;
}

.table1 tbody tr {
  background-color: white;
  border-bottom: rgb(171, 171, 171) 1px solid;
  font-size: small;
  color: rgb(102, 102, 102);
}

.table1 tbody tr:hover {
  background-color: #f1f1f1;
}

.table1 td,
.table1 th {
  padding: 10px 35px; /* Different padding */
}

.text-draft {
  color: #007bff;
  text-align: center;
  /* Primary (blue) for Draft */
}

.text-pending {
  color: #057c21;
  text-align: center;
  /* Success (green) for Pending */
}

.text-canceled {
  color: #dc3545;
  text-align: center;
  /* Danger (red) for Canceled */
}

.text-Closed {
  color: #dc3545;
  text-align: center;
  /* Danger (red) for Canceled */
}
.text-revised {
  color: #6c757d;
  text-align: center;
  /* Warning (yellow) for Revised */
}

.text-sent {
  color: #455cff;
  text-align: center;
  /* Info (cyan) for Sent */
}

.text-paid {
  color: #28a745;
  text-align: center;
  /* Success (green) for Paid */
}

.text-overdue {
  color: #ff6610;
  text-align: center;
}

.custom-close-btn {
  width: 50px;
  /* Adjust the width */
  height: 50px;
  /* Adjust the height */
}

span.lable {
  border: none;
  border-radius: 20px;
  padding: 5px 10px;
  font-weight: bold;
}

.border-draft {
  border-color: #007bff;
  background-color: #cce0f5;
  /* Primary (blue) for Draft */
}

.border-pending {
  border-color: #057c21;
  background-color: #d7ecdc;
  /* Success (green) for Pending */
}

.border-canceled {
  border-color: #dc3545;
  background-color: #eedbdd;
  /* Danger (red) for Canceled */
}

.border-Closed {
  border-color: #dc3545;
  background-color: #eedbdd;
  /* Danger (red) for Canceled */
}

.border-revised {
  border-color: #6c757d;
  background-color: #eeebeb;
  /* Warning (yellow) for Revised */
}

.border-sent {
  border-color: #5bc0de;
  background-color: #ebf0f7;
  /* Info (cyan) for Sent */
}

.border-paid {
  border-color: #28a745;
  background-color: #d4edda;
  /* Success (green) for Paid */
}

.border-overdue {
  border-color: #ff6710;
  background-color: #ffe4e0;
  /* Orange (red) for Overdue */
}

/* Modal Styles */

.popup-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  /* Semi-transparent background */
  z-index: 1000;
  /* Ensure it sits above other elements */
}

/* Styles for the modal itself */

.customer-popup {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 500px;
  /* Adjust the width as per your design */
  width: 30%;
  /* Ensure it doesn’t go beyond screen size */
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.popup-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

/* Close button */

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #333;
}

.close-btn:hover {
  color: #ff0000;
}

/* Form input styles */

.form-group {
  margin-bottom: 10px;
  text-align: left;
  font-family: Arial, sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 25.41px;
  color: #444343;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 5px;
}

.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;
  /* Allow resizing only vertically */
  min-height: 100px;
  /* Minimum height for better visibility */
  max-height: 300px;
  /* Restrict max height */
}

.form-group input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 10px;
  font-size: 1rem;
}

.file-upload-group {
  display: flex;
  align-items: center;
  /* Align the label and input vertically in the middle */
  gap: 10px;
  /* Add space between the label and the input */
}

.file-upload-group input[type="file"] {
  border: 1px solid #ccc;
  padding: 5px;
  border-radius: 4px;
  font-size: 1rem;
}

.file-row {
  display: flex;
  align-items: center;
  gap: 20px;
  /* Space between label and attachment row */
  margin-top: 10px;
}

/* Align the file attachment components */

.file-attachment {
  display: flex;
  padding: 5px 5px;
  align-items: center;
  border-radius: 10px;
  border: 1px solid #1f1e1e;
  gap: 10px;
  /* Add space between file icon, name, and select */
}

/* Style for file icon */

.file-icon {
  font-size: 1.5rem;
  /* Make the file icon slightly larger */
}

/* Style for file name */

.file-name {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}

/* Style for select dropdown */

.file-attachment select {
  padding: 5px;
  font-size: 1rem;
  border-radius: 10px;
  border: 1px solid #1f1e1e;
  outline: none;
  background-color: #f9f9f9;
  cursor: pointer;
}

/* Checkbox style */

.form-group input[type="checkbox"] {
  margin-right: 5px;
}

.form-group label small {
  font-size: 0.8rem;
  color: #555;
}

/* Buttons */

.add-btn {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-btn:hover {
  background-color: #0056b3;
}

.popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.popup-footer .add-btn {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 5px 40px;
  cursor: pointer;
  border-radius: 15px;
  border: none;
  margin-left: 10px;
  font-size: 17px;
}

.popup-footer .cancel-btn {
  background: transparent;
  color: #4262ff;
  border: 2px solid #4262ff;
  padding: 5px 40px;
  margin-right: 15px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  font-size: 17px;
}

.popup-footer .add-btn:hover {
  background-color: #218838;
}

/* General styles */

.Card {
  width: 100%;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* Row layout for filters */

.row1 {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.row1_col1 {
  width: 30%;
  /* Ensures enough space for input text */
}

.row1_col2,
.row1_col3,
.row1_col4,
.row1_col5 {
  width: 20%;
}

.row1_col1 input,
.row1_col2 select,
.row1_col3 input,
.row1_col4 input {
  width: 100%;
  height: 49px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
}

label {
  display: block;
  font-weight: bold;
  font-family: Inter;
  font-size: 15px;
  color: #333;
}

/* Row layout for action buttons */

.row2 {
  display: flex;
  align-items: right;
  justify-content: flex-end;
  margin-top: 20px;
}

.primary-button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 5px 30px;
  cursor: pointer;
  border-radius: 5px;
  border: 1px solid #4262ff;
  margin-left: 10px;
  font-size: 17px;
}

.secondary-button {
  background: transparent;
  color: #4262ff;
  border: 1px solid #4262ff;
  padding: 5px 35px;
  cursor: pointer;
  border-radius: 5px;
  font-weight: bold;
  font-size: 17px;
}

.primary-button:disabled {
  cursor: not-allowed;
  background: rgb(237, 237, 237) !important;
  border-color: gray;
  color: gray;
}

.primary-button:hover {
  background-color: linear-gradient(to right, #512ca2, #4262ff);
}

.secondary-button:hover {
  background-color: #4262ff;
  color: white;
}

.amount-type-select {
  font-size: 14px !important;
  outline: none;
  min-width: 200px;
  border-radius: 5px;
  border: 1px solid lightgray;
  color: black;
  font-weight: 600;
  padding: 7px 36px 7px 12px; /* space for the custom arrow */
  background-color: white;

  appearance: none; /* Removes default styling */
  -webkit-appearance: none;
  -moz-appearance: none;

  /* Custom arrow */
  background-image: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L5 5L9 1' stroke='%234262ff' stroke-width='1.5'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 10px 6px;
}

.custom-date-selector {
  font-size: 14px !important;
  outline: none;
  min-width: 200px;
  border-radius: 5px;
  border: 1px solid lightgray;
  color: black;
  font-weight: 600;
  padding: 7px 12px 7px 12px; /* space for the custom arrow */
  background-color: white;
}

.amount-type-select:disabled {
  cursor: not-allowed;
  color: gray;
  background-color: rgb(241, 241, 241);
}

.amount-type-select option {
  font-weight: 500;
}

.row2_col2 {
  padding: 8px 20px;
  margin: 0 10px;
  font-size: 14px;
  color: #555;
}

/* Miscellaneous Styles */

.tabs button {
  padding: 10px;
  border: none;
  background-color: lightgray;
  cursor: pointer;
}

.tabs button.active {
  background-color: darkblue;
  color: white;
}

.filter-section {
  margin: 10px 0;
}

.paid_footer {
  margin-bottom: 10px;
}

.action-bar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.action-buttons {
  display: flex;
  align-items: center;
}

.action-buttons button {
  margin-right: 10px;
}

.invoice-search {
  margin-left: auto;
}

.invoice-search input {
  width: 400px;
  height: 40px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 10px;
  font-size: 14px;
}

.selected-item-text {
  margin-left: 20px;
  font-weight: bold;
  color: #4262ff;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon-group {
  display: flex;
  gap: 20px;
}

.close-icon,
.send-icon {
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.close-icon:hover,
.send-icon:hover {
  color: #000;
}

.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 700px;
  /* Same height as the iframe */
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.search-input {
  width: 100%;
  padding-right: 30px; /* Add space for the icon inside the input */
  padding-left: 10px;
  box-sizing: border-box; /* Ensures padding doesn't increase input size */
}

.input-container i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  pointer-events: none; /* Makes sure the icon doesn't block input clicks */
}
.gradient-btn {
  background: linear-gradient(to right, #512ca2, #4262ff);
  border: none; /* Remove border if desired */
  color: white; /* Ensure text/icon contrast */
}

.gradient-btn:hover {
  background: linear-gradient(
    to right,
    #4262ff,
    #512ca2
  ); /* Optional hover effect */
  color: white;
}

/* Dropdown menu styling */
.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

/* Show dropdown menu */
.dropdown-menu.show {
  display: block;
}

/* Dropdown item styling */
.dropdown-item {
  padding: 0.5rem 1rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
  cursor: pointer;
}

.dropdown-menu .dropdown-item:hover {
  background-color: #4262ff;
}

.table {
  width: 100%;
  min-width: 800px;
  /* Minimum width to allow horizontal scroll */
}

.actions h1 {
  flex: 1;
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 30px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

.search-create button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 10px;
  border: none;
  margin-left: 10px;
  font-size: 12px;
}

.nav-tabs {
  display: flex;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  /* Smooth scrolling for iOS */
  white-space: nowrap;
}

.Row1 {
  display: flex;
  flex-direction: column;
  padding-left: 80px;
  margin-top: 30px;
  padding-right: 80px;
  /* width: 100vw; */
  justify-content: space-between;
}

.row1_col1 {
  width: 100%;
  /* Ensures enough space for input text */
}

.row1_col2,
.row1_col3,
.row1_col4,
.row1_col5 {
  width: 100%;
}

/* Filter tabs */

.tabs {
  display: flex;
  border-bottom: 2px solid #ccc;
  margin-bottom: 10px;
}

.tab-button {
  background: none;
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 16px;
}

.tab-button.active {
  border-bottom: 3px solid #007bff;
  font-weight: bold;
}

.transaction-list {
  display: flex;
  flex-direction: column;
  /* gap: 16px; */
  margin: 0;
}

.Left-side .transaction-item,
.Right-side .transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border: 1px solid #ddd;
  padding: 16px;
  border-radius: 8px;
  background-color: #fff; /* Ensures better visibility */
}

.transaction-details {
  width: 50%;
}

.transaction-options {
  width: 45%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.options-header {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 14px; /* Slightly larger for clarity */
}

.tabs-container {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.tabs button {
  padding: 6px 12px;
  background-color: #f2f2f2;
  border: 1px solid #ccc;
  cursor: pointer;
  border-radius: 5px;
  font-size: 12px;
  transition: background-color 0.3s ease;
}

.tabs button:hover {
  background-color: #ddd;
}

.action-panel {
  margin-top: 8px;
}

textarea {
  width: 100%;
  height: 60px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 5px;
  resize: vertical; /* Allow resizing vertically */
}

.tab-button {
  padding: 8px 16px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 5px;
  transition: background-color 0.3s ease;
}

.tab-button:hover {
  background-color: #e0e0e0;
}

/* selection popup */
.custom-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
  width: 300px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.btn-create {
  padding: 8px 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.btn-create:hover {
  background-color: #0056b3;
}

.btn-cancel {
  background-color: #ccc;
  padding: 6px 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.btn-submit {
  background-color: #28a745;
  color: white;
  padding: 6px 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.btn-submit:disabled {
  background-color: #a9a9a9;
  cursor: not-allowed;
}

/* Dark Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 999;
}

/* View details Popup */
.custom-dialog2 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
  max-width: 90%;
  width: auto;
  z-index: 1000;
}

.popup-title {
  text-align: left;
  margin-bottom: 15px;
  font-size: 20px;
  font-weight: bold;
  color: #4262ff;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  font-size: 25px;
  cursor: pointer;
  color: rgb(78, 78, 78);
  z-index: 1001;
}

.close-btn:hover {
  color: red;
}

/* Find Popup */
.find-popup-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.find-popup {
  background: white;
  width: 60%;
  max-width: 1000px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  height: 600px;
  overflow: hidden;
}

.find-popup-header {
  height: 60px;
  background: linear-gradient(90deg, #4262ff, #512ca2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.find-popup-search-bar {
  padding: 6px 10px;
  border-radius: 5px;
  border: 1px solid rgb(200, 200, 200);
  height: 36px;
  outline: none;
  font-size: 14px;
}

.find-popup-search-bar::placeholder {
  color: rgb(171, 171, 171);
  font-style: italic;
  font-size: 13px;
}

.find-popup-close-btn {
  color: white;
  background-color: transparent !important;
  border: none;
  width: 36px;
  height: 36px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.find-popup-transaction {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f9f9f9;
  font-size: 16px;
  height: 80px;
}

.find-popup-submit-btn {
  padding: 8px 16px;
  border: none;
  background: #512ca2;
  color: white;
  cursor: pointer;
  border-radius: 5px;
}

.find-popup-submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.find-popup-table-container {
  height: 450px;
  overflow-y: auto;
}

.find-popup-table {
  width: 100%;
  border-collapse: collapse;
}

.find-popup-table th,
.find-popup-table td {
  padding: 10px;
  text-align: left;
}

.find-popup-table th {
  /* background: #512ca2; */
  /* color: white; */
  position: sticky;
  top: 0;
  z-index: 5;
}

/* Match Trannsaction Button */
.primary-button2 {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-size: 15px;
  font-weight: 700;
  padding: 8px 20px;
  cursor: pointer;
  border-radius: 5px;
  border: none;
}

.primary-button2:disabled {
  cursor: not-allowed;
  border: 1px solid gray;
  color: gray;
  background: rgb(241, 241, 241);
}

.custom-input {
  outline: none !important;
  border: 1px solid rgb(200, 200, 200);
  color: rgb(100, 100, 100);
  border-radius: 3px;
  text-align: end;
  font-size: 13px;
  font-weight: 600 !important;
  height: 32px !important;
  padding-inline: 8px;
  appearance: textfield !important;
}

.custom-select {
  outline: none;
  border: 1px solid rgb(200, 200, 200);
  padding: 9px 10px;
  border-radius: 5px;
  font-size: 14px;
  height: 35px;
  background-color: white;
}
.active-button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 13px;
  border: none;
}

.bordered-button {
  color: transparent;
  background: linear-gradient(to right, #4262ff, #512ca2);
  background-clip: text;
  font-weight: bold;
  padding: 10px 18px;
  cursor: pointer;
  border-radius: 13px;
  border: 2px solid #4262ff;
}

.divider-float {
  position: relative;
}

.divider-float::before {
  content: "";
  position: absolute;
  width: 1.5px;
  height: 20px;
  background-color: rgb(200, 200, 200);
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.find-transaction-tbl {
  border: 0;
  width: 100%;
  border-collapse: collapse;
  border-radius: 0% !important;
  height: auto;
}

.find-transaction-tbl tr th,
.find-transaction-tbl tr td {
  position: relative;
  font-weight: 500;
  font-size: 13px;
  background-color: #d7dbf5;
  text-align: end;
  padding: 15px 25px;
}

.find-transaction-tbl tr td {
  background-color: white;
}

.find-transaction-tbl tr:nth-child(n + 3) {
  border-bottom: 1px solid rgb(200, 200, 200);
}

.find-transaction-tbl tr th:not(:last-child)::before,
.find-transaction-tbl tr td:not(:last-child)::before {
  position: absolute;
  content: "";
  width: 1px;
  background-color: darkgray;
  height: 10px;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.find-transaction-tbl tr th:first-child,
.find-transaction-tbl tr td:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  text-align: start;
}

.find-transaction-tbl tr th:last-child,
.find-transaction-tbl tr td:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  text-align: center;
}

.invoice-mini-label {
  background-color: #512ca2;
  color: white;
  font-weight: 500;
  border-radius: 5px;
  padding: 1px 4px;
  font-size: 13.5px;
}

.bank-rule-mini-label-spend {
  background-color: #ff2600;
  color: white;
  font-weight: 500;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
  width: fit-content;
}

.bank-rule-mini-label-receive {
  background-color: #66ff00;
  color: black;
  font-weight: 500;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
  width: fit-content;
}


.receipt-mini-label {
  background-color: #4262ff;
  color: white;
  font-weight: 500;
  border-radius: 5px;
  padding: 1px 4px;
  font-size: 13px;
}

.voucher-mini-label {
  background-color: #00a2ff;
  color: white;
  font-weight: 600;
  border-radius: 5px;
  padding: 1px 4px;
  font-size: 13px;
}

.bill-mini-label {
  background-color: #ff0062;
  color: white;
  font-weight: 600;
  border-radius: 5px;
  padding: 1px 4px;
  font-size: 13px;
}

.custom-form-label label {
  color: rgb(102, 102, 102);
  font-weight: 500 !important;
  font-size: 13px;
}

.custom-form-label input {
  color: black !important;
  font-weight: 600 !important;
}

.bottom-space {
  margin-bottom: 20px !important;
}

.mini-search-button {
  background-color: #00a2ff;
  color: white;
  border-radius: 15px;
  font-size: 13px;
}

.mini-create-button {
  background-color: darkgray;
  color: white;
  font-size: 13px;
  border-radius: 15px;
}

/* .mini-transfer-button{
  color:  #4262ff;
  background-color: #ccd4ff73;
  font-size: 13px;
  border-radius: 15px;
} */

.mini-transfer-button {
  background-color: #4262ff;
  color: white;
  font-size: 13px;
  border-radius: 15px;
}

.mini-match-button {
  background-color: rgb(30, 167, 30);
  color: white;
  border-radius: 15px;
  font-size: 13px;
}

.mini-match-button:disabled{
  cursor: not-allowed;
}

.rule-detail-table {
  width: 100%;
  border-collapse: collapse;
  border: 0;
  margin: 0;
  padding: 0;
}

.rule-detail-table tr td {
  padding: 3px;
  margin: 0;
}

.total-cell {
  font-size: 13px;
  font-weight: 700;
  text-align: end;
  border-block: 1px solid lightgray;
  position: relative;
}

.total-cell::after{
  position: absolute;
  content: '';
  width: 100%;
  height: 1px;
  left: 0;
  bottom: -5px;
  border: 0;
  background-color:lightgray ;
}

/* .rule-detail-table tr:not(:last-child){
  border-bottom: 1px solid lightgray;
} */
/* #00a2ff; #4262ff; #512ca2; */
