body {
  font-family: "Inter";
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  height: 100vh;
  /* margin: 0; */
  margin-bottom: 20vh;
  padding-top: 20px;
}

.form-container {
  background-color: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 750px;
  width: 100%;
  text-align: center;
}

.form-container h2 {
  margin-bottom: 5px;
  font-family: Inter;
  font-size: 30px;
  font-weight: 600;
  text-align: left;
  color: #535353;
}

.form-container h3 {
  color: #4262ff;
  margin-bottom: 40px;
  font-family: Inter;
  font-size: 20px;
  font-weight: 600;
  line-height: 38.73px;
  text-align: left;
}

.form-container h4 {
  text-align: left;
  margin-bottom: 20px;
  color: #333;
  font-family: Inter;
  font-size: 20px;
  font-weight: 600;
  line-height: 38.73px;
  text-align: left;
}

.form-container .user-name {
  display: flex;
  gap: 20px;
}

.form-container .class-name {
  display: flex;
  gap: 20px;
}

.form-container .class-name .form-group {
  width: 100%;
}

.form-group {
  margin-bottom: 25px;
  text-align: left;
  font-family: Inter;
  font-size: 15px;
  font-weight: 600;
  line-height: 25px;
  color: #444343;
  position: relative;
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 2px solid #c7c7c7;
  border-radius: 8px;
  box-sizing: border-box;
  font-size: 16px;
  font-family: Inter;
}

.input-group-1 {
  position: relative;
}

.input-group-1 input {
  padding-right: 110px;
}

.lookup-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  padding: 10px;
  border: none;
  border-radius: 5px;
  background-color: white;
  color: #4262ff;
  cursor: pointer;
  font-family: Inter;
  font-size: 14px;
  font-weight: 700;
  line-height: 24.2px;
  text-align: left;
}

.input-group .bi-search {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2em;
  color: #666;
  /* pointer-events: none;  */
  cursor: pointer;
}

.form-group-subscription-payment {
  margin-bottom: 20px;
  text-align: left;
  font-family: Inter;
}

.form-actions {
  display: flex;
  justify-content: space-between;
}

.form-actions button {
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-family: Inter;
  font-size: 17px;
  font-weight: 700;
  line-height: 30px;
  text-align: center;
  color: white;
  margin-top: 15px;
}

.invite-button,
.create-button {
  padding: 10px 100px;
  background: linear-gradient(to right, #4262ff, #512ca2);
  margin-left: 10px;
}

.invite-button:hover,
.create-button:hover {
  background: linear-gradient(to left, #4262ff, #512ca2);
}

.actions{
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: space-between;
}

.form-actions .invite-button {
  padding: 10px 100px;
  background-color: #4262ff;
}

.create-button {
  padding: 10px 100px;
  background-color: #4262ff;
}

.form-actions .back-button {
  padding: 10px 75px;
  border: 2px solid #4262ff;
  color: white;
  color: #4262ff;
  background: transparent;
  margin-left: 10px;
}

.form-actions .back-button:hover {
  color: #4262ff;
  background-color: white;
}

.valid {
  color: green;
}
.invalid {
  color: red;
}

.check-box {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.check-box label {
  font-weight: bold;
}

.address-dropdown {
  position: relative;
  margin-top: 5px;
  width: 100%;
  z-index: 1000;
}

.address-dropdown ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.address-dropdown ul li {
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.address-dropdown ul li:hover {
  background-color: #f1f1f1;
}

.address-dropdown ul li.active {
  background-color: #e9e9e9;
}

.input-group {
  position: relative;
}

.img-preview {
  margin-left: 20px;
  margin-bottom: 10px;
}


@media (max-width: 768px) {
.form-container {
    background-color: white;
    padding: 16px;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    width: 100%;
    text-align: center;
  }

  .user-name{
    display: flex;
    flex-direction: column;
  }

  .form-actions button {
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-family: Inter;
  font-size: 17px;
  font-weight: 700;
  line-height: 30px;
  text-align: center;
  color: white;
  margin-top: 15px;
}

.invite-button,
.create-button {
  padding: 10px 100px;
  background: linear-gradient(to right, #4262ff, #512ca2);
}

.actions{
  display: flex;
  flex-direction: column-reverse;
  width: 100%;
  justify-content: flex-end;
}


.form-actions{
  display: flex;
  flex-direction: column-reverse;
  width: 100%;
  justify-content: flex-end;
}

}