import { Component,ElementRef,HostListener,ViewChild } from '@angular/core';
import { ApInvoiceHead, CoaLedgerAccount } from '../bill';
import { Entity } from 'src/app/modules/entity/entity';
import {
  BusinessPartner,
} from 'src/app/modules/business-partner/business-partner';
import { BillService } from '../bill.service';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { ActivatedRoute, Router } from '@angular/router';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { DateAdapter } from '@angular/material/core';

@Component({
  selector: 'app-view-payable-bill',
  templateUrl: './view-payable-bill.component.html',
  styleUrls: ['./view-payable-bill.component.css'],
})
export class ViewPayableBillComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  apInvoiceHeadData: ApInvoiceHead = new ApInvoiceHead();
  businessEntity: Entity = new Entity();
  customers: BusinessPartner[] = [];
  glAccounts: CoaLedgerAccount[] = [];
  showTaxApplicabilityDropdown: boolean = true;
  taxApplicabilityEnabled = true;
  defaultTaxRate = 0;
  isDropdownOpen = false;
  id: number = 0;
  businessEntityId: number = 0;

  constructor(
    private billService: BillService,
    private entityService: EntityService,
    private router: Router,
    private businessPartnerService: BusinessPartnerService,
    private route: ActivatedRoute,
    private swalAlertsService: SwalAlertsService,
    private dateAdapter: DateAdapter<Date>,
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }

  ngOnInit() {
    this.id = this.route.snapshot.params['id'];
    this.loadCustomers();
    this.getBusinessEntityByEntityId().then(() => {
      this.getBillHeadById().then(() => {
        this.getBillDetailsByHeadId();
        this.fetchGlAccounts();
      });
    });
  }

  getBusinessEntityByEntityId() {
    return new Promise<void>((resolve, reject) => {
      this.businessEntityId = +(localStorage.getItem('entityId') + '');
      this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
        (data) => {
          this.businessEntity = data;
          this.taxApplicabilityEnabled = data.taxApplicability === 'yes';
          this.defaultTaxRate = data.countryId?.defaultTaxRate || 0;
          resolve();
        },
        (error) => {
          console.error(error);
          reject();
        }
      );
    });
  }

  getBillHeadById() {
    return new Promise<void>((resolve, reject) => {
      this.billService.getApInvoiceHeadById(this.id).subscribe(
        (data) => {
          this.apInvoiceHeadData = data;
          resolve();
        },
        (error) => {
          console.error(error);
          reject();
        }
      );
    });
  }

  getBillDetailsByHeadId() {
    this.apInvoiceHeadData.apInvoiceDetails = [];
    this.billService.getApInvoiceDetailsByApInvoiceHeadId(this.id).subscribe(
      (data) => {
        this.apInvoiceHeadData.apInvoiceDetails = data;
      },
      (error) => {
        console.error(error);
      }
    );
  }

  fetchGlAccounts() {
    const entityId = +localStorage.getItem('entityId')!;
    this.billService
      .getActiveCoaLedgerAccountListByEntityBill(entityId)
      .subscribe(
        (glAccounts: CoaLedgerAccount[]) => {
          this.glAccounts = glAccounts;
        },
        (error: HttpErrorResponse) => {
          console.error('Error fetching GL Accounts', error);
          Swal.fire({
            title: 'Error!',
            text: 'Failed to load GL Accounts.',
            icon: 'error',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#be0032',
            cancelButtonColor: '#007bff',
            showCancelButton: true,
          }).then((result) => {
            if (
              result.isDismissed &&
              result.dismiss === Swal.DismissReason.cancel
            ) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData(
                      'Failed to load GL Accounts.'
                    );
                    this.chatBotComponent.responseReceived.subscribe(
                      (response) => {
                        Swal.close();
                        this.chatResponseComponent.showPopup = true;
                        this.chatResponseComponent.responseData = response;
                        this.playLoadingSound();
                        this.stopLoadingSound();
                      }
                    );
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
        }
      );
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

  loadCustomers() {
    const entityId = +(localStorage.getItem('entityId') + '');

    this.businessPartnerService.getSupplierListByEntity(entityId).subscribe(
      (customers: BusinessPartner[]) => {
        // Filter customers where businessPartnerType is 'Supplier'
        this.customers = customers.filter(
          (customer) =>
            customer.businessPartnerTypeId?.businessPartnerType === 'Supplier'
        );
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching Suppliers', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load Supplier.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Failed to load customers.'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

    editBill(id: number) {
    this.billService.getApInvoiceHeadById(id).subscribe(bill => {
      if (bill.status === 'Pending') {
        this.router.navigate(['/update-bill', id]);
      } else {
        this.swalAlertsService.showWarning("Only bill with status 'Pending' can be edited.", () => {});
      }
    }, error => {
      console.error("Error fetching bill:", error);
      this.swalAlertsService.showErrorDialog("Failed to fetch bill details.");
    });
  }


  recordBatchPayment(id: number): void {
  this.router.navigate(['/record-batch-payments'], {
    queryParams: { billIds: JSON.stringify([id]) }
  });
}

  onCreditNoteMultiple(id: number): void {
  this.router.navigate(['/credit-note-bill'], {
    queryParams: { billIds: JSON.stringify([id]) }
  });
}


cancelSelectedBills(): void {
  const billId = this.route.snapshot.params['id'];

  Swal.fire({
    title: 'Confirm Cancellation',
    text: `Are you sure you want to cancel this bill?`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Yes, Cancel',
    cancelButtonText: 'No',
    confirmButtonColor: '#ff7e5f',
    cancelButtonColor: '#be0032',
  }).then(result => {
    if (result.isConfirmed) {
      this.billService.cancelBill(billId).subscribe(
        () => {
          Swal.fire({
            title: 'Success',
            text: 'Bill canceled successfully.',
            icon: 'success',
            confirmButtonText: 'OK',
          });
          this.router.navigate(['/payable-bill-list']); // or refresh view
        },
        error => {
          console.error('Error canceling bill:', error);
          Swal.fire({
            title: 'Error',
            text: 'Failed to cancel the bill. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
          });
        }
      );
    }
  });
}


  @ViewChild('dropdownRef') dropdownRef!: ElementRef;

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown() {
    this.isDropdownOpen = false;
  }

      
  // Listen for clicks on the whole document
        @HostListener('document:click', ['$event'])
        handleClickOutside(event: MouseEvent) {
          if (
            this.dropdownRef && !this.dropdownRef.nativeElement.contains(event.target) 
          ) {
            this.closeDropdown();
          }
        }

}
