import { ViewportScroller } from '@angular/common';
import { Component, HostListener } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-home-header',
  templateUrl: './home-header.component.html',
  styleUrls: ['./home-header.component.css'],
})
export class HomeHeaderComponent {
  isMenuHidden = true; // Control the visibility of the menu

  constructor(private router: Router, private viewportScroller: ViewportScroller) { }

  // Toggle menu visibility on button click
  toggleMenu() {
    this.isMenuHidden = !this.isMenuHidden;
  }

  // Close the menu if clicked outside
  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    const menu = document.querySelector('.navbar-nav');
    const toggleButton = document.querySelector('.navbar-toggler');
    if (menu && !menu.contains(event.target as Node) && !toggleButton?.contains(event.target as Node)) {
      this.isMenuHidden = true;
    }
  }

  // Navigate to the Register page
  navigateToRegister() {
    this.router.navigate(['/create-user']);
  }

  // Navigate to the Login page
  navigateToLogin() {
    this.router.navigate(['/user-login']);
  }

  scrollToPricing() {
    if (this.router.url === '/home') {
      // Already on home page – scroll directly
      setTimeout(() => {
        this.viewportScroller.scrollToAnchor('pricing');
      }, 0);
    } else {
      // Navigate to home and scroll after navigation
      this.router.navigate(['/home'], { fragment: 'pricing' }).then(() => {
        setTimeout(() => {
          this.viewportScroller.scrollToAnchor('pricing');
        }, 300); // wait for view to render
      });
    }
  }
}
