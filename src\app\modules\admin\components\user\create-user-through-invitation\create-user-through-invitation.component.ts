import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { User, UserType } from '../user';
import { Entity } from 'src/app/modules/entity/entity';
import { UserService } from '../user.service';
import { ActivatedRoute, Router } from '@angular/router';
import { EntityService } from 'src/app/modules/entity/entity.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-create-user-through-invitation',
  templateUrl: './create-user-through-invitation.component.html',
  styleUrls: ['./create-user-through-invitation.component.css'],
})
export class CreateUserThroughInvitationComponent implements OnInit {
  entityName: string = '';
  gstInformation: boolean = false;
  userForm: FormGroup;
  user: User = new User();
  entity: Entity = new Entity();
  entityId: number = 0;
  userTypeId: number = 0;
  userType: UserType = new UserType();
  isUsernameExists: boolean = false;
  usernameExistsMessage: string = '';
  isPasswordVisible: boolean = false;
  isSubmitting = false;

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private router: Router,
    private entityService: EntityService,
    private route: ActivatedRoute
  ) {
      this.userForm = this.fb.group(
    {
      firstName: [
        '',
        [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')],
      ],
      lastName: [
        '',
        [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')],
      ],
      email: ['', [Validators.required, this.customEmailValidator()]],
      password: [
        '',
        [
          Validators.required,
          Validators.pattern(
            '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()_+\\-=?.])[A-Za-z\\d!@#$%^&*()_+\\-=?.]{8,}$'
          ),
        ],
      ],
      rePassword: ['', Validators.required],
    },
    { validators: this.passwordMatchValidator }
  );
  }

passwordMatchValidator(form: FormGroup) {
  return form.get('password')?.value === form.get('rePassword')?.value
    ? null
    : { mismatch: true };
}

customEmailValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    if (!value) {
      return null; // Let required validator handle empty value
    }

    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
    const isValid = emailRegex.test(value);
    return isValid ? null : { customEmail: true };
  };
}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.entityId = params['entityId'];
      this.userTypeId = params['userTypeId'];
      this.user.firstName = params['fName'];
      this.user.lastName = params['lName'];
      this.user.username = params['email'];
      this.user.subscriptionPlanId =  params['subscriptionPlanId'];
    });
    this.getEntity();
    this.getUserType();
  }

  getEntity() {
    this.entityService.getBusinessEntityById(this.entityId).subscribe(
      (response) => {
        this.entity = response;
      },
      (error) => {
        console.error('Error fetching entity data:', error);
        this.showErrorSwal('Failed to load entity information.');
      }
    );
  }

  getUserType() {
    this.userService.getUserTypeById(this.userTypeId).subscribe(
      (response) => {
        this.userType = response;
      },
      (error) => {
        console.error('Error fetching user type data:', error);
        this.showErrorSwal('Failed to load user type information.');
      }
    );
  }

  async onSubmitRegister(): Promise<void> {
  if (this.userForm.invalid) {
    this.userForm.markAllAsTouched();

       if (this.userForm.errors?.['mismatch']) {
      this.showErrorSwal('Passwords do not match.');
    } else {
      this.showErrorSwal('Please fill in all required fields.');
    }
    return;
  }

  this.isSubmitting = true;

  try {
    this.user.entityIds = [this.entity.entityId];
    this.user.userTypeId = this.userType;
    await this.onUserRegister();
  } catch (error) {
    console.error('Error during user registration:', error);
    this.showErrorSwal('Registration failed. Please try again.');
  } finally {
    this.isSubmitting = false;
  }
}


  async onUserRegister(): Promise<void> {
    try {
      const response = await this.userService.register(this.user).toPromise();
      this.userService.verifyUser(this.user.username).subscribe((response) => {
      });
      Swal.fire({
        title: 'Success!',
        text: 'We’ve sent a verification email. Please verify your email address.',
        icon: 'success',
        confirmButtonText: 'OK',
        confirmButtonColor: '#28a745',
      }).then(() => {
        this.router.navigate(['/user-login']);
      });
    } catch (error) {
      console.error('Error during registration:', error);
      this.userService.setAuthToken(null);
      this.showErrorSwal('User registration failed. Please try again.');
    }
  }

  checkUser() {
    if (this.userForm.get('email')?.valid) {
      const email = this.userForm.get('email')?.value;
      this.userService.checkUser(email).subscribe((exists) => {
        if (exists) {
          this.isUsernameExists = true;
          this.usernameExistsMessage =
            'This user already exists in the system. Please try a different email.';
        } else {
          this.isUsernameExists = false;
          this.usernameExistsMessage = '';
        }
      });
    } else {
      this.isUsernameExists = false;
      this.usernameExistsMessage = '';
    }
  }

  showErrorSwal(message: string): void {
    Swal.fire({
      title: 'Error',
      text: message,
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#be0032',
    });
  }
}
