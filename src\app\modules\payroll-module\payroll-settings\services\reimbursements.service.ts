import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpService } from 'src/app/http.service';
import { environment } from 'src/environments/environment';
import { Reimbursement } from '../payroll-setting';

@Injectable({
  providedIn: 'root'
})
export class ReimbursementsService {
  private readonly baseURL = environment.payrollApiUrl;


  constructor(private http: HttpClient, private httpService: HttpService) { }

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  getReimbursementsList(entityId: number): Observable<Reimbursement[]> {
    return this.request('GET', `/reimbursements/getAll?entityId=${entityId}`, {});
  }

  saveReimbursement(reimbursement: Reimbursement): Observable<any> {
    return this.request('POST', '/reimbursements/save', reimbursement);
  }


  deleteReimbursement(reimbursementId: number): Observable<any> {
    return this.request('DELETE', `/reimbursements/deleteById/${reimbursementId}`, {});
  }
}
