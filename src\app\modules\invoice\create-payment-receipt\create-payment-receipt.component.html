<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <div class="header">
    <h3>Create Payment Receipt</h3>
  </div>

  <form #f="ngForm" class="styled-form" novalidate>

    <div class="bd">

      <div class="form-section">

        <div class="form-row"
          style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
          <div class="form-group">
            <label for="paymentNumber">Payment Receipt No:</label>
            <input id="paymentNumber" type="text" required [(ngModel)]="paymentReceiptsHead.paymentNumber"
              name="paymentNumber" disabled />
          </div>
        </div>


        <div class="form-row"
          style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
          <div class="form-group">
            <label for="documentDate">Payment Date:</label>
            <input id="documentDate" type="date" required [(ngModel)]="paymentReceiptsHead.documentDate"
              name="documentDate" />
            <div *ngIf="f.controls['documentDate']?.touched && f.controls['documentDate']?.invalid" class="text-danger">
              Date is required.
            </div>
          </div>
        </div>
      </div>

      <div class="table-section">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>#</th>
                <th>Invoice No</th>
                <th>Invoice Date</th>
                <th style="text-align: right;">Amount</th>
                <th style="text-align: right;">Balance</th>
                <th style="text-align: right;">Amount Received</th>
                <th style="text-align: right;">Remaining Balance</th>
              </tr>
            </thead>
            <tbody>
              <!-- Loop through selected invoices -->
              <tr *ngFor="let invoice of selectedInvoices; let i = index">
                <td>{{ i + 1 }}</td>

                <!-- Invoice Number -->
                <td>
                  <input type="text" class="form-control" required [(ngModel)]="invoice.invoiceNumber"
                    name="invoiceNumber{{i}}" disabled />
                </td>

                <!-- Invoice Date -->
                <td>
                  <input type="text" class="form-control" [ngModel]="invoice.postingDate | date: 'dd MMM yyyy'"
                    name="postingDate{{i}}" disabled />
                </td>

                <!-- Amount -->
                <td>
                  <input type="text" class="form-control" [value]="invoice.grandTotal | currency:'USD':'symbol':'1.2-2'"
                    disabled />
                  <input type="hidden" [(ngModel)]="invoice.grandTotal" name="grandTotal{{i}}" />
                </td>

                <!-- Balance -->
                <td>
                  <input type="text" class="form-control"
                    [value]="invoice.balanceAmount ? (invoice.balanceAmount | currency:'USD':'symbol':'1.2-2') : '0.00'"
                    disabled />
                  <input type="hidden" [(ngModel)]="invoice.balanceAmount" name="balanceAmount{{i}}" />
                </td>

                <!-- Credit Amount -->
                <td>
                  <input type="number" class="form-control" required [(ngModel)]="invoice.creditAmount"
                    name="creditAmount{{i}}" (keydown)="preventEnter($event)" min="0"
                    [ngClass]="{ 'is-invalid': f.submitted && f.controls['creditAmount' + i].invalid }" />
                  <div *ngIf="f.controls['creditAmount' + i]?.touched && f.controls['creditAmount' + i]?.invalid"
                    class="text-danger">
                    Paid Amount is required.
                  </div>
                </td>

                <!-- New Invoice Balance -->
                <td>
                  <input id="InvoiceBalance" type="text" class="form-control"
                    [value]="getInvoiceNewBalance(invoice) | currency:'USD':'symbol':'1.2-2'" disabled />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="form-section_2">
        <div class="form-row" >
          <div class="form-group_2" >
            <label for="totalCreditAmount">Total Paid Amount</label>
            <input id="totalCreditAmount" type="text" class="form-control"
              [value]="getTotalPaidAmount() | currency:'USD':'symbol':'1.2-2'" disabled />
          </div>

        </div>

        <div class="form-row">
          <div class="form-group_2">
            <label for="remarks">Remarks:</label>
            <textarea id="remarks" placeholder="Enter any notes or reference (optional)" required [(ngModel)]="paymentReceiptsHead.remarks" name="remarks"
              rows="3"></textarea>
          </div>
        </div>


        <div class="d-flex justify-content-end mt-5 mb-4 btns" style="background-color: transparent;">
          <button type="button" class="btn btn-secondary me-2" (click)="onCancel()">Cancel</button>
          <button type="submit" class="btn btn-primary" (click)="onSubmit(f)">Save Payment Receipt</button>
        </div>
      </div>
    </div>
      
  </form>
</div>
