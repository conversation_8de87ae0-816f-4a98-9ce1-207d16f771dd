.container {
  width: 90%;
  margin: 2% auto;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.highlight1 {
  color: #455cff;
}

.subtitle {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 40px;
  text-align: center;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  padding: 0 20px 20px 20px;
}

.feature-card {
  background-color: #ffffff;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.icon {
  font-size: 2rem;
  /* color: #ff6f61; */
  margin-bottom: 15px;
  text-align: center;
}

.feature-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-align: center;
  height: 65px;
}

.feature-description {
  font-size: 0.95rem;
  color: #666;
  text-align: center;
}

.footertitle {
  font-size: 1.1rem;
  margin-top: 20px;
  text-align: center;
}

.highlight2 {
  font-weight: bold;
}
