<div class="popup-message" *ngIf="showPopup">
    <span>Hello! Welcome to the LedgerChimp Chat Assistant!</span>
    <button class="close-btn-2" (click)="closePopup()">&#x2715;</button>
  </div>
  <div class="chat-container" *ngIf="isChatVisible">
    <div class="chat-box" >
      <div class="chat-header" >
        <img class="chat-header-img" src="../../assets/images/Ledger_Chimp - icon_2.png" alt="Chatbot Image">
        <div class="chat-title">LedgerChimp Chat Assistant</div>
      </div>
  
      <div class="conversation" #conversationContainer>
        <div class="text3">Hello! Welcome to the LedgerChimp Chat Assistant !</div>
        <div class="text4">What can I help you with today?</div>
        <p *ngFor="let message of conversation" [ngClass]="{'sent': message.sender === 'User'}">
          <ng-container *ngIf="message.sender === 'User'">
            <strong>{{ message.sender }}:</strong>
          </ng-container>  
          <ng-container *ngIf="message.sender === 'Assistant'">
            <img class="img-assistant" src="../../assets/images/Ledger_Chimp - icon_2.png" alt="Assistant Icon">
            <span class="assistant-label">Assistant:</span>
          </ng-container>
          <span [innerHTML]="message.text"></span>
        </p>
        <div *ngIf="isWaitingForResponse" class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      <input [(ngModel)]="input" type="text" placeholder="Type your message..." (keydown.enter)="sendData()" />
      <div class="send-icon" (click)="sendData()">
        <img class="img-send" src="../../assets/images/send.png" alt="Send Icon">
      </div>
    </div>
  </div>
  
  <div class="chat-icon" (click)="toggleChat()">
    <img class="img"  [src]="isChatVisible ? '../../assets/images/close1.png' : '../../assets/images/Ledger_Chimp - icon.png'"  alt="Chat Icon">
  </div>
  
