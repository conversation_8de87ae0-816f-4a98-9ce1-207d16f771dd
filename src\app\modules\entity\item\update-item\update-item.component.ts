import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ItemService } from '../item.service';
import { Item } from '../item';
import { NgForm } from '@angular/forms';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { EntityService } from '../../entity.service';
import { Entity } from '../../entity';

@Component({
  selector: 'app-update-item',
  templateUrl: './update-item.component.html',
  styleUrls: ['./update-item.component.css'],
})
export class UpdateItemComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  item: Item = new Item();
  showTaxApplicabilityDropdown: boolean = true;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private itemService: ItemService,
    private entityService: EntityService
  ) {}

 

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.getItemById(+id);
    }

    const entityId = +(localStorage.getItem('entityId') || '');

    if (entityId) {
      this.entityService.getBusinessEntityById(entityId).subscribe(
        (entity: Entity) => {
          if (entity.taxApplicability === 'no') {
            this.showTaxApplicabilityDropdown = false; // Hide dropdown
            this.item.taxApplicability = 'no'; // Default value if tax not applicable
          } else if (entity.taxApplicability === 'yes') {
            this.showTaxApplicabilityDropdown = true; // Show dropdown
          }
        },
        error => {
          console.error('Error fetching entity:', error);
          this.showTaxApplicabilityDropdown = false; // Hide dropdown on error
          this.item.taxApplicability = 'no'; // Default value on error
        }
      );
    } else {
      this.showTaxApplicabilityDropdown = false; // Hide dropdown if no entity ID
      this.item.taxApplicability = 'no'; // Default value if no entity
    }
  }

  getItemById(id: number): void {
    this.itemService.getItemById(id).subscribe(
      (data: Item) => {
        this.item = data;
      },
      (error) => {
        console.error('Error fetching item:', error);
        this.showErrorAlert('Could not fetch the item. Please try again later.');
      }
    );
  }

  onSubmit(f: NgForm): void {
    if (f.valid) {
      this.item.userId = +localStorage.getItem('userid')!;
      this.item.entityId = +localStorage.getItem('entityId')!;
      this.itemService.updateItem(this.item.salesItemId, this.item).subscribe(
        (response) => {
          this.showSuccessAlert('Item updated successfully.');
        },
        (error) => {
          console.error('Error updating item:', error);
          this.showErrorAlert('Failed to update item. Please try again.');
        }
      );
    } else {
      this.showErrorAlert('Please fill in all required fields.');
    }
  }

  validateUnitPrice(): void {
    this.item.unitPriceInvalid = this.item.unitPrice < 0;
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  private showSuccessAlert(message: string): void {
    Swal.fire({
      title: 'Success!',
      text: message,
      icon: 'success',
      confirmButtonText: 'OK',
      confirmButtonColor: '#28a745',
    }).then((result) => {
      if (result.isConfirmed) {
        this.router.navigate(['/item']);
      }
    });
  }

  private showErrorAlert(message: string): void {
    Swal.fire({
      title: 'Error!',
      text: message,
      icon: 'error',
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#be0032',
      cancelButtonColor: '#007bff',
      showCancelButton: true,
    }).then((result) => {
      if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        if (this.chatBotComponent) {
          Swal.fire({
            title: 'Processing...',
            text: 'Please wait while Chimp processes your request.',
            allowOutsideClick: false,
            didOpen: () => {
              Swal.showLoading();
              this.chatBotComponent.setInputData(message);
              this.chatBotComponent.responseReceived.subscribe(response => {
                Swal.close();
                this.chatResponseComponent.showPopup = true;
                this.chatResponseComponent.responseData = response;
                this.playLoadingSound();
                this.stopLoadingSound() 
              });
            },
          });
        } else {
          console.error('ChatBotComponent is not available.');
        }
      }
    });
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }
}
