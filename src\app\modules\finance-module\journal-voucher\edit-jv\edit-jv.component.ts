import { Component, OnInit } from '@angular/core';
import { GlPostingDetails, GlPostingHead } from '../journal-voucher';
import { JournalVoucherService } from '../journal-voucher.service';
import { Router, ActivatedRoute } from '@angular/router';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { Entity } from 'src/app/modules/entity/entity';
import { BillService } from '../../bill/bill.service';
import Swal from 'sweetalert2';
import { CoaLedgerAccount } from '../../gl-account/gl-account';

@Component({
  selector: 'app-edit-jv',
  templateUrl: './edit-jv.component.html',
  styleUrls: ['./edit-jv.component.css'],
})
export class EditJvComponent implements OnInit {
  rows: Array<{
    description: string;
    bankAccount: number | null;
    debit: number | null;
    credit: number | null;
  }> = [{ description: '', bankAccount: null, debit: null, credit: null }];

  totalDebit: number = 0;
  totalCredit: number = 0;
  uploadedFiles: File[] = [];
  jvNumber: string = '';
  narration: string = '';
  date: string = '';
  businessEntity: Entity = new Entity();
  glPostingHead: GlPostingHead = new GlPostingHead();
  glPostingDetails: GlPostingDetails[] = [];
  coaLedgerAccounts: CoaLedgerAccount[] = [];
  selectedCoaLedgerAccount: CoaLedgerAccount = new CoaLedgerAccount();

  constructor(
    private journalVoucherService: JournalVoucherService,
    private entityService: EntityService,
    private router: Router,
    private billService: BillService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.fetchGlPostingHead(this.route.snapshot.params['id']);
  }

  addRow() {
    this.rows.push({
      description: '',
      bankAccount: null,
      debit: null,
      credit: null,
    });
  }

  onDescriptionInput(index: number) {
    if (index === this.rows.length - 1 && this.rows[index].description.trim()) {
      this.addRow();
    }
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  removeRow(index: number) {
    this.rows.splice(index, 1);
    this.updateTotals();
  }

  updateTotals() {
    this.totalDebit = this.rows.reduce((sum, row) => sum + (row.debit || 0), 0);
    this.totalCredit = this.rows.reduce(
      (sum, row) => sum + (row.credit || 0),
      0
    );
  }

  mapTableToGlPostingDetails(): void {
    this.glPostingDetails = this.rows.map(
      (row) =>
        new GlPostingDetails(
          null,
          this.glPostingHead,
          this.selectedCoaLedgerAccount,
          null,
          row.debit,
          row.credit
        )
    );
  }

  navigateJVList(): void {
    this.router.navigate(['/journal-voucher-list']);
  }

  fetchGlPostingHead(id: number): void {
    this.journalVoucherService.getGlPostingHeadById(id).subscribe(
      (data) => {
        this.glPostingHead = data;
      },
      (error) => {
        console.error('Error fetching journal voucher:', error);
      }
    );
  }

  fetchGlAccounts(): void {
    const entityId = +localStorage.getItem('entityId')!;
    this.billService.getAllCoaLedgerAccounts(entityId).subscribe((data) => {
      this.coaLedgerAccounts = data;
    });
  }

  saveJV(): void {
    if (
      this.glPostingHead.description == null ||
      this.selectedCoaLedgerAccount == null ||
      this.totalCredit == 0 ||
      this.totalCredit == 0
    ) {
      Swal.fire({
        title: 'Error!',
        text: 'Fill all the required fields.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      });
      return;
    }
  }

  saveDraftJV(): void {
    if (
      this.glPostingHead.description == null ||
      this.selectedCoaLedgerAccount == null ||
      this.totalCredit == null ||
      this.totalCredit == null
    ) {
      Swal.fire({
        title: 'Error!',
        text: 'Fill all the required fields.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      });
      return;
    }
  }
}
