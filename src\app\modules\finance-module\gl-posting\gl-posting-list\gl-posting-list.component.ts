import { Component } from '@angular/core';
import { JournalVoucherService } from '../../journal-voucher/journal-voucher.service';
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { fromEvent, Subscription } from 'rxjs';
import { GlPostingHead, GlPostingDetails } from '../../journal-voucher/journal-voucher';
import { MatDialog } from '@angular/material/dialog';
import { GlPostingViewPopUpComponent } from '../gl-posting-view-pop-up/gl-posting-view-pop-up.component';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
@Component({
  selector: 'app-gl-posting-list',
  templateUrl: './gl-posting-list.component.html',
  styleUrls: ['./gl-posting-list.component.css']
})
export class GlPostingListComponent {

  filteredApInvoices: GlPostingHead[] = [];
  searchTerm: string = ''; 
  quotes:  GlPostingHead[] = []; 
  quotesDetails:  GlPostingDetails[] = []; 
  selectedApInvoices: Set<GlPostingHead> = new Set<GlPostingHead>();
  activeTab = 'all';
  startDate: string | null = null; 
  endDate: string | null = null; 
  expandedRowId: number | null = null;
  documentTypes: string[] = ['Journal Voucher', 'Bill', 'Batch Payment', 'Expense'];

  constructor(private journalVoucherService: JournalVoucherService, private router: Router, public sanitizer: DomSanitizer, private dialog: MatDialog) {}

  ngOnInit() {
    this.fetchQuotations();
    this.fetchQuotesDetails();
  }

  modalHideSubscription?: Subscription;

  ngAfterViewInit() {
    const modalElement = document.getElementById('quotePreviewModal');
    if (modalElement) {
      this.modalHideSubscription = fromEvent(modalElement, 'hide.bs.modal').subscribe(() => {
        this.clearSelectedCheckboxes();
      });
    }
  }

  ngOnDestroy() {
    if (this.modalHideSubscription) {
      this.modalHideSubscription.unsubscribe();
    }
  }
  
  clearSelectedCheckboxes() {
    this.selectedApInvoices.clear();
  }

  toggleSelection(quotation: GlPostingHead, event: any): void {
    if (event.target.checked) {
      this.selectedApInvoices.add(quotation);
    } else {
      this.selectedApInvoices.delete(quotation);
    }
  }

  fetchQuotations() {
    const entityId = parseInt(localStorage.getItem('entityId') || "0", 10);

    console.log("entity id:" , entityId);

    if (!entityId || isNaN(entityId)) {
      console.error("Invalid entityId. Cannot fetch quotations.");
      return;
  }
  
    this.journalVoucherService.getGlPostingHeadList(entityId).subscribe(
      (data: GlPostingHead[]) => {
        this.quotes = data;
        this.filteredApInvoices = this.quotes;
      },
      (error) => {
        console.error('Error fetching quotations:', error);
      }
    );
  }

  fetchQuotesDetails() {

    const entityId = parseInt(localStorage.getItem('entityId') || "0", 10);

    console.log("entity id:" , entityId);

    if (!entityId || isNaN(entityId)) {
      console.error("Invalid entityId. Cannot fetch quotations.");
      return;
  }
  
    this.journalVoucherService.getGlPostingDetailsList(entityId).subscribe(
      (data: GlPostingDetails[]) => {
        this.quotesDetails = data
        console.log('Gl Posting Details details:', this.quotesDetails);
      },
      (error) => {
        console.error('Error fetching quotations:', error);
      }
    );
  }

  onSearchChange() {
    this.filterQuotes();
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.filterQuotes();
  }

  filterQuotes() {
    const searchTermLower = this.searchTerm?.toLowerCase().trim() || '';
    let filtered = this.quotes;
  
    // Filter by active tab
    if (this.activeTab !== 'all') {
      filtered = filtered.filter(quote => quote.status?.toLowerCase() === this.activeTab);
    }
  
    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(quote =>
        quote.documentType?.toLowerCase().includes(searchTermLower) ||
        quote.documentNumber?.toLowerCase().includes(searchTermLower)
      );
    }
  
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(quote => new Date(quote.date!) >= startDate);
    }
  
    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(quote => new Date(quote.date!) <= endDate);
    }
  
    this.filteredApInvoices = filtered;
  }
  
  resetFilters() {
    this.searchTerm = '';
    this.activeTab = 'all';
    this.startDate = '';
    this.endDate = '';
    this.filteredApInvoices = this.quotes;
  }

  selectAll(event: any) {
    const isChecked = event.target.checked;
    this.quotes.forEach(quote => quote.selected = isChecked);
  }

  recordBatchPayment(): void {
    const selectedBills = this.quotes.filter((bill) => bill.selected);
    if (selectedBills.length === 0) {
      alert('Please select at least one bill to record batch payment.');
      return;
    }

    this.router.navigate(['/record-batch-payments'], {
      queryParams: { bills: JSON.stringify(selectedBills) }
    });
  }

  toggleDetails(glTransactionId: number) {
    if (this.expandedRowId === glTransactionId) {
      this.expandedRowId = null; 
    } else {
      this.expandedRowId = glTransactionId;
    }
  }

  getDetailsForPosting(glTransactionId: number | null) {
    console.log('Fetching details for glTransactionId:', glTransactionId);  
    const filteredDetails = this.quotesDetails.filter(detail => detail.glTransactionId?.glTransactionId === glTransactionId);
    console.log('Filtered details:', filteredDetails); 
    return filteredDetails;
  }

  openPopup(glTransactionId: number, documentType: string, documentNumber: string) {
    if (glTransactionId) {
      this.dialog.open(GlPostingViewPopUpComponent, {
        data: { transactionId: glTransactionId, documentType, documentNumber }
      });
    } else {
      console.error('Invalid transaction ID or document type');
    }
  }
  exportToExcel(): void {
    if (!this.filteredApInvoices || this.filteredApInvoices.length === 0) {
      alert('No data available to export.');
      return;
    }

    const exportData = this.filteredApInvoices.map(quote => ({
      'Document Number': quote.documentNumber || 'N/A',
      'Document Type': quote.documentType || 'N/A',
      'Date': quote.date ? new Date(quote.date).toLocaleDateString() : 'N/A', // Format date safely
      'Total Debit ($)': `$${(quote.totalDr ?? 0).toFixed(2)}`, // Handle null values with default 0
      'Total Credit ($)': `$${(quote.totalCr ?? 0).toFixed(2)}`  // Handle null values with default 0
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'AP Invoices');

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    this.saveAsExcelFile(excelBuffer, `AP_Invoices_${timestamp}`);
}

private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    });

    saveAs(data, `${fileName}.xlsx`);
}
}
