<div class="container-fluid">
  <!-- Commented out sections from React component -->
  <!--
  <div *ngIf="!isIos" class="d-flex align-items-center justify-content-center w-100 bg-light p-3 mb-3">
    <div class="h4 text-dark me-4">Download Mobile app for Android</div>
    <button 
      (click)="handleAndroidDownloadClick()" 
      [disabled]="!deferredPrompt"
      class="btn btn-custom fw-bold">
      Download now
    </button>
  </div>
  
  <div *ngIf="isIos && !isStandalone" class="d-flex align-items-center justify-content-center w-100 bg-light p-3 mb-3">
    <div class="h4 text-dark me-4">Download app for IOS</div>
    <button 
      (click)="handleIosInstructions()"
      class="btn btn-custom fw-bold">
      Download now
    </button>
  </div>
  -->

  <div class="mb-2">
    <button (click)="handleAndroidDownloadClick()"
      class="btn btn-custom d-flex align-items-center justify-content-center download-btn">
      <img src="assets/images/android.png" alt="Android" class="me-2 icon-size" />
      <div class="d-flex flex-column align-items-start">
        <div class="install-text">INSTALL FOR</div>
        <div class="platform-text">Android</div>
      </div>
    </button>
  </div>

  <div>
    <button (click)="handleIosInstructions()"
      class="btn btn-custom d-flex align-items-center justify-content-center download-btn">
      <img src="assets/images/apple.png" alt="iOS" class="me-2 icon-size" />
      <div class="d-flex flex-column align-items-start">
        <div class="install-text">INSTALL FOR</div>
        <div class="platform-text">iOS</div>
      </div>
    </button>
  </div>
</div>