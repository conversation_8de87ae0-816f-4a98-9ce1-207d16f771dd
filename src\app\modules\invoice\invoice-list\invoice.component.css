/* Base Styles */
* {
  box-sizing: border-box;
  font-family: "Inter", sans-serif;

}

body {
  margin: 0;
  padding: 0;
}

.main-container {
  background-color: rgb(241, 241, 241);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Typography */
label {
  display: block;
  font-weight: bold;
  font-family: Inter, sans-serif;
  font-size: 15px;
  color: #333;
  margin-bottom: 5px;
}

/* Header and Action Buttons */
.actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.actions h1 {
  margin: 0 0 20px 0;
  font-family: Inter, sans-serif;
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

.gradient-btn {
  background: linear-gradient(to right, #512ca2, #4262ff);
  border: none;
  color: white;
}

.gradient-btn:hover {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
}

/* Search and Create Section */
.search-create {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
  gap: 10px;
}

.search-create button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 13px;
  border: none;
}

.search-create button:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

/* Tabs Navigation */
.tabs-container {
  margin-bottom: 20px;
}

.nav-tabs {
  display: flex;
  border-bottom: 1px solid #dee2e6;
}

.nav-item {
  margin-right: 10px;
  width: 13.4%;
  text-align: center;
  font-family: Inter, sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  text-decoration: none;
  cursor: pointer;
}

.nav-link.active {
  border-top: 3px solid blue;
  color: #4262ff;
  font-weight: bold;
}

/* Card/Filter Section */
.Card {
  width: 100%;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.row1 {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.row1_col1 {
  width: 30%;
}

.row1_col3,
.row1_col4 {
  width: 20%;
}

.input-container {
  position: relative;
  width: 100%;
}

.search-input,
.date-picker,
.form-control {
  width: 100%;
  height: 49px;
  padding: 8px 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
}

.search-input {
  padding-right: 30px; /* Space for search icon */
}

.input-container i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  pointer-events: none;
}

.row2 {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  align-items: right;
}

.primary-button,
.add-btn {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: bold;
  padding: 5px 30px;
  cursor: pointer;
  border-radius: 12px;
  border: none;
  font-size: 17px;
  margin-left: 10px;
}

.secondary-button,
.cancel-btn {
  background: transparent;
  color: #4262ff;
  border: 1px solid #4262ff;
  padding: 5px 40px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  font-size: 17px;
  margin-right: 5px;
}

.primary-button:hover,
.add-btn:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

.secondary-button:hover,
.cancel-btn:hover {
  background-color: #4262ff;
  color: white;
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
  border-radius: 10px;
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

.table-head th {
  padding: 12px 10px;
  background-color: #d7dbf5;
  text-align: left;
  color: #000;
  font-size: 14px;
  font-weight: bold;
}

.table-head th:first-child {
  border-top-left-radius: 10px;
}

.table-head th:last-child {
  border-top-right-radius: 10px;
}

.text-center {
  text-align: center !important;
}

.text-right {
  text-align: right !important;
}

tbody tr {
  background-color: white;
  border-bottom: 1px solid #ababab;
  font-size: 14px;
  color: #666;
}

tbody tr:hover {
  background-color: #f1f1f1;
}

td, th {
  padding: 12px 10px;
}

td.valueCheckbox,
th.valueCheckbox {
  width: 3%;
}

td.value,
th.valuehead {
  width: 10.5%;
}

/* Status Labels */
.status-cell {
  text-align: center;
}

span.lable {
  display: inline-block;
  border: none;
  border-radius: 20px;
  padding: 5px 10px;
  font-weight: bold;
  text-align: center;
  width: 90px;
  /* white-space: nowrap; */
}

.border-draft {
  background-color: #2c4968;
  color: #007bff;
}

.border-pending {
  color: #debe15;
  background-color: #f2edd0;
}

.border-canceled {
  background-color: #eedbdd;
  color: #dc3545;
}

.border-revised {
  background-color: #eeebeb;
  color: #512ca2;
}

.border-sent {
  background-color: #ebf0f7;
  color: #455cff;
}

.border-paid {
  background-color: #d4edda;
  color: #28a745;
}

.border-overdue {
  background-color: #ffe4e0;
  color: #FF6610;
}

.border-awaiting-payment {
  background-color: #d1ecf1;
  color: #17a2b8;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: flex-start;
  gap: 8px;
}

.btn-action {
  border: none;
  background: none;
  padding: 4px;
  font-size: 1rem;
  cursor: pointer;
}

.edit-btn i {
  color: #4262ff;
}

.preview-btn i {
  color: #debe15;
}

.delete-btn i {
  color: #ff0000;
}

.btn-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dropdown Menu */
.dropdown-menu {
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: #4262ff;
  color: white;
}

/* Modal Styles */
.modal-dialog {
  max-width: 740px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
  padding: 15px 20px;
}

.modal-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
}

.icon-group {
  display: flex;
  gap: 20px;
}

.close-icon {
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.close-icon:hover {
  color: #000;
}

.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 600px;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.iframe-container {
  margin-top: 20px;
}

#invoicePreviewFrame {
  width: 100%;
  height: 700px;
  border: none;
}


/* Form Styles */
.form-group {
  margin-bottom: 15px;
}


/* Form input styles */

.form-group {
  margin-bottom: 10px;
  text-align: left;
  font-family: Arial, sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 25.41px;
  color: #444343;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 5px;
}

.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;
  /* Allow resizing only vertically */
  min-height: 100px;
  /* Minimum height for better visibility */
  max-height: 300px;
  /* Restrict max height */
}

.form-group input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 10px;
  font-size: 1rem;
}


.custom-close-btn {
  width: 50px;
  /* Adjust the width */
  height: 50px;
  /* Adjust the height */
}

.message-area {
  min-height: 350px;
  resize: vertical;
}

.popup-footer {
  display: flex;
  justify-content: flex-end;
  /* gap: 10px; */
  margin-top: 20px;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .actions h1 {
    font-size: 36px;
  }

  .search-create button {
    background: linear-gradient(to right, #4262ff, #512ca2);
    color: white;
    font-weight: bold;
    padding: 10px 10px;
    cursor: pointer;
    border-radius: 13px;
    border: none;
    margin-left: 10px;
    width: 350px;
    top: 356px;
    left: 879px;
    border-radius: 13px;
    font-size: 14px;
  }

  .nav-item {
    width: auto;
    margin-right: 5px;
  }
}

@media (max-width: 768px) {
  .actions h1 {
    font-size: 28px;
  }

  .row1_col1,
  .row1_col3,
  .row1_col4 {
    width: 100%;
    margin-bottom: 15px;
  }

  .nav-tabs {
    overflow-x: auto;
    white-space: nowrap;
    flex-wrap: nowrap;
    -webkit-overflow-scrolling: touch;
  }

  .nav-item {
    flex: 0 0 auto;
  }

  .modal-dialog {
    max-width: 95%;
    margin: 10px auto;
  }
}

@media (max-width: 576px) {
  .actions h1 {
    font-size: 24px;
  }

  .container {
    padding: 10px;
  }

  .card {
    width: 100%;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  .row2 {
      display: flex;
      flex-direction: column-reverse;
      align-items: right;
      justify-content: flex-end;
      margin-top: 20px;
    }

        .primary-button {
          background: linear-gradient(to right, #4262ff, #512ca2);
          color: white;
          font-weight: bold;
          padding: 5px 30px;
          cursor: pointer;
          border-radius: 12px;
          border: 1px solid #4262ff;
          margin-left: 0px;
          font-size: 17px;
          width: 100%;
        }

        .secondary-button {
          background: transparent;
          color: #4262ff;
          border: 1px solid #4262ff;
          padding: 5px 40px;
          margin-right: 0px;
          cursor: pointer;
          border-radius: 12px;
          font-weight: bold;
          font-size: 17px;
          width: 100%;
          margin-top: 10px;
        }

  .popup-footer {
    flex-direction: column;
  }

  .cancel-btn,
  .add-btn {
    width: 100%;
    margin: 5px 0;
  }
}
