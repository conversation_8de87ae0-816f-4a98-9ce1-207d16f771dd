import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';
import { CanActivate, Router } from '@angular/router';
import { map, Observable } from 'rxjs';
import { SubscriptionService } from './modules/subscription/subscription.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router,
    private subscriptionService: SubscriptionService
  ) {}

  canActivate(): Observable<boolean> {
    if (!this.authService.isLoggedIn()) {
      this.router.navigate(['/user-login']);
      return new Observable<boolean>((observer) => {
        observer.next(false);
        observer.complete();
      });
    }

    const entityId = Number(localStorage.getItem('entityId'));

    return this.subscriptionService.checkSubscriptionExpiryCached(entityId).pipe(
      map((isExpired: boolean) => {
        if (isExpired) {
          this.router.navigate(['/manage-subscription']);
          return false;
        }
        return true;
      })
    );
  }
}
