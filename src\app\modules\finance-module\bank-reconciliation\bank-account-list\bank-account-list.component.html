<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <!-- Heading And Button -->
  <div class="actions">
    <h1>Bank Accounts</h1>
  </div>
  <!-- <div class="Card"> -->
  <!-- Filter and Search Section -->
  <!-- <div class="row1"> -->
  <!-- Input for number, reference -->
  <!-- <div class="row1_col1">
          <label for="document-type">Search Term</label>
          <div class="input-container" class="">
            <input id="document-type" class="search-input" [(ngModel)]="searchTerm"/>
          </div>
      </div>
      <div class="row1_col3">
        <label for="min-balance">Min Balance</label>
        <div class="input-container">
          <input id="min-balance" type="number" class="search-input" [(ngModel)]="minBalance" placeholder="Min Balance"/>
        </div>
      </div>

      <div class="row1_col4">
        <label for="max-balance">Max Balance</label>
        <div class="input-container">
          <input id="max-balance" type="number" class="search-input" [(ngModel)]="maxBalance" placeholder="Max Balance"/>
        </div>
      </div>
    </div> -->

  <!-- <div class="row2">
      <div class="row2_col4">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>
      <div class="row2_col1">
        <button type="button" class="primary-button" (click)="filterBankAccounts()">
          Search
        </button>
      </div>
    </div>
  </div> -->

  <div class="table-responsive">
    <table>
      <colgroup>
        <col width="50%" />
        <col width="20%" />
        <col width="20%" />
        <col width="10%" />
      </colgroup>
      <tr class="tb-head">
        <th>Account Details</th>
        <th>Bank Balance</th>
        <th>System Balance</th>
        <th>Action</th>
      </tr>
      <tr class="tb-detail" *ngFor="let bankAccount of filteredBankAccounts">
        <td class="position-relative">
          <p *ngIf="bankAccount.bsbNo">{{ "BSB "+ bankAccount.bsbNo }}</p>
          <p>{{ bankAccount.accountNumber }}</p>
          <p>{{ bankAccount.accountName }}</p>
          <span  class="float-act-type" [ngClass]="{'bg-success-subtle text-success': bankAccount.accountType === 'Savings','bg-primary-subtle text-primary': bankAccount.accountType === 'PRIMARY'}" >{{bankAccount.accountType}}</span>
        </td>
        <td>{{ bankAccount.statementBalance | currency }}</td>
        <td>
          {{ bankAccount.systemBalance | currency }}
        </td>
        <td>
          <button
            class="btn"
            (click)="openBankReconciliation(bankAccount)"
            class="active-button"
          >
            Reconcile
          </button>
        </td>
      </tr>
    </table>
  </div>
</div>
