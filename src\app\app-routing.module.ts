import { FinalBankRecReportComponent } from './modules/finance-module/bank-reconciliation/final-bank-rec-report/final-bank-rec-report.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserLoginComponent } from './modules/admin/components/user/user-login/user-login.component';
import { HomeComponent } from './home/<USER>';
import { CreateUserComponent } from './modules/admin/components/user/create-user/create-user.component';
import { SalesDashboardComponent } from './modules/admin/components/dashboard/sales-dashboard/sales-dashboard.component';
import { UserInvitationComponent } from './modules/admin/components/user/user-invitation/user-invitation.component';
import { CreateUserThroughInvitationComponent } from './modules/admin/components/user/create-user-through-invitation/create-user-through-invitation.component';
import { UserAgreementComponent } from './modules/admin/components/user/user-agreement/user-agreement.component';
import { CreateEntityComponent } from './modules/entity/create-entity/create-entity.component';
import { PaymentComponent } from './modules/subscription/payment/payment.component';
import { ContactUsComponent } from './pages/contact-us/contact-us.component';
import { QuotationComponent } from './modules/quotation/quotation.component';
import { CreateQuotationComponent } from './modules/quotation/create-quotation/create-quotation.component';
import { CreditNoteComponent } from './modules/credit-note/credit-note-list/credit-note.component';
import { CreateCreditNoteComponent } from './modules/credit-note/create-credit-note/create-credit-note.component';
import { CreateBusinessPartnerComponent } from './modules/business-partner/create-business-partner/create-business-partner.component';
import { CreateInvoiceComponent } from './modules/invoice/create-invoice/create-invoice.component';
import { InvoiceComponent } from './modules/invoice/invoice-list/invoice.component';
import { EditQuotationComponent } from './modules/quotation/edit-quotation/edit-quotation.component';
import { UpdateInvoiceComponent } from './modules/invoice/update-invoice/update-invoice.component';
import { ReviseInvoiceComponent } from './modules/invoice/revise-invoice/revise-invoice.component';
import { HomeFeaturesComponent } from './home/<USER>/home-features.component';
import { ReviseQuotationComponent } from './modules/quotation/revise-quotation/revise-quotation.component';
import { ManageSubscriptionComponent } from './modules/subscription/manage-subscription/manage-subscription.component';
import { UserRegistrationComponent } from './modules/admin/components/user/user-registration/user-registration.component';
import { CopyFromQuotationComponent } from './modules/invoice/copy-from-quotation/copy-from-quotation.component';
import { QuotationReportComponent } from './modules/quotation/quotation-report/quotation-report.component';
import { InvoiceReportComponent } from './modules/invoice/invoice-report/invoice-report.component';
import { CreditNoteReportComponent } from './modules/credit-note/credit-note-report/credit-note-report.component';
import { PaymentReceiptComponent } from './modules/invoice/payment-receipt/payment-receipt.component';
import { CreatePaymentReceiptComponent } from './modules/invoice/create-payment-receipt/create-payment-receipt.component';
import { CopyFromQuoteComponent } from './modules/quotation/copy-from-quote/copy-from-quote.component';
import { BusinessEntityEditComponent } from './modules/settings/business-entity-edit/business-entity-edit.component';
import { CustomerStatementComponent } from './modules/customer-statement/customer-statement.component';
import { EntitySelectionComponent } from './modules/entity/entity-selection/entity-selection.component';
import { HelpPageComponent } from './pages/help/help-page/help-page.component';
import { ForgotPasswordComponent } from './modules/admin/components/user/user-login/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './modules/admin/components/user/user-login/reset-password/reset-password.component';
import { AccountStatementComponent } from './modules/account-statement/account-statement.component';
import { PaymentReceiptReportComponent } from './modules/invoice/payment-receipt-report/payment-receipt-report.component';
import { CreateCreditNoteUserComponent } from './modules/credit-note/create-credit-note-user/create-credit-note-user.component';
import { CreatePaymentReceiptUserComponent } from './modules/invoice/create-payment-receipt-user/create-payment-receipt-user.component';
import { CreateCountryComponent } from './modules/entity/country/create-country/create-country.component';
import { ListCountryComponent } from './modules/entity/country/list-country/list-country.component';
import { UpdateCountryComponent } from './modules/entity/country/update-country/update-country.component';
import { ListBusinessPartnerComponent } from './modules/business-partner/list-business-partner/list-business-partner.component';
import { UpdateBusinessPartnerComponent } from './modules/business-partner/update-business-partner/update-business-partner.component';
import { CreateItemComponent } from './modules/entity/item/create-item/create-item.component';
import { ListItemComponent } from './modules/entity/item/list-item/list-item.component';
import { UpdateItemComponent } from './modules/entity/item/update-item/update-item.component';
import { UserProfileComponent } from './modules/admin/components/user/user-profile/user-profile.component';
import { AuthGuard } from './auth.guard';
import { InviteAccountantComponent } from './modules/accountant/invite-accountant/invite-accountant.component';
import { AddAccountantComponent } from './modules/accountant/add-accountant/add-accountant.component';
import { RequestEntityComponent } from './modules/accountant/request-entity/request-entity.component';
import { AcceptRequestComponent } from './modules/accountant/accept-request/accept-request.component';
import { EntityRequestsComponent } from './modules/entity/entity-requests/entity-requests.component';
import { SubscriptionPlanListComponent } from './modules/subscription/subscription-plan-list/subscription-plan-list.component';
import { InviteEntityComponent } from './modules/entity/invite-entity/invite-entity.component';
import { ExpenceClaimsComponent } from './modules/finance-module/expence-claims/expence-claims.component';
import { GlAccountListComponent } from './modules/finance-module/gl-account/gl-account-list/gl-account-list.component';
import { PayrollSettingsComponent } from './modules/payroll-module/payroll-settings/payroll-settings.component';
import { BASComponent } from './modules/finance-module/bas/bas.component';
import { UpdatePayCalendarComponent } from './modules/payroll-module/pay-calendar/update-pay-calendar/update-pay-calendar.component';
import { EmpolyeeComponent } from './modules/payroll-module/payroll-settings/empolyee/empolyee.component';
import { UpdateEarningComponent } from './modules/payroll-module/update-earning/update-earning.component';
import { CreateGlAccountComponent } from './modules/finance-module/gl-account/create-gl-account/create-gl-account.component';
import { UpdateGlAccountComponent } from './modules/finance-module/gl-account/update-gl-account/update-gl-account.component';
import { CreateTransactionComponent } from './modules/finance-module/transaction/create-transaction/create-transaction.component';
import { TransactionListComponent } from './modules/finance-module/transaction/transaction-list/transaction-list.component';
import { UpdateTransactionComponent } from './modules/finance-module/transaction/update-transaction/update-transaction.component';
import { PayableBillListComponent } from './modules/finance-module/bill/payable-bill-list/payable-bill-list.component';
import { AddPayableBillComponent } from './modules/finance-module/bill/add-payable-bill/add-payable-bill.component';
import { UpdateDeductionComponent } from './modules/payroll-module/update-deduction/update-deduction.component';
import { PayRunUsersComponent } from './modules/payroll-module/payroll-settings/pay-run-users/pay-run-users.component';
import { BankAccountComponent } from './modules/finance-module/bank/bank-account/bank-account.component';
import { BankAccountsComponent } from './modules/finance-module/bank/bank-accounts/bank-accounts.component';
import { CreateJvComponent } from './modules/finance-module/journal-voucher/create-jv/create-jv.component';
import { JvListComponent } from './modules/finance-module/journal-voucher/jv-list/jv-list.component';
import { PayrollCalendarComponent } from './modules/payroll-module/payroll-calendar/payroll-calendar.component';
import { PayrollPayItemsComponent } from './modules/payroll-module/payroll-pay-items/payroll-pay-items.component';
import { PayrollPayRunComponent } from './modules/payroll-module/payroll-pay-run/payroll-pay-run.component';
import { PayRunUserDetailsComponent } from './modules/payroll-module/payroll-pay-run/pay-run-user-details/pay-run-user-details.component';
import { RecordBatchPaymentsComponent } from './modules/finance-module/bill/record-batch-payments/record-batch-payments.component';
import { PayRunPayTemplateComponent } from './modules/payroll-module/payroll-pay-run/pay-run-pay-template/pay-run-pay-template.component';
import { ExpenceClaimsListComponent } from './modules/finance-module/expence-claims/expence-claims-list/expence-claims-list.component';
import { FinanceDashboardComponent } from './modules/admin/components/dashboard/finance-dashboard/finance-dashboard.component';
import { ReccordBatchPaymentsListComponent } from './modules/finance-module/bill/reccord-batch-payments-list/reccord-batch-payments-list.component';
import { EditJvComponent } from './modules/finance-module/journal-voucher/edit-jv/edit-jv.component';
import { PaymentExpensesComponent } from './modules/finance-module/expence-claims/payment-expenses/payment-expenses.component';
import { PaymentExpensesListComponent } from './modules/finance-module/expence-claims/payment-expenses-list/payment-expenses-list.component';
import { ReportsComponent } from './modules/payroll-module/payroll-settings/reports/reports.component';
import { CreditNoteBillListComponent } from './modules/finance-module/bill/credit-note-bill-list/credit-note-bill-list.component';
import { CreditNoteBillComponent } from './modules/finance-module/bill/credit-note-bill/credit-note-bill.component';
import { PayrollPayrollSettingsComponent } from './modules/payroll-module/payroll-pay-run/payroll-payroll-settings/payroll-payroll-settings.component';
import { ReportPageComponent } from './modules/payroll-module/payroll-settings/report-page/report-page.component';
import { PayableBillListReportComponent } from './modules/finance-module/bill/payable-bill-list-report/payable-bill-list-report.component';
import { JvListReportComponent } from './modules/finance-module/journal-voucher/jv-list-report/jv-list-report.component';
import { EditPayableBillComponent } from './modules/finance-module/bill/edit-payable-bill/edit-payable-bill.component';
import { RecordBatchPaymentsListReportComponent } from './modules/finance-module/bill/record-batch-payments-list-report/record-batch-payments-list-report.component';
import { CreditNoteBillListReportComponent } from './modules/finance-module/bill/credit-note-bill-list-report/credit-note-bill-list-report.component';
import { GlPostingListComponent } from './modules/finance-module/gl-posting/gl-posting-list/gl-posting-list.component';
import { GlReportComponent } from './modules/finance-module/gl-posting/gl-report/gl-report.component';
import { GlViewEachAccountComponent } from './modules/finance-module/gl-posting/gl-view-each-account/gl-view-each-account.component';
import { PeriodClosingComponent } from './modules/finance-module/period-closing/period-closing/period-closing.component';
import { PnlReportComponent } from './modules/finance-module/p&l/pnl-report/pnl-report.component';
import { BsReportComponent } from './modules/finance-module/balance-sheet/bs-report/bs-report.component';
import { SupplierStatementComponent } from './modules/finance-module/supplier-statement/supplier-statement.component';
import { PaySlipComponent } from './modules/payroll-module/payroll-settings/reports/pay-slip/pay-slip/pay-slip.component';
import { EditExpenceComponent } from './modules/finance-module/expence-claims/edit-expence/edit-expence.component';
import { GlPostingListBySuppliersComponent } from './modules/finance-module/gl-posting/gl-posting-list-by-suppliers/gl-posting-list-by-suppliers.component';
import { ImportBankReconciliationComponent } from './modules/finance-module/bank-reconciliation/import-bank-reconciliation/import-bank-reconciliation.component';
import { BankAccountListComponent } from './modules/finance-module/bank-reconciliation/bank-account-list/bank-account-list.component';
import { UserVerificationComponent } from './modules/admin/components/user/user-verification/user-verification.component';
import { SuperannuationComponent } from './modules/payroll-module/superannuation/superannuation/superannuation.component';
import { MatchTransactionsComponent } from './modules/finance-module/bank-reconciliation/match-transactions/match-transactions/match-transactions.component';
import { ViewQuotationComponent } from './modules/quotation/view-quotation/view-quotation.component';
import { InvoiceCashReportComponent } from './modules/invoice/invoice-cash-report/invoice-cash-report.component';
import { ViewInvoiceComponent } from './modules/invoice/view-invoice/view-invoice.component';
import { PaymentSuccessComponent } from './pages/stripe/payment-success/payment-success.component';
import { WhyChooseUsComponent } from './pages/why-choose-us/why-choose-us.component';
import { BookkeeperOrAccountantComponent } from './pages/bookkeeper-or-accountant/bookkeeper-or-accountant.component';
import { PayrollComponent } from './pages/payroll/payroll.component';
import { InvoicingAndQuotingComponent } from './pages/invoicing-and-quoting/invoicing-and-quoting.component';
import { ExpenseTrackingComponent } from './pages/expense-tracking/expense-tracking.component';
import { GstBasComponent } from './pages/gst-bas/gst-bas.component';
import { ReportingComponent } from './pages/reporting/reporting.component';
import { PartnershipComponent } from './pages/partnership/partnership.component';
import { ManageUsersComponent } from './modules/admin/components/user/manage-users/manage-users.component';
import { ViewPayableBillComponent } from './modules/finance-module/bill/view-payable-bill/view-payable-bill.component';
import { BasPeriodComponent } from './modules/finance-module/bas/bas-period/bas-period/bas-period.component';
import { SalesReportsComponent } from './modules/quotation/sales-reports/sales-reports/sales-reports.component';
import { BasViewComponent } from './modules/finance-module/bas/bas-view/bas-view.component';
import { FinanceReportsComponent } from './modules/finance-module/finance-reports/finance-reports.component';
import { BankRuleListComponent } from './modules/finance-module/bank-reconciliation/bank-rule-list/bank-rule-list.component';
import { CreateBankRuleComponent } from './modules/finance-module/bank-reconciliation/create-bank-rule/create-bank-rule.component';
import { StpComponent } from './modules/payroll-module/stp/stp.component';
import { GlViewPnlComponent } from './modules/finance-module/gl-posting/gl-view-pnl/gl-view-pnl.component';
const routes: Routes = [
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  { path: 'home', component: HomeComponent },
  { path: 'features', component: HomeFeaturesComponent },
  { path: 'contact-us', component: ContactUsComponent },
  { path: 'payment-success', component: PaymentSuccessComponent },

  { path: 'user-login', component: UserLoginComponent },
  { path: 'forgot-password', component: ForgotPasswordComponent },
  { path: 'reset-password', component: ResetPasswordComponent },
  { path: 'create-user', component: CreateUserComponent },
  { path: 'user-registration', component: UserRegistrationComponent },
  { path: 'verify-user', component: UserVerificationComponent },
  // Protected Routes with AuthGuard
  {
    path: 'sales-dashboard',
    component: SalesDashboardComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'finance-dashboard',
    component: FinanceDashboardComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'finance-reports',
    component: FinanceReportsComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'invite-user',
    component: UserInvitationComponent,
    canActivate: [AuthGuard],
  },
   {
    path: 'manage-users',
    component: ManageUsersComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'user-profile',
    component: UserProfileComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'account-statement',
    component: AccountStatementComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-user-through-invitation',
    component: CreateUserThroughInvitationComponent,
  },
  { path: 'user-agreement', component: UserAgreementComponent },
  { path: 'create-entity', component: CreateEntityComponent },
  { path: 'select-entity', component: EntitySelectionComponent },
  { path: 'payment', component: PaymentComponent },

  {
    path: 'customer-statement',
    component: CustomerStatementComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'quotation',
    component: QuotationComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-quotation',
    component: CreateQuotationComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'edit-quotation/:id',
    component: EditQuotationComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'view-quotation/:id',
    component: ViewQuotationComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'revise-quote/:id',
    component: ReviseQuotationComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'copy-quote/:id',
    component: CopyFromQuoteComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'quotation-report',
    component: QuotationReportComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'invoice',
    component: InvoiceComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-invoice',
    component: CreateInvoiceComponent,
    canActivate: [AuthGuard],
  },
    {
    path: 'view-invoice/:id',
    component: ViewInvoiceComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'copy-from-quotation/:id',
    component: CopyFromQuotationComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'create-invoice/:id',
    component: CreateInvoiceComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'update-invoice/:id',
    component: UpdateInvoiceComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'revise-invoice/:id',
    component: ReviseInvoiceComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'invoice-report',
    component: InvoiceReportComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'invoice-cash-report',
    component: InvoiceCashReportComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'payment-receipt',
    component: PaymentReceiptComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-payment-receipt',
    component: CreatePaymentReceiptComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-payment-receipt-user',
    component: CreatePaymentReceiptUserComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'credit-note',
    component: CreditNoteComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-credit-note',
    component: CreateCreditNoteComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'credit-note-report',
    component: CreditNoteReportComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-credit-note-user',
    component: CreateCreditNoteUserComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'business-entity-edit',
    component: BusinessEntityEditComponent,
    canActivate: [AuthGuard],
  },
  { path: 'invoice', component: InvoiceComponent },
  { path: 'create-invoice', component: CreateInvoiceComponent },
  { path: 'copy-from-quotation/:id', component: CopyFromQuotationComponent },
  { path: 'create-invoice/:id', component: CreateInvoiceComponent },
  { path: 'update-invoice/:id', component: UpdateInvoiceComponent },
  { path: 'revise-invoice/:id', component: ReviseInvoiceComponent },
  { path: 'invoice-report', component: InvoiceReportComponent },
  { path: 'payment-receipt', component: PaymentReceiptComponent },
  { path: 'bas-loadge', component: BASComponent },

  { path: 'create-payment-receipt', component: CreatePaymentReceiptComponent },
  {
    path: 'create-payment-receipt-user',
    component: CreatePaymentReceiptUserComponent,
  },
  { path: 'credit-note', component: CreditNoteComponent },
  { path: 'create-credit-note', component: CreateCreditNoteComponent },
  { path: 'credit-note-report', component: CreditNoteReportComponent },
  { path: 'create-credit-note-user', component: CreateCreditNoteUserComponent },
  { path: 'business-entity-edit', component: BusinessEntityEditComponent },
  {
    path: 'create-invoice',
    component: CreateInvoiceComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'copy-from-quotation/:id',
    component: CopyFromQuotationComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-invoice/:id',
    component: CreateInvoiceComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'update-invoice/:id',
    component: UpdateInvoiceComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'revise-invoice/:id',
    component: ReviseInvoiceComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'invoice-report',
    component: InvoiceReportComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'payment-receipt',
    component: PaymentReceiptComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-payment-receipt',
    component: CreatePaymentReceiptComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'reports',
    component: ReportsComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'reportPage',
    component: ReportPageComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'create-payment-receipt-user',
    component: CreatePaymentReceiptUserComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'credit-note',
    component: CreditNoteComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-credit-note',
    component: CreateCreditNoteComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'credit-note-report',
    component: CreditNoteReportComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-credit-note-user',
    component: CreateCreditNoteUserComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'business-entity-edit',
    component: BusinessEntityEditComponent,
    canActivate: [AuthGuard],
  },
  { path: 'help-page', component: HelpPageComponent },
  {
    path: 'payment-receipt-report',
    component: PaymentReceiptReportComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-country',
    component: CreateCountryComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'country',
    component: ListCountryComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'update-country/:id',
    component: UpdateCountryComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'update-business-Partner/:id',
    component: UpdateBusinessPartnerComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'business-partner',
    component: ListBusinessPartnerComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-business-partner',
    component: CreateBusinessPartnerComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-item',
    component: CreateItemComponent,
    canActivate: [AuthGuard],
  },
  { path: 'item', component: ListItemComponent, canActivate: [AuthGuard] },
  {
    path: 'update-item/:id',
    component: UpdateItemComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'manage-subscription',
    component: ManageSubscriptionComponent,
  },

  {
    path: 'invite-accountant',
    component: InviteAccountantComponent,
    canActivate: [AuthGuard],
  },

  //Finance
  {
    path: 'bank-account',
    component: BankAccountComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'bank-accounts',
    component: BankAccountsComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'ExpenceClaims',
    component: ExpenceClaimsComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'expence-claims-list',
    component: ExpenceClaimsListComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'record-batch-payments',
    component: RecordBatchPaymentsComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'record-batch-payments-list',
    component: ReccordBatchPaymentsListComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'record-batch-payments-list-report',
    component: RecordBatchPaymentsListReportComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'payment-expenses',
    component: PaymentExpensesComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'payment-expenses-list',
    component: PaymentExpensesListComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'update-bill/:id',
    component: EditPayableBillComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'import-bank-reconciliation',
    component: ImportBankReconciliationComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'bank-account-list',
    component: BankAccountListComponent,
    canActivate: [AuthGuard],
  },

  // payroll paths
  {
    path: 'payroll-settings',
    component: PayrollSettingsComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'payroll-calendar',
    component: PayrollCalendarComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'payroll-pay-items',
    component: PayrollPayItemsComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'payroll-pay-run',
    component: PayrollPayRunComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'payRun-user-details/:payCalendarId/:payPeriodId/:payRunId',
    component: PayRunUserDetailsComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'payRun-pay-template/:employeeId/:payPeriodId/:payRunId',
    component: PayRunPayTemplateComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'payroll-settings-employee',
    component: EmpolyeeComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'update-calendar/:id',
    component: UpdatePayCalendarComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'update-earning/:id',
    component: UpdateEarningComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'update-deduction/:id',
    component: UpdateDeductionComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'pay-slip/:employeeId/:payCalendarId/:payPeriodId',
    component: PaySlipComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'superannuation',
    component: SuperannuationComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'stp',
    component: StpComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'add-accountant',
    component: AddAccountantComponent,
  },
  {
    path: 'request-businessEntity',
    component: RequestEntityComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'entity-requests',
    component: EntityRequestsComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'payRun-users',
    component: PayRunUsersComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'invite-entity',
    component: InviteEntityComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'accept-request',
    component: AcceptRequestComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'subscription-plan-list',
    component: SubscriptionPlanListComponent,
  },

  //finance paths
  {
    path: 'payable-bill-list',
    component: PayableBillListComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'add-payable-bill',
    component: AddPayableBillComponent,
    canActivate: [AuthGuard],
  },

    {
    path: 'bookkeeper',
    component: BookkeeperOrAccountantComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'gl-account',
    component: GlAccountListComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'update-gl-account/:id',
    component: UpdateGlAccountComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-gl-account',
    component: CreateGlAccountComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'transaction-list',
    component: TransactionListComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'update-transaction/:id',
    component: UpdateTransactionComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'create-transaction',
    component: CreateTransactionComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'create-journal-voucher',
    component: CreateJvComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'payroll-payroll-setting',
    component: PayrollPayrollSettingsComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'journal-voucher-list',
    component: JvListComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'update-journal-voucher/:id',
    component: EditJvComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'credit-note-bill',
    component: CreditNoteBillComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'credit-note-bill-list',
    component: CreditNoteBillListComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'credit-note-bill-list-report',
    component: CreditNoteBillListReportComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'payable-bill-list-report',
    component: PayableBillListReportComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'jv-list-report',
    component: JvListReportComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'gl-posting-list',
    component: GlPostingListComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'gl-report',
    component: GlReportComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'gl-account-details',
    component: GlViewEachAccountComponent,
    canActivate: [AuthGuard],
  },

    {
    path: 'gl-view-pnl',
    component: GlViewPnlComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'period-closing',
    component: PeriodClosingComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'pnl-report',
    component: PnlReportComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'bs-report',
    component: BsReportComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'supplier-statement',
    component: SupplierStatementComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'edit-expense/:id',
    component: EditExpenceComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'gl-account-details-by-supplier',
    component: GlPostingListBySuppliersComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'match-transactions',
    component: MatchTransactionsComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'view-payable-bill/:id',
    component: ViewPayableBillComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'bas-period',
    component: BasPeriodComponent,
    canActivate: [AuthGuard],
  },
    {
    path: 'bas-view/:id',
    component: BasViewComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'sales-reports',
    component: SalesReportsComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'bank-rule-list',
    component: BankRuleListComponent,
    canActivate: [AuthGuard],
  },

   {
    path: 'create-bank-rule',
    component: CreateBankRuleComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'final-bank-rec',
    component: FinalBankRecReportComponent,
    canActivate: [AuthGuard],
  },


  { path: 'why-choose-us', component: WhyChooseUsComponent },
  { path: 'bookkeeper-or-accountant', component: BookkeeperOrAccountantComponent },
  { path: 'payroll', component: PayrollComponent },
  { path: 'invoicing-and-quoting', component: InvoicingAndQuotingComponent },
  { path: 'expense-tracking', component: ExpenseTrackingComponent },
  { path: 'gst-bas', component: GstBasComponent },
  { path: 'reporting', component: ReportingComponent },
  { path: 'partnership', component: PartnershipComponent },

  { path: '**', redirectTo: '/home' },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      scrollPositionRestoration: 'top',
      anchorScrolling: 'enabled',
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}


