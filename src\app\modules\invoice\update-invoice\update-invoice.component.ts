import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';
import { Entity, EntityTradingName } from '../../entity/entity';
import { EntityService } from '../../entity/entity.service';
import { SalesItem } from '../../quotation/quotation';
import { QuotationService } from '../../quotation/quotation.service';
import { InvoiceHead, InvoiceDetail, InvoiceLog } from '../invoice';
import { InvoiceService } from '../invoice.service';
import { HttpErrorResponse } from '@angular/common/http';
import { NgForm } from '@angular/forms';
import { HttpService } from 'src/app/http.service';
import { UserService } from '../../admin/components/user/user.service';
import { BusinessPartnerType, BusinessPartner } from '../../business-partner/business-partner';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { Observable, map, catchError, of } from 'rxjs';
import { StorageService } from '../../entity/storage.service';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';
import { PeriodClosingService } from '../../finance-module/period-closing/period-closing.service';

@Component({
  selector: 'app-update-invoice',
  templateUrl: './update-invoice.component.html',
  styleUrls: ['./update-invoice.component.css']
})
export class UpdateInvoiceComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  invoiceHead: InvoiceHead = new InvoiceHead();
  invoiceDetail: InvoiceDetail = new InvoiceDetail();
  invoiceHeadList: InvoiceHead[] = [];
  invoiceDetails: InvoiceDetail[] = [];
  businessPartnerType: BusinessPartnerType[] = [];
  invoiceLogs: InvoiceLog[] = [];

  id: number = 0;
  businessEntityId: number = 0;
  lastInvoiceNumber: string = '';


  salesItems: SalesItem[] = [];

  itemPopUp: boolean = false;
  businessEntity: Entity = new Entity();
  newItem: SalesItem = {
    salesItemId: 0,
    entityId: 1,
    userId: 1,
    itemTypeId: 1,
    itemCode: '',
    itemName: '',
    sellingPrice: 0,
    standardDiscount: 0,
    salesAccount: '',
    taxApplicability: '',
    itemStatus: 'active',
    description: '',
    unitPrice: 0,
    amount: 0,
    transactionDate: '2023-07-15',
  };

  unitPriceInvalid: boolean = false;
  itemCode: string = '';
  taxApplicable: boolean = false;
  businessPartner: BusinessPartner = new BusinessPartner();
  customers: BusinessPartner[] = [];
  allSalesItems: SalesItem[] = [];
  filteredSalesItems: SalesItem[] = [];
  showLogs: boolean = false;
  entityTradingNames: EntityTradingName[] = [];
  showTaxApplicabilityDropdown: boolean = true;
  entityId: number = 0;
  userId: number = 0;
  showUpdateLink = true;
  isSaving: boolean = false;

  constructor(
    private httpService: HttpService,
    private quotationService: QuotationService,
    private entityService: EntityService,
    private router: Router,
    private route: ActivatedRoute,
    private invoiceService: InvoiceService,
    private businessPartnerService: BusinessPartnerService,
    private userService: UserService,
    private storageService: StorageService,
    private swalAlertsService: SwalAlertsService,
    private periodClosingService: PeriodClosingService
  ) {
    this.entityId = this.storageService.getEntityId();
    this.userId = this.storageService.getUserId();
  }


  ngOnInit() {

    this.id = this.route.snapshot.params['id'];
    this.getBusinessEntityById();
    this.getEntityTradingNamesByEntityId();
    this.loadCustomers();
    this.fetchAllSalesItems();
    this.getInvoiceHeadById();
    this.getInvoiceDetailsByInvoiceHeadId();
    this.getAllInvoiceLogsByInvoiceHeadId();

    if (!this.entityId) {
      this.showTaxApplicabilityDropdown = false;
      this.newItem.taxApplicability = 'no';
      return;
    }

    this.entityService.getBusinessEntityById(this.entityId).subscribe(
      (entity: Entity) => {
        this.showTaxApplicabilityDropdown = entity.taxApplicability === 'yes';
        this.newItem.taxApplicability = entity.taxApplicability || 'no';
      },
      error => {
        console.error('Error fetching entity:', error);
        this.showTaxApplicabilityDropdown = false;
        this.newItem.taxApplicability = 'no';
      }
    );

      const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      const userType = user.userType; // assuming userType is here

      if (userType === 'General user' || userType === 'Accountant') {
        this.showUpdateLink = false;
      }
    }
  }



  /** Entity **/
  getEntityTradingNamesByEntityId() {
    this.businessEntityId = this.entityId;

    this.entityService.getEntityTradingNamesByEntityId(this.businessEntityId).subscribe(data => {
      this.entityTradingNames = data;

    }, error => console.error(error));
  }

  getBusinessEntityById() {

    this.businessEntityId = this.entityId;

    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(data => {
      this.businessEntity = data;

    }, error => console.error(error));

  }

  getInvoiceHeadById() {

    this.invoiceService.getInvoiceHeadById(this.id).subscribe(data => {
      this.invoiceHead = data;

    }, error => console.error(error));

  }

  getAllInvoiceLogsByInvoiceHeadId() {

    this.invoiceService.getAllInvoiceLogsByInvoiceHeadId(this.id).subscribe(data => {
      this.invoiceLogs = data;

    }, error => console.error(error));

  }

  getInvoiceDetailsByInvoiceHeadId() {
    this.invoiceHead.invoiceDetails = [];
    this.salesItems = [];
    this.invoiceService.getInvoiceDetailsByInvoiceHeadId(this.id).subscribe(data => {

      this.invoiceHead.invoiceDetails = data;
      this.salesItems = this.invoiceHead.invoiceDetails.map(detail => detail.salesItem);

    }, error => console.error(error));

  }


  customSearchFn(term: string, item: any) {
    term = term.toLowerCase();
    const itemCode = (item.itemCode || '').toLowerCase(); // Default to empty string if undefined
    const description = (item.description || '').toLowerCase(); // Default to empty string if undefined
    return itemCode.includes(term) || description.includes(term);
  }


  addEmptyRows(count: number) {
    for (let i = 0; i < count; i++) {
      this.addNewRow();
    }
  }

  addNewRow() {
    this.invoiceHead.invoiceDetails.push(this.createEmptyRow());
  }

  // Helper method to create an empty row
  createEmptyRow() {
    return {
      salesItem: new SalesItem(),
      taxCategoryId: 0,
      invoiceNumber: this.invoiceHead.invoiceNumber,
      quantity: 0,
      unitPrice: 0,
      description: '',
      discount: 0,
      tax: 0,
      amount: 0,
      discountType: '$',
      notes: '',
      subTotal: 0,
      totalDiscountPercentage: 0,
      coaLedgerAccountId:0,
      ledgerAccountName: '',
      ledgerAccountCode: '',
    };
  }


  // New method to reset taxApplicability
  resetTaxApplicability(index: number): void {
    this.invoiceHead.invoiceDetails[index].taxApplicability = false; // Reset to unchecked
    this.invoiceHead.invoiceDetails[index].tax = 0; // Reset tax value
  }

  updateInvoiceDetails(selectedItem: SalesItem, index: number) {
    // Update the details for the selected index
    this.invoiceHead.invoiceDetails[index].salesItem = selectedItem; // Set the selected item
    this.invoiceHead.invoiceDetails[index].unitPrice = selectedItem.unitPrice; // Set the unit price
    this.invoiceHead.invoiceDetails[index].amount = this.invoiceHead.invoiceDetails[index].quantity * selectedItem.unitPrice; // Calculate the amount

    this.checkTaxApplicability(index);

    this.calculateSubTotal(); // Recalculate subtotal after item is selected

    // Check if this is the last row, if so, add a new row
    if (index === this.invoiceHead.invoiceDetails.length - 1) {
      // this.addNewRow(); // Add a new row for the next item
    }
  }

  onDescriptionInput(index: number) {
    // Check if this is the last row, if so, add a new row
    if (index === this.invoiceHead.invoiceDetails.length - 1) {
      // this.addNewRow();
    }
  }


  //calculation


  applyDiscount(detail: InvoiceDetail, itemAmount: number) {
    if (detail.quantity === 0) {
      detail.amount = 0;
    } else {
      if (detail.discountType === 'B') {
        detail.amount = itemAmount - itemAmount * (detail.discount / 100);
      } else if (detail.discountType === '$') {
        detail.amount = itemAmount - detail.discount;
      } else {
        detail.amount = itemAmount;
      }

      // Ensure amount doesn't go below zero
      detail.amount = Math.max(0, detail.amount);
    }
  }




  updateAmount(index: number): void {
    const detail = this.invoiceHead.invoiceDetails[index];
    const itemAmount = detail.quantity * detail.unitPrice;

    if (detail.quantity < 0 || detail.unitPrice < 0 || detail.discount < 0) {
      this.swalAlertsService.showWarning("Negative values are not allowed.", () => {
        this.resetInvalidInput(detail);
      });
      return;
    }

    if (detail.quantity === 0 && (detail.discount > 0 || detail.discountType !== '')) {
      this.swalAlertsService.showWarning("Please specify a quantity first.", () => {
        detail.discount = 0;
        detail.discountType = '$';
        detail.amount = 0;
        detail.tax = 0;
      });
      return;
    }

    this.applyDiscount(detail, itemAmount);

    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (!this.invoiceHead.invoiceDetails[index].taxApplicability) {
            this.checkTaxApplicability(index);
          }

          if (this.invoiceHead.invoiceDetails[index].taxApplicability && this.taxApplicable) {
            this.applyFlatTax(index, taxRate);
          } else {
            detail.tax = 0;
          }

          this.calculateSubTotal();
        },
        error => {
          console.error('Error fetching entity data:', error);
          detail.tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      detail.tax = 0;
      this.calculateSubTotal();
    }
  }


  resetInvalidInput(detail: InvoiceDetail) {
    if (detail.quantity < 0) {
      detail.quantity = 0;
    }

    if (detail.discount < 0) {
      detail.discount = 0; // Reset discount
    }

    if (detail.unitPrice < 0) {
      detail.unitPrice = 0;
    }
    detail.discount = detail.discount; // Reset discount
    detail.discountType = detail.discountType; // Reset discount type
    detail.amount = detail.amount; // Reset amount
    detail.unitPrice = detail.unitPrice;
    detail.quantity = detail.quantity;
    detail.tax = detail.tax; // Reset tax

  }



  updateDiscountType(index: number, value: string) {
    const detail = this.invoiceHead.invoiceDetails[index];
    const itemAmount = detail.quantity * detail.unitPrice;
    detail.discountType = value;

    // Reset discount amount when changing type
    detail.discount = 0;

    if (detail.quantity === 0) {
      this.swalAlertsService.showWarning("Please add a quantity before applying a discount", () => {
        detail.discountType = '$';
      });
    } else {
      // this.applyDiscount(detail, itemAmount);
      this.updateAmount(index);
    }

    this.calculateSubTotal();
  }


  updateQuantity(index: number) {
    this.updateAmount(index); // Ensure amount is updated whenever quantity changes
  }

  calculateTotalDiscount(): number {
    let totalDiscount = 0;

      if (!this.invoiceHead?.invoiceDetails || this.invoiceHead.invoiceDetails.length === 0) {
    this.invoiceHead.totalDiscAmount = 0;
    return 0;
  }
  
    this.invoiceHead.invoiceDetails.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;
      let discountAmount = 0;

      if (detail.discountType === 'B') {
        discountAmount = itemAmount * (detail.discount / 100);
      } else if (detail.discountType === '$') {
        discountAmount = detail.discount;
      }

      totalDiscount += discountAmount;
    });

    this.invoiceHead.totalDiscAmount = totalDiscount;
    return totalDiscount;
  }

  checkTaxApplicability(index: number): void {
    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          this.updateDetailsTaxApplicability(index, taxRate);
          this.calculateSubTotal();
        },
        error => {
          console.error('Error fetching entity data:', error);
          this.updateDetailsTaxApplicability(index, 0);
          this.calculateSubTotal();
        }
      );
    } else {
      this.updateDetailsTaxApplicability(index, 0);
      this.calculateSubTotal();
    }
  }

  updateDetailsTaxApplicability(index: number, taxRate: number): void {
    const detail = this.invoiceHead.invoiceDetails[index];
    const itemTaxApplicable = detail.salesItem.taxApplicability === 'yes';

    if (this.taxApplicable && itemTaxApplicable) {
      detail.tax = detail.amount * (taxRate / 100);
    } else {
      detail.tax = 0;
    }
  }



  onTaxApplicableChange(index: number): void {
    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (this.taxApplicable && this.invoiceHead.invoiceDetails[index].taxApplicability) {
            this.applyFlatTax(index, taxRate);
          } else {
            this.invoiceHead.invoiceDetails[index].tax = 0;
          }

          this.calculateSubTotal();
        },
        error => {
          console.error('Error fetching entity data:', error);
          this.invoiceHead.invoiceDetails[index].tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      this.invoiceHead.invoiceDetails[index].tax = 0;
      this.calculateSubTotal();
    }
  }

  applyFlatTax(index: number, taxRate: number): void {
    const detail = this.invoiceHead.invoiceDetails[index];
    detail.tax = detail.amount * (taxRate / 100);
    this.calculateSubTotal();
  }


  removeItem(index: number): void {
    const invoiceDetailId = this.invoiceHead.invoiceDetails[index]?.invoiceDetailId;

    if (invoiceDetailId === undefined) {
      this.removeItemLocally(index);
      return;
    }

    this.swalAlertsService.showConfirmationDialog(
      'Are you sure?',
      'Do you want to remove this item from the Invoice?',
      () => this.confirmRemoveItem(index, invoiceDetailId)
    );
  }

  private removeItemLocally(index: number): void {
    this.salesItems.splice(index, 1);
    this.invoiceHead.invoiceDetails.splice(index, 1);
    this.calculateSubTotal();
  }

  private confirmRemoveItem(index: number, invoiceDetailId: number): void {
    this.deletedItemIds.push(invoiceDetailId);
    this.removeItemLocally(index);
  }

  private deletedItemIds: number[] = [];

  getDeletedItemIds(): number[] {
    return this.deletedItemIds;
  }



  calculateSubTotal() {
    let subTotal = 0;
    let totalTax = 0;
    let totalDiscount = 0;

    this.invoiceHead.invoiceDetails.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;

      if (detail.quantity > 0) {
        this.applyDiscount(detail, itemAmount);
        subTotal += detail.amount;
        totalTax += detail.tax;
        totalDiscount +=
          detail.discountType === 'B'
            ? itemAmount * (detail.discount / 100)
            : detail.discount;
      } else {
        subTotal += 0;
        totalTax += 0;
        totalDiscount += 0;
      }
    });

    this.invoiceHead.totalAmount = subTotal;
    this.invoiceHead.totalGst = totalTax;
    this.invoiceHead.totalDiscAmount = totalDiscount;
    this.calculateGrandTotal();
  }

  calculateGrandTotal() {
    const { totalAmount, totalGst } = this.invoiceHead;
    this.invoiceHead.grandTotal = totalAmount + totalGst;
  }





  fetchAllSalesItems() {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.quotationService.getAllSalesItemsByEntity(entityId).subscribe(
      (items: SalesItem[]) => {
        this.allSalesItems = items.filter(item => item.itemCode !== 'SISSERVICE'); // Store all items
        this.filteredSalesItems = items; // Initialize filtered items with all items
      },
      (error: any) => {
        console.error('Error fetching items', error);
        Swal.fire({
          title: 'Error!',
          text: 'Unable to fetch items.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Unable to fetch items.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
  }



  onItemSelected(selectedItem: SalesItem, index: number): void {
    if (selectedItem && selectedItem.itemCode) {
      // Check if the selected item already exists in the invoiceDetails array excluding the current index
      const existingItemIndex = this.invoiceHead.invoiceDetails.findIndex(
        (detail, i) => detail.salesItem.itemCode === selectedItem.itemCode && i !== index
      );

      if (existingItemIndex !== -1) {
        // If the item exists at a different index, show a confirmation dialog
        Swal.fire({
          title: 'Item already exists',
          text: 'Do you want to add this item again?',
          icon: 'question',
          showCancelButton: true,
          confirmButtonText: 'Yes',
          confirmButtonColor: '#ff7e5f',
          cancelButtonText: 'No',
          cancelButtonColor: '#be0032',
        }).then((result) => {
          if (result.isConfirmed) {
            this.updateInvoiceDetails(selectedItem, index); // Update the current row with the selected item
            this.resetTaxApplicability(index); // Reset taxApplicability when item is selected
          } else if (result.dismiss === Swal.DismissReason.cancel) {
            // Remove the entire row if the user clicks "No"
            this.invoiceHead.invoiceDetails.splice(index, 1);
          }
        });
      } else {
        // If the item doesn't exist, directly assign it to the current index
        this.updateInvoiceDetails(selectedItem, index);
        this.resetTaxApplicability(index); // Reset taxApplicability when item is selected
      }
    } else {
      Swal.fire({
        title: 'Error!',
        text: 'Please select a valid item.',
        icon: 'error',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
      }).then((result) => {
        if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData('Please select a valid item.');
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound()
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });
    }
  }




  loadCustomers() {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.businessPartnerService.getCustomerListByEntity(entityId).subscribe(
      (customers: BusinessPartner[]) => {
        this.customers = customers;
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching customers', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load customers.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Failed to load customers.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
    this.invoiceHead.businessPartnerId = "";
  }

  loadBusinessPartnerTypes() {
    this.businessPartnerService.getBusinessPartnerTypesList().subscribe(
      (businessPartnerType: BusinessPartnerType[]) => {
        // Find the "Customer" type from the list
        const customerType = businessPartnerType.find(
          (type) => type.businessPartnerType.toLowerCase() === 'customer'
        );

        if (customerType) {
          // Assign the customer type to the businessPartner object
          this.businessPartner.businessPartnerTypeId.businessPartnerTypeId = customerType.businessPartnerTypeId;
        }

        // Optionally store the filtered list if needed
        this.businessPartnerType = businessPartnerType;

      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching Business Partner Type', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load Business Partner Type.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Failed to load Business Partner Type.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );

  }

  setTodayDate() {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const dd = String(today.getDate()).padStart(2, '0');

    this.invoiceHead.postingDate = `${yyyy}-${mm}-${dd}`;
    this.updateValidityMinDate();
  }

  updateValidityMinDate() {
    const invoiceDate = this.invoiceHead.postingDate;
    if (invoiceDate) {
      const invoiceDateObj = new Date(invoiceDate);
      const yyyy = invoiceDateObj.getFullYear();
      const mm = String(invoiceDateObj.getMonth() + 1).padStart(2, '0');
      const dd = String(invoiceDateObj.getDate()).padStart(2, '0');

      const minValidUntilDate = `${yyyy}-${mm}-${dd}`;
      (document.getElementById('dueDate') as HTMLInputElement).min = minValidUntilDate;
    }
  }


  invoiceStatus: any;

  setStatus(status: string) {
    this.invoiceStatus = status;
  }


  onSubmit(f: NgForm) {
    if (!f.valid) return;

    // Validate if each row has either an item or a description
    if (this.hasInvalidRows()) {
      this.isSaving = false; // Reset saving state when showing error
      this.handleApiError('Each row must include either a selected item or a provided description.');
      return;
    }

    // If all rows are valid, proceed with the form submission
    this.isSaving = true;
    this.invoiceHead.invoiceStatus = this.invoiceStatus;
    this.checkForZeroQuantity(this.invoiceStatus);
  }

  hasInvalidRows(): boolean {
    return this.invoiceHead.invoiceDetails.some(detail =>
      (!detail.salesItem || !detail.salesItem.itemCode) && (!detail.description || detail.description.trim() === '')
    );
  }

  checkForZeroQuantity(status: string) {
    if (this.hasZeroQuantity()) {
      this.swalAlertsService.showWarning('Selected item(s) have zero quantity. Do you want to continue?', () => this.updateInvoice(status));
      return;
    }

    if (this.isEmptyQuotation(status)) {
      this.handleApiError('You must add at least one item.');
      return;
    }

    this.updateInvoice(status);
  }

  hasZeroQuantity(): boolean {
    return this.invoiceHead.invoiceDetails.some(detail => detail.quantity === 0);
  }

  isEmptyQuotation(status: string): boolean {
    return (status === 'Pending' || status === 'Draft') && this.invoiceHead.invoiceDetails.length === 0;
  }


  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(cust => cust.businessPartnerId === +selectedCustomerId);

    // Set the reference field
    this.invoiceHead.reference = selectedCustomer?.bpName || '';
  }

 
  onInvoiceDateChange() {
    this.updateValidityMinDate();
    this.validateDates();
  
    const entityId = +localStorage.getItem('entityId')!;
    const date = this.invoiceHead.postingDate;
  
    if (date) {
      this.periodClosingService.isDateLocked(entityId, date).subscribe({
        next: (isLocked: boolean) => {
          if (isLocked) {
            Swal.fire({
              icon: 'error',
              title: 'Posting Date is Locked',
              text: 'The selected date falls within a closed accounting period. Please choose another date.',
              confirmButtonColor: '#ff7e5f'
            });
  
            // Reset the posting date
              this.setTodayDate();
          }
        },
        error: (err) => {
          console.error('Error validating posting date lock', err);
        }
      });
    }
  }

  onValidUntilDateChange(): void {
    this.validateDates();
  }

  validateDates(): boolean {
    const postingDate = new Date(this.invoiceHead.postingDate);
    const dueDate = new Date(this.invoiceHead.dueDate);

    if (dueDate < postingDate) {
      Swal.fire({
        title: 'Invalid Date',
        text: 'The Due Date cannot be before the "Posting Date".',
        icon: 'warning',
        confirmButtonText: 'OK',
      });
      return false;
    }
    return true;
  }


  updateInvoice(status: string): void {
    this.invoiceHead.balanceAmount = this.invoiceHead.grandTotal;
    this.invoiceHead.invoiceStatus = status; // Set status based on parameter
    this.invoiceHead.userId = this.userId;
    this.invoiceHead.entityId = this.entityId;

    
    const firstName = localStorage.getItem('firstName');
    const lastName = localStorage.getItem('lastName');
    this.invoiceHead.createdBy = `${firstName} ${lastName}`;


    if (!this.validateDates()) return;

    const deletedItemIds = this.getDeletedItemIds();
    this.invoiceHead.deletedItemIds = deletedItemIds; // Send deleted items list

    this.invoiceService.updateInvoice(this.id, this.invoiceHead).subscribe(
      (response: any) => {
        this.updateBusinessEntityInvoiceNumber();

        this.swalAlertsService.showSuccessDialog('Success!', 'The Sales Invoice has been successfully updated.', () => {
          this.router.navigate([`/view-invoice/${this.id}`]);
        });

      },
      (error: HttpErrorResponse) => {

        this.swalAlertsService.showErrorDialog('Unable to Update the Invoice. Please try again');
      }
    );
  }





  updateBusinessEntityInvoiceNumber() {
    this.businessEntity.invoiceNumber = this.invoiceHead.invoiceNumber;
    this.entityService.updateInvoiceNumber(this.businessEntity, this.businessEntityId).subscribe(
      (data) => {

      },
      (error) => {
        console.error(error);
      }
    );
  }

  //item
  onItemAdded(savedItem: SalesItem) {
  this.fetchAllSalesItems(); 
  this.itemCode = savedItem.itemCode; 
}

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }


  handleApiError(errorMessage: string, error: any = null) {
    this.swalAlertsService.showErrorDialog(errorMessage);
  }

}
