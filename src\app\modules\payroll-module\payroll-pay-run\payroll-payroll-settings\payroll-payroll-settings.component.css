.account-settings {
  display: flex;
  flex-direction: column;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 32px 24px 24px 24px;
  margin-bottom: 24px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 16px rgba(66, 98, 255, 0.07);
}

.form-row {
  display: flex;
  flex-direction: row;
  gap: 24px;
  margin-bottom: 18px;
}

.form-group {
  display: flex;
  flex-direction: column;
  width: 50%;
  margin-bottom: 0;
}

label {
  font-weight: 600;
  margin-bottom: 8px;
  font-family: Inter, sans-serif;
  font-size: 15px;
  color: #333;
}

select,
.form-control {
  padding: 10px 12px;
  border: 1.5px solid #e0e4f7;
  border-radius: 8px;
  font-size: 15px;
  background: #f7f9fc;
  transition: border 0.2s;
}

select:focus,
.form-control:focus {
  border-color: #4262ff;
  outline: none;
}

.text-danger {
  color: #ff4d4f;
  font-size: 13px;
  margin-top: 4px;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.emp-open-footer-wrapper {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0 0 0;
  width: 100%;
}

.right-buttons {
  display: flex;
  gap: 16px;
}

.right-buttons button,
.btn-primary {
  min-width: 150px;
  height: 44px;
  font-weight: 600;
  font-size: 16px;
  border-radius: 10px;
  border: none;
  transition: background 0.2s, color 0.2s;
  background: linear-gradient(90deg, #4262ff 0%, #512ca2 100%);
  color: #fff;
  box-shadow: 0 2px 8px rgba(66, 98, 255, 0.07);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.right-buttons button:disabled,
.btn-primary:disabled {
  background: #e0e4f7;
  color: #aaa;
  cursor: not-allowed;
}

.right-buttons button:hover,
.btn-primary:hover {
  background: linear-gradient(90deg, #512ca2 0%, #4262ff 100%);
  color: #fff;
}

/* Payroll Settings Heading */
.actions {
  display: flex;
  align-items: center;
}

.actions h2 {
  flex: 1;
  font-family: Inter, sans-serif;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262FF;
  margin: 0;
}

/* Sub-container Styles for Heading */
.sub-container {
  background-color: #ffffff;
  padding: 20px;
  border: 1px solid #4262FF;
  border-radius: 10px;
  margin-bottom: 20px;
}

.sub-container h2 {
  font-size: 24px;
  font-weight: bold;
}

@media (max-width: 900px) {
  .form-row {
    flex-direction: column;
    gap: 16px;
  }
  .form-group {
    width: 100%;
  }
  .container {
    padding: 12px;
  }
  .account-settings {
    padding: 16px 8px 8px 8px;
  }
}

@media (max-width: 500px) {
  .form-row {
    flex-direction: column;
    gap: 16px;
  }
  .form-group {
    width: 100%;
  }
  .container {
    padding: 8px;
  }
  .account-settings {
    padding: 8px 4px 4px 4px;
  }
  .actions h2 {
    font-size: 28px;
  }
}
