<app-payroll-nevigation></app-payroll-nevigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <div class="actions sub-container">
    <h2>Single Touch Payroll</h2>

    <nav class="navbar modern-navbar">
      <div class="nav-items modern-nav-items">
        <ul class="nav nav-tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <a class="nav-link modern-nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" role="tab" aria-controls="overview" aria-selected="true">
              Overview
            </a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link modern-nav-link" id="finalization-tab" data-bs-toggle="tab" data-bs-target="#finalization" role="tab" aria-controls="finalization" aria-selected="false">
              Finalization
            </a>
          </li>
        </ul>
      </div>
    </nav>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">
    <!-- Overview Tab -->
    <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Calendar</th>
              <th>Period</th>
              <th>Payment Date</th>
              <th>Net Pay</th>
              <th>STP</th>
              <th>
                Status
                <span style="cursor: pointer; margin-left: 8px;" title="Refresh" (click)="refreshStatus()">🔄</span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="let payRunMasters of pagedPayRunList"
              (click)="viewDetails(payRunMasters.payPeriod.payCalendar.payCalendarId, payRunMasters.payPeriod.payPeriodId, payRunMasters.payRunId)"
            >
              <td>{{ payRunMasters.payPeriod.payCalendar.calendarName }}</td>
              <td>{{ payRunMasters.payPeriod.payStartDate}} {{payRunMasters.payPeriod.nextPayDate}}</td>
              <td>{{ payRunMasters.paymentDate }}</td>
              <td>{{ payRunMasters.netPay | number : "1.2-4" }}</td>
              <td>
                <button
                  *ngIf="payRunMasters.atoStatus !== 'ERROR'"
                  class="post"
                  [ngClass]="{
                    'btn-submit': filingStatus[payRunMasters.payRunId] !== 'filed' && payRunMasters.stpFilling !== 'Submitted',
                    'btn-submitted': filingStatus[payRunMasters.payRunId] === 'filed' || payRunMasters.stpFilling === 'Submitted',
                    'btn-submitting': filingStatus[payRunMasters.payRunId] === 'submitting'
                  }"
                  (click)="stpFile($event, payRunMasters.payPeriod.payCalendar.payCalendarId, payRunMasters.payPeriod.payPeriodId, payRunMasters.payRunId)"
                  [disabled]="
                    filingStatus[payRunMasters.payRunId] === 'submitting' ||
                    filingStatus[payRunMasters.payRunId] === 'filed' ||
                    payRunMasters.stpFilling === 'Submitted'
                  "
                  [ngStyle]="{
                    'background-color': filingStatus[payRunMasters.payRunId] === 'submitting' ? 'yellow' :
                                        (filingStatus[payRunMasters.payRunId] === 'filed' || payRunMasters.stpFilling === 'Submitted') ? 'lightgreen' : 'lightgray',
                    'color': '#000',
                    'padding': '4px 10px',
                    'border-radius': '15px',
                    'width': '130px',
                    'text-align': 'center',
                    'font-weight': '500',
                    'border': 'none',
                    'cursor': 'pointer'
                  }"
                >
                  {{
                    filingStatus[payRunMasters.payRunId] === 'submitting'
                      ? 'Submitting...'
                      : (filingStatus[payRunMasters.payRunId] === 'filed' || payRunMasters.stpFilling === 'Submitted')
                          ? 'Submitted'
                          : 'Submit'
                  }}
                </button>
                <button
                  *ngIf="(shouldShowResubmit(payRunMasters) || payRunMasters.atoStatus === 'ERROR') && filingStatus[payRunMasters.payRunId] !== 'submitting'"
                  class="post"
                  [ngClass]="{
                    'btn-submit': filingStatus[payRunMasters.payRunId] !== 'filed' && payRunMasters.stpFilling !== 'Submitted',
                    'btn-submitted': filingStatus[payRunMasters.payRunId] === 'filed' || payRunMasters.stpFilling === 'Submitted',
                    'btn-submitting': filingStatus[payRunMasters.payRunId] === 'submitting'
                  }"
                  [ngStyle]="{
                    'margin-left': '10px',
                    'background-color': filingStatus[payRunMasters.payRunId] === 'submitting' ? 'yellow' :
                                        (payRunMasters.stpFilling === 'Submitted') ? 'lightgreen' : 'orange',
                    'color': '#000',
                    'padding': '4px 10px',
                    'border-radius': '15px',
                    'width': '130px',
                    'text-align': 'center',
                    'font-weight': '500',
                    'border': 'none',
                    'cursor': 'pointer'
                  }"
                  (click)="stpFile($event, payRunMasters.payPeriod.payCalendar.payCalendarId, payRunMasters.payPeriod.payPeriodId, payRunMasters.payRunId)"
                  title="Resubmit"
                >
                  <i class="fas fa-redo"></i> Resubmit
                </button>
              </td>
              <td>
                <span [ngStyle]="{
                  'background-color': payRunMasters.atoStatus === 'ERROR' ? 'red' :
                                      payRunMasters.atoStatus === 'DELIVERED' ? 'lightgreen' :
                                      payRunMasters.atoStatus === 'PENDING' ? 'yellow' :
                                      payRunMasters.atoStatus === 'Not Found' ? 'orange' :
                                      payRunMasters.atoStatus === 'DELIVERY_NOT_SCHEDULED' ? 'lightgray' : '',
                  'color': '#000',
                  'padding': '4px 10px',
                  'border-radius': '15px',
                  'display': 'inline-block',
                  'width': '130px',
                  'text-align': 'center',
                  'font-weight': '500'
                }">
                  {{ payRunMasters.atoStatus === 'DELIVERY_NOT_SCHEDULED' ? 'PROCESSING' : payRunMasters.atoStatus }}
                </span>
                <button *ngIf="payRunMasters.atoStatus === 'ERROR'"
                      class="btn btn-sm btn-outline-warning gap-2"
                      style="margin-left: 10px;"
                      (click)="viewErrorDetails($event, payRunMasters.atoMessageId)" title="View">
                <i class="fas fa-eye"></i> 
              </button>  
              <div class="modal fade" id="errorDetailsModal" tabindex="-1" aria-labelledby="errorDetailsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                  <div class="modal-content">
                    <div class="modal-header">
                      <h5 class="modal-title" id="errorDetailsModalLabel">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        Error Details
                      </h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                      <div *ngIf="isLoadingError" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                          <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading error details...</p>
                      </div>
                      
                      <div *ngIf="!isLoadingError && errorDetails" class="error-content">
                        <div class="alert alert-danger" role="alert">
                          <h6 class="alert-heading">Error Message:</h6>
                          <pre class="mb-0">{{ errorDetails }}</pre>
                        </div>
                        
                        <div class="mt-3">
                          <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Message ID: {{ currentMessageId }}
                          </small>
                        </div>
                      </div>
                      
                      <div *ngIf="!isLoadingError && errorLoadFailed" class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load error details. Please try again.
                      </div>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Close
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              </td>
            </tr> 
          </tbody>
        </table>
        <div class="d-flex justify-content-between align-items-center mt-3">
          <div>
            Page {{ currentPage + 1 }} of {{ totalPages }}
          </div>
          <div>
            <nav>
              <ul class="pagination pagination-sm mb-0">
                <li class="page-item" [class.disabled]="currentPage === 0">
                  <a class="page-link" (click)="prevPage()">Previous</a>
                </li>

                <li
                  class="page-item"
                  *ngFor="let page of visiblePages"
                  [class.active]="page === currentPage"
                >
                  <a class="page-link" (click)="goToPage(page)">{{ page + 1 }}</a>
                </li>

                <li class="page-item" [class.disabled]="currentPage + 1 >= totalPages">
                  <a class="page-link" (click)="nextPage()">Next</a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- Finalization Tab -->
    <div class="tab-pane fade" id="finalization" role="tabpanel" aria-labelledby="finalization-tab">
    <div class="Card">
  <div class="row1">
    <div class="row1_col">
      <label for="fromYear">Start Year</label>
      <select id="fromYear" [(ngModel)]="fromYear" class="year-select form-control" (change)="onStartYearChange()">
        <option [ngValue]="null">Select Year</option>
        <option *ngFor="let year of availableYears" [ngValue]="year">{{ year }}</option>
      </select>

    </div>

    <div class="row1_col">
      <label for="toYear">End Year</label>
      <input type="text" id="toYear" [value]="toYear || 'Auto calculated'" class="year-input form-control" readonly>
      <div *ngIf="toYearWarning" style="color: red; font-size: 12px;">{{ toYearWarning }}</div>
    </div>

    <div class="row1_col">
      <button type="button" class="btn btn-primary" [disabled]="isSearchDisabled()" (click)="searchByYear()">
        <i class="fas fa-search"></i> Search
      </button>
    </div>

    <div class="row1_col">
      <button type="button" class="btn btn-secondary" (click)="resetSearch()">
        <i class="fas fa-refresh"></i> Reset
      </button>
    </div>
  </div>
</div>



      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th scope="col" class="valueCheckbox">
                <input type="checkbox" [checked]="isAllSelected" (change)="selectAll($event)" />
              </th>
              <th>Name</th>
              <th>Employment Status</th>
              <th>Financial Year</th>
              <!-- <th>Status</th> -->
              <th style="padding-right: 16px; padding-left: 24px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>STP Status</span>
                <button 
                  type="button" 
                  class="btn btn-sm btn-link p-0" 
                  title="Refresh" 
                  (click)="refreshStatus()"
                >
                  🔄
                </button>
              </div>
            </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let payProcesse of payProcesses">
              <td class="valueCheckbox">
                <!-- <ng-container *ngIf="payProcesse.status === 'PENDING'"> -->
                  <input
                    type="checkbox"
                    [checked]="selectedPayProcessDTO.has(payProcesse)"
                    (change)="toggleSelection(payProcesse, $event)" />
                <!-- </ng-container> -->
              </td>
              <td>{{ payProcesse.firstName }} {{ payProcesse.lastName }}</td>
              <td>{{ payProcesse.inactiveFrom }}</td>
              <td>{{ payProcesse.finalSTPStatus }}</td>
              <!-- <td>
                <span [ngStyle]="{
                  'background-color': payProcesse.status === 'PENDING' ? 'yellow' :
                                      payProcesse.status === 'SUBMITTED' ? 'lightgreen' :
                                      payProcesse.status === 'DELIVERED' ? 'lightblue' : 'lightgray',
                  'color': '#000',
                  'padding': '4px 10px',
                  'border-radius': '15px',
                  'display': 'inline-block',
                  'width': '130px',
                  'text-align': 'center',
                  'font-weight': '500'
                }">
                  {{ payProcesse.status }}
                </span>
              </td> -->
              <td style="padding-right: 16px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                
                <!-- Pill status -->
                <div [ngStyle]="{
                  'background-color': payProcesse.finalSTPStatus === 'PENDING' ? 'yellow' :
                                      payProcesse.finalSTPStatus === 'PROCESSED' ? 'lightgreen' :
                                      payProcesse.finalSTPStatus === 'PROCESSING' ? 'lightblue' :
                                      payProcesse.finalSTPStatus === 'CANCELED' ? 'lightgray' :
                                      payProcesse.finalSTPStatus === 'ERROR' ? 'red' : 'inherit',
                  'padding': '4px 10px',
                  'color': '#000',
                  'border-radius': '15px',
                  'font-weight': '500',
                  'min-width': '130px',
                  'text-align': 'center'
                }">
                  {{ payProcesse.finalSTPStatus || '-' }}
                </div>
              </div>
            </td>
            </tr>
          </tbody>
        </table>
        <div class="text-end">
          <button
            class="btn btn-primary"
            type="button"
            [disabled]="isSubmitting"
            (click)="submitSuperannuations()"
          >
            {{ isSubmitting ? 'Submitting...' : 'Submit Payment' }}
          </button>
        </div>
        <div class="d-flex justify-content-between align-items-center mt-3">
          <div>
            Page {{ currentPage + 1 }} of {{ totalPages }}
          </div>
          <div>
            <nav>
              <ul class="pagination pagination-sm mb-0">
                <li class="page-item" [class.disabled]="currentPage === 0">
                  <a class="page-link" (click)="prevPage()">Previous</a>
                </li>

                <li
                  class="page-item"
                  *ngFor="let page of visiblePages"
                  [class.active]="page === currentPage"
                >
                  <a class="page-link" (click)="goToPage(page)">{{ page + 1 }}</a>
                </li>

                <li class="page-item" [class.disabled]="currentPage + 1 >= totalPages">
                  <a class="page-link" (click)="nextPage()">Next</a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>