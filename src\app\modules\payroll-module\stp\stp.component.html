<app-payroll-nevigation></app-payroll-nevigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <div class="actions sub-container">
    <h2>Single Touch Payroll</h2>
  </div>
 <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Calendar</th>
              <th>Period</th>
              <th>Payment Date</th>
              <th>Net Pay</th>
              <th>STP</th>
              <th>
                Status
                <span style="cursor: pointer; margin-left: 8px;"  title="Refresh" (click)="refreshStatus()">🔄</span>
              </th>

            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let payRunMasters of pagedPayRunList
              "
              (click)="viewDetails(payRunMasters.payPeriod.payCalendar.payCalendarId, payRunMasters.payPeriod.payPeriodId, payRunMasters.payRunId)"
            >
            <td>{{ payRunMasters.payPeriod.payCalendar.calendarName }}</td>
            <td>{{ payRunMasters.payPeriod.payStartDate}} {{payRunMasters.payPeriod.nextPayDate}}</td>
            <td>{{ payRunMasters.paymentDate }}</td>
            <td>{{ payRunMasters.netPay | number : "1.2-4" }}</td>
            <td>
              <button
                class="post"
                [ngClass]="{
                  'btn-submit': filingStatus[payRunMasters.payRunId] !== 'filed' && payRunMasters.stpFilling !== 'Submitted',
                  'btn-submitted': filingStatus[payRunMasters.payRunId] === 'filed' || payRunMasters.stpFilling === 'Submitted',
                  'btn-submitting': filingStatus[payRunMasters.payRunId] === 'submitting'
                }"
                (click)="stpFile($event, payRunMasters.payPeriod.payCalendar.payCalendarId, payRunMasters.payPeriod.payPeriodId, payRunMasters.payRunId)"
                [disabled]="
                  filingStatus[payRunMasters.payRunId] === 'submitting' || 
                  filingStatus[payRunMasters.payRunId] === 'filed' || 
                  payRunMasters.stpFilling === 'Submitted'
                "
                [ngStyle]="{
                  'background-color': filingStatus[payRunMasters.payRunId] === 'submitting' ? 'yellow' :
                                      (filingStatus[payRunMasters.payRunId] === 'filed' || payRunMasters.stpFilling === 'Submitted') ? 'lightgreen' : 'lightgray',
                  'color': '#000',
                  'padding': '4px 10px',
                  'border-radius': '15px',
                  'width': '130px',
                  'text-align': 'center',
                  'font-weight': '500',
                  'border': 'none',
                  'cursor': 'pointer'
                }"
              >
                {{
                  filingStatus[payRunMasters.payRunId] === 'submitting'
                    ? 'Submitting...'
                    : (filingStatus[payRunMasters.payRunId] === 'filed' || payRunMasters.stpFilling === 'Submitted'
                        ? 'Submitted' 
                        : 'Submit')
                }}
              </button>
            </td>
           <td>
            <span [ngStyle]="{
              'background-color': payRunMasters.atoStatus === 'ERROR' ? 'red' :
                                  payRunMasters.atoStatus === 'DELIVERED' ? 'lightgreen' :
                                  payRunMasters.atoStatus === 'DELIVERY_NOT_SCHEDULED' ? 'lightgray' : '',
              'color': '#000',
              'padding': '4px 10px',
              'border-radius': '15px',
              'display': 'inline-block',
              'width': '130px',
              'text-align': 'center',
              'font-weight': '500'
            }">
              {{ payRunMasters.atoStatus }}
            </span>
          </td>
            </tr> 
          </tbody>
        </table>
        <div class="d-flex justify-content-between align-items-center mt-3">
          <div>
            Page {{ currentPage + 1 }} of {{ totalPages }}
          </div>
          <div>
            <nav>
              <ul class="pagination pagination-sm mb-0">
                <li class="page-item" [class.disabled]="currentPage === 0">
                  <a class="page-link" (click)="prevPage()">Previous</a>
                </li>

                <li
                  class="page-item"
                  *ngFor="let page of visiblePages"
                  [class.active]="page === currentPage"
                >
                  <a class="page-link" (click)="goToPage(page)">{{ page + 1 }}</a>
                </li>

                <li class="page-item" [class.disabled]="currentPage + 1 >= totalPages">
                  <a class="page-link" (click)="nextPage()">Next</a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
      </div>
    
   
