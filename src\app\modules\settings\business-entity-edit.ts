export class BusinessEntityEdit {
}


export interface BusinessEntity {
  entityId: number;
  userId: number;
  industryId: any;
  countryId: any;
  businessPartnerTypeId: any;
  itemTypeId: any;
  abn: string;
  entityName: string;
  gstRegistrationNumber: string;
  gstReturnFrequency: string;
  defaultCreditPeriod: string;
  email: string;
  businessStructure: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  enableSubscriptionPayment: string;
  uniqueEntityCode: string;
  businessAddress: string;
  industryClassification: string;
  profitAndLossTemplate: string;
  invoiceNumber: string;
  taxApplicability: string;
  quoteNumber: string;
  creditNoteNumber: string;
  paymentReceiptNumber: string;
  tradingName: string ;
}
export class EntityTradingName {
  entityTradingNameId: number = 0;
  tradingName: string = '';
  logo: string = '';
  entityId: Entity = new Entity();
}

export class Entity {
  entityId: number = 0;
  userId: number = 0;
  abn: string = '';
  entityName: string = '';
  gstRegistrationNumber: string = '';
  gstReturnFrequency: string = '';
  defaultCreditPeriod: string = '';
  email: string = '';
  businessStructure: string = '';
  city: string = '';
  state: string = '';
  postalCode: string = '';
  enableSubscriptionPayment: string = '';
  uniqueEntityCode: string = '';
  businessAddress: string = '';
  industryClassification: string = '';
  profitAndLossTemplate: string = '';
  invoiceNumber: string = '';
  taxApplicability: string = '';
  quoteNumber: string = '';
  creditNoteNumber: string = '';
  paymentReceiptNumber: string = '';
}

export class EmailTemplate {
    emailTemplateId: number = 0;
    templateName: string = '';
    subject: string = '';
    content: string = '';
}

