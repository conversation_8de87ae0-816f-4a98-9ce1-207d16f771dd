import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { BusinessPartnerService } from '../business-partner.service';
import { BusinessPartner, BusinessPartnerType } from '../business-partner';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';

@Component({
  selector: 'app-list-business-partner',
  templateUrl: './list-business-partner.component.html',
  styleUrls: ['./list-business-partner.component.css']
})
export class ListBusinessPartnerComponent implements OnInit{
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  

  businessPartner: BusinessPartner [] =[];

  constructor(private businessPartnerService : BusinessPartnerService , private router: Router) {}

  ngOnInit(): void {
    this.getBusinessPartnersListByEntity();
  }
  getBusinessPartners(): void {
    this.businessPartnerService.getBusinessPartnerList().subscribe(
      (data: BusinessPartner[]) => {
        this.businessPartner = data;
      },
      (error) => {
        console.error('Error fetching countries:', error);
      }
    );
  }

  private getBusinessPartnersListByEntity(): void {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.businessPartnerService.getBusinessPartnersListByEntity(entityId).subscribe(data => {
      this.businessPartner = data;
     
    });
  }

  createNewBusinessPartner() {
    this.router.navigate(['/create-business-partner']);
  }

  private getBusinessPartnerByIds(id: number): void {
    this.businessPartnerService.getBusinessPartnerById(id).subscribe(
      (data: BusinessPartner) => {
        this.businessPartner = [data]; 
      },
      (error) => {
        console.error('Error fetching Business Partner by ID:', error);
        Swal.fire({
          title: 'Error!',
          text: `Business Partner with ID ${id} could not be found.`,
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }
  
  deleteBusinessPartner(id: number): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you really want to delete this Business Partner?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (result.isConfirmed) {
        this.businessPartnerService.deleteBusinessPartner(id)
          .subscribe(
            () => {
              Swal.fire({
                title: 'Deleted!',
                text: 'Business Partner deleted successfully.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
              }).then(() => {
                window.location.reload();
              });
            },
            (error) => {
              console.error('Failed to delete Business Partner!', error);
  
              Swal.fire({
                title: 'Error!',
                text: 'Failed to delete Business Partner!',
                icon: 'error',
                confirmButtonText: 'OK',
                cancelButtonText: 'Ask Chimp',
                confirmButtonColor: '#be0032',
                cancelButtonColor: '#007bff',
                showCancelButton: true, 
              }).then((result) => {
                if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                  if (this.chatBotComponent) {
                    Swal.fire({
                      title: 'Processing...',
                      text: 'Please wait while Chimp processes your request.',
                      allowOutsideClick: false,
                      didOpen: () => {
                        Swal.showLoading();
                        this.chatBotComponent.setInputData('Failed to delete Business Partner!');
                        this.chatBotComponent.responseReceived.subscribe(response => {
                          Swal.close();
                          this.chatResponseComponent.showPopup = true;
                          this.chatResponseComponent.responseData = response;
                          this.playLoadingSound();
                          this.stopLoadingSound() 
                        });
                      },
                    });
                  } else {
                    console.error('ChatBotComponent is not available.');
                  }
                }
              });
            }
          );
      }
    });
  }
  updateBusinessPartner(id: number): void {
    this.router.navigate(['/update-business-Partner', id]);
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }

 
} 
 

