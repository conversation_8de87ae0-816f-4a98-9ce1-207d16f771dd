<app-admin-navigation></app-admin-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
    <div class="actions">
        <h1>Country Settings</h1>
    </div>
    <div>
        <table>
            <thead>
              <tr class="table-head">
                <th scope="col" style="width: 100px; text-align: center;" class="valuehead">Country</th>
                <th scope="col" style="width: 100px; text-align: center;" class="valuehead">Currency</th>
                <th scope="col" style="width: 100px; text-align: center;" class="valuehead">Currency Symbol</th>
                <th scope="col" style="width: 100px; text-align: center;" class="valuehead">Tax Rate (%)</th>
                <th scope="col" style="width: 100px; text-align: center;" class="valuehead">Tax Type</th>
                <th scope="col" style="width: 100px; text-align: center;" class="valuehead">Financial Year Start</th>
                <th scope="col" style="width: 100px; text-align: center;" class="valuehead">Financial Year End</th>
                <th scope="col" style="width: 100px; text-align: center;" class="valuehead">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let country of filteredCountry">
                <td style="width: 100px; text-align: center;" class="value ">{{ country.countryName }}</td>
                <td style="width: 100px; text-align: center;" class="value ">{{ country.defaultCurrency }}</td>
                <td style="width: 100px; text-align: center;" class="value ">{{ country.currencySymbol }}</td>
                <td style="width: 100px; text-align: center;" class="value ">
                  {{ country.defaultTaxRate ? country.defaultTaxRate : '' }}
                </td>
                <td style="width: 100px; text-align: center;" class="value ">{{ country.defaultTaxType }}</td>
                <td style="width: 100px; text-align: center;" class="value ">{{ country.financialYearStart }}</td>
                <td style="width: 100px; text-align: center;" class="value ">{{ country.financialYearEnd }}</td>
                <td style="width: 100px; text-align: center;" class="value">
                  <button (click)="updateCountry(country.countryId)" class="btn btn-orange btn-sm" style="margin-right: 4px; border: none; background: none; padding: 2px; font-size: 1.2rem;"
                    title="Edit">
                    <i class="ri-edit-box-line" style="color: #4262FF;"></i>
                  </button>
                  <button class="btn btn-danger btn-sm" 
                          (click)="deleteCountry(country.countryId)" 
                          style="margin-right: 2px; border: none; background: none; padding: 4px; font-size: 1.2rem;"
                          title="Delete">
                    <i class="ri-delete-bin-line" style="color: #FF0000;"></i>
                  </button>
                </td>
              </tr>
            </tbody>    
          </table>            
    </div>
</div>
