* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Inter;
}

form {
  max-width: 450px;
  margin: 60px auto;
  padding: 25px;
  border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e6e6e6;
}

label {
  display: block;
  font-size: 1rem;
  margin-bottom: 8px;
  color: #222222;
  font-weight: 500;
}

input {
  width: 100%;
  padding: 0.6rem;
  font-size: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 1.2rem;
}

input:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.2);
}

#card-element {
  padding: 12px;
  border: 1px solid #cccccc;
  border-radius: 8px;
  margin-bottom: 1.2rem;
  background-color: #f9f9f9;
}

.StripeElement {
  height: 45px;
  padding: 12px;
  border-radius: 8px;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  transition: box-shadow 0.3s ease, border-color 0.3s ease;
}

.StripeElement--focus {
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.2);
  border-color: #007bff;
}

.StripeElement--invalid {
  border-color: #e3342f;
}

.StripeElement--webkit-autofill {
  background-color: #fefde5 !important;
}

.error {
  color: #e3342f;
  margin-top: 1rem;
  font-size: 0.875rem;
}

button {
  width: 100%;
  padding: 14px;
  font-size: 1rem;
  color: #ffffff;
  background: linear-gradient(to right, #4262ff, #512ca2);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

button:not(:disabled):hover {
  background: linear-gradient(to left, #4262ff, #512ca2);
}
