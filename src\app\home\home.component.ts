import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SubscriptionService } from '../modules/subscription/subscription.service';
import { HomeService } from './service/home.service';
import { SubscriptionFee } from '../modules/subscription/subscription';
import { ViewportScroller } from '@angular/common';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css'],
})
export class HomeComponent implements OnInit {
  subscriptionPlans: SubscriptionFee[] = [];
  selectedPlan: any = null;
  isPurchasing: number | null = null; // holds subscriptionFeeId of the button being processed

  constructor(
    private router: Router,
    private subscriptionPlanService: SubscriptionService,
    private viewportScroller: ViewportScroller
  ) {}

  ngOnInit(): void {
    this.clearLocalStorageIfTokenExpired();
    this.loadSubscriptionPlanList();
  }

  selectPlan(plan: any): void {
    this.selectedPlan = plan;
  }

  isSelected(plan: any): boolean {
    return this.selectedPlan === plan;
  }

  getPlanName(plan: any): string {
    return plan.subscriptionPlanId.subscriptionPlan.toString().split(' ')[0];
  }

  private clearLocalStorageIfTokenExpired(): void {
    if (this.isTokenExpired()) {
      localStorage.clear();
    }
  }

  private isTokenExpired(): boolean {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return true; // No token means it's expired
    }

    const payload = JSON.parse(atob(token.split('.')[1]));
    const expiry = payload.exp * 1000;
    return Date.now() >= expiry;
  }

  private loadSubscriptionPlanList(): void {
    this.subscriptionPlanService.getAllSubscriptionFee().subscribe(
      (data) => {
        this.subscriptionPlans = data;

        // ✅ Select Free plan after plans are loaded
        this.selectedPlan = this.subscriptionPlans.find(
          (plan) => this.getPlanName(plan) === 'Free'
        );
      },
      (error) => {
        console.error('Error loading subscription plans:', error);
      }
    );
  }

  getDisplayPlanName(planName: string): string {
    switch (planName) {
      case 'Free':
        return 'Free';
      case 'Invoicer':
        return 'Invoicer';
      case 'Basic':
        return 'Basic';
      case 'Premium':
        return 'Premium';
      case 'Payroll Only':
        return 'Payroll Only';
      default:
        return planName;
    }
  }

  navigateUserRegistration(subscriptionPlanId: number): void {
    if (this.isPurchasing !== null) return; // Prevent multiple clicks

    this.isPurchasing = subscriptionPlanId; // Set the button being processed

    // Allow Angular time to disable the button visually before routing
    setTimeout(() => {
      this.router.navigate(['/user-agreement'], {
        queryParams: { 
          subscriptionPlanId,
          planName: this.getPlanName(this.selectedPlan)
        },
      });
    }, 100); // small delay to reflect the button change

    // this.router.navigate(['/user-agreement'], {
    //   queryParams: { subscriptionPlanId },
    // });
  }

  navigateUserLogin(): void {
    this.router.navigate(['/user-login']);
  }

  navigateContactUs(): void {
    this.router.navigate(['/contact-us']);
  }

  scrollToPricing() {
    if (this.router.url === '/home') {
      // Already on home page – scroll directly
      setTimeout(() => {
        this.viewportScroller.scrollToAnchor('pricing');
      }, 0);
    } else {
      // Navigate to home and scroll after navigation
      this.router.navigate(['/home'], { fragment: 'pricing' }).then(() => {
        setTimeout(() => {
          this.viewportScroller.scrollToAnchor('pricing');
        }, 300); // wait for view to render
      });
    }
  }

  // Plans

  features = {
    features: [
      'Send Quote',
      'Send Invoice',
      'Sales Reports',
      'Invoice Auto Reminders',
      'Collect payments',
      'Connected bank accounts',
      'Bank Reconciliation',
      'Track & calculate GST/BAS',
      'Financial Reports',
      'Connect to Accountant/Bookkeeper',
      'Payroll',
      'Single Touch Payroll reporting',
      'Pay superannuation',
      'Leave calculation',
      'Support',
    ],
  };

  plandetails : {
  plans: { [key: string]: string[] }
} = {
    plans: {
      Free: [
        'Up to 5 a month',
        'Up to 5 a month',
        '✔',
        '✔',
        '—',
        '—',
        '—',
        '—',
        '—',
        '—',
        '—',
        '—',
        '—',
        '—',
        '—',
      ],
      Invoicer: [
        '✔',
        '✔',
        '✔',
        '✔',
        '✔',
        '—',
        '—',
        '—',
        '—',
        '✔',
        '—',
        '—',
        '—',
        '—',
        '✔',
      ],
      Basic: [
        '✔',
        '✔',
        '✔',
        '✔',
        '✔',
        'Up to 2',
        '✔',
        '✔',
        '✔',
        '✔',
        '—',
        '—',
        '—',
        '—',
        '✔',
      ],
      Premium: [
        '✔',
        '✔',
        '✔',
        '✔',
        '✔',
        '✔',
        '✔',
        '✔',
        '✔',
        '✔',
        'Up to 5 Employees *',
        'Up to 5 Employees *',
        'Up to 5 Employees *',
        'Up to 5 Employees *',
        '✔',
      ],
      Payroll: [
        '—',
        '—',
        '—',
        '—',
        '—',
        '—',
        '—',
        '—',
        '—',
        '✔',
        'Up to 5 Employees *',
        'Up to 5 Employees *',
        'Up to 5 Employees *',
        'Up to 5 Employees *',
        '✔',
      ],
    },
  };

  getPlanDetails(plan: any): string[] {
    const planName = this.getPlanName(plan);
    return this.plandetails.plans[planName] || [];
  }

  // Plans
}
