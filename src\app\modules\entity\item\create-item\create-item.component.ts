import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ItemService } from '../item.service';
import { Item } from '../item';
import { NgForm } from '@angular/forms';
import Swal from 'sweetalert2';
import { EntityService } from '../../entity.service';
import { Entity } from '../../entity';

@Component({
  selector: 'app-create-item',
  templateUrl: './create-item.component.html',
  styleUrls: ['./create-item.component.css'],
})
export class CreateItemComponent {
  items: Item[] = [];
  item: Item = new Item();
  showTaxApplicabilityDropdown: boolean = true;

  constructor(
    private router: Router,
    private itemService: ItemService,
    private entityService: EntityService
  ) {}

  ngOnInit() {
    const entityId = +(localStorage.getItem('entityId') || '');

    if (entityId) {
      this.entityService.getBusinessEntityById(entityId).subscribe(
        (entity: Entity) => {
          if (entity.taxApplicability === 'no') {
            this.showTaxApplicabilityDropdown = false;
            this.item.taxApplicability = 'no';
          } else if (entity.taxApplicability === 'yes') {
            this.showTaxApplicabilityDropdown = true;
          }
        },
        (error) => {
          console.error('Error fetching entity:', error);
          this.showTaxApplicabilityDropdown = false;
          this.item.taxApplicability = 'no';
        }
      );
    } else {
      this.showTaxApplicabilityDropdown = false;
      this.item.taxApplicability = 'no';
    }
  }

  onSubmit(f: NgForm): void {
    if (f.valid) {
      this.setItemDetails();
      this.itemService.createItem(this.item).subscribe(
        () => this.showSuccessAlert(),
        (error) => {
          console.error('Error creating item:', error);
          this.showErrorAlert();
        }
      );
    }
  }

  setItemDetails(): void {
    this.item.userId = Number(localStorage.getItem('userid'));
    this.item.entityId = Number(localStorage.getItem('entityId'));
  }

  showSuccessAlert(): void {
    Swal.fire({
      title: 'Success!',
      text: 'Item created successfully.',
      icon: 'success',
      confirmButtonText: 'OK',
      confirmButtonColor: '#28a745',
    }).then((result) => {
      if (result.isConfirmed) {
        this.router.navigate(['/item']);
      }
    });
  }

  showErrorAlert(): void {
    Swal.fire({
      title: 'Error!',
      text: 'There was an issue creating the item.',
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#be0032',
    });
  }

  showValidationError(): void {
    Swal.fire({
      title: 'Validation Error',
      text: 'Please fill in all required fields correctly.',
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f',
    });
  }

  validateUnitPrice(): void {
    this.item.unitPriceInvalid = this.item.unitPrice < 0;
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  confirmCancel(): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Unsaved changes will be lost if you proceed.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, cancel',
      cancelButtonText: 'No, stay',
      confirmButtonColor: '#be0032',
      cancelButtonColor: '#007bff',
    }).then((result) => {
      if (result.isConfirmed) {
        this.navigateToItemList();
      }
    });
  }

  navigateToItemList(): void {
    this.router.navigate(['/item']);
  }
}

