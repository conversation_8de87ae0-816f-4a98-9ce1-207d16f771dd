<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div id="invoice-container">
  <div class="container">
    <form #f="ngForm" class="row g-1" novalidate="feedback-form">
      <div class="heading">
        <h3>
          Invoice -
          <span
            [ngClass]="{
              'text-danger': quotationData.invoiceStatus === 'Overdue',
              'text-success': quotationData.invoiceStatus === 'Paid'
            }"
          >
            {{ quotationData.invoiceStatus }}
          </span>
        </h3>

        <div class="d-flex align-items-center gap-2"
        *ngIf="quotationData.invoiceStatus !== 'Revised'">
          <!-- Send Button -->
          <button
            type="button"
            class="button-send"
            (click)="loadEmailTemplate()"
            data-bs-target="#sendInvoiceModal"
            data-bs-toggle="modal"
          >
            <i class="bi bi-envelope-fill"></i> Send
          </button>
          <div class="btn-group"  #dropdownRef [class.show]="isDropdownOpen">
            <button
              type="button"
              class="btn btn-secondary dropdown-toggle gradient-btn"
              data-bs-toggle="dropdown"
              aria-expanded="false"
              (click)="toggleDropdown()"
            >
              <i class="bi bi-three-dots-vertical"></i>
            </button>
            <ul
              class="dropdown-menu dropdown-menu-end"
              [class.show]="isDropdownOpen"
            >
              <!--<li>
                <a
                  class="dropdown-item"
                  data-bs-target="#sendInvoiceModal"
                  data-bs-toggle="modal"
                  (click)="loadEmailTemplate()"
                  >Send</a
                >
              </li>-->
              <li>
                <a
                  class="dropdown-item"
                  (click)="handleReviseInvoice(quotationData.invoiceHeadId)"
                  >Revise</a
                >
              </li>
              <li>
                <a
                  class="dropdown-item"
                  (click)="editInvoice(quotationData.invoiceHeadId)"
                  >Edit</a
                >
              </li>
              <li>
                <a class="dropdown-item" (click)="handleCreateInvoice()"
                  >Create Invoice</a
                >
              </li>

              <li>
                <a
                  class="dropdown-item"
                  (click)="onCreateCreditNote(quotationData.invoiceHeadId)"
                  >Credit Note</a
                >
              </li>
              <li *ngIf="userRole !== 'Free'">
                <a
                  class="dropdown-item"
                  (click)="onPaymentreceipt(quotationData.invoiceHeadId)"
                  >Payment</a
                >
              </li>

              <li>
                <a
                  class="dropdown-item"
                  (click)="previewInvoice(quotationData.invoiceHeadId)"
                  data-bs-target="#invoicePreviewModal"
                  data-bs-toggle="modal"
                >
                  Print
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div class="bd">
        <div class="form-section">
          <div
            class="form-row"
            style="
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 20px;
            "
          >
            <!-- Invoice Number -->
            <div class="form-group" style="display: flex; flex-grow: 1">
              <label
                for="invoiceNo"
                id="invoiceNumber"
                style="margin-right: 10px; white-space: nowrap"
              >
                Invoice Number
              </label>
              <input
                class="input-style"
                type="text"
                id="invoiceNo"
                [(ngModel)]="quotationData.invoiceNumber"
                name="invoiceNumber"
                #invoiceNumber="ngModel"
                readonly
              />
            </div>

            <!-- posting date -->
            <div class="form-group" style="display: flex; flex-grow: 1">
              <label
                for="postingDate"
                style="margin-right: 25px; white-space: nowrap"
              >
                Posting Date
              </label>
              <input
                class="input-style"
                type="date"
                id="postingDate"
                [(ngModel)]="quotationData.postingDate"
                name="postingDate"
                readonly
              />
            </div>
          </div>

          <!-- Second row: Customer and Quotation Date -->
          <div
            class="form-row"
            style="display: flex; justify-content: space-between"
          >
            <!-- Customer -->
            <div class="form-group" style="flex-grow: 1">
              <div style="display: flex" class="form-group">
                <label for="customer" style="margin-right: 50px">
                  Customer
                </label>
                <select
                  class="form-select"
                  id="customer"
                  [(ngModel)]="quotationData.businessPartnerId"
                  name="customerName"
                  disabled
                >
                  <option value="" selected disabled>Select Customer</option>
                  <option
                    *ngFor="let customer of customers"
                    [value]="customer.businessPartnerId"
                  >
                    {{ customer.bpName }}
                  </option>
                </select>
              </div>
            </div>

            <!-- due Date -->
            <div class="form-group" style="display: flex; flex-grow: 1">
              <label
                for="dueDate"
                style="margin-right: 45px; white-space: nowrap"
              >
                Due Date
              </label>
              <input
                class="input-style"
                type="date"
                id="dueDate"
                [(ngModel)]="quotationData.dueDate"
                name="dueDate"
                readonly
              />
            </div>
          </div>

          <div
            class="form-row"
            style="display: flex; justify-content: space-between"
          >
            <!-- Trading Name -->
            <div class="form-group" style="flex-grow: 1">
              <div style="display: flex" class="form-group">
                <label
                  for="tradingName"
                  style="margin-right: 23px; white-space: nowrap"
                >
                  Trading Name
                </label>
                <select
                  class="form-select"
                  id="tradingName"
                  [(ngModel)]="quotationData.entityTradingNameId"
                  name="tradingName"
                  [disabled]="entityTradingNames.length <= 1"
                  readonly
                >
                  <option value="" selected disabled>
                    Select Trading Name
                  </option>
                  <option
                    *ngFor="let tradingName of entityTradingNames"
                    [value]="tradingName.entityTradingNameId"
                  >
                    {{ tradingName.tradingName }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Uploaded document -->
            <div class="form-group" style="display: flex; flex-grow: 1">
              <label
                for="document"
                style="margin-right: 45px; white-space: nowrap"
                >Uploaded Document</label
              >
              <div class="col-12">
                <ng-container *ngIf="uploadedFileName">
                  <div *ngIf="url; else fileNameOnly">
                    <img
                      [src]="url"
                      alt="Uploaded Invoice Document"
                      class="img-thumbnail"
                      style="max-height: 200px; object-fit: contain"
                    />
                  </div>
                  <ng-template #fileNameOnly>
                    <p class="mb-0">
                      <i class="bi bi-file-earmark-text"></i>
                      {{ uploadedFileName }}
                    </p>
                  </ng-template>
                </ng-container>
                <div *ngIf="!uploadedFileName" class="text-muted">
                  No file uploaded
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="table-section">
          <div class="table-responsive">
            <table class="table">
              <thead>
                <tr>
                  <th style="width: 35%">Item</th>
                  <th style="width: 25%">Description</th>
                  <th style="width: 7%">Quantity</th>
                  <th style="width: 17%;">
                    Unit Price
                  </th>
                  <th style="width: 5%">Discount</th>
                  <th style="width: 8%; text-align: center">Discount Type</th>
                  <th style="width: 8%">
                    {{ businessEntity.countryId.defaultTaxType }}({{
                      businessEntity.countryId.defaultTaxRate
                    }}%)
                  </th>
                  <th style="width: 5%; text-align: right">Tax</th>
                  <th style="width: 14%; text-align: right; padding-right: 0;">
                    Amount ({{ businessEntity.countryId.defaultCurrency }})
                  </th>
                  <th style="width: 3%"></th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let detail of quotationData.invoiceDetails;
                    let i = index
                  "
                >
                  <td>
                    <!-- ng-select will only show if itemCode is not 'SISSERVICE' -->
                    <ng-select
                      *ngIf="
                        quotationData.invoiceDetails[i].salesItem?.itemCode !==
                        'SISSERVICE'
                      "
                      name="salesItem-{{ i }}"
                      [appendTo]="'body'"
                      [items]="allSalesItems"
                      bindLabel="description"
                      [(ngModel)]="quotationData.invoiceDetails[i].salesItem"
                      [searchable]="true"
                      [clearable]="false"
                      [disabled]="true"
                      placeholder="Select item"
                    >
                      <ng-template ng-option-tmp let-item="item">
                        {{ item.itemCode }} - {{ item.description }}
                      </ng-template>
                    </ng-select>
                  </td>

                  <td>
                    <input
                      type="text"
                      [(ngModel)]="quotationData.invoiceDetails[i].description"
                      name="description-{{ i }}"
                      class="form-control"
                      placeholder="Enter description"
                      [disabled]="
                        quotationData.invoiceDetails[i].salesItem &&
                        quotationData.invoiceDetails[i].salesItem.itemCode !==
                          'SISSERVICE' &&
                        quotationData.invoiceDetails[i].salesItem.itemCode !==
                          ''
                      "
                      readonly
                    />
                  </td>

                  <td>
                    <input
                      type="number"
                      [(ngModel)]="quotationData.invoiceDetails[i].quantity"
                      name="quantity-{{ i }}"
                      class="form-control"
                      min="0"
                      readonly
                    />
                  </td>

                  <td>
                    <div class="input-group">
                      <div class="input-group-prepend">
                        <span class="input-group-text">$</span>
                      </div>
                      <input
                        type="number"
                        [(ngModel)]="quotationData.invoiceDetails[i].unitPrice"
                        name="unitPrice-{{ i }}"
                        class="form-control"
                        min="0"
                        step="0.01"
                        [disabled]="
                          quotationData.invoiceDetails[i].salesItem &&
                          quotationData.invoiceDetails[i].salesItem.itemCode !==
                            'SISSERVICE' &&
                          quotationData.invoiceDetails[i].salesItem.itemCode !==
                            ''
                        "
                        readonly
                      />
                    </div>
                  </td>

                  <td>
                    <input
                      type="number"
                      [(ngModel)]="quotationData.invoiceDetails[i].discount"
                      name="discount-{{ i }}"
                      class="form-control"
                      min="0"
                      readonly
                    />
                  </td>

                  <td>
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="discountType{{ i }}"
                        id="percent{{ i }}"
                        value="B"
                        [(ngModel)]="
                          quotationData.invoiceDetails[i].discountType
                        "
                        disabled
                      />
                      <label class="form-check-label" for="percent{{ i }}"
                        >%</label
                      >
                      <input
                        class="form-check-input"
                        type="radio"
                        name="discountType{{ i }}"
                        id="dollar{{ i }}"
                        value="$"
                        [(ngModel)]="
                          quotationData.invoiceDetails[i].discountType
                        "
                        disabled
                      />
                      <label class="form-check-label" for="dollar{{ i }}"
                        >$</label
                      >
                    </div>
                  </td>
                  <td>
                    <div class="form-check-tax">
                      <label>
                        Incl.GST
                        <input
                          type="checkbox"
                          [(ngModel)]="
                            quotationData.invoiceDetails[i].taxApplicability
                          "
                          name="taxApplicable-{{ i }}"
                          [disabled]="true"
                          style="margin-left: 5px"
                        />
                      </label>
                    </div>
                  </td>

                  <td style="text-align: right">
                    {{ quotationData.invoiceDetails[i].tax | currency }}
                  </td>
                  <td style="text-align: right">
                    {{ quotationData.invoiceDetails[i].amount | currency }}
                  </td>

                  <td></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- note text area -->
        <div class="main-row-note">
          <div class="notes-totals-section">
            <div class="notes-section">
              <label for="notes">Notes</label>
              <textarea
                id="notes"
                class="form-control"
                [(ngModel)]="quotationData.note"
                name="note"
                readonly
              ></textarea>
            </div>
            <div class="totals-section" style="margin-top: 25px;">
              <div class="totals-row">
                <span class="totals-row1">Subtotal (After Discount)</span>
                <span class="totals-row2">{{
                  quotationData.totalAmount | currency
                }}</span>
              </div>
              <div class="totals-row">
                <span class="totals-row1">Total Discount</span>
                <span class="totals-row2">{{
                  calculateTotalDiscount() | currency
                }}</span>
              </div>
              <div class="totals-row">
                <span class="totals-row1"
                  >Total {{ businessEntity.countryId.defaultTaxType }}
                </span>
                <span class="totals-row2">{{
                  quotationData.totalGst | currency
                }}</span>
              </div>
              <div class="totals-row" style="
    border-top: 1px solid #eee;
    padding-top: 20px;
              ">
                <strong class="totals-row1">Grand Total</strong>
                <strong class="totals-row2">{{
                  quotationData.grandTotal | currency
                }}</strong>
              </div>
            </div>
          </div>
        </div>

        <br />

        <div class="card-show-history">
          <div>
            <button
              type="button"
              class="custom-button"
              (click)="showLogs = !showLogs"
            >
              <span class="arrow-icon">{{ showLogs ? '▲' : '▼' }}</span>
              <span class="label-text">Show History</span>
            </button>
          </div>

          <div *ngIf="showLogs && invoiceLogs.length > 0" class="log-wrapper">
            <div class="log-label">
              <label>Notes</label>
            </div>
            <div *ngFor="let log of invoiceLogs" class="log-entry">
              <div class="log-info">
                {{ log.logDate }} - {{ log.logTime }} - <b>{{ log.note }}</b>
              </div>
            </div>
          </div>

          <div *ngIf="showLogs && invoiceLogs.length === 0" class="no-logs">
            <p>No logs available for this invoice.</p>
          </div>
        </div>

      </div>
    </form>
  </div>

  <!-- Send Quote
  <div class="modal fade" id="sendQuoteModal1" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel"
      aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" style="max-width: 640px; max-height: 1080px;">
          <div class="modal-content">
              <div class="modal-header">
                  <h5 class="modal-title" id="simpleModalLabel">Send Quotes</h5>
                  <button type="button" class="btn-close custom-close-btn" aria-label="Close"
                      data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                  <form #sendQuote="ngForm" name="sendQuote" (ngSubmit)="sendQuote.form.valid && sendSelectedQuotes()"
                      novalidate="feedback-form">
                      <div class="form-group">
                          <label for="recipientEmail">Recipient Email</label>
                          <input type="email" id="TempRecipientEmail2" name="TempRecipientEmail"
                              [(ngModel)]="TempRecipientEmail" placeholder="Enter a temporary email or  loaded email"
                              class="form-control" required />
                          <p *ngIf="!TempRecipientEmail">Using stored email: {{ recipientEmail }}</p>
                          <div *ngIf="sendQuote.submitted && sendQuote.controls['TempRecipientEmail'].invalid"
                              class="text-danger">
                              <div *ngIf="sendQuote.controls['TempRecipientEmail'].errors?.['required']">Email is
                                  required.</div>
                              <div *ngIf="sendQuote.controls['TempRecipientEmail'].errors?.['email']">Enter a valid email
                                  address.</div>
                          </div>
                      </div>
                      <div class="class-name">
                          <div class="form-group">
                              <label for="subject">Subject</label>
                              <input type="text" id="subject2" name="subject" [(ngModel)]="subject" required />
                              <div *ngIf="sendQuote.submitted && sendQuote.controls['subject'].invalid"
                                  class="text-danger">
                                  <div *ngIf="sendQuote.controls['subject'].errors?.['required']">Subject is required.
                                  </div>
                              </div>
                          </div>
                      </div>

                      <div class="class-name">
                          <div class="form-group">
                              <label for="message">Message</label>
                              <textarea id="message2" name="message" rows="4"
                                  style="min-height: 350px; height: fit-content;" [(ngModel)]="content"
                                  required></textarea>
                              <div *ngIf="sendQuote.submitted && sendQuote.controls['message'].invalid"
                                  class="text-danger">
                                  <div *ngIf="sendQuote.controls['message'].errors?.['required']">Message is required.
                                  </div>
                              </div>
                          </div>
                      </div>
                      <div class="popup-footer">
                          <button type="button" #closeSendQuote class="cancel-btn" data-bs-dismiss="modal">
                              Close
                          </button>
                          <button type="submit" class="add-btn" [disabled]="isSending">
                              {{ isSending ? 'Sending...' : 'Send' }}
                          </button>
                      </div>
                  </form>
              </div>
          </div>
      </div>
  </div>-->

  <!-- Send Invoice -->
  <div
    class="modal fade"
    id="sendInvoiceModal"
    tabindex="-1"
    role="dialog"
    aria-labelledby="simpleModalLabel"
    aria-hidden="true"
  >
    <div
      class="modal-dialog modal-dialog-centered"
      style="max-width: 640px; max-height: 1080px"
    >
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="simpleModalLabel">Send Invoice</h5>
          <button
            type="button"
            class="btn-close custom-close-btn"
            aria-label="Close"
            data-bs-dismiss="modal"
          ></button>
        </div>

        <div class="modal-body">
          <form
            #sendInvoice="ngForm"
            name="sendInvoice"
            (ngSubmit)="sendInvoice.form.valid && sendSelectedInvoices()"
            novalidate="feedback-form"
          >
            <div class="form-group">
              <label for="recipientEmail">Recipient Email</label>
              <input
                type="email"
                id="TempRecipientEmail"
                name="TempRecipientEmail"
                [(ngModel)]="TempRecipientEmail"
                placeholder="Enter a temporary email or  loaded email"
                class="form-control"
                required
                #TempRecipientEmailRef="ngModel"
                email
              />
              <p *ngIf="!TempRecipientEmail">
                Using stored email: {{ recipientEmail }}
              </p>
             <!--<div
                *ngIf="
                  sendInvoice.submitted &&
                  sendInvoice.controls['TempRecipientEmail'].invalid
                "
                class="text-danger"
              >
                <div
                  *ngIf="sendInvoice.controls['TempRecipientEmail'].errors?.['required']"
                >
                  Email is required.
                </div>
                <div
                  *ngIf="sendInvoice.controls['TempRecipientEmail'].errors?.['email']"
                >
                  Enter a valid email address.
                </div>
              </div>-->

              <div
  *ngIf="sendInvoice.submitted && TempRecipientEmailRef.invalid"
  class="text-danger"
>
  <div *ngIf="TempRecipientEmailRef.errors?.['required']">
    Email is required.
  </div>
  <div *ngIf="TempRecipientEmailRef.errors?.['email']">
    Enter a valid email address.
  </div>
</div>

            </div>

            <div class="form-group">
              <label for="subject">Subject</label>
              <input
                type="text"
                id="subject"
                name="subject"
                [(ngModel)]="subject"
                required
              />
              <div
                *ngIf="
                  sendInvoice.submitted &&
                  sendInvoice.controls['subject'].invalid
                "
                class="text-danger"
              >
                <div
                  *ngIf="sendInvoice.controls['subject'].errors?.['required']"
                >
                  Subject is required.
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="message">Message</label>
              <textarea
                id="message"
                name="message"
                rows="4"
                style="min-height: 350px; height: fit-content"
                [(ngModel)]="content"
                required
              ></textarea>
              <div
                *ngIf="
                  sendInvoice.submitted &&
                  sendInvoice.controls['message'].invalid
                "
                class="text-danger"
              >
                <div
                  *ngIf="sendInvoice.controls['message'].errors?.['required']"
                >
                  Message is required.
                </div>
              </div>
            </div>

            <div class="popup-footer">
              <button
                type="button"
                #closeSendInvoice
                class="cancel-btn"
                data-bs-dismiss="modal"
              >
                Close
              </button>
              <button type="submit" class="add-btn" [disabled]="isSending">
                {{ isSending ? "Sending..." : "Send" }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Invoice Preview -->
<div
  class="modal fade"
  id="invoicePreviewModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="simpleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" style="max-width: 740px">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="simpleModalLabel">Invoice</h5>
        <div class="ml-auto icon-group">
          <i
            class="bi bi-x-circle close-icon"
            #closePreview
            data-bs-dismiss="modal"
            aria-label="Close"
            title="Close"
          ></i>
        </div>
      </div>

      <div class="modal-body">
        <!-- Loading Spinner -->
        <div *ngIf="isLoading" class="spinner-container">
          <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
          </div>
        </div>

        <!-- IFrame for Quotation Preview -->
        <div style="margin-top: 20px" [ngClass]="{ 'd-none': isLoading }">
          <iframe
            #invoicePreviewFrame
            id="invoicePreviewFrame"
            width="700px"
            height="700px"
          ></iframe>
        </div>
      </div>
    </div>
  </div>
</div>
