import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Country } from '../country';
import { CountryService } from '../country.service';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';


@Component({
  selector: 'app-list-country',
  templateUrl: './list-country.component.html',
  styleUrls: ['./list-country.component.css']
})
export class ListCountryComponent implements OnInit{
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  filteredCountry: Country[] = [];
  constructor(private countryService: CountryService, private router: Router) {}

  ngOnInit(): void {
    this.getCountries();
  }
  getCountries(): void {
    this.countryService.getAllCountries().subscribe(
      (data: Country[]) => {
        this.filteredCountry = data;
      },
      (error) => {
        console.error('Error fetching countries:', error);
      }
    );
  }

  createNewCountry() {
    this.router.navigate(['/create-country']);
  }

  private getCountryByIds(id: number): void {
    this.countryService.getCountryById(id).subscribe(
      (data: Country) => {
        this.filteredCountry = [data]; 
      },
      (error) => {
        console.error('Error fetching country by ID:', error);
        Swal.fire({
          title: 'Error!',
          text: `Country with ID ${id} could not be found.`,
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );
  }
  deleteCountry(id: number): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you really want to delete this country?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (result.isConfirmed) {
  
        this.countryService.deleteCountry(id)
          .subscribe(
            () => {
  
              Swal.fire({
                title: 'Deleted!',
                text: 'Country has been deleted.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
              }).then(() => {
                window.location.reload();
              });
            },
            (error) => {
              console.error('Failed to delete country:', error);
  
              Swal.fire({
                title: 'Error!',
                text: 'Failed to delete country.',
                icon: 'error',
                confirmButtonText: 'OK',
                cancelButtonText: 'Ask Chimp',
                confirmButtonColor: '#be0032',
                cancelButtonColor: '#007bff',
                showCancelButton: true, 
                }).then((result) => {
                  if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                    if (this.chatBotComponent) {
                      Swal.fire({
                        title: 'Processing...',
                        text: 'Please wait while Chimp processes your request.',
                        allowOutsideClick: false,
                        didOpen: () => {
                          Swal.showLoading();
                          this.chatBotComponent.setInputData('Failed to delete country');
                          this.chatBotComponent.responseReceived.subscribe(response => {
                            Swal.close();
                            this.chatResponseComponent.showPopup = true;
                            this.chatResponseComponent.responseData = response;
                            this.playLoadingSound();
                            this.stopLoadingSound() 
                          });
                        },
                      });
                    } else {
                      console.error('ChatBotComponent is not available.');
                    }
                  }
              });
            }
          );
      }
    });
  }
  updateCountry(id: number): void {
    this.router.navigate(['/update-country', id]);
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }

  
} 
 

