<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <form
    #f="ngForm"
    (ngSubmit)="f.form.valid && onSubmit(f)"
    class="row g-1"
    novalidate="feedback-form"
  >
    <div class="heading" (keydown)="preventSubmit($event)">
      <h3>Record Expenses</h3>
      <div class="button-group">
        <button
          type="button"
          class="transparent-button"
          (click)="onScanBillClick(fileInput)"
        >
          Scan Expense
        </button>
      </div>
    </div>
    <input
      type="file"
      #fileInput
      (change)="fileSelected($event)"
      accept=".pdf, .doc, .docx, .xls, .xlsx, image/*"
      style="display: none"
    />
    <img
      *ngIf="previewImageSrc"
      [src]="previewImageSrc"
      style="max-width: 100%; margin-top: 10px"
    />

    <canvas #canvas style="display: none"></canvas>

    <div class="bd">
      <div class="form-section">
        <!-- First row: Reference Number and Valid Until -->
        <div class="form-rows" style="display: flex; margin-bottom: 10px">
          <!-- Expenses Number -->
          <div
            class="form-group form-groups"
            style="display: flex; flex-grow: 1"
          >
            <label
              for="expensesNumber"
              id="quotation"
              style="margin-right: 15px; white-space: nowrap"
            >
              Expenses Number
            </label>
            <input
              class="input-style"
              type="text"
              id="expensesNumber"
              [(ngModel)]="otherExpensesHeadData.expensesNumber"
              name="expensesNumber"
              #expensesNumber="ngModel"
              style="background-color: #ffffff"
              readonly
              disabled
            />
          </div>
        </div>

        <div
          class="form-row"
          style="display: flex; justify-content: space-between"
        >
          <!-- Spent On -->
          <div
            class="form-group"
            style="display: flex; flex-direction: column; flex-grow: 1"
          >
            <div style="display: flex" class="form-dataInput">
              <label
                for="spentOn"
                style="margin-right: 75px; white-space: nowrap"
                >Spent On</label
              >
              <div class="input-container">
                <input
                  matInput
                  [matDatepicker]="spentOnPicker"
                  class="input-style"
                  id="spentOn"
                  [(ngModel)]="otherExpensesHeadData.spentOn"
                  name="spentOn"
                  (change)="onExpensesDateChange()"
                  required
                />
                <mat-datepicker-toggle matSuffix [for]="spentOnPicker"></mat-datepicker-toggle>
              </div>
            </div>
            <mat-datepicker #spentOnPicker></mat-datepicker>

            <!-- Error message directly under the input field -->
            <div
              *ngIf="f.submitted && f.controls['spentOn'].invalid"
              class="text-danger"
              style="margin-top: 5px; margin-left: 110px"
            >
              <div *ngIf="f.controls['spentOn'].errors?.['required']">
                Spent At required.
              </div>
            </div>
          </div>
        </div>

        <div
          class="form-row"
          style="display: flex; justify-content: space-between"
        >
          <!-- Spent At -->
          <div
            class="form-group"
            style="display: flex; flex-direction: column; flex-grow: 1"
          >
            <div class="form-dataInput">
              <label
                for="spentAt"
                style="margin-right: 80px; white-space: nowrap"
                >Spent At</label
              >
              <input
                class="input-style"
                type="text"
                id="spentAt"
                [(ngModel)]="otherExpensesHeadData.spentAt"
                name="spentAt"
                required
              />
            </div>

            <!-- Error message directly under the input field -->
            <div
              *ngIf="f.submitted && f.controls['spentAt'].invalid"
              class="text-danger"
              style="margin-top: 5px; margin-left: 110px"
            >
              <div *ngIf="f.controls['spentAt'].errors?.['required']">
                Spent At required.
              </div>
            </div>
          </div>
        </div>

        <!-- <div
          class="form-row"
          style="display: flex; justify-content: space-between"
        >
          <div
            class="form-group"
            style="display: flex; align-items: center; flex-grow: 1"
          ></div>
        </div> -->
      </div>
      <div *ngIf="isLoading" class="spinner"></div>
      <div class="table-section">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th style="width: 33%">Expense Description</th>
                <th style="width: 17%">Amount</th>
                <th style="width: 13%">Account</th>
                <th style="width: 8%">
                  {{ businessEntity.countryId.defaultTaxType }}({{
                    businessEntity.countryId.defaultTaxRate
                  }}%)
                </th>
                <th style="width: 5%; text-align: right">Tax</th>
                <th style="width: 14%; text-align: right; padding-right: 0%">
                  Total Amount ({{ businessEntity.countryId.defaultCurrency }})
                </th>
                <th style="width: 3%"></th>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let detail of otherExpensesHeadData.otherExpensesDetails;
                  let i = index
                "
              >
                <td>
                  <input
                    type="text"
                    [(ngModel)]="
                      otherExpensesHeadData.otherExpensesDetails[i].description
                    "
                    name="description-{{ i }}"
                    class="form-control"
                    placeholder="Enter Description"
                    (input)="onDescriptionInput(i)"
                  />
                </td>
                <td>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">$</span>
                    </div>
                    <input
                      type="number"
                      [(ngModel)]="
                        otherExpensesHeadData.otherExpensesDetails[i].amount
                      "
                      (input)="updateAmount(i)"
                      name="amount{{ i }}"
                      class="form-control"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </td>

                <td>
                  <select
                    class="form-control"
                    name="coaLedgerAccountId-{{ i }}"
                    required
                    [(ngModel)]="
                      otherExpensesHeadData.otherExpensesDetails[i]
                        .coaLedgerAccountId
                    "
                    (change)="onGLChange($event, i)"
                  >
                    <option value="" disabled selected>GL Account</option>
                    <option
                      *ngFor="let account of glAccounts"
                      [value]="account.coaLedgerAccountId"
                    >
                      {{ account.ledgerAccountName }}
                    </option>
                  </select>
                </td>

                <td>
                  <div class="form-check-tax">
                    <label>
                      In.Tax
                      <input
                        type="checkbox"
                        [disabled]="!taxApplicabilityEnabled"
                        [(ngModel)]="
                          otherExpensesHeadData.otherExpensesDetails[i]
                            .taxApplicability
                        "
                        name="taxApplicable-{{ i }}"
                        (change)="onTaxApplicableChange(i)"
                        style="margin-left: 5px"
                      />
                    </label>
                  </div>
                </td>

                <td style="text-align: right">
                  {{
                    otherExpensesHeadData.otherExpensesDetails[i].tax | currency
                  }}
                </td>
                <td style="text-align: right">
                  {{
                    otherExpensesHeadData.otherExpensesDetails[i].total_amount
                      | currency
                  }}
                </td>

                <td>
                  <button
                    type="button"
                    class="btn btn-link"
                    (click)="removeItem(i)"
                  >
                    <i
                      class="fa fa-trash"
                      style="color: red"
                      title="Delete this row"
                    ></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- <button type="button" class="btn btn-primary" (click)="addNewRow()">Add New Row</button> -->
          <button
            type="button"
            class="btn btn-primary new-item"
            (click)="addNewRow()"
          >
            <i class="bi bi-plus-circle-fill"></i> Add New Row
          </button>
        </div>
      </div>

      <div class="notes-totals-section">
        <div class="notes-section" style="padding: 0 10px 0 10px">
          <label for="remarks">Notes</label>
          <textarea
            id="remarks"
            class="form-control"
            [(ngModel)]="otherExpensesHeadData.remarks"
            name="note"
            rows="10"
          ></textarea>
        </div>

        <div class="totals-section" style="margin-top: 25px">
          <div class="totals-row">
            <span class="totals-row1">Sub Total Amount </span>
            <span class="totals-row2">
              {{ otherExpensesHeadData.grossAmount | currency }}
            </span>
          </div>

          <div class="totals-row">
            <span class="totals-row1">
              Total {{ businessEntity.countryId.defaultTaxType }}
            </span>
            <span class="totals-row2">
              {{ otherExpensesHeadData.totalGst | currency }}
            </span>
          </div>

          <div class="totals-row">
            <strong class="totals-row1">Grand Total</strong>
            <strong class="totals-row2">
              {{ otherExpensesHeadData.netAmount | currency }}
            </strong>
          </div>
        </div>
      </div>

      <div class="footer-section" style="margin-bottom: 50px">
        <div class="btn-rows">
          <div class="footer-btn-section">
            <!--<button type="submit" class="transparent-button">
            File Attach <i class="bi bi-paperclip"></i>
          </button>-->
            <!-- <button type="submit" class="transparent-button">
            Add Note <i class="bi bi-plus"></i>
          </button> -->
          </div>

          <div class="footer-btn-section footer-Btn" style="margin-right: 20px">
            <button type="button" class="cancel-btn" (click)="onCancel()">
              Cancel
            </button>
            <button type="submit" class="add-btn">Save Expense</button>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<style>
  /* Default style: full width on screens greater than 1024px */
  .form-rows {
    width: 50%;
    padding-right: 26px;
  }

  /* Media query for screens smaller than 1024px */
  @media screen and (max-width: 680px) {
    .form-rows {
      width: 100%;
    }
  }
</style>
