import { Component, OnInit, ViewChild } from '@angular/core';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { CreditNoteBillHead } from '../bill';
import { Entity } from 'src/app/modules/entity/entity';
import { BillService } from '../bill.service';
import { ActivatedRoute, Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import Swal from 'sweetalert2';
import { DateAdapter } from '@angular/material/core';

@Component({
  selector: 'app-credit-note-bill-list',
  templateUrl: './credit-note-bill-list.component.html',
  styleUrls: ['./credit-note-bill-list.component.css'],
})
export class CreditNoteBillListComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;

  activeTab = 'all';
  currentPage = 1;
  pageSize = 10;
  billHeadList: CreditNoteBillHead[] = [];
  selectedCreditNotes: Set<CreditNoteBillHead> = new Set<CreditNoteBillHead>();
  isAllSelected: boolean = false;
  searchTerm: string = ''; // Store the search term
  filteredCreditNotes: CreditNoteBillHead[] = [];
  startDate: string | null = null; // Bind to the start date input
  endDate: string | null = null; // Bind to the end date input
  entity: Entity = new Entity();

  constructor(
    private billService: BillService,
    private router: Router,
    public sanitizer: DomSanitizer,
    private dateAdapter: DateAdapter<Date>,
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }

  ngOnInit(): void {
    this.getBillHeadListByEntity();
  }

  isDropdownOpen = false;

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  private getBillHeadListByEntity() {
    const entityId = +((localStorage.getItem('entityId')) + '');
    this.billService.getAllCreditNoteBillHeadList(entityId).subscribe((data) => {
      this.billHeadList = data;
    });
  }

  // Function to filter bills based on the search term and active tab
  filterBills(): void {
    if (!this.searchTerm && !this.startDate && !this.endDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Search Criteria',
        text: 'Please provide at least one search criterion: Credit Note Number, Start Date, or End Date.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    let filtered = this.billHeadList;

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter((bill) =>
        bill.creditNoteNumberBill.toString().toLowerCase().includes(searchTermLower)
      );
    }

    // Filter by date range
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter((bill) => new Date(bill.documentDate) >= startDate);
    }

    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter((bill) => new Date(bill.documentDate) <= endDate);
    }

    this.filteredCreditNotes = filtered;
  }

  // Reset filters
  resetFilters() {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.activeTab = 'all'; // Reset the active tab to 'all'
    this.filteredCreditNotes = this.billHeadList;
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.filterBills(); // Filter bills when tab changes
  }

  toggleSelection(bill: CreditNoteBillHead, event: any): void {
    if (event.target.checked) {
      this.selectedCreditNotes.add(bill); // Add the selected credit note
    } else {
      this.selectedCreditNotes.delete(bill); // Remove the deselected credit note
    }
  
    // Update "Select All" checkbox state
    this.isAllSelected = this.filteredCreditNotes.every((bill) =>
      this.selectedCreditNotes.has(bill)
    );
  }
  

  selectAll(event: any): void {
    this.isAllSelected = event.target.checked;
  
    if (this.isAllSelected) {
      this.filteredCreditNotes.forEach((bill) => {
        this.selectedCreditNotes.add(bill); // Add all bills to the set
        bill.selected = true; // Update the `selected` property in the UI
      });
    } else {
      this.filteredCreditNotes.forEach((bill) => {
        this.selectedCreditNotes.delete(bill); // Remove all bills from the set
        bill.selected = false; // Update the `selected` property in the UI
      });
    }
  }
  
  

  clearSelectedCheckboxes() {
    // Deselect all checkboxes
    this.selectedCreditNotes.clear();
  }

  
  
  addCreditNote() {
    Swal.fire({
      title: 'Please Select Bills',
      text: 'You need to select one or more bills before proceeding.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        // Navigate to the payable bill list after confirmation
        this.router.navigate(['/payable-bill-list']);
      }
    });}

  exportToExcel(): void {
    const exportData = this.filteredCreditNotes.map((bill) => ({
      'Credit Note Number': bill.creditNoteNumberBill,
      'Bill Numbers': bill.referenceNos,
      'Supplier Name': bill.supplierName,
      'Document Date': new Date(bill.documentDate).toLocaleDateString(), // Format date
      'Total Credit Amount': bill.totalCreditAmount,
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Credit Notes');

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    this.saveAsExcelFile(excelBuffer, `Credit_Notes_${timestamp}`);
  }

  private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
    });
    const file = new File([data], `${fileName}.xlsx`, { type: data.type });
    saveAs(file);
  }

  cancelSelectedCreditNotes(): void {
    if (this.selectedCreditNotes.size === 0) {
      // Show error if no credit notes are selected
      Swal.fire({
        title: 'No Credit Notes Selected',
        text: 'Please select at least one credit note to cancel.',
        icon: 'warning',
        confirmButtonText: 'OK',
      });
      return;
    }
  
    // Confirm cancellation
    Swal.fire({
      title: 'Are you sure?',
      text: `You are about to cancel ${this.selectedCreditNotes.size} credit note(s).`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, Cancel',
      cancelButtonText: 'No',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (result.isConfirmed) {
        const selectedIds = Array.from(this.selectedCreditNotes).map(
          (bill) => bill.creditNoteBillHeadId
        );
        this.billService.cancelCreditNotes(selectedIds).subscribe(
          () => {
            Swal.fire('Canceled!', 'Selected credit notes have been canceled.', 'success');
            this.getBillHeadListByEntity(); // Refresh list
            this.clearSelectedCheckboxes();
          },
          (error) => {
            Swal.fire('Error!', 'Failed to cancel selected credit notes.', 'error');
            console.error(error);
          }
        );
      }
    });
  }
  
  
}
