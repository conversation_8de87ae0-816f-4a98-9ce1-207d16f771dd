import { Component } from '@angular/core';
import { Subscription } from 'rxjs';
import { SubscriptionFee } from '../subscription';
import { SubscriptionService } from '../subscription.service';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-subscription-plan-list',
  templateUrl: './subscription-plan-list.component.html',
  styleUrls: ['./subscription-plan-list.component.css'],
})
export class SubscriptionPlanListComponent {
  inviteLogId: number = 0;
  selectedSubscriptionPlan: any = '';
  subscriptionPlans: SubscriptionFee[] = [];
  subscription: Subscription = new Subscription();
  subscriptionfee: SubscriptionFee = new SubscriptionFee();
  isEntityInvitaion: boolean = false;

  constructor(
    private subscriptionService: SubscriptionService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.inviteLogId = Number(params['inviteLogId']);
      this.isEntityInvitaion = params.hasOwnProperty('inviteLogId');
    });
    this.getSubscriptionPlanList();
  }

  private getSubscriptionPlanList() {
    this.subscriptionService.getAllSubscriptionFee().subscribe((data) => {
      this.subscriptionPlans = data;
    });
  }

  navigateUserRegistration(planId: number): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to select this plan?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes',
      cancelButtonText: 'No',
    }).then((result) => {
      if (result.isConfirmed) {
        this.subscriptionService.setSelectedSubscriptionFeeId(planId);
        if (this.isEntityInvitaion) {
          this.router.navigate([`/user-agreement`], {
            queryParams: { inviteLogId: this.inviteLogId },
          });
        } else {
          this.router.navigate([`/user-agreement`]);
        }
      } else {
        console.log('Subscription selection was canceled');
      }
    });
  }
}
