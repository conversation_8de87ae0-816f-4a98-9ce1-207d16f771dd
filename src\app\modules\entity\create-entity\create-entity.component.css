body {
  font-family: Inter;
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 25px;
  background-color: transparent;
}

.form-container {
  background: #ffffff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 850px;
}

h2 {
  margin-bottom: 40px;
  margin-left: 20px;
  font-family: Inter;
  font-size: 30px;
  font-weight: 600;
  text-align: left;
  color: #535353;
}

.radio-group {
  display: flex;
  justify-content: left;
  margin-left: 20px;
  margin-bottom: 20px;
  gap: 50px;
}

.radio-group label {
  margin: 0 10px;
  font-family: Inter;
  font-size: 15px;
  font-weight: 600;
  line-height: 29.05px;
  text-align: left;
}

.form-group {
  margin-bottom: 20px;
  margin-left: 20px;
  text-align: left;
  font-family: "Inter", sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 1.6;
  color: #444343;
  position: relative;
  flex: 1;
}

.form-group button {
  border: none;
  border-radius: 5px;
  background-color: white;
  color: #4262ff;
  cursor: pointer;
  font-family: Inter;
  font-size: 14px;
  font-weight: 700;
  text-align: left;
  margin-top: 8px;
}

.form-group .remove-btn {
  border: none;
  border-radius: 5px;
  background-color: white;
  color: #FF0000;
  cursor: pointer;
  font-family: Inter;
  font-size: 14px;
  font-weight: 700;
  text-align: left;
  margin-top: 8px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.form-row-1 {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 5px;
}

.form-group input[type="text"],
.form-group select {
  width: 100%;
  padding: 10px;
  border: 2px solid #c7c7c7;
  border-radius: 8px;
  box-sizing: border-box;
  font-size: 1em;
  font-family: "Inter", sans-serif;
}

.form-group select:focus {
  border-color: #4262ff;
  box-shadow: 0 0 5px rgba(66, 98, 255, 0.3);
  outline: none;
}

.form-group select:hover {
  border-color: #a3a3a3;
}

.form-group input[type="file"] {
  width: 47%;
  border: 2px solid #c7c7c7;
  border-radius: 8px;
  box-sizing: border-box;
  font-size: 1em;
  font-family: Inter;
  padding: 5px;
  margin-bottom: 20px;
}

.img-preview {
  margin-left: 20px;
  margin-bottom: 10px;
}

.btn {
  margin-left: 20px;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  padding: 10px 100px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-family: Inter;
  font-size: 17px;
  font-weight: 700;
  line-height: 29.05px;
  text-align: center;
}

.btn:hover {
  background: linear-gradient(to left, #4262ff, #512ca2);
}

.read-only {
  background-color: #e0e0e0;
}
