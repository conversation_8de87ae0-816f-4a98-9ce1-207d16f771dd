<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <div class="row px-3">
    <div class="col-12 g-0 actions">
      <h1>Bank Rules</h1>
    </div>
    <div class="col-12">
      <div class="row">
        <div class="col-12 bottom-border g-0">
          <div class="float-start">
            <button
              class="tab-view-button"
              [ngClass]="{ 'active-button': activeTab === 'spend' }"
              (click)="activeTab = 'spend'"
            >
              Spend Money Rules ( {{spendRuleList.length}} )
            </button>
            <button
              class="tab-view-button"
              [ngClass]="{ 'active-button': activeTab === 'receive' }"
              (click)="activeTab = 'receive'"
            >
              Receive Money Rules ( {{receiveRuleList.length}} )
            </button>
          </div>
        </div>
        <div
          class="col-12 bg-white p-4"
          style="border: 1px solid lightgray; border-top: 0"
          [ngSwitch]="activeTab"
        >
          <div *ngSwitchCase="'spend'">
            <div class="col-12 text-end mb-4">
              <button
                class="primary-button"
                (click)="navigateToBankRuleCreation(activeTab)"
              >
                <i class="fa fa-plus" style="font-size: 12px"></i
                >&nbsp;&nbsp;&nbsp;Create Spend Rule
              </button>
            </div>
            <ng-container *ngIf="!spendRuleList || spendRuleList.length == 0">
              <ng-container
                *ngTemplateOutlet="noRules; context: { $implicit: 'Spend' }"
              ></ng-container>
            </ng-container>
            <ng-container *ngIf="spendRuleList && spendRuleList.length > 0">
              <ng-container
                *ngTemplateOutlet="
                  ruleList;
                  context: { $implicit: spendRuleList }
                "
              ></ng-container>
            </ng-container>
          </div>
          <div *ngSwitchCase="'receive'">
            <div class="col-12 text-end mb-4">
              <button
                class="primary-button"
                (click)="navigateToBankRuleCreation(activeTab)"
              >
                <i class="fa fa-plus" style="font-size: 12px"></i
                >&nbsp;&nbsp;&nbsp;Create Receive Rule
              </button>
            </div>
            <ng-container
              *ngIf="!receiveRuleList || receiveRuleList.length == 0"
            >
              <ng-container
                *ngTemplateOutlet="noRules; context: { $implicit: 'Receive' }"
              ></ng-container>
            </ng-container>
            <ng-container *ngIf="receiveRuleList && receiveRuleList.length > 0">
              <ng-container
                *ngTemplateOutlet="
                  ruleList;
                  context: { $implicit: receiveRuleList }
                "
              ></ng-container>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #noRules let-type>
  <p>
    There are no <span style="font-weight: 600">{{ type }} Money Rules</span> to
    display.
  </p>
</ng-template>

<ng-template #ruleList let-rules>
  <table>
    <colgroup>
      <col width="80%" />
      <col width="20%" />
    </colgroup>
    <tr *ngFor="let rule of rules">
      <td>
        <p class="text-black-50" style="font-size: 13px;">{{ rule.bankRuleHead.createdAt }}</p>
        <p style="font-weight: 600; font-size: 15px;">{{ rule.bankRuleHead.bankRuleName }}</p>
      </td>
      <td class="text-center">
        <button type="button" class="btn btn-sm bg-danger-subtle">
          <i class="fa fa-trash"></i>
        </button>&nbsp;&nbsp;&nbsp;
        <button type="button" class="btn btn-sm bg-primary-subtle">
          <i class="fa fa-edit"></i>
        </button>
      </td>
    </tr>
  </table>
</ng-template>
