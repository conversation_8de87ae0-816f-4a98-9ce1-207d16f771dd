import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class PayrollService {
  leaves = [
    {
      name: 'Annual Leave',
      category: 'Paid Leave',
      units: 'Hours',
      entitlement: '102.00',
      loadingRate: 'No',
      showOnPayslip: 'Yes',
    },
    {
      name: 'Personal Leave',
      category: 'Paid Leave',
      units: 'Hours',
      entitlement: '190.00',
      loadingRate: 'No',
      showOnPayslip: 'No',
    },
    {
      name: 'Long Service Leave',
      category: 'Unpaid Leave',
      units: 'Days',
      entitlement: '150.00',
      loadingRate: 'No',
      showOnPayslip: 'Yes',
    },
    {
      name: 'Other Leave',
      category: 'Paid Leave',
      units: 'Hours',
      entitlement: '130.00',
      loadingRate: 'No',
      showOnPayslip: 'Yes',
    },
    {
      name: 'Paid Family and Domestic Violence Leave',
      category: 'Paid Leave',
      units: 'Days',
      entitlement: '192.00',
      loadingRate: 'Yes',
      showOnPayslip: 'No',
    },
    {
      name: 'Other Leave',
      category: 'Unpaid Leave',
      units: 'Hours',
      entitlement: '102.00',
      loadingRate: 'Yes',
      showOnPayslip: 'Yes',
    },
  ];
}
