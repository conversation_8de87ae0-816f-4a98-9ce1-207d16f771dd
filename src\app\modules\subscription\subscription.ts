import { Entity } from '../entity/entity';

export class Subscription {
  subscriptionId: number = 0;
  entityId: Entity = new Entity();
  subscriptionPlanId: SubscriptionPlan = new SubscriptionPlan();
  subscriptionCycleId: SubscriptionCycle = new SubscriptionCycle();
  subscriptionFeeId: SubscriptionFee | undefined = new SubscriptionFee();
  subscriptionTransactionId: number = 0;
  amount: string = '';
  description: string = '';
  purchaseDate: string = '';
  validUntilDate: string = '';
  subscriptionStatus: string = '';
  renewalMethod: string = '';
}

export class SubscriptionFee {
  subscriptionFeeId: number = 0;
  subscriptionPlanId: SubscriptionPlan = new SubscriptionPlan();
  subscriptionCycleId: SubscriptionCycle = new SubscriptionCycle();
  amount: string = '';
  graceAmount: string = '';
}

export class SubscriptionPlan {
  subscriptionPlanId: number = 0;
  subscriptionPlan: string = '';
  userLimitPerEntitiy: number = 0;
}

export class SubscriptionCycle {
  subscriptionCycleId: number = 0;
  subscriptionCycle: string = '';
  noOfDays: number = 0;
}
