import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { Subscription, SubscriptionFee } from '../subscription';
import { SubscriptionService } from '../subscription.service';
import { StripeService } from './stripe/stripe.service';
import { AuthService } from 'src/app/auth.service';
import { Entity } from '../../entity/entity';
import { EntityService } from '../../entity/entity.service';

@Component({
  selector: 'app-payment',
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.css'],
})
export class PaymentComponent implements OnInit {
  paymentForm: FormGroup;
  paymentProcessing: boolean = false;
  paymentError: string | null = null;
  card: any;
  selectedSubscriptionFeeId: number | null = null;
  isOnGrace: string = '';
  selectedSubscriptionAmount: string = '';
  subscriptionFee: SubscriptionFee = new SubscriptionFee();
  subscription: Subscription = new Subscription();
  businessEntity: Entity = new Entity();
  entityId: number = 0;

  constructor(
    private fb: FormBuilder,
    private stripeService: StripeService,
    private router: Router,
    private route: ActivatedRoute,

    private subscriptionService: SubscriptionService,
    private authService: AuthService,
    private entityService: EntityService
  ) {
    this.paymentForm = this.fb.group({
      amount: ['', [Validators.required, Validators.min(1)]],
    });
  }

  async ngOnInit(): Promise<void> {
    this.selectedSubscriptionFeeId =
      this.route.snapshot.queryParams['subscriptionPlanId'];
    this.isOnGrace =
      this.route.snapshot.queryParams['onGrace'];

    this.entityId = Number(localStorage.getItem('entityId'));
    await this.loadSubscriptionFee();
    await this.setupStripeElements();

    this.isFreePlan();
  }

  isFreePlan(): void {
    if (this.selectedSubscriptionFeeId === 1) {
      Swal.fire({
        title: 'Success!',
        text: 'Registration successfully done. Please verify your email.',
        icon: 'success',
        iconColor: '#28a745',
        confirmButtonText: 'OK',
        confirmButtonColor: '#28a745',
      }).then((result) => {
        if (result.isConfirmed) {
          this.router.navigate(['/user-login']);
        }
      });
    }
  }

  async onSubmit(): Promise<void> {

    if (this.paymentForm.invalid) {
      return;
    }

    this.paymentProcessing = true;
    this.paymentError = null;

    const amount = this.paymentForm.value.amount;

    try {
      const { token, error } = await this.stripeService.createToken(this.card);

      if (error) {
        this.handlePaymentError(error);
        return;
      }

      this.stripeService.chargeCard(token.id, amount).subscribe({
        next: () => {
          this.paymentProcessing = false;

          if (this.authService.isLoggedIn()) {
            this.subscriptionService
              .isSubscriptionExistsForEntity(this.entityId)
              .subscribe((response) => {
                if (response) {
                  this.updateEntitySubscription(this.entityId);
                } else {
                  this.setSubscriptionPlanForEntity(this.entityId);
                }
              });

            setTimeout(() => {
              this.router.navigate(['/user-login']).then(() => {
                window.location.reload();
              });
            }, 1000);
          } else {
            this.setSubscriptionPlanForEntity(this.entityId);

            this.showPaymentSuccessAlert().then(() => {
              this.router.navigate(['/user-login']).then(() => {});
            });
          }
        },
        error: (error) => {
          this.handlePaymentError(error);
        },
      });
    } catch (error) {
      console.error('Error creating token:', error);
      this.handlePaymentError(error);
    }
  }

  setSubscriptionPlanForEntity(entityId: number): void {
    this.entityService.getBusinessEntityById(entityId).subscribe((data) => {
      this.businessEntity = data;

      this.subscriptionService
        .getSubscriptionFeeById(Number(this.selectedSubscriptionFeeId))
        .subscribe((response) => {
          this.subscriptionFee = response;
          this.subscription.entityId = this.businessEntity;
          this.subscription.subscriptionFeeId = this.subscriptionFee;

          this.subscriptionService
            .saveSubscription(this.subscription)
            .subscribe((subscriptionData) => {
              this.subscription = subscriptionData;
            });
        });
    });
  }

  private async loadSubscriptionFee(): Promise<void> {
    if (this.selectedSubscriptionFeeId !== null) {
      this.subscriptionService
        .getSubscriptionFeeById(this.selectedSubscriptionFeeId)
        .subscribe((data) => {
          this.subscriptionFee = data;
          this.selectedSubscriptionAmount = this.isOnGrace === "true" ?
            this.subscriptionFee.graceAmount.toString() : this.subscriptionFee.amount.toString();
            this.paymentForm
            .get('amount')
            ?.setValue(this.selectedSubscriptionAmount);
        });
    } else {
      console.error('Selected subscription fee ID is null');
      Swal.fire({
        title: 'Error!',
        text: 'An error occurred. Please try again',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      }).then((result) => {
        if (result.isConfirmed) {
          this.router.navigate(['/home']);
        }
      });
    }
  }

  updateEntitySubscription(entityId: number): void {
    this.subscriptionService
      .updateSubscription(entityId, Number(this.selectedSubscriptionFeeId))
      .subscribe((data) => {
        this.subscription = data;
      });
  }

  private async setupStripeElements(): Promise<void> {
    await this.stripeService.initializeStripe();

    const elements = this.stripeService.stripe!.elements();

    this.card = elements.create('card', {
      hidePostalCode: true,
      style: {
        base: {
          fontSize: '16px',
          color: '#32325d',
          fontFamily: 'Open Sans, sans-serif',
          '::placeholder': {
            color: '#aab7c4',
          },
        },
        invalid: {
          color: '#fa755a',
          iconColor: '#fa755a',
        },
      },
    });

    this.card.mount('#card-element');
  }

  private handlePaymentError(error: any): void {
    this.paymentError =
      typeof error === 'string'
        ? error
        : (error as any).message ?? 'An unknown error occurred';
    this.paymentProcessing = false;
    this.showPaymentErrorAlert();
  }

  private showPaymentSuccessAlert(): Promise<any> {
    return Swal.fire({
      title: 'Success!',
      text: 'Payment successful! Please check your email to verify your account.',
      icon: 'success',
      iconColor: '#28a745',
      confirmButtonText: 'OK',
      confirmButtonColor: '#28a745',
    });
  }

  private showPaymentErrorAlert(): void {
    Swal.fire({
      title: 'Error!',
      text: this.paymentError || 'An error occurred.',
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#be0032',
    });
  }
}
