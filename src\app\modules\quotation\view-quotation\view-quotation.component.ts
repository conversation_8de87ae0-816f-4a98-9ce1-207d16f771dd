import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgForm } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';

// Third-party libraries
import { Observable, map, catchError, of } from 'rxjs';

// Services
import { QuotationService } from '../quotation.service';
import { HttpService } from 'src/app/http.service';
import { EntityService } from '../../entity/entity.service';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { StorageService } from '../../entity/storage.service';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';

// Models
import { QuoteHead, QuotationDetail, SalesItem } from '../quotation';
import { Entity, EntityTradingName } from '../../entity/entity';
import { BusinessPartner, BusinessPartnerType } from '../../business-partner/business-partner';
import { EmailTemplate, EmailTemplateService } from '../../settings/email-template.service';
import Swal from 'sweetalert2';


@Component({
  selector: 'app-view-quotation',
  templateUrl: './view-quotation.component.html',
  styleUrls: ['./view-quotation.component.css']
})
export class ViewQuotationComponent implements OnInit {
  @ViewChild('sendQuote') sendQuote!: NgForm;


  quotationData: QuoteHead = {} as QuoteHead;
  quotes: QuoteHead[] = [];
  customers: BusinessPartner[] = [];
  businessPartner: BusinessPartner = new BusinessPartner();
  quotationStatus: any;
  emailTemplates: any[] = [];
  quoteTemplate: any = null;
  subject: string = '';
  content: string = '';
  templateHtmlContent: string = '';
  recipientEmail: string = ''; // Loaded from the database
  TempRecipientEmail: string = '';
  filteredQuotes: QuoteHead[] = [];
  businessEntityId: number = 0;
  entityTradingNames: EntityTradingName[] = [];
  salesItems: SalesItem[] = [];
  allSalesItems: SalesItem[] = [];
  businessEntity: Entity = new Entity();
  isSending: boolean = false;


  entityId: number = 0;
  userId: number = 0;
  constructor(
    private route: ActivatedRoute,
    private quotationService: QuotationService,
    private router: Router,
    private emailTemplateService: EmailTemplateService,
    private entityService: EntityService,
    private businessPartnerService: BusinessPartnerService,
    private storageService: StorageService,
    private swalAlertsService: SwalAlertsService,

  ) {
    this.entityId = this.storageService.getEntityId();
    this.userId = this.storageService.getUserId();
  }


  ngOnInit() {
    this.getBusinessEntityById();
    this.getEntityTradingNamesByEntityId();
    this.loadCustomers();
    this.loadQuotationData();
    this.getAllSalesQuotesHeadList();
  }

  //dropdown
  isDropdownOpen = false;
  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

getBusinessEntityById() {

    this.businessEntityId = this.entityId;

    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(data => {
      this.businessEntity = data;

    }, error => console.error(error));

  }
  
  handleReviseQuotation(id: number) {
    this.quotationService.getSalesQuotesHeadById(id).subscribe(quote => {
      if (quote.status === 'Sent') {
        this.router.navigate(['/revise-quote', id]);
      } else {
        this.swalAlertsService.showWarning("Only quote with status 'Sent' can be revised.", () => {});
      }
    }, error => {
      console.error("Error fetching quote:", error);
      this.swalAlertsService.showErrorDialog("Failed to fetch quote details.");
    });
  }
  
  editQuotation(id: number) {
    this.quotationService.getSalesQuotesHeadById(id).subscribe(quote => {
      if (quote.status === 'Pending') {
        this.router.navigate(['/edit-quotation', id]);
      } else {
        this.swalAlertsService.showWarning("Only quote with status 'Pending' can be edited.", () => {});
      }
    }, error => {
      console.error("Error fetching quote:", error);
      this.swalAlertsService.showErrorDialog("Failed to fetch quote details.");
    });
  }

  
  handleCreateInvoice(id: number) {
    this.quotationService.getSalesQuotesHeadById(id).subscribe(quote => {
      
        this.router.navigate(['/copy-from-quotation', id]);
   
    }, error => {
      console.error("Error fetching quote:", error);
      this.swalAlertsService.showErrorDialog("Failed to fetch quote details.");
    });
  }


  
  handleCopyFromQuote(id: number) {
    this.quotationService.getSalesQuotesHeadById(id).subscribe(quote => {
     
        this.router.navigate(['/copy-quote', id]);
    
    }, error => {
      console.error("Error fetching quote:", error);
      this.swalAlertsService.showErrorDialog("Failed to fetch quote details.");
    });
  }
  


  loadRecipientEmail(): void {
    if (this.quotationData?.businessPartnerId) {
      this.businessPartnerService.getBusinessPartnerById(this.quotationData.businessPartnerId).subscribe(
        (businessPartner) => {
          if (businessPartner?.email) {
            this.recipientEmail = businessPartner.email;
            this.TempRecipientEmail = businessPartner.email;
          } else {
            this.recipientEmail = ''; // Clear if no email found
            this.TempRecipientEmail = '';
            console.warn('No email found for the selected business partner.');
          }
        },
        (error) => {
          console.error('Error fetching recipient email:', error);
          this.recipientEmail = '';
          this.TempRecipientEmail = '';
        }
      );
    }
  }
  /** 
  loadEmailTemplate(): void {
    this.emailTemplateService.getEmailTemplateByEntityId(this.entityId).subscribe(
      (data: EmailTemplate[]) => {
        if (data?.length > 0) {
          this.emailTemplates = data;
          this.quoteTemplate = this.emailTemplates.find(template => template.emailType === 'quote');

          if (this.quoteTemplate && this.quotationData) {
            // Use `this.quotationData` as the selected quote
            this.subject = this.quoteTemplate.subject.replace('${quoteNumber}', this.quotationData.quoteNumber);
            this.loadRecipientEmail();

            // Parse HTML content
            const parser = new DOMParser();
            const htmlDocument = parser.parseFromString(this.quoteTemplate.content, 'text/html');
            this.templateHtmlContent = this.quoteTemplate.content;
            this.content = htmlDocument.body.textContent || "";
          }
        }
      }
    );
  }**/


    


    loadEmailTemplate(): void {
      this.emailTemplateService.getEmailTemplateByEntityId(this.entityId).subscribe(
        (data: EmailTemplate[]) => {
          if (data?.length > 0) {
            this.emailTemplates = data;
            this.quoteTemplate = this.emailTemplates.find(template => template.emailType === 'quote');
    
            if (this.quoteTemplate && this.quotationData) {
              this.subject = this.quoteTemplate.subject.replace('${quoteNumber}', this.quotationData.quoteNumber);
              this.loadRecipientEmail();
    
              // Fetch business partner details before replacing placeholders
              this.businessPartnerService.getBusinessPartnerById(this.quotationData.businessPartnerId).subscribe(
                (businessPartner) => {
                  const businessPartnerName = businessPartner?.bpName || 'Valued Customer';
                  const quoteNumber = this.quotationData.quoteNumber || 'N/A';
    
                  const today = new Date();
                  const currentYear = today.getFullYear().toString();
                  // Replace placeholders in email content
                  this.templateHtmlContent = this.quoteTemplate.content
                    .replace('${businessPartnerName}', businessPartnerName)
                    .replace('${quoteNumber}', quoteNumber)
                    .replace('${currentYear}', currentYear);
    
                  const parser = new DOMParser();
                  const htmlDocument = parser.parseFromString(this.templateHtmlContent, 'text/html');
                  this.content = htmlDocument.body.textContent || "";
                },
                (error) => {
                  console.error('Failed to fetch business partner details:', error);
                  this.templateHtmlContent = this.quoteTemplate.content.replace('${businessPartnerName}', 'Valued Customer');
                }
              );
            }
          }
        }
      );
    }
    
    
    private getAllSalesQuotesHeadList() {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.quotationService.getAllSalesQuotesHeadList(entityId).subscribe(data => {
      this.quotes = data;
      
    });
  }

  @ViewChild('closePreview') closePreview: any;
  @ViewChild('closeSendQuote') closeSendQuote: any;
  sendSelectedQuotes(): void {

      const userString = localStorage.getItem('user');
          if (!userString) {
            // Handle case where user is not found
            return;
          }
      
          const currentUser = JSON.parse(userString);
          const userRole = currentUser.roleName;
          const userId = currentUser.id;
      
          if (userRole === 'Free') {
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth();
            const currentYear = currentDate.getFullYear();
      
            // ✅ Count invoices created by this user with status "Sent" this month
            const sentInvoicesThisMonth = this.quotes.filter(invoice => {
              const createdDate = new Date(invoice.quoteDate);
              return (
                invoice.userId === userId &&
                invoice.status === 'Sent' &&
                createdDate.getMonth() === currentMonth &&
                createdDate.getFullYear() === currentYear
              );
            }).length;
      
            if (sentInvoicesThisMonth >= 5) {
      
              Swal.fire({
                title: 'Limit Reached!',
                text: 'As a Free user, you can only send up to 5 Quotes per month.',
                icon: 'info',
                confirmButtonText: 'Upgrade Plan',
                confirmButtonColor: '#007bff',
              }).then((result) => {
                if (result.isConfirmed) {
      
                  window.location.href = '/manage-subscription';
                }
              });
      
              return;
            }
          }
      
    let selectedQuotes: QuoteHead[] = [];

    if (this.quotationData) {
      // If a quote is loaded in the view, use it as the selected quote
      selectedQuotes = [this.quotationData];
    } else {
      // Otherwise, use manually selected quotes from the list
      selectedQuotes = this.filteredQuotes.filter(quote => quote.selected);
    }

    if (selectedQuotes.length === 0) {
      this.swalAlertsService.showWarning('Please select a quote to send.', () => { });
      return;
    }

    const pendingQuotes = selectedQuotes.filter(quote => quote.status === 'Pending' || quote.status === 'Sent' || quote.status === 'To Invoice');


    if (pendingQuotes.length === 0) {
      this.swalAlertsService.showWarning('No Valid quote selected for sending.', () => { });
      return;
    }

    if (!this.TempRecipientEmail) {
      this.swalAlertsService.showErrorDialog('Please enter a recipient email address.');
      return;
    }

    this.swalAlertsService.showConfirmationDialog(
      'Send Quote',
      `Do you want to send the selected quote to ${this.TempRecipientEmail}?`,
      () => this.processQuoteSending(pendingQuotes)
    );
  }


  private processQuoteSending(pendingQuotes: QuoteHead[]): void {
    this.isSending = true;

    Promise.all(
      pendingQuotes.map(quote => this.businessPartnerService.getBusinessPartnerById(quote.businessPartnerId).toPromise())
    )
      .then(businessPartners => {
        businessPartners.forEach((businessPartner, index) => {
          if (businessPartner) {
            pendingQuotes[index].recipient = businessPartner.email;

                //Dynamically replace placeholders in the template
        this.templateHtmlContent = this.templateHtmlContent
        .replace('${businessPartnerName}', businessPartner.bpName)
        .replace('${quoteNumber}', pendingQuotes[index].quoteNumber);

     
          } else {
            console.warn('Business partner not found for quote:', pendingQuotes[index].quoteId);
          }
        });

        const finalContent = this.templateHtmlContent.replace(
          /<body[^>]*>.*<\/body>/is,
          `<body><p>${this.content.replace(/\n/g, '</p><p>')}</p></body>`
        );

        const emailData = {
          TempRecipientEmail: this.TempRecipientEmail,
          subject: this.subject,
          content: finalContent,
          quotes: pendingQuotes,
        };

        const entityUuid = localStorage.getItem('entityUuid');

          if (!entityUuid) {
            alert('Missing entity UUID.');
            return;
          }

        this.quotationService.sendQuoteWithEmail(emailData, entityUuid).subscribe(
          () => this.handleSuccessfulSend(),
          (error) => this.handleSendError('Failed to send quotes.', error)
        );
      })
      .catch(error => this.handleSendError('Failed to fetch business partner details.', error));
  }

  private handleSuccessfulSend(): void {
    this.swalAlertsService.showSuccessDialog('Success!', 'Quotes sent successfully.', () => {
      this.isSending = false;
      this.closeSendQuote?.nativeElement?.click();
      this.closePreview?.nativeElement?.click();

      //this.fetchQuotations();
      this.router.navigate(['/quotation']);
    });
  }

  private handleSendError(message: string, error?: any): void {
    console.error(message, error);
    this.isSending = false;
    this.swalAlertsService.showErrorDialog(message);
  }



  loadCustomers() {
    this.businessPartnerService.getCustomerListByEntity(this.entityId).subscribe(
      (customers: BusinessPartner[]) => {
        this.customers = customers;
      },
      (error: HttpErrorResponse) => {
        this.handleApiError("Failed to load customers.");
      }
    );
    this.quotationData.businessPartnerId = "";
  }


  /** Entity **/
  getEntityTradingNamesByEntityId() {
    this.businessEntityId = this.entityId;

    this.entityService.getEntityTradingNamesByEntityId(this.businessEntityId).subscribe(data => {
      this.entityTradingNames = data;

    }, error => console.error(error));
  }



  loadQuotationData() {
    const id = +this.route.snapshot.paramMap.get('id')!;
    this.quotationService.getSalesQuotesHeadById(id).subscribe(
      (data: QuoteHead) => {
        this.quotationData = data;
        this.loadQuotationDetails(id);
      },
      (error) => this.handleApiError('Failed to fetch quotation data.', error)
    );
  }

  loadQuotationDetails(id: number) {
    this.quotationData.details = [];
    this.salesItems = [];

    this.quotationService.getQuoteDetailsByQuoteHeadId(id).subscribe(
      (details: QuotationDetail[]) => {
        this.quotationData.details = details;
        this.salesItems = details.map(detail => detail.salesItemId);
      },
      (error) => this.handleApiError('Failed to fetch quotation details.', error)
    );
  }


  calculateTotalDiscount(): number {
    let totalDiscount = 0;

     if (!this.quotationData?.details || this.quotationData.details.length === 0) {
    this.quotationData.totalDiscAmount = 0;
    return 0;
  }
  
    this.quotationData.details.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;
      let discountAmount = 0;

      if (detail.discountType === 'B') {
        discountAmount = itemAmount * (detail.discount / 100);
      } else if (detail.discountType === '$') {
        discountAmount = detail.discount;
      }

      totalDiscount += discountAmount;
    });

    this.quotationData.totalDiscAmount = totalDiscount;
    return totalDiscount;
  }

  handleApiError(errorMessage: string, error: any = null) {
    this.swalAlertsService.showErrorDialog(errorMessage);
  }


}
