* {
  font-family: "Inter", sans-serif !important;
}

p {
  padding: 0;
  margin: 0;
}

.container {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
}

.actions {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.actions h1 {
  flex: 1;
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

table {
  width: 100%;
  margin-bottom: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.table-responsive {
  border-radius: none;
}

.table-head th {
  position: relative;
  padding: 10px;
  background-color: #d7dbf5;
  text-align: left;
  color: rgb(0, 0, 0);
  font-size: 14px;
  font-weight: bold;
}

.table-head th:first-child {
  border-top-left-radius: 10px;
}

.table-head th:last-child {
  border-top-right-radius: 10px;
}

tbody tr {
  background-color: white;
  border-bottom: rgb(221, 221, 221) 1px solid;
  font-size: small;
}

table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 0px;
  overflow: hidden;
}

.tb-head th,
.tb-detail td {
  position: relative;
  font-weight: 500;
  font-size: 14px;
  background-color: #d7dbf5;
  text-align: end;
  padding: 15px 25px;
}

.tb-head th:not(:last-child)::before,
.tb-detail td:not(:last-child)::before {
  position: absolute;
  content: "";
  width: 1px;
  background-color: darkgray;
  height: 10px;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.tb-head th:first-child,
.tb-detail td:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  text-align: start;
}

.tb-head th:last-child,
.tb-detail td:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  text-align: center;
}

.tb-detail {
  border-bottom: 1px solid rgb(237, 237, 237);
}
.tb-detail td {
  background-color: white;
  color: gray;
  font-size: 13px;
}

.mini-transaction-id-label {
  background-color: rgb(100, 100, 100);
  color: white;
  font-size: 12px;
  border-radius: 5px;
  padding: 2px 5px;
}

.mini-credit-label {
  background-color: rgb(30, 167, 30);
  color: white;
  font-size: 12px;
  padding: 2px 5px;
  font-weight: 500;
  border-radius: 5px;
  text-transform: uppercase;
}

.mini-debit-label {
  background-color: red;
  color: white;
  font-size: 12px;
  padding: 2px 5px;
  font-weight: 500;
  border-radius: 5px;
  text-transform: uppercase;
}

.invoice-mini-label {
  background-color: #512ca2;
  color: white;
  font-weight: 500;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
}

.bill-mini-label {
  background-color: #ff0062;
  color: white;
  font-weight: 500;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
}

.receipt-mini-label {
  background-color: #4262ff;
  color: white;
  font-weight: 500;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
}

.voucher-mini-label {
  background-color: #00a2ff;
  color: white;
  font-weight: 500;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
}

.gl-income-mini-label {
  background-color: #9dc000;
  color: white;
  font-weight: 500;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
}

.gl-expense-mini-label {
  background-color: #ff7300;
  color: white;
  font-weight: 500;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
}

.mini-create-button {
  background-color: #00a2ff;
  color: white;
  font-size: 12px;
  padding: 2px 5px;
  font-weight: 500;
  border-radius: 5px;
  text-transform: uppercase;
}

.card {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

label {
  color: rgb(88, 88, 88);
  font-size: 14px;
  margin-bottom: 5px;
}

.custom-input {
  outline: none !important;
  border: 1px solid rgb(200, 200, 200);
  color: rgb(100, 100, 100);
  border-radius: 5px;
  text-align: start;
  font-size: 15px;
  font-weight: 600 !important;
  padding: 7px 12px;
  appearance: textfield !important;
}

.custom-select {
  font-size: 14px !important;
  outline: none;
  min-width: 200px;
  border-radius: 5px;
  border: 1px solid lightgray;
  color: black;
  font-weight: 600;
  padding: 7px 36px 7px 12px;
  background-color: white;

  appearance: none; /* Removes default styling */
  -webkit-appearance: none;
  -moz-appearance: none;

  /* Custom arrow */
  background-image: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L5 5L9 1' stroke='%234262ff' stroke-width='1.5'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 10px 6px;
}

.custom-select option {
  font-weight: 400;
  text-transform: capitalize;
}

.matched-transaction-table {
  width: 100%;
  border: 1px solid lightgray;
  border-collapse: collapse;
}

.matched-transaction-table thead tr th,
.matched-transaction-table tbody tr td {
  font-size: 14px;
  font-weight: 600;
  border: 1px solid lightgray;
  padding: 10px 15px;
}

.matched-transaction-table tbody tr td {
  font-size: 13px;
  font-weight: 500;
  padding: 15px;
}

.bank-transfer-mini-label {
  background-color: #00c0a6;
  color: white;
  font-weight: 500;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
}

.bank-rule-mini-label {
  background-color: #7fecff;
  color: rgb(0, 36, 56);
  font-weight: 500;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
}

.primary-button {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  font-weight: 600;
  padding: 5px 20px;
  cursor: pointer;
  border-radius: 5px;
  border: 1px solid #4262ff;
  font-size: 15px;
}

.primary-button:hover {
  background-color: linear-gradient(to right, #512ca2, #4262ff);
}
