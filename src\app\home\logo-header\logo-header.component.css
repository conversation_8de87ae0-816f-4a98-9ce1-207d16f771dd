/* Company Detail */

.company-detail {
  display: flex;
  align-items: center;
}

.company-detail img {
  height: 40px;
  margin-right: 10px;
}

.company-detail span {
  font-size: 1.2em;
  font-weight: bold;
}

.company-detail .navbar-brand h1 {
  font-size: 1.75em;
  color: #4e54c8;
  margin: 0;
  padding-left: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.navbar-brand {
  font-weight: bold;
  color: #4e54c8;
  display: flex;
  align-items: center;
}

/* Navbar Styles */

.navbar {
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  padding: 10px 0;
}

.navbar ul {
  list-style: none;
  display: flex;
  margin: 0;
  padding: 0;
  gap: 10px;
  width: max-content;
}

.navbar ul li {
  width: 130px; /* Set a fixed width that accommodates longest text */
}

.navbar ul li a {
  text-decoration: none;
  color: #4262ff;
  /* padding: 10px 20px; */
  padding: 0;
  border-radius: 8px;
  background: #d9dfff;
  font-family: Arial, sans-serif;
  font-size: 15px;
  font-weight: 500;
  line-height: 45px;
  text-align: center;
  transition: background-color 0.3s ease, color 0.3s ease;
  display: block;
  width: 100%;
  box-sizing: border-box;
  white-space: nowrap; /* Prevent text wrapping */
}

.navbar ul li a:hover {
  background: #4262ff;
  color: #fff;
  cursor: pointer;
}

.navbar ul li a.active {
  background-color: #007bff;
  color: #fff;
}

/* Dropdown Styles */

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  list-style: none;
  margin: 0;
  padding: 0;
  flex-direction: column;
  align-items: center;
  width: 200px;
  z-index: 1;
  border-radius: 5px;
}

.dropdown-menu.show {
  display: flex;
}

.dropdown-menu li {
  margin: 0;
}

.dropdown-menu .dropdown-list {
  background-color: #fff;
  border-bottom: 1px solid #ddd;
}

.dropdown-menu .dropdown-list .dropdown-list-content {
  text-decoration: none;
  color: #333;
  border-radius: 8px;
  background: white;
  font-family: Arial, sans-serif;
  font-size: 15px;
  font-weight: 500;
  line-height: 22.5px;
  text-align: center;
  display: block;
  margin: 5px 0;
}

.dropdown-menu .dropdown-list .dropdown-list-content:hover {
  background: #4262ff;
  color: #fff;
  cursor: pointer;
}

/* Select Dropdown Styles */

select {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  margin: 5px 0;
  color: black;
}

select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0px 0px 5px rgba(0, 123, 255, 0.5);
}

option {
  padding: 8px;
  background-color: #fff;
  font-size: 14px;
  color: black;
}

option:hover {
  background-color: #f1f1f1;
}

/* Toggle Link */

a {
  color: #333;
  text-decoration: none;
  padding: 8px 16px;
  font-size: 16px;
  transition: color 0.3s ease;
}

a:hover {
  color: #007bff;
}

/* General Dropdown List Hover Effect */

.dropdown-list-content:hover {
  background-color: #007bff;
  color: #fff;
}

.modern-navbar {
  background: #fff;
  box-shadow: 0 2px 16px rgba(66, 98, 255, 0.07);
  padding: 0 32px;
  min-height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modern-company-detail {
  display: flex;
  align-items: center;
}

.modern-navbar-brand {
  display: flex;
  align-items: center;
  gap: 16px;
  text-decoration: none;
}

.modern-entity-logo {
  border-radius: 12px;
  background: #f4f6fa;
  border: 1px solid #e0e0e0;
  box-shadow: 0 1px 8px rgba(66, 98, 255, 0.04);
}

.modern-company-name {
  font-family: Inter, sans-serif;
  font-size: 1.6rem;
  font-weight: 700;
  color: #4262ff;
  margin: 0;
  letter-spacing: 0.5px;
}

.modern-nav-items ul {
  display: flex;
  align-items: center;
  gap: 18px;
  margin: 0;
  padding: 0;
  list-style: none;
}

.modern-nav-link {
  display: flex;
  align-items: center;
  gap: 7px;
  font-family: Inter, sans-serif;
  font-size: 1.08rem;
  font-weight: 500;
  color: #4262ff;
  background: none;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  transition: background 0.18s, color 0.18s;
  cursor: pointer;
  text-decoration: none;
}

.modern-nav-link:hover,
.modern-nav-link:focus {
  background: #f4f6fa;
  color: #512ca2;
  text-decoration: none;
}

@media (max-width: 991px) {
  .modern-navbar {
    flex-direction: column;
    align-items: flex-start;
    padding: 0 10px;
  }
  .modern-nav-items ul {
    gap: 10px;
    flex-wrap: wrap;
  }
  .modern-company-name {
    font-size: 1.2rem;
  }
}

@media screen and (max-width: 1024px) {
  .navbar {
    flex-direction: column;
    align-items: center;
    padding: 10px;
  }

  .company-detail {
    justify-content: center;
    text-align: center;
    margin-bottom: 10px;
  }

  .company-detail .navbar-brand h1 {
    font-size: 1.5em;
  }

  .navbar ul {
    flex-wrap: wrap;
    justify-content: center;
  }

  .navbar ul li {
    margin: 5px 10px;
  }

  .navbar ul li a {
    font-size: 14px;
    padding: 8px 15px;
    line-height: 40px;
  }

  .dropdown-menu {
    width: 100%;
  }
}

@media screen and (max-width: 768px) {
  .navbar {
    flex-direction: column;
    align-items: center;
    padding: 5px;
  }

  .company-detail {
    justify-content: center;
    text-align: center;
    margin-bottom: 10px;
  }

  .company-detail .navbar-brand h1 {
    font-size: 1.3em;
  }

  .navbar ul {
    flex-direction: column;
    text-align: center;
  }

  .navbar ul li {
    margin: 3px 0;
  }

  .navbar ul li a {
    font-size: 13px;
    padding: 6px 12px;
    line-height: 35px;
  }

  .dropdown-menu {
    position: static;
    width: 100%;
  }

  .dropdown-menu.show {
    display: block;
  }
}

@media screen and (max-width: 480px) {
  .navbar {
    padding: 5px;
  }

  .company-detail {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .company-detail .navbar-brand h1 {
    font-size: 1.1em;
  }

  .navbar ul li a {
    font-size: 12px;
    padding: 5px 10px;
    line-height: 30px;
  }

  .dropdown-menu {
    width: 100%;
    text-align: center;
  }
}