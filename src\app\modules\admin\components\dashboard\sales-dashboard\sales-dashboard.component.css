* {
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  flex: 1;
    margin-bottom: 20px;
    font-family: Inter;
    font-size: 36px;
    font-weight: 700;
    text-align: left;
    color: #4262ff;
}

.card {
  background: white;
  border-radius: 24px;
  box-shadow: 0px 1px 20px 15px #4262ff1a;
  padding: 20px;
  flex: 1 1 48%;
  margin-bottom: 20px;
}

.card-wrapper {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: space-between;
}

.read-only-card {
  background: #F4F6FF;
  pointer-events: none;
  cursor: default;
}

.card h3 {
  margin-top: 0;
  font-size: 1.2rem;
  color: #4262ff;
}

.divider {
  height: 1px;
  background: #e1e4e8;
  margin: 0 0 15px;
}

.table-responsive {
  width: 100%;
  border-collapse: collapse;
}

.table-responsive th,
.table-responsive td {
  text-align: left;
  padding: 6px 0;
  font-size: 0.95rem;
}

.table-responsive th {
  color: #888;
  font-weight: normal;
}

/* .table-responsive td:last-child {
  text-align: right;
} */

.table-responsive td {
  color: #2c3e50;
}
