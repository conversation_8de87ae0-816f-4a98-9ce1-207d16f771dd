import { Component, OnInit } from '@angular/core';
import { UserService } from '../../admin/components/user/user.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { EntityService } from '../entity.service';
import { InviteLog } from '../entity';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-invite-entity',
  templateUrl: './invite-entity.component.html',
  styleUrls: ['./invite-entity.component.css'],
})
export class InviteEntityComponent implements OnInit {
  recipientEmail: string = '';
  inviteLog: InviteLog = new InviteLog();
  entityInviteForm: FormGroup;
  isUsernameExits: boolean = false;
  usernameExistsMessage: string = '';

  constructor(
    private userService: UserService,
    private fb: FormBuilder,
    private router: Router,
    private entityService: EntityService
  ) {
    this.entityInviteForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
    });
  }
  ngOnInit(): void {
    if (this.entityInviteForm.invalid || this.isUsernameExits) {
      this.markFormGroupTouched(this.entityInviteForm);
      return;
    }
  }

  onSubmit(event: Event): void {
    event.preventDefault();
    if (this.entityInviteForm.invalid) {
      return;
    }
    this.inviteLog.email = this.recipientEmail;
    this.inviteLog.status = 'PENDING';
    this.inviteLog.userId.userId = Number(localStorage.getItem('userid'));
    this.inviteLog.entityId = Number(localStorage.getItem('entityId'));
    this.entityService.saveEntityInviteLog(this.inviteLog).subscribe(
      (response) => {
        this.inviteLog = response;
        this.entityService.inviteEntity(this.inviteLog.inviteLogId).subscribe(
          (response) => {
            Swal.fire({
              title: 'Invitation Sent!',
              text: 'Entity invitation has been sent successfully.',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            }).then(() => {
              this.router.navigate(['/dashboard']);
            });
          },
          (error) => {
            Swal.fire({
              icon: 'error',
              title: 'Invitation Failed',
              text: 'We could not send the invitation. Please try again later.',
            });
          }
        );
      },
      (error) => {
        Swal.fire({
          icon: 'error',
          title: 'Log Save Failed',
          text: 'The invitation was sent, but we could not save the log.',
        });
      }
    );
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      control.markAsDirty();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
  checkUser() {
    if (this.entityInviteForm.get('email')?.valid) {
      const email = this.entityInviteForm.get('email')?.value;
      this.userService.checkUser(email).subscribe((response) => {
        if (response) {
          this.isUsernameExits = true;
          this.usernameExistsMessage =
            'Email is already in the system. Please enter another.';
        } else {
          this.isUsernameExits = false;
          this.usernameExistsMessage = '';
        }
      });
    }
  }
}
