.form-container {
  font-family: "Inter";
  background-color: transparent;
  display: flex;
  justify-content: center;
  /* height: 100vh; */
  padding-block: 10px;
}

.user-creation-form {
  background: #fff;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  min-width: 300px;
  height: auto;
  text-align: center;
  margin-top: 20px;
}

.form-container .user-name {
  display: flex;
  gap: 20px;
}

.user-creation-form h2 {
  margin-bottom: 30px;
  font-family: Inter;
  font-size: 30px;
  font-weight: 600;
  text-align: left;
  color: #535353;
}

.user-creation-form .invitation {
  color: #4262ff;
  margin-bottom: 40px;
  font-family: Inter;
  font-size: 17px;
  font-weight: 600;
  line-height: 38.73px;
  text-align: left;
}

.user-creation-form .form-group {
  margin-bottom: 20px;
  text-align: left;
  font-family: Inter;
  font-size: 15px;
  font-weight: 600;
  line-height: 25.41px;
  color: #444343;
  position: relative;
  flex: 1;
}

.user-creation-form .form-group label {
  display: block;
  margin-bottom: 7px;
  font-weight: bold;
}

.user-creation-form .form-group input,
.user-creation-form .form-group select {
  width: 100%;
  padding: 7px;
  border: 2px solid #c7c7c7;
  border-radius: 8px;
  box-sizing: border-box;
  font-size: 1em;
  font-family: Inter;
  margin-bottom: 8px;
}

.user-creation-form .form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
}

.user-creation-form .form-actions button {
  padding: 10px 40px;
  font-size: 16px;
  cursor: pointer;
  border-radius: 12px;
  font-family: Inter;
  font-weight: 700;
  text-align: center;
}

.user-creation-form .form-actions .cancel {
  background: transparent;
  color: #4262ff;
  border: #4262ff 2px solid;
  margin-left: 245px;
}

.user-creation-form .form-actions .send-invite {
  background: linear-gradient(to right, #4262ff, #512ca2);
  border: none;
  color: #fff;
}

.user-creation-form .form-actions .send-invite:hover {
  background: linear-gradient(to left, #4262ff, #512ca2);
}

.user-creation-form .form-actions .cancel:hover {
  background: #4262ff;
  color: #fff;
}

@media (max-width: 599px) {
  .user-creation-form .form-actions .cancel {
      background: transparent;
      color: #4262ff;
      border: #4262ff 2px solid;
      margin-left: 70px;
    }
}