<div class="modal-dialog" style="width: 30%">
    <div class="modal-content">
        <div class="ot-modal-header">
            <button type="button" class="custom-close-btn" data-bs-dismiss="modal" style="margin-left: 95%"
                (click)="navigateToPayrollSettings()">
                <i class="bi bi-x-circle"></i>
            </button>
            <i class="bi bi-person-workspace" style="font-size: 70px; color: #4262ff"></i>
            <div class="modal-header">
                <h5 class="modal-title" id="add_pay_calendar" style="margin-left: 30%">
                    Ordinary Time Earnings
                </h5>
            </div>
        </div>
        <div class="modal-body">
        <form>
            <div class="mb-3">
                <label for="earnings-name" class="form-label">Earnings Name</label>
                <input name="earningsName" type="text" class="form-control" id="earnings-name" [(ngModel)]="earning.earningsName" />
              </div>
              <div class="mb-3">
                <label for="dis-name" class="form-label">Display Name (optional)</label>
                <input name="earningsDisplayName" type="text" class="form-control" id="dis-name" [(ngModel)]="earning.displayName" />
              </div>
              <div class="mb-3">
                <label for="rate" class="form-label">Rate</label>
                <select name="earningsRate" class="form-control" id="rate" [(ngModel)]="earning.rate">
                  <option [ngValue]="0.5">0.5</option>
                  <option [ngValue]="0.75">0.75</option>
                  <option [ngValue]="1.0">1.0</option>
                </select>
              </div>
              <div class="mb-3">
                <label for="unit-types" class="form-label">Types of Units (e.g Hours)</label>
                <input name="earningsTypeOfUnit" type="text" class="form-control" id="unit-types" [(ngModel)]="earning.typeOfUnits" />
              </div>
              <div class="mb-3">
                <label for="Category" class="form-label">Category</label>
                <input name="earningsCategory" type="text" class="form-control" id="Category" [(ngModel)]="earning.category" />
              </div>
              <div class="mb-3">
                <label for="gl-account" class="form-label">GL Account</label>
                <select name="earningsGlAccount" class="form-control" id="gl-account" [(ngModel)]="earning.glAccount">
                  <option value="" disabled>Select a GL Account</option>
                  <option *ngFor="let account of glAccounts" [value]="account">{{ account }}</option>
                </select>
              </div>

                <!-- <div class="mb-3">
              <label for="wi" class="form-label">WI (Reportable)</label>
              <select
                name="earningsWIReportable"
                class="form-control"
                id="wi"
                [(ngModel)]="earning.wiReportable"
              >
                <option value="">None</option>
              </select>
            </div> -->
            <div class="mb-3" style="margin-top: 20px; margin-left: 20px">
                <input type="checkbox" [(ngModel)]="earning.exemptFromPAYG" name="exemptFromPAYG" />
                <strong style="margin-left: 10px">Exempt from PAYG withholding</strong>
                <br />
                <input type="checkbox" [(ngModel)]="earning.exemptFromSuperannuation" name="exemptFromSuperannuation" />
                <strong style="margin-left: 10px">Exempt from Superannuation Guarantee Contribution</strong>
                <br />
                <input type="checkbox" [(ngModel)]="earning.wiReportable" name="wiReportable" />
                <strong style="margin-left: 10px">WI Reportable</strong>
              </div>
            </form>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                    (click)="navigateToPayrollSettings()">
                    Cancel
                </button>
                <button type="button" class="btn btn-primary" (click)="updateEarning()">
                    Add
                </button>
            </div>
        </div>
    </div>
</div>