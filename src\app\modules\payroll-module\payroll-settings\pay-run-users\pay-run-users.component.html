<app-sales-navigation></app-sales-navigation>
<div class="payrun">

    <div class="heading">
        PayRoll Users
    </div>

    <div class="menu-row">

        <div class="menu-sub-col">
            <div class="menu-topic">Employers</div>
            <div class="menu-value">3000</div>
        </div>
        
        <div class="line"></div>

        <div class="menu-sub-col">
            <div class="menu-topic">Earning</div>
            <div class="menu-value">3000</div>
        </div>

        <div class="line"></div>

        <div class="menu-sub-col">
            <div class="menu-topic">PAYG</div>
            <div class="menu-value">3000</div>
        </div>

        <div class="line"></div>

        <div class="menu-sub-col">
            <div class="menu-topic">Superannuation</div>
            <div class="menu-value">3000</div>
        </div>

        <div class="line"></div>

        <div class="menu-sub-col">
            <div class="menu-topic">Net Pay</div>
            <div class="menu-value">3000</div>
        </div>

        <!-- <div class="line"></div> -->

        <!-- <div class="menu-sub-col">
            <div class="menu-topic">Status</div>
            <div class="menu-value">3000</div>
        </div> -->
    </div>

    <div class="payroll-info">
        <div class="payroll-tabs">
            <ul class="nav nav-tabs">
                <li class="nav-item">
                    <a class="nav-link" [class.active]="activeTab === 'DraftPayRun'"
                        (click)="setActiveTab('DraftPayRun')">Draft PayRun</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" [class.active]="activeTab === 'AllPayRuns'" (click)="setActiveTab('AllPayRuns')">
                        All PayRuns
                    </a>
                </li>
            </ul>
        </div>
    </div>


    



<div class="pay-items-sections" *ngIf="isActiveTab('DraftPayRun')">
    <!-- Sidebar -->
    <div class="side-bar">
        <ul class="nav nav_pay_item">
            <li>
                <a href="#" [class.active]="isActiveSides('DraftPayRun')"
                    (click)="setActiveSides('DraftPayRun'); $event.preventDefault()">
                    Draft Pay Run
                </a>
            </li>
            <li>
                <a href="#" [class.active]="isActiveSides('PayRunHistory')"
                    (click)="setActiveSides('PayRunHistory'); $event.preventDefault()">
                    Pay Run History
                </a>
            </li>
            <li>
                <a href="#" [class.active]="isActiveSides('ReviewPayRun')"
                    (click)="setActiveSides('ReviewPayRun'); $event.preventDefault()">
                    Review Pay Run
                </a>
            </li>
            <li>
                <a href="#" [class.active]="isActiveSides('LeaveLoading')"
                    (click)="setActiveSides('LeaveLoading'); $event.preventDefault()">
                    Leave Loading
                </a>
            </li>
        </ul>

    </div>

    <!-- Main Content -->
    <div class="Leave" *ngIf="isActiveSides('DraftPayRun')">
        <div class="pay_item_head">
            <h2>Draft PayRun</h2>
            <div class="btn-group">
            </div>
        </div>
        <!-- <button>hiran</button> -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Pay Calendar</th>
                        <th>Pay Period</th>
                        <th>Payment Date</th>
                        <th>wages</th>
                        <th>Tax</th>
                        <th>Superannuation</th>
                        <th>Net Pay</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2024-12-01</td>
                        <td>Monthly</td>
                        <td>2024-12-18</td>
                        <td>pay</td>
                        <td>21.00</td>
                        <td>high</td>
                        <td>23000</td>
                    </tr>

                    <tr>
                        <td>2024-12-01</td>
                        <td>Monthly</td>
                        <td>2024-12-18</td>
                        <td>pay</td>
                        <td>21.00</td>
                        <td>high</td>
                        <td>23000</td>
                    </tr>

                    <tr>
                        <td>2024-12-01</td>
                        <td>Monthly</td>
                        <td>2024-12-18</td>
                        <td>pay</td>
                        <td>21.00</td>
                        <td>high</td>
                        <td>23000</td>
                    </tr>

                    <tr>
                        <td>2024-12-01</td>
                        <td>Monthly</td>
                        <td>2024-12-18</td>
                        <td>pay</td>
                        <td>21.00</td>
                        <td>high</td>
                        <td>23000</td>
                    </tr>
                </tbody>
            </table>

            <pagination-controls class="d-flex justify-content-end" >
            </pagination-controls>

            <button class="btn btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#AddPayRun">Add Pay
                Run</button>
        </div>
    </div>
</div>


<div class="pay-items-sections" *ngIf="isActiveTab('AllPayRuns')">
    <!-- Sidebar -->
    <div class="side-bar">
        <ul class="nav nav_pay_item">
            <li>
                <a href="#" [class.active]="isActiveSides('DraftPayRun')"
                    (click)="setActiveSides('DraftPayRun'); $event.preventDefault()">
                    Draft Pay Run
                </a>
            </li>
            <li>
                <a href="#" [class.active]="isActiveSides('PayRunHistory')"
                    (click)="setActiveSides('PayRunHistory'); $event.preventDefault()">
                    Pay Run History
                </a>
            </li>
            <li>
                <a href="#" [class.active]="isActiveSides('ReviewPayRun')"
                    (click)="setActiveSides('ReviewPayRun'); $event.preventDefault()">
                    Review Pay Run
                </a>
            </li>
            <li>
                <a href="#" [class.active]="isActiveSides('LeaveLoading')"
                    (click)="setActiveSides('LeaveLoading'); $event.preventDefault()">
                    Leave Loading
                </a>
            </li>
        </ul>

    </div>

    <!-- Main Content -->
    <div class="Leave" *ngIf="isActiveSides('DraftPayRun')">
        <div class="pay_item_head">
            <h2>All PayRuns</h2>
            <div class="btn-group">
            </div>
        </div>
        <!-- <button>hiran</button> -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Pay Calendar</th>
                        <th>Pay Period</th>
                        <th>Payment Date</th>
                        <th>wages</th>
                        <th>Tax</th>
                        <th>Superannuation</th>
                        <th>Net Pay</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2024-12-01</td>
                        <td>Monthly</td>
                        <td>2024-12-18</td>
                        <td>pay</td>
                        <td>21.00</td>
                        <td>high</td>
                        <td>23000</td>
                    </tr>

                    <tr>
                        <td>2024-12-01</td>
                        <td>Monthly</td>
                        <td>2024-12-18</td>
                        <td>pay</td>
                        <td>21.00</td>
                        <td>high</td>
                        <td>23000</td>
                    </tr>

                    <tr>
                        <td>2024-12-01</td>
                        <td>Monthly</td>
                        <td>2024-12-18</td>
                        <td>pay</td>
                        <td>21.00</td>
                        <td>high</td>
                        <td>23000</td>
                    </tr>

                    <tr>
                        <td>2024-12-01</td>
                        <td>Monthly</td>
                        <td>2024-12-18</td>
                        <td>pay</td>
                        <td>21.00</td>
                        <td>high</td>
                        <td>23000</td>
                    </tr>
                </tbody>
            </table>

            <pagination-controls class="d-flex justify-content-end">
            </pagination-controls>

            <button class="btn btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#AddPayRun">Add Pay
                Run</button>
        </div>
    </div>
</div>

</div>