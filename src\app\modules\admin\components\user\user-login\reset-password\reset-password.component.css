* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Inter", sans-serif;
  background-color: #1a1a1a;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  color: #fff;
  background-image: url("/assets/images/MacBook Air - 1.png");
  background-size: cover;
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.form {
  background-color: #fff;
  padding: 2rem;
  border-radius: 20px;
  width: 450px;
  min-height: 350px;
  text-align: center;
  box-shadow: 0px 1px 20px 15px rgba(66, 98, 255, 0.1);
  margin-bottom: 150px;
}

h2 {
  font-family: "Segoe UI", sans-serif;
  font-size: 30px;
  font-weight: 700;
  color: #000000;
  text-align: left;
  margin-bottom: 10px;
}

p {
  color: #a7a7a7;
  text-align: left;
  margin-bottom: 2rem;
  font-size: 14px;
}

p a {
  color: #4262ff;
  text-decoration: none;
  font-weight: bold;
  cursor: pointer;
}

p a:hover {
  text-decoration: underline;
}

.input-group {
  text-align: left;
  /* margin-bottom: 1.5rem; */
}

.input-group label {
  display: block;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  color: #000000;
}

.input-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #a7a7a7;
  border-radius: 9px;
  font-size: 15px;
  font-weight: 500;
  color: #000000;
  background-color: #f9f9f9;
  margin-bottom: 20px;
}

.input-group input::placeholder {
  color: #a7a7a7;
}

button {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 9px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  color: #fff;
  background: linear-gradient(to right, #4262ff, #512ca2);
  transition: background 0.3s ease;
}

button:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

@media (max-width: 600px) {
  .form {
    width: 90%;
    padding: 1.5rem;
  }

  h2 {
    font-size: 24px;
  }

  button {
    font-size: 14px;
  }
}
