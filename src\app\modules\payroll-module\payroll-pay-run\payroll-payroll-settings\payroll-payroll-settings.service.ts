import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { PayrollConfig } from '../../payroll-settings/payroll-setting';

@Injectable({
  providedIn: 'root'
})
export class PayrollPayrollSettingsService {
  private readonly baseURL = environment.payrollApiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }
  
  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  savePayrollConfig(config: PayrollConfig | any): Observable<PayrollConfig> {
    return this.request('POST', '/payrollConfig/save', config);
  }

  getPayrollConfigByEntity(entityId: number): Observable<PayrollConfig> {
    return this.request('GET', `/payrollConfig/${entityId}`, null);
  }
  

  updatePayrollConfig(id: number, payrollConfig: PayrollConfig): Observable<any> {
    return this.request('PUT', `/payrollConfig/update/${id}`, payrollConfig);

}



  
  
}

