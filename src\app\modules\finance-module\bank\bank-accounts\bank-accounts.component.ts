import { Component } from '@angular/core';
import { BankService } from '../bank.service';
import { BankAccount } from '../bank';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';
import { Router } from '@angular/router';
import { NgForm } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { BasiqService } from 'src/app/modules/settings/basiq.service';

@Component({
  selector: 'app-bank-accounts',
  templateUrl: './bank-accounts.component.html',
  styleUrls: ['./bank-accounts.component.css']
})
export class BankAccountsComponent {

   bankAccount: BankAccount = new BankAccount();
   basiqAccounts: any[] = [];
   selectedBasiqAccountId: string | null = null;

  constructor(
    private bankService: BankService,
    private swalAlertsService: SwalAlertsService,
    private basiqConnectService: BasiqService,
    private router: Router,
  ) {}

  ngOnInit(): void { 
    this.loadBasiqAccounts();
  }

  loadBasiqAccounts() {
  const entityId = +localStorage.getItem('entityId')!;
  this.basiqConnectService.getAccounts(entityId).subscribe({
    next: (data) => {
      this.basiqAccounts = data;
    },
    error: (err) => {
      console.error('Error loading Basiq accounts', err);
    }
  });
}

onBasiqAccountSelected() {
  const selected = this.basiqAccounts.find(acc => acc.id === this.selectedBasiqAccountId);
  if (selected) {
    this.bankAccount.bankName = selected.institution?.name || '';
    this.bankAccount.accountName = selected.name;
    this.bankAccount.accountNumber = selected.accountNo;
    this.bankAccount.balance = selected.balance || 0;
    this.bankAccount.basiqAccountId = selected.id;
    this.bankAccount.basiqConnectionId = selected.connection;
  }
}

  submit(form: NgForm) {
  if (form.invalid) {
    form.control.markAllAsTouched();
    return;
  }

  const userId = +localStorage.getItem('userid')!;
  const entityId = +localStorage.getItem('entityId')!;

  this.bankAccount.entityId = entityId;
  this.bankAccount.userId = userId;
  this.bankAccount.accountType = 'PRIMARY'; // You can make this dynamic if needed
  this.bankAccount.balanceType = 'DEBIT';   // Or 'CREDIT' depending on context

  this.bankService.saveBankAccount(this.bankAccount).subscribe(
    (res) => {
      this.swalAlertsService.showSuccessDialog(
        'Success!',
        'Bank account saved successfully.',
        () => this.router.navigate(['/gl-account'])
      );
    },
    (err: HttpErrorResponse) => {
      this.swalAlertsService.showErrorWithChimpSupport(
        'Failed to save bank account.',
        'Why is saving a bank account failing in the banking module?'
      );
    }
  );
}

 onCancel() {
    this.router.navigate(['/bank-accounts']);
  }
}