<app-payroll-nevigation></app-payroll-nevigation>

<div class="container">

  <!-- Section: Employee Detail Report -->
  <div class="actions sub-container">
    <h2>Employee Detail Report</h2>
  </div>

  <div class="table-responsive">
    <div class="modal-body">
      <form>
        <div class="mb-3">
          <label for="pay-calendar" class="form-label2">Select Payroll Calendar</label>
          <select
            name="payCalendar"
            class="form-control"
            id="pay-calendar"
            [(ngModel)]="selectedPayPeriod"
            (change)="onPayPeriodChange(selectedPayPeriod)">
            <option [ngValue]="null" disabled selected>Select a Payroll Calendar</option>
            <option [ngValue]="'ALL'">All Employees</option>
            <option *ngFor="let period of openPayPeriods" [ngValue]="period">
              {{ period.payCalendar.calendarName + " : " + period.payStartDate + " - " + period.nextPayDate }}
            </option>
          </select>
        </div>
      </form>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="navigateToPayrollPayRun()">
          Cancel
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          [disabled]="!selectedPayPeriod" 
          data-bs-toggle="modal" 
          data-bs-target="#simpleModal" 
          (click)="generateReport()">
          Employee Details
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          [disabled]="!selectedPayPeriod" 
          (click)="navigateToEmailPayslip()">
          Email PaySlip
        </button>
      </div>

      <!-- Section: PayRun Summary Date Range -->
      <div class="actions sub-container mt-4 mb-3">
        <h2>Generate PayRun Summary for a Date Range</h2>
      </div>

      <div class="date-picker-group">
        <div class="mb-3">
          <label class="form-label2">From Date</label>
          <input type="date" class="form-control input-width" [(ngModel)]="fromDate">
        </div>
        <div class="mb-3">
          <label class="form-label2">To Date</label>
          <input type="date" class="form-control input-width" [(ngModel)]="toDate">
        </div>
      </div>

      <div class="modal-footer justify-end">
        <button 
          type="button" 
          class="btn btn-primary"
          [disabled]="!fromDate || !toDate"
          data-bs-toggle="modal" 
          data-bs-target="#payRunSummaryModal" 
          (click)="filterPayRuns()">
          Generate PayRun Summary
        </button>
      </div>
    </div>
  </div>

  <!-- Report Preview Modal -->
  <div class="modal fade" id="simpleModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered custom-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="simpleModalLabel">Employee Detail Report</h5>
          <button type="button" class="btn-close" aria-label="Close" data-bs-dismiss="modal">
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <div *ngIf="isLoading" class="spinner-container">
            <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>
          <div style="margin-top: 20px;" [hidden]="isLoading">
            <iframe #reportPreviewFrame id="reportPreviewFrame" width="700px" height="700px"></iframe>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- PayRun Summary Report Preview Modal -->
  <div class="modal fade" id="payRunSummaryModal" tabindex="-1" role="dialog" aria-labelledby="payRunSummaryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered custom-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="payRunSummaryModalLabel">PayRun Summary Report</h5>
          <button type="button" class="btn-close" aria-label="Close" data-bs-dismiss="modal">
            <i class="bi bi-x-circle"></i>
          </button>
        </div>
        <div class="modal-body">
          <div *ngIf="isLoadingSummary" class="spinner-container">
            <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>
          <div style="margin-top: 20px;" [hidden]="isLoadingSummary">
            <iframe #payRunSummaryFrame id="payRunSummaryFrame" width="700px" height="700px"></iframe>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
