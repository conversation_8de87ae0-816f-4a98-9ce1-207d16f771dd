{"version": "0.2.0", "configurations": [{"name": "ng serve", "type": "chrome", "request": "launch", "preLaunchTask": "npm: start", "url": "http://localhost:4200/"}, {"name": "ng test", "type": "chrome", "request": "launch", "preLaunchTask": "npm: test", "url": "http://localhost:9876/debug.html"}, {"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/Users/<USER>/Documents/GitHub/Navitsa/LedgerChimp/front-end/src/app/modules/finance-module/gl-posting/gl-posting-list", "program": "c:/Users/<USER>/Documents/GitHub/Navitsa/LedgerChimp/front-end/src/app/modules/finance-module/gl-posting/gl-posting-list/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}