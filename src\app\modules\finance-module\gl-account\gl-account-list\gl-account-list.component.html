<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container mt-4">
  <!-- Action Buttons and Table Content -->
  <div class="actions d-flex justify-content-between align-items-center mb-3">
    <h3>GL Accounts</h3>
    <!-- <div class="btn-group">
            <button type="button" class="btn btn-secondary dropdown-toggle gradient-btn" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-three-dots-vertical"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="#">Print</a></li>
                <li><a class="dropdown-item" href="#">Edit</a></li>
                <li><a class="dropdown-item" href="#">Delete</a></li>
            </ul>
        </div> -->
  </div>

  <!-- Search and Create Button -->
  <div class="search-create">
    <div class="input-container">
      <input
        type="text"
        class="form-control search-input"
        placeholder="Search from any field"
        [(ngModel)]="searchTerm"
      />
      <i class="bi bi-search position-absolute top-50 end-0 translate-middle-y me-3"></i>
    </div>
    <div class="button-container">
      <button
        data-bs-toggle="modal"
        data-bs-target="#addGLAccountModal"
      >
        Add New GL Account
      </button>
      <button (click)="exportToExcel()">Export to Excel</button>
    </div>
  </div>

  <!-- Table -->
  <div class="table-responsive">
    <table class="table table-hover">
      <thead>
        <tr class="table-head">
          <!-- <th scope="col" class="valueCheckbox">
                        <input type="checkbox" (change)="toggleSelectAll($event)" />
                    </th> -->
          <th scope="col" class="valuehead">Type</th>
          <th scope="col" class="valuehead">Code</th>
          <th scope="col" class="valuehead">Acount Name</th>
          <th scope="col" class="valuehead">Default Tax Code</th>
          <th scope="col" class="valuehead">Status</th>
          <th scope="col" class="valuehead">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let account of filteredAccounts">
          <!-- <td class="valueCheckbox">
                        <input type="checkbox" [(ngModel)]="account.selected" />
                    </td> -->
          <td class="value">{{ account.coaHeaderId.accountHeaderType }}</td>
          <td class="value">{{ account.ledgerAccountCode }}</td>
          <td class="value">{{ account.ledgerAccountName }}</td>
          <td class="value">{{ account.defaultTaxCode }}</td>
          <td class="value">{{ account.status }}</td>
          <td class="value">
            <!-- <button class="btn btn-sm btn-primary me-2" data-bs-toggle="modal" data-bs-target="#editGLAccountModal" (click)="editCoaLedgerAccount(account)">
                            <i class="bi bi-pencil-square"></i> Edit
                        </button> -->
            <button
              data-bs-toggle="modal"
              data-bs-target="#editGLAccountModal"
              (click)="editCoaLedgerAccount(account)"
              class="btn btn-orange btn-sm"
              style="
                margin-right: 2px;
                border: none;
                background: none;
                padding: 2px;
                font-size: 1rem;
              "
              title="Edit"
            >
              <i class="ri-edit-box-line" style="color: #4262ff"></i>
            </button>
            <!-- <button class="btn btn-sm btn-danger" (click)="deleteGlAccount(account.coaLedgerAccountId)">
                            <i class="bi bi-trash"></i> Delete
                        </button> -->
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!-- Add New GL Account Modal -->
<div
  class="modal fade"
  id="addGLAccountModal"
  tabindex="-1"
  aria-labelledby="addGLAccountModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <form
        #coaLedgerAccountForm="ngForm"
        (ngSubmit)="coaLedgerAccountForm.form.valid && addCoaLedgerAccount()"
        class="row g-1"
        novalidate="feedback-form"
      >
        <div class="modal-header">
          <h5 class="modal-title" id="addGLAccountModalLabel">
            Add New GL Account
          </h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            #closeaddGLAccountPopUp
          ></button>
        </div>
        <div class="modal-body">
          <!-- Account Type -->
          <div class="mb-3">
            <label for="accountHeader" class="form-label">Account Type</label>
            <select
              class="form-select"
              id="accountHeader"
              [(ngModel)]="coaLedgerAccount.coaHeaderId.coaHeaderId"
              name="coaHeaderId"
              (change)="setLedgerAccountCode($event)"
              required
            >
              <option value="" disabled selected>Select Account Type</option>
              <option
                *ngFor="let header of coaHeaders"
                [value]="header.coaHeaderId"
              >
                {{ header.accountHeaderName }}
              </option>
            </select>

            <div
              *ngIf="
                coaLedgerAccountForm.submitted &&
                coaLedgerAccountForm.controls['coaHeaderId'].invalid
              "
              class="text-danger"
            >
              <div
                *ngIf="coaLedgerAccountForm.controls['coaHeaderId'].errors?.['required']"
              >
                Select Account Type
              </div>
            </div>
          </div>

          <!-- Code -->
          <div class="mb-3">
            <label for="ledgerAccountCode" class="form-label">Code</label>
            <input
              type="text"
              class="form-control"
              id="ledgerAccountCode"
              [(ngModel)]="coaLedgerAccount.ledgerAccountCode"
              name="ledgerAccountCode"
              required
              readonly
            />

            <div
              *ngIf="
                coaLedgerAccountForm.submitted &&
                coaLedgerAccountForm.controls['ledgerAccountCode'].invalid
              "
              class="text-danger"
            >
              <div
                *ngIf="coaLedgerAccountForm.controls['ledgerAccountCode'].errors?.['required']"
              >
                Code is required.
              </div>
            </div>
          </div>

          <!-- Remaining Fields -->
          <div class="mb-3">
            <label for="ledgerAccountName" class="form-label">Name</label>
            <input
              type="text"
              class="form-control"
              id="ledgerAccountName"
              [(ngModel)]="coaLedgerAccount.ledgerAccountName"
              name="ledgerAccountName"
              required
            />

            <div
              *ngIf="
                coaLedgerAccountForm.submitted &&
                coaLedgerAccountForm.controls['ledgerAccountName'].invalid
              "
              class="text-danger"
            >
              <div
                *ngIf="
                    coaLedgerAccountForm.controls['ledgerAccountName'].errors?.['required']
                  "
              >
                Ledger Account Name is required.
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="ledgerAccountDescription" class="form-label"
              >Description</label
            >
            <textarea
              class="form-control"
              id="ledgerAccountDescription"
              rows="3"
              [(ngModel)]="coaLedgerAccount.ledgerAccountDescription"
              name="ledgerAccountDescription"
              required
            ></textarea>

            <div
              *ngIf="
                coaLedgerAccountForm.submitted &&
                coaLedgerAccountForm.controls['ledgerAccountDescription']
                  .invalid
              "
              class="text-danger"
            >
              <div
                *ngIf="
                    coaLedgerAccountForm.controls['ledgerAccountDescription'].errors?.['required']
                  "
              >
                Description is required.
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="defaultTaxCode" class="form-label">Tax Code</label>
            <select
              class="form-select"
              id="defaultTaxCode"
              [(ngModel)]="coaLedgerAccount.defaultTaxCode"
              name="defaultTaxCode"
              required
            >
              <option value="" disabled selected>Select Tax Code</option>
              <option value="Exclude From BAS">Exclude From BAS</option>
              <option value="GST on Income">GST on Income</option>
              <option value="GST on Expenses">GST on Expenses</option>
              <option value="GST Free Income">GST Free Income</option>
              <option value="GST Free Expenses">GST Free Expenses</option>
            </select>
          
            <div
              *ngIf="
                coaLedgerAccountForm.submitted &&
                coaLedgerAccountForm.controls['defaultTaxCode'].invalid
              "
              class="text-danger"
            >
              <div
                *ngIf="
                  coaLedgerAccountForm.controls['defaultTaxCode'].errors?.['required']
                "
              >
                Tax Code is required.
              </div>
            </div>
          </div>
          
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            #closeaddGLAccountPopUp
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="submit" class="btn btn-primary">Add</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Edit GL Account Modal -->
<div
  class="modal fade"
  id="editGLAccountModal"
  tabindex="-1"
  aria-labelledby="editGLAccountModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <form
        *ngIf="updateCoaLedgerAccount"
        #updateCoaLedgerAccountForm="ngForm"
        (ngSubmit)="updateCoaLedgerAccountForm.form.valid && updateGlAccount()"
        class="row g-1"
        novalidate="feedback-form"
      >
        <div class="modal-header">
          <h5 class="modal-title" id="editGLAccountModalLabel">
            Modify GL Account
          </h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            #closeUpdateGLAccountPopUp
          ></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label for="accountHeader" class="form-label">Account Type</label>
            <select
              class="form-select"
              id="accountHeader"
              [(ngModel)]="updateCoaLedgerAccount.coaHeaderId.coaHeaderId"
              name="coaHeaderId"
              required
              disabled
            >
              <option value="" disabled selected>Select Account Type</option>
              <option
                *ngFor="let header of coaHeaders"
                [value]="header.coaHeaderId"
              >
                {{ header.accountHeaderName }}
              </option>
            </select>

            <div
              *ngIf="
                updateCoaLedgerAccountForm.submitted &&
                updateCoaLedgerAccountForm.controls['coaHeaderId'].invalid
              "
              class="text-danger"
            >
              <div
                *ngIf="updateCoaLedgerAccountForm.controls['coaHeaderId'].errors?.['required']"
              >
                Select Account Type
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="ledgerAccountCode" class="form-label">Code</label>
            <input
              type="text"
              class="form-control"
              id="ledgerAccountCode"
              [(ngModel)]="updateCoaLedgerAccount.ledgerAccountCode"
              name="ledgerAccountCode"
              required
              disabled
            />

            <div
              *ngIf="
                updateCoaLedgerAccountForm.submitted &&
                updateCoaLedgerAccountForm.controls['ledgerAccountCode'].invalid
              "
              class="text-danger"
            >
              <div
                *ngIf="updateCoaLedgerAccountForm.controls['ledgerAccountCode'].errors?.['required']"
              >
                Code is required.
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="ledgerAccountName" class="form-label">Name</label>
            <input
              type="text"
              class="form-control"
              id="ledgerAccountName"
              [(ngModel)]="updateCoaLedgerAccount.ledgerAccountName"
              name="ledgerAccountName"
              required
            />

            <div
              *ngIf="
                updateCoaLedgerAccountForm.submitted &&
                updateCoaLedgerAccountForm.controls['ledgerAccountName'].invalid
              "
              class="text-danger"
            >
              <div
                *ngIf="updateCoaLedgerAccountForm.controls['ledgerAccountName'].errors?.['required']"
              >
                Ledger Account Name is required.
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="ledgerAccountDescription" class="form-label"
              >Description</label
            >
            <textarea
              class="form-control"
              id="ledgerAccountDescription"
              rows="3"
              [(ngModel)]="updateCoaLedgerAccount.ledgerAccountDescription"
              name="ledgerAccountDescription"
              required
            ></textarea>

            <div
              *ngIf="
                updateCoaLedgerAccountForm.submitted &&
                updateCoaLedgerAccountForm.controls['ledgerAccountDescription']
                  .invalid
              "
              class="text-danger"
            >
              <div
                *ngIf="updateCoaLedgerAccountForm.controls['ledgerAccountDescription'].errors?.['required']"
              >
                Description is required.
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="defaultTaxCode" class="form-label">Tax Code</label>
            <select
              class="form-select"
              id="defaultTaxCode"
              [(ngModel)]="updateCoaLedgerAccount.defaultTaxCode"
              name="defaultTaxCode"
              required
            >
              <option value="" disabled selected>Select Tax Code</option>
              <option value="Exclude From BAS">Exclude From BAS</option>
              <option value="GST on Income">GST on Income</option>
              <option value="GST on Expenses">GST on Expenses</option>
              <option value="GST Free Income">GST Free Income</option>
              <option value="GST Free Expenses">GST Free Expenses</option>
            </select>
          
            <div
              *ngIf="
                coaLedgerAccountForm.submitted &&
                coaLedgerAccountForm.controls['defaultTaxCode'].invalid
              "
              class="text-danger"
            >
              <div
                *ngIf="
                  coaLedgerAccountForm.controls['defaultTaxCode'].errors?.['required']
                "
              >
                Tax Code is required.
              </div>
            </div>
          </div>
          

          <div class="row mb-4">
            <div class="col-md-12">
              <label for="status" class="col-form-label">Status</label>

              <div
                class="btn-group w-100"
                role="group"
                aria-label="Basic radio toggle button group"
              >
                <input
                  type="radio"
                  class="btn-check"
                  name="status"
                  id="statusA"
                  [(ngModel)]="updateCoaLedgerAccount.status"
                  value="Active"
                  required
                  #status="ngModel"
                  [ngClass]="{
                    'is-invalid':
                      updateCoaLedgerAccountForm.submitted && status.invalid
                  }"
                  autocomplete="off"
                />
                <label class="btn btn-outline-orange" for="statusA"
                  >ACTIVE</label
                >

                <input
                  type="radio"
                  class="btn-check"
                  name="status"
                  id="statusI"
                  [(ngModel)]="updateCoaLedgerAccount.status"
                  value="Inactive"
                  required
                  [ngClass]="{
                    'is-invalid':
                      updateCoaLedgerAccountForm.submitted && status.invalid
                  }"
                  autocomplete="off"
                />
                <label class="btn btn-outline-orange" for="statusI"
                  >INACTIVE</label
                >
              </div>

              <!-- Validation Error -->
              <div
                *ngIf="updateCoaLedgerAccountForm.submitted && status.invalid"
                class="text-danger"
              >
                <div *ngIf="status.errors?.['required']">
                  Status is required.
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
            #closeUpdateGLAccountPopUp
          >
            Cancel
          </button>
          <button type="submit" class="btn btn-primary">Save</button>
        </div>
      </form>
    </div>
  </div>
</div>
