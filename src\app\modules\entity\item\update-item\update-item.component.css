
body {
    font-family: Arial, sans-serif;
    background-color: transparent;
    margin: 0;
    padding: 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: transparent
}


.popup-header {
    display: flex;
    background-color: transparent;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    box-shadow: none;
    border: none;
    gap: 5px;
    
}

.popup-header h3 {
    flex: 1;
    margin-bottom: 0;
    font-family: Inter;
    font-size: 36px;
    font-weight: 700;
    text-align: left;
    color: #4262FF;
    top: 264px;
}

.header .transparent-button {
    top: 256px;
    border: 1px solid #4262FF;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 12px;
    font-weight: bold;
    background-color: white;
    color: #4262FF;
    font-family: Inter;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
}

.header .transparent-button:hover {
    background-color: #4262FF;
    color: white;
}
.bd{
  border: 2px solid #CEC9C980; 
  border-radius: 12px; 
}
.form-section {
  display: flex;
  font-family: Arial, sans-serif;
  flex-direction: column;
  padding: 20px;
  background-color: #f7f7f7; 

}
.form-row {
    display: flex;
    gap: 32px;
    padding-right: 20px;
    margin-bottom: 5px;
}

.form-group {
    flex: 1;
}

.input-style {
    height: 49px;
    top: 656px;
    left: 111px;
    padding: 10px;
    font-size: 14px;
}

.create-customer-container {
    display: flex;
    justify-content: flex-end;
}

label {
    display: block;
    /* margin-bottom: 5px; */
    font-weight: bold;
    color: #333;
}

.create-customer-btn {
    float: right;
    color: #4a4ae2;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-style: italic;
    font-weight: bold;
}


.text-center {
    text-align: center;
}
/* Icon button */

.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 24px;
    color: #4a4ae2;
}

.popup-container {
    position: fixed;
    /* padding-top: 10%; */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.popup {
    background: white;
    width: 100%;
    max-height: 95%;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
    position: relative;
    overflow-y: auto;
}

.popup::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}

.popup {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.popup-header h2 {
    margin-bottom: 5px;
    font-family: Segoe UI, sans-serif;
    font-size: 20px;
    font-weight: 600;
    text-align: left;
    color: #535353;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
}

.form-group {
    margin-bottom: 10px;
    text-align: left;
    font-family: Segoe UI, sans-serif;
    font-size: 15px;
    font-weight: 600;
    line-height: 25.41px;
    color: #444343;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: normal;
}

.form-group input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #E6E6E6;
    border-radius: 8px;
    font-size: 14px;
}

.popup-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.cancel-btn {
    padding: 10px 70px;
    border-radius: 25px;
    cursor: pointer;
    font-family: Segoe UI, sans-serif;
    font-size: 15px;
    font-weight: 700;
    text-align: center;
    background: #ffffff;
    color: #6822FF;
    border: 2px solid #4262FF;
    margin-top: 15px;
    margin-left: 160px;
}

.cancel-btn:hover {
    background: #4262FF;
    color: white;
}

.add-btn {
    padding: 10px 80px;
    border: none;
    border-radius: 17px;
    cursor: pointer;
    font-family: Segoe UI, sans-serif;
    font-size: 15px;
    font-weight: 700;
    text-align: center;
    background: linear-gradient(to right, #4262FF, #512CA2);
    color: white;
    margin-top: 15px;
    margin-left: 72%;
}

.add-btn:hover {
    background: linear-gradient(to right, #512CA2, #4262FF);
}

.form-check {
    display: inline-block;
    margin-right: 10px;
}
/* Mobile responsive styling */
@media (max-width: 768px) {
  .add-btn {
    margin-left: auto;
    margin-right: auto;
    display: block; /* makes auto margins work */
    width: 100%;    /* or set a fixed width like 80% if preferred */
    max-width: 300px; /* optional: control button size */
  }

  .container {
    padding: 15px;
  }
}