# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: DEV Workflow

on:
  push:
    branches:
      - CICD      
      - DEV
      - UAT
      - main
  pull_request:
    branches:
      - DEV
      - UAT
      - main

jobs:
  build:

    runs-on: ubuntu-latest
    environment: DEV

    strategy:
      matrix:
        node-version: [16.20.2]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm install --package-lock-only    
    - run: npm ci
    - run: npm run build --if-present
    - run: npm test

    - name: Docker Login
      uses: docker/login-action@v3.2.0
      with:
        registry: ${{ vars.AZURE_CONTAINER_REGISTRY }}
        username: ${{ secrets.CLOUD_SPN_ID }}
        password: ${{ secrets.CLOUD_SPN_SEC }}

    - name: Build and push Docker images
      run: |
        docker build -t ${{ vars.AZURE_CONTAINER_REGISTRY }}/navitsa-frontend:${{ github.sha }} .
        docker push ${{ vars.AZURE_CONTAINER_REGISTRY }}/navitsa-frontend:${{ github.sha }}
