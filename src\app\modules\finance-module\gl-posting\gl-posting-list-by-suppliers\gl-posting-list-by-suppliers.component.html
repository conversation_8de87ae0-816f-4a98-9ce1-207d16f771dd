<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <!-- Heading And Button -->
  <div class="actions">
    <h1 *ngIf="accountCode === '800'">Creditors</h1>
    <h1 *ngIf="accountCode === '600'">Debitors</h1>
  </div>

  <div class="Card">
    <div class="actions-2">
      <h4><b>Account Code:</b> {{accountCode}}</h4>
      <h4><b>Account Name:</b> {{accountName}}</h4>
    </div>

    <div class="row1">
      <!-- Input for number, reference -->
      <div class="row1_col1">
        <label for="search-input"> Name</label>
        <div class="input-container">
          <input type="text" class="search-input" id="search-input" [(ngModel)]="searchTerm" />
          <i class="bi bi-search"></i>
        </div>
      </div>
    </div>

    <div class="row2">
      <div class="row2_col1">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>
      <div class="row2_col1">
        <button type="button" class="primary-button" (click)="filterQuotes()">
          Search
        </button>
      </div>
    </div>
  </div>

  <div class="table-responsive">
    <table>
      <thead>
        <tr class="table-head">
          <th scope="col" class="valuehead" style="text-align: left">Business Partner</th>
          <th scope="col" class="valuehead" style="text-align: right">Dr</th>
          <th scope="col" class="valuehead" style="text-align: right">Cr</th>
          <th scope="col" class="valuehead" style="text-align: leftcenter">Action</th>
        </tr>
      </thead>
      <tbody>
      <tbody *ngFor="let detail of filteredRecords; let i = index">
        <tr>
          <td class="value" style="text-align: left">{{ detail.businessPartnerDetails?.bpFirstName + ' ' +
            detail.businessPartnerDetails?.bpName }}
          </td>
          <td class="value" style="text-align: right">
            {{ detail.dr_amount | currency }}
          </td>
          <td class="value" style="text-align: right">
            {{ detail.cr_amount | currency }}
          </td>
          <td class="value" style="text-align: left">
            <button class="btn btn-orange btn-sm"
              (click)="openSupplierRecordsPage(accountCode, detail.businessPartnerDetails?.businessPartnerId)"
              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" title="View">
               <i class="bi bi-arrows-fullscreen" style="color: #4262ff"></i>
            </button>
            <button class="btn btn-orange btn-sm"
              (click)="generateGLReport(detail.businessPartnerDetails?.businessPartnerId)" data-bs-toggle="modal"
              data-bs-target="#simpleModal"
              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
              title="Generate Report">
              <i class="ri-printer-line" style="color: #5d5656;"></i>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Report Preview -->
  <div class="modal fade" id="simpleModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 740px;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="simpleModalLabel">Supplier Statement </h5>
          <button type="button" class="btn-close custom-close-btn" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true"></span>
          </button>
        </div>
        <div class="modal-body">
          <!-- Loading Spinner -->
          <div *ngIf="isLoading" class="spinner-container">
            <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>

          <!-- IFrame for Report Preview -->
          <div style="margin-top: 20px;" [ngClass]="{'d-none': isLoading}">
            <iframe #supplierStatementPreviewFrame id="supplierStatementPreviewFrame" width="700px"
              height="700px"></iframe>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>