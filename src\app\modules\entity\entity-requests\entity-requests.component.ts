import { Component, OnInit, ViewChild } from '@angular/core';
import { BusinessEntityRequest } from '../entity';
import { Router } from '@angular/router';
import { EntityService } from '../entity.service';
import Swal from 'sweetalert2';
import { Bot<PERSON>ontrollerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { UserService } from '../../admin/components/user/user.service';

@Component({
  selector: 'app-entity-requests',
  templateUrl: './entity-requests.component.html',
  styleUrls: ['./entity-requests.component.css'],
})
export class EntityRequestsComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent)
  chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  businessEntityRequestList: BusinessEntityRequest[] = [];

  constructor(
    private entityService: EntityService,
    private router: Router,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.loadBusinessEntities();
  }

  private loadBusinessEntities() {
    const entityId = +(localStorage.getItem('entityId') + '');
    this.entityService
      .getAllBusinessEntityRequestsByEntity(entityId)
      .subscribe((data) => {
        this.businessEntityRequestList = data;
      });
  }

  acceptRequest(request: BusinessEntityRequest): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to add this user to the entity?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes',
      cancelButtonText: 'No',
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#6c757d',
    }).then((result) => {
      if (result.isConfirmed) {
        this.userService
          .addUserToEntity(request.userId.userId, request.entityId.entityId)
          .subscribe((response) => {
            this.entityService
              .declineBusinessEntityRequest(request.businessEntityRequestId)
              .subscribe(
                () => {
                  this.showSuccessAlert('Request has been accepted.');
                  this.loadBusinessEntities();
                },
                (error) => {
                  console.error('Failed to accept request:', error);
                  this.showErrorAlert(
                    'Failed to accept request. Please try again later.'
                  );
                }
              );
          });
        console.log('Subscription updated successfully');
      } else {
        console.log('Subscription update canceled');
      }
    });
  }

  declineRequest(id: number): void {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you really want to decline request?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, decline it!',
      cancelButtonText: 'No',
      confirmButtonColor: '#ff7e5f',
      cancelButtonColor: '#be0032',
    }).then((result) => {
      if (result.isConfirmed) {
        this.entityService.declineBusinessEntityRequest(id).subscribe(
          () => {
            this.showSuccessAlert('Request has been declined.');
            this.loadBusinessEntities();
          },
          (error) => {
            console.error('Failed to decline request:', error);
            this.showErrorAlert(
              'Failed to decline request. Please try again later.'
            );
          }
        );
      }
    });
  }

  private showSuccessAlert(message: string): void {
    Swal.fire({
      title: 'Success!',
      text: message,
      icon: 'success',
      confirmButtonText: 'OK',
      confirmButtonColor: '#28a745',
    });
  }

  private showErrorAlert(message: string): void {
    Swal.fire({
      title: 'Error!',
      text: message,
      icon: 'error',
      confirmButtonText: 'OK',
      cancelButtonText: 'Ask Chimp',
      confirmButtonColor: '#be0032',
      cancelButtonColor: '#007bff',
      showCancelButton: true,
    }).then((result) => {
      if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        if (this.chatBotComponent) {
          Swal.fire({
            title: 'Processing...',
            text: 'Please wait while Chimp processes your request.',
            allowOutsideClick: false,
            didOpen: () => {
              Swal.showLoading();
              this.chatBotComponent.setInputData(message);
              this.chatBotComponent.responseReceived.subscribe((response) => {
                Swal.close();
                this.chatResponseComponent.showPopup = true;
                this.chatResponseComponent.responseData = response;
                this.playLoadingSound();
                this.stopLoadingSound();
              });
            },
          });
        } else {
          console.error('ChatBotComponent is not available.');
        }
      }
    });
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}
