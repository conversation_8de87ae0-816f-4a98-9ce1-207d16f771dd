import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  Stripe,
  loadStripe,
  StripeCardElement,
  TokenResult,
} from '@stripe/stripe-js';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class StripeService {
  public stripe: Stripe | null = null;

  constructor(private http: HttpClient) {
    this.initializeStripe();
  }

   private readonly baseURL = environment.apiUrl;

  getAuthToken(): string | null {
          return window.sessionStorage.getItem('auth_token');
        }
      
        request(
          method: string,
          url: string,
          data: any,
          params?: any
        ): Observable<any> {
          let headers = new HttpHeaders();
      
          const authToken = this.getAuthToken();
  
    if (authToken) {
      headers = headers.set('Authorization', 'Bearer ' + authToken);
    } else {
      // Add secure API key for protected-but-public endpoints
      headers = headers.set('X-API-KEY', environment.secureApiKey);
    }
      
          const options = {
            headers: headers,
            params: new HttpParams({ fromObject: params }),
          };
      
          switch (method.toUpperCase()) {
            case 'GET':
              return this.http.get(this.baseURL + url, options);
            case 'POST':
              return this.http.post(this.baseURL + url, data, options);
            case 'PUT':
              return this.http.put(this.baseURL + url, data, options);
            case 'DELETE':
              return this.http.delete(this.baseURL + url, options);
            // Add more HTTP methods as needed
            default:
              throw new Error('Unsupported HTTP method');
          }
        }

  async initializeStripe() {
    if (!this.stripe) {
      this.stripe = await loadStripe(environment.stripePublicKey);
    }
  }

  createToken(card: StripeCardElement): Promise<TokenResult> {
    if (!this.stripe) {
      return Promise.reject(new Error('Stripe not initialized'));
    }
    return this.stripe.createToken(card);
  }

  chargeCard(token: string, amount: number): Observable<any> {
  return this.request(
    'POST',
    '/api/payment/charge',
    { token, amount }
  );
}

}
