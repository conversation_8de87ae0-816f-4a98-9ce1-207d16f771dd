import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, tap } from 'rxjs';
import { MultiplePayCalendar,PayCalendar,PayCycle, PayPeriod, PayRunDetail } from '../payroll-setting';
import { environment } from 'src/environments/environment';
import { BankAccount, BankAccountNew, EmployeeLeave, EmployeeMaster, EmployeeTax, Employment, EmploymentData, LeaveType, NewDeduction, NewEarning, NewLeave, NewReimbursement, NewSuperannuation, NewTax, Personal, Superannuation } from '../empolyee/employee';

@Injectable({
  providedIn: 'root',
})
export class EmployeeService {
  private readonly baseURL = environment.payrollApiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }
 
  getFilteredNames(name: string, entityId: number): Observable<any[]> {
    const encodedName = encodeURIComponent(name);
    return this.request('GET', `/employeeMaster/getName?name=${encodedName}&entityId=${entityId}`, {});
  }
  getEmployeeById(id: number): Observable<Personal> {
    return this.request('GET', `/employeeMaster/getEmployeeById?id=${id}`, {});
  }

  previewEmployeeById(id: number, entityId: number): Observable<EmploymentData[]> {
    return this.request('GET', `/employeeEmployment/getCalendarByIdByEmployee/${id}?entityId=${entityId}`, {});
  }

  previewEarningById(id: number, entityId: number): Observable<NewEarning[]> {
    return this.request('GET', `/employeeEarning/getEarningById/${id}?entityId=${entityId}`, {});
  }
  previewDeductionById(id: number, entityId: number): Observable<NewDeduction[]> {
    return this.request('GET', `/employeeDeduction/getDeductionById/${id}?entityId=${entityId}`, {});
  }
  previewReimbursementById(id: number, entityId: number): Observable<any[]> {
    return this.request('GET', `/payRunDetail/getReimbursementById/${id}?entityId=${entityId}`, {});
  }

  savePersonalData(personalData: Personal | any): Observable<Personal> {
    return this.request('POST', '/employeeMaster/save', personalData);
  }

  updateStatus(id: number, updatedStatus: { status: boolean }): Observable<any> {
    return this.request('PUT', `/employeeMaster/updateStatus/${id}`, updatedStatus);
  }

  saveEmploymentData(employmentData: Employment | any): Observable<Employment> {
    return this.request('POST', '/employeeEmployment/save', employmentData);
  }

  saveEmployeeTaxData(employeeTaxData: EmployeeTax | any): Observable<EmployeeTax> {
    return this.request('POST', '/employeeTax/save', employeeTaxData);
  }

  getAllTaxes(employeeId: number): Observable<EmployeeTax[]> {
    return this.request('GET', `/employeeTax/getAll?employeeId=${employeeId}`, {});
  }
  
  
  saveEmployeeLeaveData(employeeLeaveData: EmployeeLeave | any): Observable<EmployeeLeave> {
    return this.request('POST', '/employeeLeave/save', employeeLeaveData);
  }
  
  updateEmpolyee(id: number,personalData: Personal ): Observable<any> {
    return this.request('PUT', `/employeeMaster/update/${id}`,personalData );
  }

  updateEmployement(id: number,employmentData: Employment ): Observable<any> {
    return this.request('PUT', `/employeeEmployment/update/${id}`,employmentData );
  }

  updateTax(id: number,TaxData: EmployeeTax ): Observable<any> {
    return this.request('PUT', `/employeeTax/update/${id}`,TaxData );
  }

  getPayTemplateEarnings(employeeId: number): Observable<NewEarning[]> {
    return this.request('GET', `/employeeEarning/getAll?employeeId=${employeeId}`, {});
  }
  
  saveEarning(earningData: NewEarning | any): Observable<NewEarning> {
    return this.request('POST', '/employeeEarning/save', earningData);
  }

  saveDeduction(deductionData: NewDeduction | any): Observable<NewEarning> {
    return this.request('POST', '/employeeDeduction/save', deductionData);
  }
  getPayTemplateDeductions(employeeId: number): Observable<NewDeduction[]> {
    return this.request('GET', `/employeeDeduction/getAll?employeeId=${employeeId}`, {});
  }
  
  saveReimbursement(reimbursementData: NewReimbursement | any): Observable<NewReimbursement> {
    return this.request('POST', '/employeeReimbursement/save', reimbursementData);
  }
  getPayTemplateReimbursements(employeeId: number): Observable<NewReimbursement[]> {
    return this.request('GET', `/employeeReimbursement/getAll?employeeId=${employeeId}`, {});
  }

  saveLeave(leaveData: NewLeave | any): Observable<NewLeave> {
    return this.request('POST', '/employeeLeave/save', leaveData);
  }
  getPayTemplateLeaves(employeeId: number): Observable<NewLeave[]> {
    return this.request('GET', `/employeeLeave/getAll?employeeId=${employeeId}`, {});
  }
  
  getLeaveAccrual(employeeId: number): Observable<any[]> {
    return this.request('GET', `/leaveAccrual/getAll?employeeId=${employeeId}`, {});
  }

  saveTax(taxData: NewTax | any): Observable<NewTax> {
    return this.request('POST', '/employeeTax/save', taxData);
  }
  getPayTemplateTaxs(employeeId: number): Observable<NewTax[]> {
    return this.request('GET', `/employeeTax/getAll?employeeId=${employeeId}`, {});
  }

  saveSuperannuation(superannuationData: NewSuperannuation | any): Observable<NewSuperannuation> {
    return this.request('POST', '/employeeSuperannuation/save', superannuationData);
  }
  getPayTemplateSuperannuations(employeeId: number): Observable<NewSuperannuation[]> {
    return this.request('GET', `/employeeSuperannuation/getAll?employeeId=${employeeId}`, {});
  }

  getSuperannuationsList(): Observable<Superannuation[]> {
    return this.request('GET', '/superannuationFund/getAll', {});
  }

  saveBankAccount(bankAccountData: BankAccount | any): Observable<BankAccount> {
    return this.request('POST', '/employeeBankAccount/save', bankAccountData);
  }
  saveBankAccount2(bankAccountData2: BankAccountNew | any): Observable<BankAccountNew> {
    return this.request('POST', '/employeeBankAccount/save', bankAccountData2);
  }
  getBankAccountList(employeeId: number): Observable<BankAccountNew[]> {
    return this.request('GET', `/employeeBankAccount/getAll?employeeId=${employeeId}`, {});
  }

  getAllEmployments(employeeId: number): Observable<Employment[]> {
    return this.request('GET', `/employeeEmployment/getAll?employeeId=${employeeId}`, {});
  }
  
  deleteEarning(employeeEarningId: number): Observable<any> {
    return this.request('DELETE', `/employeeEarning/deleteById/${employeeEarningId}`, {});
  }
  deleteDeduction(employeeDeductionId: number): Observable<any> {
    return this.request('DELETE', `/employeeDeduction/deleteById/${employeeDeductionId}`, {});
  }
  deleteReimbursement(employeeReimbursementId: number): Observable<any> {
    return this.request('DELETE', `/employeeReimbursement/deleteById/${employeeReimbursementId}`, {});
  }
  deleteLeave(employeeLeaveId: number): Observable<any> {
    return this.request('DELETE', `/employeeLeave/deleteById/${employeeLeaveId}`, {});
  }
  deleteSuperannuation(employeeuperannuationId: number): Observable<any> {
    return this.request('DELETE', `/employeeSuperannuation/deleteById/${employeeuperannuationId}`, {});
  }
  deleteBankAccount(bankAccountId: number): Observable<any> {
    return this.request('DELETE', `/employeeBankAccount/deleteById/${bankAccountId}`, {});
  }

  getLeavesList(entityId: number): Observable<LeaveType[]> {
    return this.request('GET', `/leaveType/getAll?entityId=${entityId}`, {});
  }


  generateEmployeeDetailReport(payCalendarId: string | number, entityId: number): Observable<Blob> {
    const url = payCalendarId === 'ALL'
      ? `/employeeMaster/EmployeeDetailReport?payCalendarId=ALL&entityId=${entityId}`
      : `/employeeMaster/EmployeeDetailReport?payCalendarId=${payCalendarId}&entityId=${entityId}`;
    
    return this.request('GET', url, {});
  }

  getEmployeesByPayCalendar(payCalendarId: string | number, entityId: number): Observable<{ firstName: string, lastName: string, email: string }[]> {
    return this.request('GET', `/employeeMaster/getEmployeesByPayCalendar?payCalendarId=${payCalendarId}&entityId=${entityId}`, {});
  }
  
  getPayRunSummaryReportByPostingDate(startDate: string, endDate: string, entityId: number) {
    return this.request('POST', '/payRunDetail/getPayRunSummaryReportByPostingDate', {
      startDate,
      endDate,
      entityId
    });
  }
  
  
  getPayRunDetailsByEntityIdAndType(entityId: any): Observable<PayRunDetail[]> {
      return this.request(
        'GET',
        '/payRunDetail/getPayRunDetailsByEntityIdAndType',
        {},
        { entityId: entityId }
      );
    }
  
  getBeamStatus(beamProcessId: string, entityId: number): Observable<any> {
    return this.request('GET', `/payRunDetail/beam-status/${beamProcessId}/${entityId}`, {});
  }  

  
}