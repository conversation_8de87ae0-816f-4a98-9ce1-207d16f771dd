import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class BotService {


  private readonly baseURL = environment.apiUrl;

  constructor(private http: HttpClient) { }

  getAuthToken(): string | null {
    return window.sessionStorage.getItem("auth_token");
  }

  setAuthToken(token: string | null): void {
    if (token !== null) {
      window.sessionStorage.setItem("auth_token", token);
    } else {
      window.sessionStorage.removeItem("auth_token");
    }
  }

  request(method: string, url: string, data: any, params?: any): Observable<any> {
    let headers = new HttpHeaders();
    if (this.getAuthToken() !== null) {
      headers = headers.set("Authorization", "Bearer " + this.getAuthToken());
    }
    const options = { headers: headers, params: new HttpParams({ fromObject: params }), responseType: 'text' as 'json' };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }
}
