.top-nav {
  background: linear-gradient(to right, #455cff, #6822ff);
  color: white;
  padding: 15px 0;
  height: 105px;
}

.top-nav .btn-light {
  background-color: white;
  color: #4e54c8;
  border: none;
  font-weight: bold;
  padding: 5px 15px;
  border-radius: 20px;
}

.top-nav .container button {
  font-family: Inter;
  font-size: 15px;
  font-weight: 700;
  line-height: 30px;
  text-align: center;
  border-radius: 15px;
}

.top-nav .container label {
  font-family: Inter;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
}

.top-nav .form-check-input {
  background-color: transparent;
  border-color: white;
}

.top-nav .form-check-input:checked {
  background-color: white;
  border-color: white;
}

.top-nav .form-check-label {
  color: white;
  margin-right: 15px;
}

/* Bottom section */
.bottom-nav {
  background-color: white;
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
  /* padding: 0; */
  /* margin-bottom: none; */
  height: 74px;
  display: flex;
  align-items: center;
}

.bottom-nav .container {
  display: flex;
  align-items: center;
}

/* .navbar-brand {
  font-weight: bold;
  color: #4e54c8;
  display: flex;
  align-items: center;
} */

/* .Ledger_Chimp {
  margin: -1px 0;
} */

.offer-text {
  color: rgb(255, 255, 255);
  font-size: 26px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 20px;
  height: 100%;
  margin-top: 10px;
}

.buy-Btn-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.navbar-nav {
  margin-left: -8%;
}

.nav-link {
  color: #333;
  margin-left: 20px;
  font-weight: bold;
  position: relative;
  padding-block: 25px;
}

.nav-link:hover,
.nav-link.active {
  color: #4e54c8;
}

.nav-link.active::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 100%;
  height: 5px;
  background-color: #4262ff;
}

.btn-register {
  font-family: Inter;
  font-size: 17px;
  font-weight: 600;
  line-height: 27.84px;
  text-align: center;
  background-color: white;
  color: #4e54c8;
  border: none;
  border-radius: 20px;
  margin-right: 10px;
  margin-left: 50px;
  padding: 10px 20px;
}

.btnSet {
  display: flex;
  margin-right: 20px;
  /* background-color: yellowgreen; */
}

.btn-login {
  font-family: Inter;
  font-size: 17px;
  font-weight: 600;
  line-height: 27.84px;
  background: linear-gradient(to right, #4262ff, #512ca2);
  border: 2px solid #4262ff;
  border-radius: 15px;
  color: white;
  border: none;
  padding: 10px 25px;
}

.btn-login:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

.btn-register:hover,
.btn-login:hover {
  opacity: 0.9;
}

/* Hide the menu by default on mobile, show it when isMenuHidden is false */
.navbar-nav {
  display: flex;
  /* Show links on larger screens */
}

.navbar-nav.d-none {
  display: none;
  /* Hide links on mobile */
}

.navbar-toggler {
  background-color: transparent;
  border: none;
  font-size: 30px;
  display: none;
  /* Make sure the toggle button is hidden by default */
}

.navbar-toggler-icon {
  width: 30px;
  height: 30px;
}

/* Media query for mobile screens */
@media (max-width: 400px) {
  .buy-Btn-row {
    display: flex;
    flex-direction: column;
    /* Change to column layout for small screens */
    align-items: center;
    margin-bottom: 10px;
    /* This will make it more visible for testing */
  }

  .top-nav .btn-light {
    background-color: white;
    color: #4e54c8;
    border: none;
    font-weight: bold;
    padding: 5px 15px;
    border-radius: 20px;
    visibility: hidden;
  }

  .top-nav .container label {
    font-family: Inter;
    font-size: 12px;
    font-weight: 700;
    text-align: center;
  }
}
/* Media query for mobile screens */
@media (max-width: 992px) {
  .navbar-nav {
    display: none;
    /* Hide menu on mobile by default */
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    background-color: white;
    flex-direction: column;
    text-align: center;
    padding: 10px;
    border-top: 1px solid #ddd;
  }

  /* When the menu is visible on mobile, show it */
  .navbar-nav.d-flex {
    display: flex;
  }

  .navbar-toggler {
    display: block;
    /* Ensure the toggle button is shown */
  }

  .bottom-nav {
    background-color: rgb(250, 250, 250);
    padding: 0;
    margin-top: 20px;
    margin-bottom: none;
  }

  .buy-Btn-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
  }

  .offer-text {
    font-size: 10;
  }

  .offer-text {
    color: rgb(255, 255, 255);
    font-size: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-right: 20px;
    height: 100%;
    margin-top: 10px;
  }
}

/* Media query for larger screens (tablet and up) */
@media (min-width: 992px) {
  .navbar-nav {
    display: flex !important;
    /* Always show the navbar on desktop and larger devices */
  }

  .navbar-toggler {
    display: none;
    /* Hide the toggle button on desktop and larger devices */
  }

  /* Ensures the menu stays visible on larger screens */
  .navbar-nav.d-none {
    display: flex;
  }
  .buy-Btn-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
  }
}

.Ledger_Chimp {
  width: 210px;
  height: 40px;
}

@media (max-width: 1024px) {
  .Ledger_Chimp {
    width: 150px;
    /* Smaller image for tablet screens */
    height: 30px;
  }
}

@media (max-width: 768px) {
  .Ledger_Chimp {
    width: 120px;
    /* Smaller image for mobile screens */
    height: 25px;
  }
}

@media (max-width: 480px) {
  .Ledger_Chimp {
    width: 100px;
    /* Very small image for small devices */
    height: 20px;
  }
}
/* Default button styling */
.btn-register {
  font-family: Inter;
  font-size: 17px;
  font-weight: 600;
  line-height: 27.84px;
  text-align: center;
  background-color: white;
  color: #4e54c8;
  border: none;
  border-radius: 5px;
  margin-right: 10px;
  margin-left: 10px;
  padding: 10px 20px;
}

.btn-login {
  padding: 10px 20px;
  /* Set initial padding */
  font-size: 16px;
  /* Set initial font size */
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

@media (max-width: 1024px) {
  .btn-register,
  .btn-login {
    padding: 8px 16px;
    /* Reduce padding for tablets */
    font-size: 14px;
    /* Smaller font size for tablets */
  }
}

@media (max-width: 768px) {
  .btn-register,
  .btn-login {
    padding: 6px 12px;
    /* Reduce padding further for small screens */
    font-size: 12px;
    /* Even smaller font size */
  }
}

@media (max-width: 480px) {
  .btn-register,
  .btn-login {
    padding: 5px 10px;
    /* Minimal padding for very small screens */
    font-size: 10px;
    /* Smaller font size for small mobile screens */
  }
}
