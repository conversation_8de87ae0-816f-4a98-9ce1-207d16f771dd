*{
  font-family: "Inter",sans-serif !important;
}

p{
  margin: 0;
  padding: 0;
}

.h1 {
  flex: 1;
  margin-bottom: 20px;
  font-family: Inter;
  font-size: 40px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
}

.container {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
}
.custom-input {
  outline: none !important;
  border: 1px solid rgb(200, 200, 200);
  color: rgb(100, 100, 100);
  border-radius: 5px;
  text-align: start;
  font-size: 15px;
  font-weight: 600 !important;
  padding: 7px 12px;
  appearance: textfield !important;
}

.custom-select {
  font-size: 14px !important;
  outline: none;
  min-width: 200px;
  border-radius: 5px;
  border: 1px solid lightgray;
  color: black;
  font-weight: 600;
  padding: 7px 36px 7px 12px;
  background-color: white;

  appearance: none; /* Removes default styling */
  -webkit-appearance: none;
  -moz-appearance: none;

  /* Custom arrow */
  background-image: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L5 5L9 1' stroke='%234262ff' stroke-width='1.5'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 10px 6px;
}

.custom-select option {
  font-weight: 400;
  text-transform: capitalize;
}

.card {
  background-color: #ffffff;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 0px;
  overflow: hidden;
}

.tb-head th, .tb-detail td{
  position: relative;
  font-weight: 500;
  font-size: 14px;
  background-color: #d7dbf5;
  text-align: end;
  padding: 15px 25px;
}

.tb-head th:not(:last-child)::before, .tb-detail td:not(:last-child)::before{
  position: absolute;
  content: '';
  width: 1px;
  background-color: darkgray;
  height: 10px;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.tb-head th:first-child, .tb-detail td:first-child {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    text-align: start;
}

.tb-head th:last-child , .tb-detail td:last-child {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    text-align: center;
}

.tb-detail{
  border-bottom: 1px solid rgb(237, 237, 237);
}

.tb-detail td{
  background-color: white;
}

.tb-detail td:first-child p:first-child{
  font-size: 13px;
  color: darkgray;
}

.tb-detail td:first-child p:nth-child(2){
  font-size: 14px;
  color: black;
  font-weight: 600;
}

label{
  color: balck;
  font-size: 14px;
  margin-bottom: 5px;
}
