import { Component } from '@angular/core';
import { GlPostingDetails, GlPostingHead } from '../../journal-voucher/journal-voucher';
import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';
import { JournalVoucherService } from '../../journal-voucher/journal-voucher.service';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { forkJoin } from 'rxjs';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-gl-view-pnl',
  templateUrl: './gl-view-pnl.component.html',
  styleUrls: ['./gl-view-pnl.component.css']
})


export class GlViewPnlComponent {
  quotes: GlPostingHead[] = [];
  quotesDetails: GlPostingDetails[] = [];
  selectedQuotesDetails: GlPostingDetails[] = [];
  processedData: any[] = [];
  filteredRecords: any[] = [];
  businessPartners: BusinessPartner[] = [];
  accountCode: string | null = null;
  accountName: string | null = null;
  businessPartner: number | null = null;
  businessPartnerDetails?: BusinessPartner | null;
  searchTerm: string = '';
  startDate: string | null = null;
  endDate: string | null = null;
  activeTab = 'all';

  constructor(
    private journalVoucherService: JournalVoucherService,
    private businessPartnerService: BusinessPartnerService,
    public sanitizer: DomSanitizer,
    private route: ActivatedRoute,
  ) { }



  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.accountCode = params['selectedAccountCode'] || null;
      this.accountName = params['accountName'] ? decodeURIComponent(params['accountName']) : null;
      this.startDate = params['from'] ? decodeURIComponent(params['from']) : null;
      this.endDate = params['to'] ? decodeURIComponent(params['to']) : null;
    });

    forkJoin({
      businessPartners: this.businessPartnerService.getBusinessPartnerList(),
      quotesDetails: this.journalVoucherService.getGlPostingDetailsList(
        parseInt(localStorage.getItem('entityId') || '0', 10)
      ),
      glPostingHead: this.journalVoucherService.getGlPostingHeadList(
        parseInt(localStorage.getItem('entityId') || '0', 10)
      ),
    }).subscribe(
      ({ businessPartners, quotesDetails, glPostingHead }) => {
        this.businessPartners = businessPartners;
        this.quotesDetails = quotesDetails;
        this.quotes = glPostingHead;

        if (this.accountCode) {
          // Case 1: Directly filter using accountCode
          this.selectedQuotesDetails = this.quotesDetails.filter(detail =>
            detail.coaLedgerAccount?.ledgerAccountCode === this.accountCode
          );
          this.accountName = this.selectedQuotesDetails[0]?.coaLedgerAccount?.ledgerAccountName ?? null;
        } else if (this.accountName) {
          // Case 2: Find accountCode from name
          const matchedDetail = this.quotesDetails.find(detail =>
            detail.coaLedgerAccount?.ledgerAccountName?.toLowerCase().trim() === this.accountName?.toLowerCase().trim()
          );

          if (matchedDetail) {
            this.accountCode = matchedDetail.coaLedgerAccount?.ledgerAccountCode ?? null;
            this.selectedQuotesDetails = this.quotesDetails.filter(detail =>
              detail.coaLedgerAccount?.ledgerAccountCode === this.accountCode
            );
          } else {
            Swal.fire({
              title: 'No Matching Account',
              text: `Could not find an account matching name: ${this.accountName}`,
              icon: 'warning',
              confirmButtonText: 'OK'
            });
          }
        }

        this.processedData = this.aggregateData(this.quotes, this.selectedQuotesDetails);


        if (this.startDate || this.endDate) {
          this.filterQuotes();
        } else {
          this.filteredRecords = [...this.processedData];
        }
      },
      error => console.error('Error loading data:', error)
    );
  }

  aggregateData(glPostingHead: GlPostingHead[], glPostingDetails: GlPostingDetails[]) {
    const mergedData = glPostingHead.map(head => {
      const filteredDetails = glPostingDetails.filter(detail =>
        detail.glTransactionId?.glTransactionId === head.glTransactionId
      );

      return {
        businessPartnerId: head.businessPartnerId,
        details: filteredDetails
      };
    });

    console.log("mergedData:", mergedData);

    // No businessPartner filter — include all
    const supplierDetails = mergedData;

    // No need to fetch business partner info anymore
    this.businessPartnerDetails = null;

    return supplierDetails;
  }




  private dateFilterTimeout: any;

  onDateChange() {
    clearTimeout(this.dateFilterTimeout);
    this.dateFilterTimeout = setTimeout(() => {
      this.filterQuotes();
    }, 300); // waits 300ms
  }





  onSearchChange() {
    this.filterQuotes();
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.filterQuotes();
  }

  filterQuotes() {
    const searchTermLower = this.searchTerm?.toLowerCase().trim() || '';
    let filtered = this.processedData;

    // Filter by active tab
    if (this.activeTab !== 'all') {
      filtered = filtered.filter(record =>
        record.details?.some((transaction: any) =>
          transaction.glTransactionId?.status?.toLowerCase() === this.activeTab
        )
      );
    }

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(record =>
        record.details?.some((transaction: any) =>
          String(transaction.glTransactionId?.glTransactionId).toLowerCase().includes(searchTermLower)
        )
      );
    }

    // Filter by start date
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(record =>
        record.details?.some((transaction: any) =>
          new Date(transaction.glTransactionId?.date) >= startDate
        )
      );
    }

    // Filter by end date
    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(record =>
        record.details?.some((transaction: any) =>
          new Date(transaction.glTransactionId?.date) <= endDate
        )
      );
    }

    this.filteredRecords = filtered;
  }

  resetFilters() {
    this.searchTerm = '';
    this.activeTab = 'all';
    this.startDate = '';
    this.endDate = '';
    this.filteredRecords = this.processedData;
  }

}

