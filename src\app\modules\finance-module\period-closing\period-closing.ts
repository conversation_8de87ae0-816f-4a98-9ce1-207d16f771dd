export class PeriodClosing {
}


  export class PeriodClosingHead {
    periodClosingHeadId: any = 0;
    periodCode: string = '';
    fromDate: string = '';
    toDate: string = '';
    entityId: number = 0;
    userId: number = 0;
  }
  
  export class PeriodClosingDetail {
    periodClosingDetailId: any = 0;
    periodClosingHeadId: PeriodClosingHead = new PeriodClosingHead();
    glCode: string = '';
    dr: number = 0.0;
    cr: number = 0.0;

  }