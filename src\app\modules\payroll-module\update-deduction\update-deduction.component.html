<div class="modal-dialog" style="width: 30%">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="add-deductions">Add Deductions</h5>
            <button type="button" class="custom-close-btn" data-bs-dismiss="modal"
                (click)="navigateToPayrollSettings()">
                <i class="bi bi-x-circle"></i>
            </button>
        </div>
        <div class="modal-body">
            <form #deductionForm='ngForm' (ngSubmit)="updateDeduction()">
                <div class="mb-3">
                    <label for="deduction-name" class="form-label">Deductions Name</label>
                    <input type="text" class="form-control" id="deduction-name" name="deduction-name"
                        [(ngModel)]="deduction.deductionName" />
                </div>
                <div class="mb-3">
                    <label for="deduction-cat" class="form-label">Deductions Category</label>
                    <select class="form-control" id="deduction-cat" name="deductionCategory"
                        [(ngModel)]="deduction.deductionCategory">
                        <option *ngFor="let deduction of deductionCategory" Value="deduction">{{deduction}}</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="gl-account" class="form-label">GL Account</label>
                    <input type="text" class="form-control" id="gl-account" name="gl-account"
                        [(ngModel)]="deduction.glAccount" />
                </div>
                <div class="mb-3">
                    <label for="wi-reportable" class="form-label">WI (Reportable)</label>
                    <input type="text" class="form-control" id="wi-reportable" name="wi-reportable"
                        [(ngModel)]="deduction.wiReportable" />
                </div>
                <div class="mb-3" style="margin-top: 20px; margin-left: 20px">
                    <input type="checkbox" name="exemptFromPAYG" [(ngModel)]="deduction.exemptFromPAYG" /><strong
                        style="margin-left: 10px">
                        Exempt from PAYG</strong><br />
                    <input type="checkbox" name="exemptFromSuperannuation"
                        [(ngModel)]="deduction.exemptFromSuperannuation" /><strong style="margin-left: 10px">
                        Exempt from Superannuation</strong>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                        (click)="navigateToPayrollSettings()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">Add</button>
                </div>
            </form>
        </div>

    </div>
</div>