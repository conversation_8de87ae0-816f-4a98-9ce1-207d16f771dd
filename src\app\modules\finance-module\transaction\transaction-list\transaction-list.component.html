<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">

    <div class="actions d-flex justify-content-between align-items-center mb-3">
        <h1 class="display-6 fw-bold" style="color: rgb(47, 44, 216);">All Transactions</h1>

        <div class="d-flex gap-2">

            <button type="button" class="btn btn-primary rounded-pill">Preview & Print</button>

            <button type="button" class="btn btn-primary rounded-pill">Email</button>
        </div>
    </div>


    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="search-create d-flex align-items-center mb-3 gap-3">
            <div class="search-label">
                <label for="search-input" class="form-label fw-bold text-dark">Search</label>
            </div>
            <div class="input-container position-relative" style="width: 600px;">
                <input type="text" class="form-control px-3 py-2 rounded-pill" id="search-input"
                    placeholder="Search here..." />
                <i class="bi bi-search position-absolute text-muted"
                    style="right: 30px; top: 50%; transform: translateY(-50%);"></i>
            </div>

        </div>


        <div>
            <button type="button" class="btn btn-outline-primary me-2 " data-bs-toggle="modal"
                data-bs-target="#newTransactionModal">Apply for Suggest Transaction</button>
            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal"
                data-bs-target="#createTransactionModal">
                Create a New Transaction</button>

        </div>
    </div>


    <div class="table-responsive">
        <table class="table table-responsive">
            <thead>
                <tr class="table-primary text-center">
                    <th scope="col"><input type="checkbox" /></th>
                    <th scope="col">Type</th>
                    <th scope="col">Description</th>
                    <th scope="col">Due Date</th>
                    <th scope="col">Total</th>
                </tr>
            </thead>
            <tbody>
                <tr class="text-center">
                    <td><input type="checkbox" /></td>
                    <td>Invoice</td>
                    <td>E Bank Dep</td>
                    <td>2024/01/31</td>
                    <td>$150.00</td>
                </tr>
                <tr class="text-center">
                    <td><input type="checkbox" /></td>
                    <td>Expense</td>
                    <td>E Bank Dep</td>
                    <td>2024/01/31</td>
                    <td>$50.00</td>
                </tr>
                <tr class="text-center">
                    <td><input type="checkbox" /></td>
                    <td>Invoice</td>
                    <td>E Bank Dep</td>
                    <td>2024/01/31</td>
                    <td>$150.00</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>


<div class="modal fade" id="newTransactionModal" tabindex="-1" aria-labelledby="newTransactionModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title fw-bold" id="newTransactionModalLabel">Suggested Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="transactionType" class="form-label">Type</label>
                        <select class="form-select" id="transactionType">
                            <option value="Invoice">Invoice</option>
                            <option value="Expense">Expense</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="transactionDescription" class="form-label">Description</label>
                        <input type="text" class="form-control" id="transactionDescription"
                            placeholder="Auto-populated based on statement line" />
                    </div>
                    <div class="mb-3">
                        <label for="transactionAmount" class="form-label">Amount</label>
                        <input type="text" class="form-control" id="transactionAmount" placeholder="$450.00" />
                    </div>
                    <div class="mb-3">
                        <label for="transactionDate" class="form-label">Date</label>
                        <input type="date" class="form-control" id="transactionDate" />
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Accept Transaction</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="createTransactionModal" tabindex="-1" aria-labelledby="createTransactionModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        
            <div class="modal-header">
                <h5 class="modal-title fw-bold" id="createTransactionModalLabel">Create New Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
          
            <div class="modal-body">
                <form>
              
                    <div class="mb-3">
                        <label for="transactionType" class="form-label">Type</label>
                        <select class="form-select" id="transactionType">
                            <option value="Invoice">Invoice</option>
                            <option value="Expense">Expense</option>
                        </select>
                    </div>
                  
                    <div class="mb-3">
                        <label for="transactionDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="transactionDescription" rows="2"
                            placeholder="Enter description"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="transactionAmount" class="form-label">Amount</label>
                        <input type="number" class="form-control" id="transactionAmount" placeholder="Enter amount" />
                    </div>
                
                    <div class="mb-3">
                        <label for="transactionDate" class="form-label">Date</label>
                        <input type="date" class="form-control" id="transactionDate" />
                    </div>
                </form>
            </div>
          
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Create Transaction</button>
            </div>
        </div>
    </div>
</div>