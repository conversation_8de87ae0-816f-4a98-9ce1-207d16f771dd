<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <div class="header">
    <h3>Create Credit Note</h3>
  </div>

  <form #f="ngForm" class="styled-form" novalidate>
    <div class="bd">
      <div class="form-section">
        <div class="form-row">
          <div class="form-group">
            <label for="invoiceNumber">Invoice No:</label>
            <input id="invoiceNumber" type="text" required [(ngModel)]="creditNote.invoiceNumber" name="invoiceNumber" disabled />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="creditNoteNumber">Credit Note No:</label>
            <input id="creditNoteNumber" type="text" required [(ngModel)]="creditNote.creditNoteNumber" name="creditNoteNumber" disabled />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="documentDate">Document Date:</label>
            <input id="documentDate" type="date" required [(ngModel)]="creditNote.documentDate" name="documentDate" />
            <div *ngIf="f.controls['documentDate']?.touched && f.controls['documentDate']?.invalid" class="text-danger">
              Date is required.
            </div>
          </div>
        </div>
      </div>

      <div class="table-section">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>#</th>
                <th>Description</th>
                <th>Unit Price</th>
                <th>Quantity</th>
                <th>Subtotal (Excl.GST)</th>
                <!-- <th>Invoice Balance</th> -->
                <th>Total GST</th>
                <th>Credit Issued (Pre Tax)</th>
                <th>Remaining Balance</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let invoice of selectedInvoices; let i = index">
                <td>{{ i + 1 }}</td>
                <td>
                  <input type="text" class="form-control" required [(ngModel)]="invoice.description" name="invoiceNumber{{i}}" disabled />
                </td>
                <td>
                  <input type="text" class="form-control" [ngModel]="invoice.unitPrice" name="unitPrice{{i}}" disabled />
                </td>
                <td>
                  <input type="text" class="form-control" [ngModel]="invoice.quantity" name="quantity{{i}}" disabled />
                </td>
                <td>
                  <input type="text" class="form-control" [value]="invoice.amount | currency:'USD':'symbol':'1.2-2'" disabled />
                  <input type="hidden" [(ngModel)]="invoice.grandTotalamount" name="amount{{i}}" />
                </td>
                <td>
                  <input type="text" class="form-control" [value]="invoice.tax | currency:'USD':'symbol':'1.2-2'" disabled />
                  <input type="hidden" [(ngModel)]="invoice.tax" name="tax{{i}}" />
                </td>
                <!-- <td>
                  <input type="text" class="form-control" [value]="invoice.balanceAmount ? (invoice.balanceAmount | currency:'USD':'symbol':'1.2-2') : '0.00'" disabled />
                  <input type="hidden" [(ngModel)]="invoice.balanceAmount" name="balanceAmount{{i}}" />
                </td> -->
                <td>
                  <input type="number" class="form-control" required [(ngModel)]="invoice.creditAmount" name="creditAmount{{i}}" (keydown)="preventEnter($event)" min="0" [ngClass]="{ 'is-invalid': f.submitted && f.controls['creditAmount' + i].invalid }" />
                  <div *ngIf="f.controls['creditAmount' + i]?.touched && f.controls['creditAmount' + i]?.invalid" class="text-danger">
                    Credit Amount is required.
                  </div>
                </td>
                <td>
                  <input id="InvoiceBalance" type="text" class="form-control" [value]="getInvoiceNewBalance(invoice) | currency:'USD':'symbol':'1.2-2'" disabled />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="form-section_2 flex-row gap-5 pb-0">
        <div class="form-row w-50">
          <div class="form-group_2">
            <label for="remarks">Remarks:</label>
            <textarea id="remarks" placeholder="Add a note or reference here (optional)" required [(ngModel)]="creditNote.remarks" name="remarks" rows="3"></textarea>
          </div>
        </div>
        <div class="form-row w-50 d-grid">
          <div class="form-group_2">
            <label for="creditApplied">Credit Applied</label>
            <input style="height: 40px;" id="creditApplied" type="text" class="form-control" [value]="getTotalCreditAmount() | currency:'USD':'symbol':'1.2-2'" disabled />
          </div>
          <div class="form-group_2">
            <label for="creditAppliedWithGST">Credit Applied With GST</label>
            <input id="creditAppliedWithGST" type="text" class="form-control" style="height: 40px;" [value]="getTotalCreditAmount() | currency:'USD':'symbol':'1.2-2'" disabled />
          </div>
        </div>
      </div>
      <div class="form-section_2 pt-0">
        <div class="d-flex justify-content-end mt-5 mb-4 btns w-100">
          <button type="button" class="btn btn-secondary me-2" (click)="onCancel()">Cancel</button>
          <button type="submit" class="btn btn-primary" (click)="onSubmit(f)">Save Credit Note</button>
        </div>
      </div>
    </div>
  </form>
</div>
