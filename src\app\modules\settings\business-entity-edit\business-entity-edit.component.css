body {
  font-family: Inter;
  margin: 0;
  padding: 0;
}

.container-fluid {
  background-color: #ffffff;
  height: fit-content;
  width: 100%;
  margin: 0;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.header h3 {
  font-family: Inter;
  font-size: 36px;
  font-weight: 700;
  color: #4262ff;
}

.search-create {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.search-create .nav-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 0;
  border-bottom: none;
  list-style: none;
}

/* Tab items */
.search-create .nav-item {
  flex: 1 1 auto; /* allow equal width and responsiveness */
}

/* Tab buttons */
.search-create button {
  width: 100%;
  background: #d9dfff;
  color: #4262ff;
  font-weight: 500;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 15px;
  /* width: 160px; */
  border-radius: 8px;
  border: none; /* Remove any border */
  text-decoration: none; /* Remove underline */
}

.search-create button:hover {
  background: #4262ff;
  color: #fff;
}

.nav-item button {
  outline: none !important; /* Remove button outline if it appears */
}

.search-create .nav-tabs .nav-link {
  background: #fff;
  color: #4262ff;
  font-weight: 500;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 15px;
  border-radius: 8px;
  border: 1px solid #4262ff !important;
  margin: 20px 0 0 0;
  border: none;
  text-decoration: none;
}

.search-create .nav-tabs .nav-link:hover {
  background: #4262ff;
  color: #fff;
}

.main-form {
  border: 1px solid #cec9c9;
  background-color: #fff;
  border-radius: 12px;
  margin: 1% 10%;
}

.middle-form {
  margin: 3% 3% 3% 3%;
}

.form-headline {
  color: #000000;
  margin-top: 20px;
}

.subhead {
  font-weight: 600;
  font-family: Inter;
  font-size: 28px;
  margin-bottom: 30px;
}

/* .detail {
  margin: 1% 0 1% 0;
} */

img {
  margin-left: 20px;
}

.abn-input {
  border: 1px solid #cec9c9;
  background-color: #ffffff;
  /* border-right: none; */
  border-radius: 9px;
  /* height: 46.5px; */
}

.en-input {
  border: 1px solid #cec9c9;
  background-color: #ffffff;
  border-radius: 9px;
  /* height: 46.5px; */
}

.abn-button {
  font-family: Inter;
  color: #4262ff;
  border: 1px solid #cec9c9;
  background-color: #ffffff;
  border-left: none;
  border-radius: 9px;
  font-weight: 700;
}

.bs-button {
  border: 1px solid #cec9c9;
  background-color: #ffffff;
  border-left: none;
  border-radius: 9px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  margin-bottom: 2%; /* Align items vertically in the center */
}

.checkbox-container h6 {
  margin-left: 8px;
  /* Adjust spacing between checkbox and text */
  margin-bottom: 0;
  /* Remove default margin-bottom of h6 */
}

.input-headline {
  font-size: 20px;
  font-weight: 400;
  font-family: Inter;
}

.radio-btn {
  font-size: 20px;
  font-weight: 600;
  font-family: Inter;
}

.file-input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 400px;
  border: 1px solid #cec9c9;
  border-radius: 9px;
}

.browse-btn {
  background-color: #007bff;
  /* Blue button color */
  color: white;
  padding: 10px 10px;
  border-radius: 12px;
  cursor: pointer;
  display: inline-block;
  line-height: 4px;
  width: 20%;
  font-size: 16px;
  margin-left: 5%;
}

.file-input {
  display: none;
}

.file-label {
  flex-grow: 1;
  padding: 10px;
  background-color: #fff;
  color: #6c757d;
  height: 40px;
  line-height: 20px;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.toolbar {
  margin-bottom: 10px;
}

.img-group {
  width: 30px;
  height: 30px;
  margin-right: 1%;
}

textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  resize: vertical;
}

.btn-group {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin: 20px;
}

.save-btn {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  border: none;
  width: 192px;
  padding: 10px 20px;
  border-radius: 15px;
  cursor: pointer;
  margin-bottom: 10px !important;
}

.cancel-btn {
  width: 192px;
  padding: 10px 20px;
  border: 2px solid #4262ff;
  background: #ffffff;
  color: #4262ff;
  border-radius: 17px;
  cursor: pointer;
  margin-bottom: 10px !important;
}

.cancel-btn:hover {
  background-color: #4262ff;
  color: white;
}

/*email template cancel n save buttons*/
.email-save-btn {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  border: none;
  border-radius: 17px;
  font-size: 16px;
  font-weight: bold;
  width: 127px;
  padding: 10px 20px;
}

.email-cancel-btn {
  width: 127px;
  padding: 10px 20px;
  border: 2px solid #4262ff;
  background: #ffffff;
  color: #4262ff;
  border-radius: 17px;
  font-size: 16px;
  font-weight: bold;
}

.email-cancel-btn:hover {
  background-color: #4262ff;
  color: white;
}

/* connect basiq cncel n sve buttons */
.basiq-save-btn {
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  border: none;
  border-radius: 17px;
  font-size: 16px;
  font-weight: bold;
  width: 150px;
  padding: 10px 10px;
}

.basiq-cancel-btn {
  width: 130px;
  padding: 10px 10px;
  border: 2px solid #4262ff;
  color: #4262ff;
  border-radius: 17px;
  font-size: 16px;
  font-weight: bold;
}

.basiq-cancel-btn:hover {
  background-color: #4262ff;
  color: white;
}

.modal-footer {
  padding: 10px 0;
}

.custom-close-btn {
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1.5rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 50%;
}

.row label {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}
.custom-close-btn:hover {
  background-color: #e9ecef;
}

.input {
  height: 28px;
  padding-block: 20px;
}

.btn-group-1 {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-bottom: 20px;
  margin-right: 140px;
  /* margin: 0 5% 5% 0; */
}

.edit-entity-container {
  display: flex;
  margin-top: 10px;
}

.edit-entity-btn {
  color: #4a4ae2;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-style: italic;
  font-weight: bold;
}

.edit-entity-btn:hover {
  color: #4a4ae2; /* same as default */
  background: none;
}

.form-headline {
  display: flex;
  justify-content: space-between;
}

h2 {
  margin-bottom: 40px;
  font-family: Inter;
  font-size: 30px;
  font-weight: 600;
  text-align: left;
  color: #535353;
}

.close-icon {
  font-size: 24px;
  color: #6c757d;
  cursor: pointer;
}

.radio-group {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 50px;
}

.radio-group label {
  margin: 0 10px;
  font-family: Inter;
  font-size: 15px;
  font-weight: 600;
  line-height: 29.05px;
  text-align: left;
}

.form-group {
  margin-bottom: 20px;
  /* margin-left: 20px; */
  text-align: left;
  font-family: "Inter", sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 1.6;
  color: #444343;
  position: relative;
  flex: 1;
}

.form-group button {
  border: none;
  border-radius: 5px;
  background-color: white;
  color: #4262ff;
  cursor: pointer;
  font-family: Inter;
  font-size: 14px;
  font-weight: 700;
  text-align: left;
  margin-top: 8px;
}

.form-group .remove-btn {
  border: none;
  border-radius: 5px;
  background-color: white;
  color: #ff0000;
  cursor: pointer;
  font-family: Inter;
  font-size: 14px;
  font-weight: 700;
  text-align: left;
  margin-top: 8px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.form-row-1 {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 5px;
}

.form-group input[type="text"],
.form-group select {
  width: 100%;
  padding: 10px;
  border: 2px solid #c7c7c7;
  border-radius: 8px;
  box-sizing: border-box;
  font-size: 1em;
  font-family: "Inter", sans-serif;
}

.form-group select:focus {
  border-color: #4262ff;
  box-shadow: 0 0 5px rgba(66, 98, 255, 0.3);
  outline: none;
}

.form-group select:hover {
  border-color: #a3a3a3;
}

.form-group input[type="file"] {
  width: 47%;
  border: 2px solid #c7c7c7;
  border-radius: 8px;
  box-sizing: border-box;
  font-size: 1em;
  font-family: Inter;
  padding: 5px;
  margin-bottom: 20px;
}

.img-preview {
  margin-left: 20px;
  margin-bottom: 10px;
}

.update-entity-btn {
  display: block;
  margin-left: auto;
  margin-bottom: 20px;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  padding: 10px 60px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-family: Inter;
  font-size: 17px;
  font-weight: 700;
  line-height: 29.05px;
  text-align: center;
}

.update-entity-btn:hover {
  background: linear-gradient(to left, #4262ff, #512ca2);
}

.read-only {
  background-color: #e0e0e0;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  max-width: 750px;
  width: 90%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.modern-basiq-modal-content {
  border-radius: 18px;
  box-shadow: 0 4px 32px rgba(66, 98, 255, 0.13);
  border: none;
  padding: 0;
}

.modern-basiq-modal-header {
  background: #f7f9fc;
  border-radius: 18px 18px 0 0;
  border-bottom: 1px solid #e0e4f7;
  padding: 20px 28px 16px 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modern-basiq-modal-header .modal-title {
  font-family: Inter, sans-serif;
  font-size: 1.4rem;
  font-weight: 700;
  color: #4262ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-basiq-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #888;
  cursor: pointer;
  border-radius: 50%;
  transition: background 0.15s;
  padding: 0.25rem 0.5rem;
}

.modern-basiq-close:hover {
  background: #f4f6fa;
  color: #4262ff;
}

.modern-basiq-modal-body {
  padding: 28px 28px 18px 28px;
  background: #fff;
  border-radius: 0 0 18px 18px;
}

.modern-basiq-label {
  font-weight: 600;
  color: #4262ff;
  font-family: Inter, sans-serif;
  margin-bottom: 6px;
}

.modern-basiq-input {
  border-radius: 8px;
  border: 1.5px solid #e0e4f7;
  padding: 10px 12px;
  font-size: 15px;
  background: #f7f9fc;
  transition: border 0.2s;
}

.modern-basiq-input:focus {
  border-color: #4262ff;
  outline: none;
}

.modern-basiq-btn,
.modern-basiq-btn-outline {
  background: linear-gradient(90deg, #4262ff 0%, #512ca2 100%);
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 10px 28px;
  font-size: 1.08rem;
  font-weight: 600;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
  box-shadow: 0 2px 8px rgba(66, 98, 255, 0.07);
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-basiq-btn-outline {
  background: #fff;
  color: #4262ff;
  border: 2px solid #4262ff;
  box-shadow: none;
}

.modern-basiq-btn-outline:hover {
  background: #4262ff;
  color: #fff;
}

.modern-basiq-btn:hover {
  background: linear-gradient(90deg, #512ca2 0%, #4262ff 100%);
  color: #fff;
}

.modern-basiq-btn-danger {
  background: #fff;
  color: #ff4d4f;
  border: 2px solid #ff4d4f;
  border-radius: 10px;
  padding: 10px 28px;
  font-size: 1.08rem;
  font-weight: 600;
  transition: background 0.18s, color 0.18s, border 0.18s;
  box-shadow: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-basiq-btn-danger:hover {
  background: #ff4d4f;
  color: #fff;
}

.modern-basiq-success {
  font-size: 1.1rem;
  color: #1bbf5c;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-basiq-accounts {
  margin-bottom: 18px;
}

.modern-basiq-account-item {
  border-radius: 8px;
  background: #f7f9fc;
  margin-bottom: 6px;
  padding: 10px 14px;
}

.modern-basiq-action-group {
  gap: 10px;
}

.modern-basiq-select {
  border-radius: 8px;
  border: 1.5px solid #e0e4f7;
  padding: 10px 12px;
  font-size: 15px;
  background: #fff;
  transition: border 0.2s;
}

.modern-basiq-select:focus {
  border-color: #4262ff;
  outline: none;
}

.modern-basiq-footer {
  border-top: none;
  padding: 18px 28px 18px 28px;
  background: #f7f9fc;
  border-radius: 0 0 18px 18px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modern-tab-btn {
  background: linear-gradient(90deg, #4262ff 0%, #512ca2 100%);
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 10px 28px;
  font-size: 1.08rem;
  font-weight: 600;
  margin: 0 8px 0 0;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
  box-shadow: 0 2px 8px rgba(66, 98, 255, 0.07);
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-tab-btn-outline {
  background: #fff;
  color: #4262ff;
  border: 2px solid #4262ff;
  border-radius: 10px;
  padding: 10px 28px;
  font-size: 1.08rem;
  font-weight: 600;
  margin: 0 8px 0 0;
  transition: background 0.18s, color 0.18s, border 0.18s;
  box-shadow: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-tab-btn:hover {
  background: linear-gradient(90deg, #512ca2 0%, #4262ff 100%);
  color: #fff;
}

.modern-tab-btn-outline:hover {
  background: #4262ff;
  color: #fff;
  border: 2px solid #4262ff;
}

.search-create .nav-tabs .modern-tab-btn,
.search-create .nav-tabs .modern-tab-btn-outline {
  margin: 0 8px 0 0;
  border: none;
  box-shadow: none;
}

@media (max-width: 767px) {
  .modern-tab-btn,
  .modern-tab-btn-outline {
    padding: 8px 12px;
    font-size: 0.98rem;
  }

  .search-create .nav-tabs {
    flex-direction: column;
    align-items: center;
  }

  .search-create .nav-item {
    flex: unset;
  }

  .header h3 {
    font-size: 28px;
  }

  .main-form {
    margin: 2% 10%;
  }

  .btn-group-1 {
    justify-content: center;
    margin-right: 0;
  }

  .radio-group {
    flex-direction: column;
    gap: 10px; /* reduce spacing for mobile */
    align-items: flex-start; /* optional, aligns items to the left */
  }
}

