import { Component, Inject } from '@angular/core';
import { BankReconciliationService } from '../../bank-reconciliation.service';
import {
  BankRecDocumentType,
  BankStatementMatchDetailView,
  BankStatementMatchDocumentDetail,
  BankStatementMatchedDetails,
  bankRecDocumentList,
} from '../../bank-reconciliation';
import Swal from 'sweetalert2';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { transition } from '@angular/animations';

@Component({
  selector: 'app-match-transactions',
  templateUrl: './match-transactions.component.html',
  styleUrls: ['./match-transactions.component.css'],
})
export class MatchTransactionsComponent {
  matchedDetails: any[] = [];
  bankStatementId!: string;
  documentTypes = bankRecDocumentList();
  pendingTransactionCount: number = -1;
  searchFilter = {
    searchTerm: '',
    transactionTypeTerm: 'All',
    documentTypeTerm: 'All',
  };

  constructor(
    private bankReconciliationService: BankReconciliationService,
    @Inject(MAT_DIALOG_DATA) public data: { bankStatementId: string },
    public dialogRef: MatDialogRef<MatchTransactionsComponent>
  ) {}

  ngOnInit() {
    this.bankStatementId = this.data?.bankStatementId || '';
    console.log('bankStatementId:', this.bankStatementId);
    if (this.bankStatementId) {
      localStorage.setItem('bankStatementId', this.bankStatementId);
      this.fetchMatchedDetails();
      this.fetchTransactionPendingCount();
    } else {
      console.error('No bankStatementId found in state or localStorage');
    }
  }

  fetchTransactionPendingCount() {
    const entityId = +(localStorage.getItem('entityId') || 0);
    console.log('entityId:', entityId);
    console.log('bankStatementId:', this.bankStatementId);
    if (entityId !== 0 && this.bankStatementId !== null) {
      this.bankReconciliationService
        .getPendingTransactionCount(entityId, this.bankStatementId)
        .subscribe(
          (count: number) => {
            this.pendingTransactionCount = count;
          },
          (err) => {
            console.error(err);
          }
        );
    }
  }

  fetchMatchedDetails() {
    // const entityId = localStorage.getItem('entityId');
    const entityId = +(localStorage.getItem('entityId') || 0);
    console.log('entityId:', entityId);
    // const bankStatementId = (localStorage.getItem('bankStatementId') || '');
    console.log('bankStatementId:', this.bankStatementId);
    if (entityId !== 0 && this.bankStatementId !== null) {
      this.bankReconciliationService
        .getMatchedDetails(entityId, this.bankStatementId)
        .subscribe(
          (data: BankStatementMatchedDetails[]) => {
            this.matchedDetails = [];

            // Group matchedDetails via 'groupId'
            const itemsGrouped: {
              [groupId: string]: BankStatementMatchedDetails[];
            } = {};

            data.forEach((matchedItem) => {
              const groupId = matchedItem.groupId;
              if (!groupId) {
                console.warn('Matched item missing groupId:', matchedItem);
                return;
              }
              if (!itemsGrouped[groupId]) {
                itemsGrouped[groupId] = [];
              }
              itemsGrouped[groupId].push(matchedItem);
            });

            Object.keys(itemsGrouped).forEach((groupId) => {
              const matchedItems = itemsGrouped[groupId];
              console.log('Group ID:', groupId);

              const referenceObject: BankStatementMatchedDetails =
                matchedItems[0];

              const detailList = matchedItems.map(
                (bankStatementMatchedDetail: BankStatementMatchedDetails) => {
                  const bankStatementMatchDocumentDetail: BankStatementMatchDocumentDetail =
                    {
                      documentId: bankStatementMatchedDetail.documentId,
                      glAccountId: bankStatementMatchedDetail.glAccountId,
                      transactionAmount:
                        bankStatementMatchedDetail.transactionAmount,
                      transactionDescription:
                        bankStatementMatchedDetail.transactionDescription,
                      gstApplicability:
                        bankStatementMatchedDetail.gstApplicability,
                    };
                  return bankStatementMatchDocumentDetail;
                }
              );

              const bankStatementMatchDetailView: BankStatementMatchDetailView =
                {
                  id: referenceObject.id,
                  groupId: groupId,
                  transactionId: referenceObject.transactionId,
                  transactionType: referenceObject.transactionType,
                  recordDate: referenceObject.recordDate,
                  bankRecDocumentType: referenceObject.bankRecDocumentType,
                  bankRecMatchStatus: referenceObject.bankRecMatchStatus,
                  bankRecMatchMode: referenceObject.bankRecMatchMode,
                  documentDetailList: detailList,
                };

              this.matchedDetails.push(bankStatementMatchDetailView);
            });
          },
          (error) => {
            console.error('Error fetching matched details:', error);
          }
        );
    }
  }

  get filteredMatchedDetails(): BankStatementMatchDetailView[] {
    return this.matchedDetails.filter((matchGroup) => {
      const term = this.searchFilter.searchTerm.trim().toLowerCase();

      const matchesTransactionType =
        this.searchFilter.transactionTypeTerm === 'All' ||
        matchGroup.transactionType === this.searchFilter.transactionTypeTerm;

      const normalize = (val: string | undefined | null): string =>
        val?.toLowerCase().replace(/_/g, ' ') ?? '';

      const matchesDocumentType =
        this.searchFilter.documentTypeTerm === 'All' ||
        normalize(matchGroup.bankRecDocumentType?.toString()) ===
          this.searchFilter.documentTypeTerm.toLowerCase();

      // At least one documentDetail must match the search term
      const matchesSearchTerm =
        !term ||
        matchGroup.documentDetailList?.some(
          (detail: BankStatementMatchDocumentDetail) => {
            return (
              (detail.transactionDescription &&
                detail.transactionDescription.toLowerCase().includes(term)) ||
              (detail.documentId !== null &&
                detail.documentId !== undefined &&
                detail.documentId.toString().includes(term))
            );
          }
        ) ||
        (matchGroup.transactionId?.bankStatementId?.toString() || '')
          .toLowerCase()
          .includes(term);

      return matchesSearchTerm && matchesTransactionType && matchesDocumentType;
    });
  }

  undoMatching(groupId: string) {
    if (!groupId || groupId === '') {
      console.error('MatchedTransaction groupId is null or not valid');
      return;
    }

    const entityId = +(localStorage.getItem('entityId') || 0);
    console.log('entityId:', entityId);

    this.bankReconciliationService
      .undoTransactionMatching(entityId, groupId)
      .subscribe(
        (success: Boolean) => {
          console.log('Undo ' + success ? 'Successfull' : 'Un-successfull');
          this.fetchMatchedDetails();
        },
        (err) => {
          console.error(err);
        }
      );
  }

  reconsileAll() {
    const entityId = +(localStorage.getItem('entityId') || 0);
    this.bankReconciliationService
      .reconciliationFinalize(entityId, this.bankStatementId)
      .subscribe(
        (success) => {
          console.log('All Reconsiled : ', success);
          this.fetchMatchedDetails();
        },
        (err) => {
          console.error(err);
        }
      );
  }
}
