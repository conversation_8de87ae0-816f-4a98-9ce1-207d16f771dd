<app-admin-navigation></app-admin-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response> 
<div class="container">
    <div class="actions">
        <h1>Business Partner</h1>
    </div>
    <div class="search-create">
        <button type="button" (click)="createNewBusinessPartner()" class="invoice-convert">Create New Business Partner</button>
    </div>
    <div class="table-container">
        <table>
            <thead>
                <tr class="table-head">
                    <th scope="col" style="width: 130px; text-align: left;"  class="valuehead">Business Partner Type</th>
                    <th scope="col" style="width: 145px; text-align: left;" class="valuehead">Business Partner Name</th>
                    <th scope="col" style="width: 100px; text-align: left;" class="valuehead">ABN Number</th>
                    <th scope="col" style="width: 130px; text-align: left;" class="valuehead">Business Address</th>
                    <th scope="col" style="width: 100px; text-align: left;" class="valuehead">Email</th>
                    <th scope="col" style="width: 100px; text-align: left;" class="valuehead">Contact Number</th>          
                    <th scope="col" style="width: 50px; text-align: center;" class="valuehead">Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let businessPartner of businessPartner">
                    <td style="width: 130px; text-align: left;" class="value ">{{ businessPartner.businessPartnerTypeId.businessPartnerType }}</td>
                    <td style="width: 145px; text-align: left;" class="value ">{{ businessPartner.bpName }}</td>
                    <td style="width: 100px; text-align: left;" class="value ">{{ businessPartner.abnNumber }}</td>
                    <td style="width: 130px; text-align: left;" class="value ">{{ businessPartner.businessAddress }}</td>
                    <td style="width: 100px; text-align: left;" class="value ">{{ businessPartner.email }}</td>
                    <td style="width: 100px; text-align: left;" class="value ">{{ businessPartner.contactPhoneNumber }}</td>
                    
                    <td style="width: 50px; text-align: center;" class="value">
                        <button class="btn btn-orange btn-sm" 
                            (click)="updateBusinessPartner(businessPartner. businessPartnerId)" 
                            style="margin-right: 4px; border: none; background: none; padding: 2px; font-size: 1.2rem;" title="Edit">
                            <i class="ri-edit-box-line" style="color: #4262FF;"></i>
                        </button>
                       <!-- <button class="btn btn-warning btn-sm"
                            data-bs-target="#partnerPreviewModal"
                            data-bs-toggle="modal"
                            style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1.2rem;" title="Preview">
                            <i class="bi bi-eye" style="color: #debe15;"></i>
                        </button>-->
                        <button class="btn btn-danger btn-sm" 
                            (click)="deleteBusinessPartner(businessPartner. businessPartnerId)" 
                            style="margin-right: 2px; border: none; background: none; padding: 4px; font-size: 1.2rem;" title="Delete">
                            <i class="ri-delete-bin-line" style="color: #FF0000;"></i>
                        </button>
                    </td>
                </tr>
            </tbody>    
        </table>    
    </div>
</div>

<!-- business partner preview -->
 <div class="modal fade" id="partnerPreviewModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 740px;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="simpleModalLabel">Business Partner</h5>
                <div class="ml-auto icon-group">
                    <i class="bi bi-x-circle close-icon" #closePreview data-bs-dismiss="modal" aria-label="Close" title="Close"></i>
                </div>
            </div>

            <div class="modal-body">
                <div class="spinner-container">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>

                <div style="margin-top: 10px;">
                    <iframe #partnerPreviewModal id="partnerPreviewModal" width="700px" height="100px"></iframe>
                </div>
            </div>
        </div>
    </div>
 </div>