<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response> 

<div class="container">
    <div class="heading-section">
        <h1>Reports</h1>
    </div>

    <div class="button-section">
        <button type="button" class="custom-button" (click)="navigateQuotationReport()">Sales Quote <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateInvoiceReport()">Sales Invoice <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateCreditNoteReport()">Credit Notes <span class="arrow">&gt;</span></button>
        <button *ngIf="userRole !== 'Free'"  type="button" class="custom-button" (click)="navigatePaymentReport()">Payments <span class="arrow">&gt;</span></button>
        <button  *ngIf="userRole !== 'Free'" type="button" class="custom-button" (click)="navigateInvoiceCashReport()">Sales Receivables <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateCustomerStatementReport()">Customer Statements <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateAccountsStatementReport()">Account Statements <span class="arrow">&gt;</span></button>
    </div>
</div>