import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpService } from 'src/app/http.service';
import { environment } from 'src/environments/environment';
import { Earning, PayItemType, PayPeriod, PayProcessDTO, PayRunDetailsSummary, PayRunMaster } from '../payroll-setting';
import { EmployeeEmployment } from '../empolyee/employee';

@Injectable({
  providedIn: 'root',
})
export class PayRunService {
  private readonly baseURL = environment.payrollApiUrl;

  constructor(private http: HttpClient, private httpService: HttpService) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }
    
    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  getPayRunList(id: number): Observable<PayRunMaster[]> {
    return this.request('GET', `/payRunMaster/getAllByEntityId/${id}`, {});
  }

  getPayRunById(payRunId: number): Observable<PayRunMaster> {
    return this.request('GET',`/payRunMaster/getById/${payRunId}`, {} )
  }

  getAllPayPeriods(entityId: number): Observable<PayPeriod[]> {
    return this.request('GET', '/payPeriod/getAll', null, {
      entityId: entityId.toString(),
    });
  }

  addPayRun(payRun: PayRunMaster, flag: string): Observable<PayRunMaster> {
    const params = { flag };
    return this.request('POST', '/payRunMaster/saveById', payRun, params);
  }

  addUnscheduledPayRun(payRun: PayRunMaster): Observable<PayRunMaster> {
    return this.request('POST', '/payRunMaster/unscheduledPayRun', payRun);
  }

  getPayprocessDetails(
    entityId: number,
    calendarId: number,
    peroidId: number
  ): Observable<PayProcessDTO[]> {
    return this.request(
      'GET',
      `/payProcess/getPayProcessDataByCalendar`,
      null,
      {
        entityId: entityId.toString(),
        calendarId: calendarId.toString(),
        peroidId: peroidId.toString(),
      }
    );
  }
  getPayprocessDetailsByYear(
    entityId: number,
    fromYear: any,
    toYear: any
  ): Observable<PayProcessDTO[]> {
    return this.request(
      'GET',
      `/payProcess/getPayProcessDataByCalendarYear`,
      null,
      {
        entityId: entityId.toString(),
        fromYear: fromYear.toString(),
        toYear: toYear.toString(),
      }
    );
  }
  
  getPayRunDetails(
    entityId: number,
    employeeId: number,
    payPeroidId: number
  ): Observable<any[]> {
    return this.request('GET', `/payRunDetail/getAll`, null, {
      entityId: entityId.toString(),
      employeeId: employeeId.toString(),
      payPeroidId: payPeroidId.toString(),
    });
  }

  getUnscheduledPayRunEmployees( payRunId: number): Observable<PayRunDetailsSummary[]> {
    return this.request('GET', `/payRunDetailsSummary/getEmployeesForUnscheduledPayRun/${payRunId}`, null);
  }

  getEmployeesByPayCalendar(entityId: number, payCalendarId: number): Observable<EmployeeEmployment[]> {
    return this.request('GET', `/employeeEmployment/getEmployeesByPayCalendar/${payCalendarId}`, null, {
      entityId: entityId.toString(),
    });
  }

  deletePayRun(index: number): Observable<any> {
    return this.request('DELETE', `/payRunDetail/deleteById/${index}`, {});
  }

  deleteDraftPayRun(id: number): Observable<any> {
    return this.request('DELETE', `/payRunMaster/deleteByDraftRunId/${id}`, {});
  }

  getPayPeriodById(payPeriodId: number) : Observable<PayPeriod>{
    return this.request('GET',`/payPeriod/getById/${payPeriodId}`, {} );
  }

  updatePayRunPost(payPeriodId: number) : Observable<PayPeriod> {
    return this.request('PUT', `/payPeriod/postPayRun/${payPeriodId}`, {});
  }

  updatePayRunPostDate(payPeriodId: number) : Observable<PayPeriod> {
    return this.request('PUT', `/payRunMaster/postPayRunDate/${payPeriodId}`, {});
  }

  updatePayRunBankJournal(payPeriodId: number) : Observable<PayPeriod> {
    return this.request('POST', `/payRunBankJournalHead/save/${payPeriodId}`, {});
  }

  stpFileSubmit(payCalendarId: number, payPeriodId: number, payRunId: number, entityId: number, userId: number): Observable<any> {
    return this.request('POST', `/stp/submit/${payCalendarId}/${payPeriodId}/${payRunId}/${entityId}/${userId}`, {});
  }

  stpFileResubmit(atoMessageId: number, payRunId: number, entityId: number, userId: number): Observable<any> {
    return this.request('POST', `/stp/submit/${atoMessageId}/${payRunId}/${entityId}/${userId}`, {});
  }

  stpFileSubmitByMessageId(messageId: string): Observable<any> {
    return this.request('GET', `/stp/poll-status/${messageId}`, {});
  }
  viewErrorDetails(messageId: string): Observable<any> {
    return this.request('GET', `/stp/poll-viewError/${messageId}`, {});
  }
   
  getPayRunSummaryReport(payPeriodId: number, entityId: number) : Observable<any> {
    const url = `/payRunDetail/getPayRunSummaryReport?payPeriodId=${payPeriodId}&entityId=${entityId}`;
    const token = localStorage.getItem('authToken') || '';

  const headers = {
    Authorization: `Bearer ${token}`,
  };
    return this.request('GET', url, {headers});
  }
}
