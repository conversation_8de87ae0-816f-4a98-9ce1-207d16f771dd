<app-sidebar></app-sidebar>
<app-header></app-header>
<nav class="navbar modern-navbar">
  <div class="company-detail modern-company-detail">
    <a class="navbar-brand modern-navbar-brand" href="/finance-dashboard">
      <img
        *ngIf="logoUrl"
        [src]="logoUrl"
        alt="logo"
        width="48"
        height="48"
        class="entity-logo modern-entity-logo"
      />
      <h1 class="modern-company-name">{{ entity.entityName }}</h1>
      <!-- <h1 class="navbar-entity-name" [title]="entity.entityName">
        {{ entity.entityName.length > 24 ? (entity.entityName | slice:0:24) + '…' : entity.entityName }}
      </h1> -->
    </a>
  </div>
  <div class="nav-items modern-nav-items">
    <ul>
      <li><a class="modern-nav-link" (click)="navigatePaybleBill()"><i class="bi bi-receipt"></i> Bills</a></li>
      <li><a class="modern-nav-link" (click)="navigateExpenceClaims()"><i class="bi bi-cash-stack"></i> Expenses</a></li>
      <li><a class="modern-nav-link" (click)="navigateJournalVoucher()"><i class="bi bi-journal-bookmark"></i> Journals</a></li>
      <li><a class="modern-nav-link" (click)="navigateRecordBatchPayment()"><i class="bi bi-credit-card"></i> Bill Payments</a></li>
      <li><a class="modern-nav-link" (click)="navigateCreditNoteBill()"><i class="bi bi-journal-check"></i> Credit Notes</a></li>
      <li><a class="modern-nav-link" (click)="navigateBankReconciliation()"><i class="bi bi-bank"></i> Bank Rec</a></li>
      <li><a class="modern-nav-link" (click)="navigateFinanceReport()"><i class="bi bi-bar-chart"></i> Reports</a></li>
    </ul>
  </div>
</nav>
