import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../../user.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.css'],
})
export class ResetPasswordComponent implements OnInit {
  password: string = '';
  repassword: string = '';
  token: string = '';
  mismatchError: boolean = false;
  isSubmitting: boolean = false;
  serverError: string | null = null;
  passwordValid: boolean = true; // To check if the password is valid based on regex
  passwordPattern: RegExp = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=?.])[A-Za-z\d!@#$%^&*()_+\-=?.]{8,}$/;

  constructor(
    private route: ActivatedRoute,
    private userService: UserService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.token = this.route.snapshot.queryParamMap.get('token') || '';
    if (!this.token) {
      this.serverError = 'Invalid or missing reset token.';
    }
  }

  // Check if passwords match and validate password with regex
  checkPasswordMatch(): void {
    this.mismatchError = this.password !== this.repassword;
    this.passwordValid = this.passwordPattern.test(this.password); // Validate password
  }

  // Handle form submission
  onSubmit(): void {
    if (this.mismatchError || !this.password || !this.repassword || !this.passwordValid || this.isSubmitting) {
      return; // Prevent submission on invalid input or ongoing request
    }

    this.isSubmitting = true; // Disable the form while submitting
    this.serverError = null; // Clear previous errors

    this.userService.resetPassword(this.password, this.token).subscribe(
      () => {
        // Success: Show success message and navigate to login
        this.isSubmitting = false;
        Swal.fire({
          title: 'Success!',
          text: 'Password reset successfully.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['user-login']);
          }
        });
      },
      (error) => {
        // Handle error and provide feedback
        this.isSubmitting = false;
        this.serverError =
          error?.error?.message || 'An error occurred while resetting your password.';
        console.error('Error resetting password:', error);
      }
    );
  }

  navigateUserLogin(): void {
    this.router.navigate(['user-login']);
  }
}
