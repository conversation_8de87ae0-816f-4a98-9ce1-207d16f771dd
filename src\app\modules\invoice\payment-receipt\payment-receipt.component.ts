import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { InvoiceService } from '../invoice.service';

import Swal from 'sweetalert2';
import { PaymentReceiptsHead } from '../invoice';
import { DomSanitizer } from '@angular/platform-browser';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { PeriodClosingService } from '../../finance-module/period-closing/period-closing.service';


@Component({
  selector: 'app-payment-receipt',
  templateUrl: './payment-receipt.component.html',
  styleUrls: ['./payment-receipt.component.css']
})
export class PaymentReceiptComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  [x: string]: any;
  paymentReceiptsHeads: PaymentReceiptsHead[] = [];
  searchTerm: string = ''; // Store the search term
  filteredPaymentReceiptsHeads: PaymentReceiptsHead[] = [];
  selectedPaymentReceiptsHeads: Set<PaymentReceiptsHead> = new Set<PaymentReceiptsHead>();
  paymentReceiptsHeadList: PaymentReceiptsHead[] = [];
  isAllSelected: boolean = false;
  creditNoteData: PaymentReceiptsHead = new PaymentReceiptsHead();
  activeTab = 'all';

  startDate: string | null = null; // Bind to the start date input
  endDate: string | null = null;   // Bind to the end date input



  constructor(
    private invoiceService: InvoiceService,
    private periodClosingService: PeriodClosingService,
    private router: Router,
    public sanitizer: DomSanitizer) {}

  ngOnInit(): void {
    this.fetchPaymentReceipts();
  }

  private getPaymentReceiptsHeadList() {

    this.invoiceService.getPaymentReceiptsHeadList().subscribe(data => {
      this.paymentReceiptsHeadList = data;
      this.filterPaymentReceiptsHeads(); // Filter credit notes initially
    });
  }

  toggleSelection(paymentReceiptsHead: PaymentReceiptsHead, event: any): void {
    if (event.target.checked) {
      this.selectedPaymentReceiptsHeads.add(paymentReceiptsHead);
    } else {
      this.selectedPaymentReceiptsHeads.delete(paymentReceiptsHead);
    }
  }
  fetchPaymentReceipts() {

    const entityId = +((localStorage.getItem('entityId')) + "");

    this.invoiceService.getAllPaymentReceiptsHeadList(entityId).subscribe(
      (data: PaymentReceiptsHead[]) => {
        this.paymentReceiptsHeads = data;
        this.filteredPaymentReceiptsHeads = this.paymentReceiptsHeads;


      // Check lock for each paymentReceiptsHeads based on posting date
      this.filteredPaymentReceiptsHeads.forEach((paymentReceiptsHeads) => {
        const date = paymentReceiptsHeads.documentDate;
        this.periodClosingService.isDateLocked(entityId, date).subscribe({
          next: (locked: boolean) => {
            (paymentReceiptsHeads as any).isLocked = locked; // add isLocked dynamically if model doesn't have it
          },
          error: (err) => {
            console.error('Error checking period lock for payment:', err);
            (paymentReceiptsHeads as any).isLocked = false;
          }
        });
      });


      },
      (error) => {

      }
    );
  }


  onSearchChange() {
    this.filterPaymentReceiptsHeads(); // Call filter function on input change
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.filterPaymentReceiptsHeads(); // Filter invoices when tab changes
  }


  filterPaymentReceiptsHeads(): void {
    if (!this.searchTerm && !this.startDate && !this.endDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Search Criteria',
        text: 'Please provide at least one search criterion: Payment Number or Customer, Start Date, or End Date.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();
    let filtered = this.paymentReceiptsHeads;

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(paymentReceiptsHead =>
        paymentReceiptsHead.paymentNumber.toString().toLowerCase().includes(searchTermLower) ||
        paymentReceiptsHead.customerName.toLowerCase().includes(searchTermLower)
      );
    }

    // Filter by date range
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(paymentReceiptsHead => new Date(paymentReceiptsHead.documentDate) >= startDate);
    }

    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(paymentReceiptsHead => new Date(paymentReceiptsHead.documentDate) <= endDate);
    }

    this.filteredPaymentReceiptsHeads = filtered;
  }

  // Reset filters
  resetFilters() {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.filteredPaymentReceiptsHeads = this.paymentReceiptsHeads;
  }

deleteCreditNote(paymentReceiptId: number,paumentNumber:string): void {
  Swal.fire({
    title: 'Are you sure?',
    text: `Do you really want to delete Receipt ${paumentNumber}?`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Yes, delete it!',
    cancelButtonText: 'No, keep it',
    confirmButtonColor: '#ff7e5f',
    cancelButtonColor: '#be0032',
  }).then((result) => {
    if (result.isConfirmed) {


      this.invoiceService.deletePaymentReceiptsHead(paymentReceiptId)
        .subscribe(
          () => {

            Swal.fire({
              title: 'Deleted!',
              text: 'Payment Receipt has been deleted.',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            }).then(() => {
              // Optionally update the UI or reload data
              this.fetchPaymentReceipts();
            });
          },
          (error) => {
            console.error('Failed to delete Payment Receipt:', error);

            Swal.fire({
              title: 'Error!',
              text: 'Failed to delete Payment Receipt.',
              icon: 'error',
              confirmButtonText: 'OK',
              cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Failed to delete Payment Receipt.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
            });
          }
        );
    }
  });
}

createPaymentReceipt() {
  this.router.navigate(['/create-payment-receipt-user']);
}


selectAll(event: any) {
  const isChecked = event.target.checked;
  this.paymentReceiptsHeads.forEach(paymentReceiptsHead => paymentReceiptsHead.selected = isChecked);
}
playLoadingSound() {
  let audio = new Audio();
  audio.src = "../assets/google_chat.mp3";
  audio.load();
  audio.play();
}

stopLoadingSound(): void {
  if (this.audio) {
    this.audio.pause();
    this.audio.currentTime = 0;
  }
}

exportToExcel(): void {
  if (!this.filteredPaymentReceiptsHeads || this.filteredPaymentReceiptsHeads.length === 0) {
    alert('No data available to export.');
    return;
  }

  const exportData = this.filteredPaymentReceiptsHeads.map(receipt => ({
    'Payment Number': receipt.paymentNumber,
    'Invoice Numbers': receipt.invoiceNumbers,
    'Customer Name': receipt.customerName,
    'Document Date': new Date(receipt.documentDate).toISOString().split('T')[0], // Format as YYYY-MM-DD
    'Total Paid Amount ($)': `$${receipt.totalPaidAmount.toFixed(2)}`  // Excel handles currency formatting
  }));

  const worksheet = XLSX.utils.json_to_sheet(exportData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Payment Receipts');

  const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

  const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
  this.saveAsExcelFile(excelBuffer, `Payment_Receipts_${timestamp}`);
}

private saveAsExcelFile(buffer: any, fileName: string): void {
  const data: Blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
  });

  saveAs(data, `${fileName}.xlsx`);
}
}
