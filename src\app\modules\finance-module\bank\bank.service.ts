import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { catchError } from 'rxjs/operators';
import { BankAccount } from './bank';

@Injectable({
  providedIn: 'root'
})
export class BankService {

  private selectedBankAccount: BankAccount | null = null;

  setSelectedBankAccount(account: BankAccount) {
    this.selectedBankAccount = account;
  }

  getSelectedBankAccount(): BankAccount | null {
    return this.selectedBankAccount;
  }

   private readonly baseURL = environment.financeApiUrl;
    
      constructor(private http: HttpClient) {}
    
      getAuthToken(): string | null {
        return window.sessionStorage.getItem('auth_token');
      }
    
      request(
        method: string,
        url: string,
        data: any,
        params?: any
      ): Observable<any> {
        let headers = new HttpHeaders();
    
        if (this.getAuthToken() !== null) {
          headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
        }
    
        const options = {
          headers: headers,
          params: new HttpParams({ fromObject: params }),
        };
    
        switch (method.toUpperCase()) {
          case 'GET':
            return this.http.get(this.baseURL + url, options);
          case 'POST':
            return this.http.post(this.baseURL + url, data, options);
          case 'PUT':
            return this.http.put(this.baseURL + url, data, options);
          case 'DELETE':
            return this.http.delete(this.baseURL + url, options);
          default:
            throw new Error('Unsupported HTTP method');
        }
      }


      saveBankAccount(bankAccount: BankAccount ): Observable<BankAccount> {
          return this.request('POST', '/bank-account/saveBankAccount', bankAccount);
        }
       // Send account data to the backend
  sendAccountData(accountData: any): Observable<any> {
    return this.request('POST', '/submit', accountData);
  }

  // Fetch account numbers from the backend
  getAccountNumbers(): Observable<string[]> {
    return this.request('GET', '/account-numbers', {}); // Explicitly provide empty data
  }

  // Fetch account history from the backend
  getAccountHistory(): Observable<string[]> {
    return this.request('GET', '/history', {}); // Explicitly provide empty data
  }
  
  getBankAccountsByEntityId(entityId: number): Observable<BankAccount[]> {  
      return this.request('GET', `/bank-account/listByEntityId/${entityId}`,{});
  }

  // Fetch GL accounts for dropdown
getGLAccounts(): Observable<{ id: number, name: string }[]> {
  return this.request('GET', '/coa-ledger-accounts/getActiveCoaLedgerAccountListByEntity', {});
}

  
}
