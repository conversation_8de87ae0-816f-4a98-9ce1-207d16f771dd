<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<br>
<div class="container">
  <h5>Period Closing</h5>
  <hr>
  <form
    #periodClosingForm="ngForm"
    (ngSubmit)="periodClosingForm.form.valid && savePeriodClosing()"
    class="row g-1"
    novalidate="feedback-form"
  >
    <div class="report-section">
      <div class="report-form-container">
        <div class="report-form">
          <div class="form-group">
            <label for="lableFromDate">Date</label>
            <div class="date-range">
              <div class="form-group">
                <input
                  [(ngModel)]="periodClosingHead.fromDate"
                  name="fromDate"
                  type="date"
                  id="fromDate"
                  required
                />
                <div
                  *ngIf="
                    periodClosingForm.submitted &&
                    periodClosingForm.controls['fromDate'].invalid
                  "
                  class="text-danger"
                  style="margin-top: 5px"
                >
                  <div
                    *ngIf="periodClosingForm.controls['fromDate'].errors?.['required']"
                  >
                    From Date is required.
                  </div>
                </div>
              </div>
              <span>to</span>
              <div class="form-group">
                <input
                  [(ngModel)]="periodClosingHead.toDate"
                  name="toDate"
                  type="date"
                  id="toDate"
                  class="form-control"
                  required
                />
                <div
                  *ngIf="
                    periodClosingForm.submitted &&
                    periodClosingForm.controls['toDate'].invalid
                  "
                  class="text-danger"
                  style="margin-top: 5px"
                >
                  <div
                    *ngIf="periodClosingForm.controls['toDate'].errors?.['required']"
                  >
                    To Date is required.
                  </div>
                </div>
              </div>
            </div>
          </div>
          <br>
          <button type="submit" class="generate-report-btn">Close</button>
        </div>
      </div>
    </div>
  </form>
</div>
