import { CoaLedgerAccount } from "../gl-account/gl-account";

export class GlPostingDetails {
  glTransactionDetailsId: number | null;
  glTransactionId: GlPostingHead | null;
  coaLedgerAccount: CoaLedgerAccount | null;
  fiscalPeriodId: number | null;
  drAmount: number | null;
  crAmount: number | null;

  constructor(
    glTransactionDetailsId: number | null = null,
    glTransactionId: GlPostingHead | null = null,
    coaLedgerAccount: CoaLedgerAccount | null = null,
    fiscalPeriodId: number | null = null,
    drAmount: number | null = null,
    crAmount: number | null = null
  ) {
    this.glTransactionDetailsId = glTransactionDetailsId;
    this.glTransactionId = glTransactionId;
    this.coaLedgerAccount = coaLedgerAccount;
    this.fiscalPeriodId = fiscalPeriodId;
    this.drAmount = drAmount;
    this.crAmount = crAmount;
  }
}

export class GlPostingHead {
  glTransactionId: number | null;
  documentNumber: string | null;
  documentType: string | null;
  entityId: number | null;
  userId: number | null;
  jvNumber: string | null;
  date: string | null;
  description: string | null;
  totalDr: number | null;
  totalCr: number | null;
  status: string | null;
  selected: boolean;
  businessPartnerId: any | null;

  constructor(
    glTransactionId: number | null = null,
    documentNumber: string | null = null,
    documentType: string | null = null,
    entityId: number | null = null,
    userId: number | null = null,
    jvNumber: string | null = null,
    date: string | null = null,
    description: string | null = null,
    totalDr: number | null = null,
    totalCr: number | null = null,
    status: string | null = null,
    businessPartnerId: number | null = null,
    selected: boolean = false
  ) {
    this.glTransactionId = glTransactionId;
    this.documentNumber = documentNumber;
    this.documentType = documentType;
    this.entityId = entityId;
    this.userId = userId;
    this.jvNumber = jvNumber;
    this.date = date;
    this.description = description;
    this.totalDr = totalDr;
    this.totalCr = totalCr;
    this.status = status;
    this.businessPartnerId = businessPartnerId;
    this.selected = selected;
  }
}
