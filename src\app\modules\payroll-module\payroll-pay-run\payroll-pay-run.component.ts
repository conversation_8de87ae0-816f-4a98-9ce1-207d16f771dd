import { PayCalendar } from './../payroll-settings/payroll-setting';
import {
  AfterViewInit,
  Component,
  ElementRef,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import {
  Earning,
  PayItemType,
  PayPeriod,
  PayRunMaster,
} from '../payroll-settings/payroll-setting';
import { EarningService } from '../payroll-settings/services/earning.service';
import { PayRunService } from '../payroll-settings/services/pay-run.service';
import { HttpParams } from '@angular/common/http';

declare var bootstrap: any;

@Component({
  selector: 'app-payroll-pay-run',
  templateUrl: './payroll-pay-run.component.html',
  styleUrls: ['./payroll-pay-run.component.css'],
})
export class PayrollPayRunComponent implements OnInit, AfterViewInit {
  payRunMaster: PayRunMaster = new PayRunMaster();
  payRun: PayRunMaster[] = [];
  draftPayRuns: PayRunMaster[] = [];
  skippedPayRuns: PayRunMaster[] = [];
  postPayRuns: PayRunMaster[] = [];
  unscheduledPayRuns: PayRunMaster[] = [];
  leaveTwoPage = 1;
  payPeriods: PayPeriod[] = [];
  openPayPeriods: PayPeriod[] = [];
  earning: Earning = new Earning();
  PayitemTypes: PayItemType[] = [];
  selectedPayPeriodId: number | null = null;
  payPeriodId!: number;
  isfiling: boolean = false;
  latestPostedPayRunIds: { [calendarId: number]: number } = {};
  isFiled = false;
  filingStatus: { [runId: number]: 'idle' | 'submitting' | 'filed' } = {};
  userId: number = +(localStorage.getItem('userid') + '');
  entityId: number = +(localStorage.getItem('entityId') + '');

  @ViewChild('dropdownButton', { static: false }) dropdownButton!: ElementRef;

  constructor(
    private router: Router,
    private earningService: EarningService,
    private payRunService: PayRunService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.getAllPayPeriods();
    this.getAllPayRuns();
  }

  ngAfterViewInit(): void {
    // Proper Bootstrap initialization
    setTimeout(() => {
      if (this.dropdownButton?.nativeElement) {
        new bootstrap.Dropdown(this.dropdownButton.nativeElement);
      }
    }, 0);
  }

  toggleDropdown() {
    const dropdown = new bootstrap.Dropdown(this.dropdownButton.nativeElement);
    dropdown.toggle();
  }

  activeInnerTab: string = 'DraftPayRun';

  isActiveInnerTab(innerTab: string): boolean {
    return this.activeInnerTab === innerTab;
  }

  setActiveInnerTab(innerTab: string): void {
    this.activeInnerTab = innerTab;
  }

  viewDetails(
    payCalendarId: number,
    payPeriodId: number,
    payRunId: number
  ): void {
    window.location.assign(
      `/payRun-user-details/${payCalendarId}/${payPeriodId}/${payRunId}`
    );
  }

  navigateToPayrollPayRun(): void {
    window.location.assign('/payroll-pay-run');
  }

  selectPayPeriod(payPeriodId: number): void {
    this.selectedPayPeriodId = payPeriodId;
  }

  getPayPeriodLabel(payPeriodId: number): string {
    const period = this.openPayPeriods.find(
      (p) => p.payPeriodId === payPeriodId
    );
    return period
      ? `${period.payCalendar.calendarName} : ${period.payStartDate} - ${period.nextPayDate}`
      : '';
  }

  onPayRunAdd(): void {
    this.payRunMaster.userId = this.userId;
    this.payRunMaster.entityId = this.entityId;

    if (this.selectedPayPeriodId !== null) {
      const selectedPayPeriod = this.openPayPeriods.find(
        (period) => period.payPeriodId === +this.selectedPayPeriodId!
      );

      if (selectedPayPeriod) {
        this.payRunMaster.payPeriod = selectedPayPeriod;

        this.payRunService.addPayRun(this.payRunMaster, 'DRAFT').subscribe(
          (_response: PayRunMaster) => {
            this.viewDetails(
              selectedPayPeriod.payCalendar.payCalendarId,
              selectedPayPeriod.payPeriodId,
              _response.payRunId
            );
          },
          (_error: any) => {
            Swal.fire({
              title: 'Error!',
              text: 'Failed to save pay Run. Please try again.',
              icon: 'error',
              confirmButtonText: 'OK',
              confirmButtonColor: '#be0032',
            });
            console.log('error', _error);
          }
        );
      } else {
        console.error('No PayPeriod found for the selected ID.');
      }
    }
  }
  // onPayRunAdd():void{
  //     const entityId = +((localStorage.getItem('entityId')) + "");
  //     const userId = +((localStorage.getItem('userid')) + "");
  //     const date = new Date().toISOString();

  //     const payload = {
  //       ...this.payRunMaster,
  //       entityId,
  //       userId,
  //       date,
  //       payPeriod: this.selectedPayPeriod,
  //     };

  //     this.payRunService.addPayRun(payload).subscribe(
  //       (_response: any) => {
  //         Swal.fire({
  //           title: 'Success!',
  //           text: 'Ordinary Time Earnings added successfully!',
  //           icon: 'success',
  //           confirmButtonText: 'OK',
  //           confirmButtonColor: '#007bff'
  //         }).then(() => {
  //           this.earning = new Earning();

  //         });
  //       },
  //       (_error: any) => {
  //         Swal.fire({
  //           title: 'Error!',
  //           text: 'Failed to save Ordinary Time Earnings. Please try again.',
  //           icon: 'error',
  //           confirmButtonText: 'OK',
  //           confirmButtonColor: '#be0032'
  //         });
  //       }
  //     );
  //   }

  getAllPayPeriods(): void {
    this.payRunService.getAllPayPeriods(this.entityId).subscribe({
      next: (data: PayPeriod[]) => {
        this.payPeriods = data;
        this.openPayPeriods = this.payPeriods.filter(
          (period: PayPeriod) => period.status === 'OPEN'
        );
      },
      error: (err) => {
        console.error('Error fetching PayPeriods:', err);
      },
    });
  }

  getAllPayRuns(): void {
    const entityId = +(localStorage.getItem('entityId') + '');

    this.payRunService.getPayRunList(entityId).subscribe({
      next: (data: PayRunMaster[]) => {
        this.payRun = data.map((payRun) => {
          return payRun;
        });
        this.draftPayRuns = this.payRun.filter(
          (payRunMaster: PayRunMaster) =>
            payRunMaster.payPeriod?.status === 'DRAFT'
        );
        this.skippedPayRuns = this.payRun.filter(
          (payRunMaster: PayRunMaster) =>
            payRunMaster.payPeriod?.status === 'SKIPPED'
        );
        this.postPayRuns = this.payRun.filter(
          (payRunMaster: PayRunMaster) =>
            payRunMaster.payPeriod?.status === 'POSTED'
        );
        this.unscheduledPayRuns = this.payRun.filter(
          (payRunMaster: PayRunMaster) =>
            payRunMaster.payPeriod?.status === 'UNSCHEDULED'
        );
        this.latestPostedPayRunIds = this.getLatestPostedPayRunIdsPerCalendar();
      },
      error: (err) => {
        console.error('Error fetching PayRuns:', err);
      },
    });
  }

  getLatestPostedPayRunIdsPerCalendar(): { [calendarId: number]: number } {
  const result: { [calendarId: number]: number } = {};

  const filtered = this.postPayRuns.filter(pr =>
    pr.payPeriod.status === 'POSTED' && !pr.payPeriod.unscheduled
  );

  const grouped = filtered.reduce((acc, pr) => {
    const calendarId = pr.payPeriod.payCalendar.payCalendarId;
    if (!acc[calendarId]) acc[calendarId] = [];
    acc[calendarId].push(pr);
    return acc;
  }, {} as { [calendarId: number]: any[] });

  for (const calendarId in grouped) {
    const runs = grouped[calendarId];
    const lastRun = runs[runs.length - 1]; // Assuming list is ordered
    result[+calendarId] = lastRun.payRunId;
  }
  
  return result;
}



  payRunSkip(payPeriodId: number) {
    this.payRunMaster.userId = this.userId;
    this.payRunMaster.entityId = this.entityId;

    if (payPeriodId !== null) {
      const selectedPayPeriod = this.openPayPeriods.find(
        (period) => period.payPeriodId === payPeriodId
      );

      if (selectedPayPeriod) {
        this.payRunMaster.payPeriod = selectedPayPeriod;

        this.payRunService.addPayRun(this.payRunMaster, 'SKIPPED').subscribe(
          (_response: any) => {
            Swal.fire({
              title: 'Success',
              text: 'Pay Run saved successfully.',
              icon: 'success',
              confirmButtonText: 'OK',
              confirmButtonColor: '#28a745',
            });
            this.getAllPayPeriods();
            this.getAllPayRuns();
            this.selectedPayPeriodId = null;
          },
          (_error: any) => {
            Swal.fire({
              title: 'Error!',
              text: 'Failed to save pay Run. Please try again.',
              icon: 'error',
              confirmButtonText: 'OK',
              confirmButtonColor: '#be0032',
            });
            console.log('error', _error);
          }
        );
      } else {
        console.error('No PayPeriod found for the selected ID.');
      }
    }
  }

  createUnscheduledPayRun(event: MouseEvent, payPeriod: PayPeriod) {
    event.stopPropagation();

    this.payRunMaster.userId = this.userId;
    this.payRunMaster.entityId = this.entityId;
    this.payRunMaster.payPeriod = payPeriod;

    this.payRunService.addUnscheduledPayRun(this.payRunMaster).subscribe(
      (_response: PayRunMaster) => {
        this.viewDetails(
          payPeriod.payCalendar.payCalendarId,
          _response.payPeriod.payPeriodId,
          _response.payRunId
        );
      },
      (_error: any) => {
        Swal.fire({
          title: 'Error!',
          text: 'Failed to save pay Run. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
        console.log('error', _error);
      }
    );
  }

  stpFile(
    event: MouseEvent,
    payCalendarId: number,
    payPeriodId: number,
    payRunId: number
  ): void {
    event.stopPropagation();
    this.filingStatus[payRunId] = 'submitting';

    this.payRunService
      .stpFileSubmit(
        payCalendarId,
        payPeriodId,
        payRunId,
        this.entityId,
        this.userId
      )
      .subscribe({
        next: (_response: any) => {
          Swal.fire({
            title: 'Success',
            text: '✅ ATO submission started',
            icon: 'success',
            timer: 5000,
            timerProgressBar: true,
          }).then(() => {
            this.filingStatus[payRunId] = 'filed';
          });
        },
        error: (err) => {
          console.error('STP submission failed:', err);
          Swal.fire({
            title: 'Error',
            text: '❌ Failed to file STP. Please try again.',
            icon: 'error',
          }).then(() => {
            this.filingStatus[payRunId] = 'idle';
          });
        },
      });
  }
}
