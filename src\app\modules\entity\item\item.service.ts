import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Item } from './item';

@Injectable({
  providedIn: 'root',
})
export class ItemService {
    private readonly baseURL = environment.salesApiUrl;
  
    constructor(private http: HttpClient) {}
  
    getAuthToken(): string | null {
      return window.sessionStorage.getItem('auth_token');
    }
  
    request(
      method: string,
      url: string,
      data: any,
      params?: any
    ): Observable<any> {
      let headers = new HttpHeaders();
  
      const authToken = this.getAuthToken();

      if (authToken) {
        headers = headers.set('Authorization', 'Bearer ' + authToken);
      } else {
        // Add secure API key for protected-but-public endpoints
        headers = headers.set('X-API-KEY', environment.secureApiKey);
      }
  
      const options = {
        headers: headers,
        params: new HttpParams({ fromObject: params }),
      };
  
      switch (method.toUpperCase()) {
        case 'GET':
          return this.http.get(this.baseURL + url, options);
        case 'POST':
          return this.http.post(this.baseURL + url, data, options);
        case 'PUT':
          return this.http.put(this.baseURL + url, data, options);
        case 'DELETE':
          return this.http.delete(this.baseURL + url, options);
        default:
          throw new Error('Unsupported HTTP method');
      }
    }
    createItem(item: any): Observable<any> {
      return this.request('POST', '/saveSalesItem', item);
    } 
  
    getAllItems(): Observable<Item[]> {
      return this.request('GET', '/salesItemList', null);
    }
  
    getItemById(id: number): Observable<Item>{
      return this.request('GET', `/getSalesItemById/${id}`,{});
    }
    deleteItem(id: number): Observable<void> {
      return this.request('DELETE', `/deleteSalesItem/${id}`, null);
    }  
    updateItem(id: number, item: Item): Observable<void> {
      return this.request('PUT', `/updateSalesItem/${id}`, item); 
    }

    getAllSalesItemsByEntity(entityId: any): Observable<Item[]> {
      return this.request('GET', '/getAllSalesItemsByEntity', {}, { entityId: entityId });
    }

    getSalesItemByCodeAndEntityId(itemCode: string, entityId: any): Observable<Item> {
      return this.request('GET', `/getSalesItemByCodeAndEntityId`, {}, { itemCode: itemCode, entityId: entityId });
    }
    
  }
  

