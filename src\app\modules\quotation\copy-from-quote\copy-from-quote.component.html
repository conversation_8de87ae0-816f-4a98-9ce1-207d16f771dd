<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <form #f="ngForm" (ngSubmit)="f.form.valid && onSubmit(f)" class="row g-1" novalidate="feedback-form"
    (keydown)="preventSubmit($event)">
    <div class="heading">
      <h3>Copy Quote</h3>
      <div class="button-group">
        <button type="submit" class="transparent-button" (click)="setStatus('Pending')">Save</button>
      </div>
    </div>

    <div class="bd">
      <div class="form-section">

        <!-- First row: Quotation Number and Valid Until -->
        <div class="form-row" style="display: flex; justify-content: space-between;">
          <!-- Quotation Number -->
          <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1;">
            <div class="form-dataInput" style="display: flex;">
              <label for="quotationNo" id="quotation" style="margin-right: 10px; white-space: nowrap;">Quotation Number</label>
              <input class="input-style" type="text" id="quotationNo" 
                [(ngModel)]="quotationData.quoteNumber" name="quoteNumber" #quoteNumber="ngModel" readonly />
            </div>
          </div>

        
   <!-- Quotation Date -->
          <div class="form-group" style="display: flex; flex-grow: 1;">
            <label for="quotationDate" style="margin-right: 10px; white-space: nowrap;">Quotation Date</label>
            <input class="input-style" type="date" id="quotationDate" [(ngModel)]="quotationData.quoteDate"
              name="quotationDate" (change)="onQuoteDateChange()" required />
          </div>

          <!-- Error message for Quotation Date -->
          <div *ngIf="f.submitted && f.controls['quotationDate'].invalid" class="text-danger"
            style="flex-basis: 100%; margin-top: 5px;">
            <div *ngIf="f.controls['quotationDate'].errors?.['required']">Quotation Date is required.</div>
          </div>



        </div>

        <!-- Second row: Customer and Quotation Date -->
        <div class="form-row" style="display: flex; justify-content: space-between;">
          <!-- Customer -->
          <div class="form-group" style="flex-grow: 1;">
            <div class="form-dataInput" style="display: flex;">
              <label for="customer" style="margin-right: 70px; white-space: nowrap;">Customer</label>
              <select class="form-select" id="customer" [(ngModel)]="quotationData.businessPartnerId"
                (change)="onCustomerChange($event)" name="customerName" required>
                <option value="" selected disabled>Select Customer</option>
                <option *ngFor="let customer of customers" [value]="customer.businessPartnerId">
                  {{ customer.bpName }}
                </option>
              </select>
            </div>

            <!-- "Create New Customer" button right below the dropdown -->
            <div class="create-customer-container" style="margin-top: 10px;">
              <button type="button" (click)="loadBusinessPartnerTypes()" class="create-customer-btn"
                data-bs-target="#customerPopUpModal" data-bs-toggle="modal">Create New Customer</button>
            </div>

            <!-- Error message for Customer -->
            <div *ngIf="f.submitted && f.controls['customerName'].invalid" class="text-danger" style="margin-top: 15px; margin-left: 140px;">
              <div *ngIf="f.controls['customerName'].errors?.['required']">Customer is required.</div>
            </div>
          </div>

         <!-- Valid Until -->
          <div class="form-group" style="display: flex; flex-grow: 1;">
            <label for="validityUntil" style="margin-right: 45px; white-space: nowrap;">Valid Until</label>
            <input class="input-style" type="date" id="validityUntil" (blur)="onValidUntilDateChange()"
              [(ngModel)]="quotationData.validUntilDate" name="validityUntil" required />
          </div>

          <!-- Error message for Valid Until -->
          <div *ngIf="f.submitted && f.controls['validityUntil'].invalid" class="text-danger"
            style="flex-basis: 100%; margin-top: 5px;">
            <div *ngIf="f.controls['validityUntil'].errors?.['required']">Valid Until is required.</div>
          </div>



        </div>

        <!-- third row -->
        <div class="form-row" style="display: flex; justify-content: space-between;">
          <!-- Trading Name -->
          <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1;">
            <div class="form-dataInput" style="display: flex;">
              <label for="tradingName" style="margin-right: 40px; white-space: nowrap;">Trading Name</label>
              <select class="form-select" id="tradingName" [(ngModel)]="quotationData.entityTradingNameId"
                name="tradingName" [disabled]="entityTradingNames.length <= 1" required>
                <option value="" selected disabled>Select Trading Name</option>
                <option *ngFor="let tradingName of entityTradingNames" [value]="tradingName.entityTradingNameId">
                  {{ tradingName.tradingName }}
                </option>
              </select>
            </div>

            <div *ngIf="f.submitted && f.controls['tradingName'].invalid" class="text-danger" style="margin-top: 5px;">
              <div *ngIf="f.controls['tradingName'].errors?.['required']">Trading Name is required.</div>
            </div>
          </div>

          <div class="form-group" style="display: flex; flex-grow: 1;"></div>
        </div>
      </div>

      <div class="form-row">
        <div class="search-bar d-flex justify-content-start">
          <button type="button" class="btn btn-primary new-item" data-bs-target="#itemPopupModal" data-bs-toggle="modal">
            <i class="bi bi-plus-circle-fill"></i> Add New Item
          </button>
        </div>
      </div>

      <div class="table-section">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th style="width: 35%;">Item</th>
                <th style="width: 25%;">Description</th>
                <th style="width: 7%;">Quantity</th>
                <th style="width: 17%;">Unit Price (Excluding tax)</th>
                <th style="width: 5%;">Discount</th>
                <th style="width: 8%;">Disc. Type</th>
                <th style="width: 8%;">{{ businessEntity.countryId.defaultTaxType }}({{ businessEntity.countryId.defaultTaxRate }}%)</th>
                <th style="width: 5%; text-align: right;">Tax</th>
                <th style="width: 14%; text-align: right; padding-right: 0;">Amount ({{ businessEntity.countryId.defaultCurrency}})</th>
                <th style="width: 3%;"></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let detail of quotationData.details; let i = index">
                <td>
                  <!-- ng-select will only show if itemCode is not 'SISSERVICE' -->
                  <ng-select *ngIf="quotationData.details[i].salesItemId?.itemCode !== 'SISSERVICE'"
                    name="salesItem-{{ i }}" [appendTo]="'body'" [items]="allSalesItems" bindLabel="description"
                    [(ngModel)]="quotationData.details[i].salesItemId" [searchable]="true" [clearable]="false"
                    [disabled]="quotationData.details[i].description !== ''" placeholder="Select item"
                    [searchFn]="customSearchFn" (change)="onItemSelected(quotationData.details[i].salesItemId, i)">
                    <ng-template ng-option-tmp let-item="item">
                      {{ item.itemCode }} - {{ item.description }}
                    </ng-template>
                  </ng-select>
                </td>

                <td>
                  <input type="text" [(ngModel)]="quotationData.details[i].description" name="description-{{ i }}"
                    class="form-control" placeholder="Enter description"
                    [disabled]="quotationData.details[i].salesItemId && quotationData.details[i].salesItemId.itemCode !== 'SISSERVICE' && quotationData.details[i].salesItemId.itemCode !== ''"
                    (input)="onDescriptionInput(i)" />
                </td>

                <td>
                  <input type="number" [(ngModel)]="quotationData.details[i].quantity" (input)="updateQuantity(i)"
                    name="quantity-{{ i }}" class="form-control" min="0" />
                </td>

                <td>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">$</span>
                    </div>
                    <input type="number" [(ngModel)]="quotationData.details[i].unitPrice" (input)="updateAmount(i)"
                      name="unitPrice-{{ i }}" class="form-control" min="0" step="0.01"
                      [disabled]="quotationData.details[i].salesItemId && quotationData.details[i].salesItemId.itemCode !== 'SISSERVICE' && quotationData.details[i].salesItemId.itemCode !== ''" />
                  </div>
                </td>

                <td>
                  <input type="number" [(ngModel)]="quotationData.details[i].discount" (input)="updateAmount(i)"
                    name="discount-{{ i }}" class="form-control" min="0" />
                </td>

                <td>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="discountType{{ i }}" id="percent{{ i }}" value="B"
                      [(ngModel)]="quotationData.details[i].discountType" (change)="updateDiscountType(i, 'B')" />
                    <label class="form-check-label" for="percent{{ i }}">%</label>
                    <input class="form-check-input" type="radio" name="discountType{{ i }}" id="dollar{{ i }}" value="$"
                      [(ngModel)]="quotationData.details[i].discountType" (change)="updateDiscountType(i, '$')" />
                    <label class="form-check-label" for="dollar{{ i }}">$</label>
                  </div>
                </td>
                <td>
                  <label>
                    Incl.GST
                    <input type="checkbox" [(ngModel)]="quotationData.details[i].taxApplicability"
                      name="taxApplicable-{{ i }}"
                      [disabled]=" !showTaxApplicabilityDropdown || !quotationData.details[i].description || quotationData.details[i].description.trim() === ''"
                      (change)="onTaxApplicableChange(i)" style="margin-left: 5px;" />
                  </label>
                </td>
                <td style="text-align: right">{{ quotationData.details[i].tax | currency }}</td>
                <td style="text-align: right">{{ quotationData.details[i].amount | currency }}</td>

                <td>
                  <button type="button" class="btn btn-link" (click)="removeItem(i)">
                    <i class="fa fa-trash" style="color: red;"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <button type="button" class="btn btn-primary" (click)="addNewRow()">
            <i class="bi bi-plus-circle-fill"></i> Add New Row
          </button>
        </div>
      </div>

      <!-- note text area -->
      <div class="main-row-note">
        <div class="notes-totals-section">
          <div class="notes-section">
            <label for="notes">Notes</label>
            <textarea id="notes" class="form-control" [(ngModel)]="quotationData.note" name="note"></textarea>
          </div>

          <div class="totals-section" style="margin-top: 25px;">
            <div class="totals-row">
              <span class="totals-row1">Sub Total Amount </span>
              <span class="totals-row2">{{ quotationData.subTotal | currency }}</span>
            </div>
            <div class="totals-row">
              <span class="totals-row1">Total Discount</span>
              <span class="totals-row2">{{calculateTotalDiscount()| currency}}</span>
            </div>
            <div class="totals-row">
              <span class="totals-row1">Total {{ businessEntity.countryId.defaultTaxType }} </span>
              <span class="totals-row2">{{ quotationData.totalGst | currency }}</span>
            </div>
            <div class="totals-row">
              <strong class="totals-row1">Grand Total</strong>
              <strong class="totals-row2">{{ quotationData.grandTotal | currency }}</strong>
            </div>
          </div>
        </div>
      </div>

    </div>
  </form>
</div>

<app-add-business-partner-popup
  [entityId]="entityId"
  [showUpdateLink]="showUpdateLink"
  (partnerAdded)="loadCustomers()"
  (partnerSaved)="quotationData.businessPartnerId = $event.businessPartnerId">
</app-add-business-partner-popup>


<app-add-item-popup
  [entityId]="entityId"
  (itemAdded)="onItemAdded($event)">
</app-add-item-popup>