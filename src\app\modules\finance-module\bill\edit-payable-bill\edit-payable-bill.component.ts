import { HttpErrorResponse } from '@angular/common/http';
import { Component, ViewChild } from '@angular/core';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { ApInvoiceDetail, ApInvoiceHead, CoaLedgerAccount } from '../bill';
import { BusinessPartner, BusinessPartnerType } from 'src/app/modules/business-partner/business-partner';
import { Entity } from 'src/app/modules/entity/entity';
import { HttpService } from 'src/app/http.service';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from 'src/app/modules/admin/components/user/user.service';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import Swal from 'sweetalert2';
import { NgForm } from '@angular/forms';
import { BillService } from '../bill.service';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';
import { PeriodClosingService } from '../../period-closing/period-closing.service';
import { DateAdapter } from '@angular/material/core';

@Component({
  selector: 'app-edit-payable-bill',
  templateUrl: './edit-payable-bill.component.html',
  styleUrls: ['./edit-payable-bill.component.css']
})
export class EditPayableBillComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  apInvoiceDetails: ApInvoiceDetail[] = [];
  apInvoiceHeadData: ApInvoiceHead = new ApInvoiceHead();
  apInvoiceDetail: ApInvoiceDetail = new ApInvoiceDetail();
  businessEntity: Entity = new Entity();
  businessPartnerType: BusinessPartnerType[] = [];
  taxApplicable: boolean = false;
  businessPartner: BusinessPartner = new BusinessPartner();
  customers: BusinessPartner[] = [];
  showTaxApplicabilityDropdown: boolean = true;
  id: number = 0;
  businessEntityId: number = 0;
  glAccounts: CoaLedgerAccount[] = [];
  selectedFile: File | null = null;
  url = '';
  taxApplicabilityEnabled = true;
  defaultTaxRate = 0;
  minDate = new Date();

  constructor(
    private httpService: HttpService,
    private billService: BillService,
    private entityService: EntityService,
    private router: Router,
    private route: ActivatedRoute,
    private userService: UserService,
    private periodClosingService: PeriodClosingService,
    private businessPartnerService: BusinessPartnerService,
    private swalAlertService: SwalAlertsService,
    private dateAdapter: DateAdapter<Date>,
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }

  ngOnInit() {
    this.id = this.route.snapshot.params['id'];

    this.loadCustomers();
    this.getBusinessEntityById().then(() => {
      this.getBillHeadById().then(() => {
        this.getBillDetailsByHeadId();
        this.fetchGlAccounts();
      });
    });

    const entityId = +(localStorage.getItem('entityId') || '');

    if (entityId) {
      this.entityService.getBusinessEntityById(entityId).subscribe(
        (entity: Entity) => {

          if (entity.taxApplicability === 'no') {
            this.showTaxApplicabilityDropdown = false; // Hide dropdown
          } else if (entity.taxApplicability === 'yes') {
            this.showTaxApplicabilityDropdown = true; // Show dropdown
          }
        },
        error => {
          console.error('Error fetching entity:', error);
          this.showTaxApplicabilityDropdown = false; // Hide dropdown on error
        }
      );
    } else {
      this.showTaxApplicabilityDropdown = false; // Hide dropdown if no entity ID
    }
  }



  getBusinessEntityById() {
    return new Promise<void>((resolve, reject) => {
      this.businessEntityId = +((localStorage.getItem('entityId')) + "");
      this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
        data => {
          this.businessEntity = data;
            this.taxApplicabilityEnabled = data.taxApplicability === 'yes';
            this.defaultTaxRate = data.countryId?.defaultTaxRate || 0; this.taxApplicabilityEnabled = data.taxApplicability === 'yes';
     
          resolve();
        },
        error => {
          console.error(error);
          reject();
        }
      );
    });
  }

  getBillHeadById() {
    return new Promise<void>((resolve, reject) => {
      this.billService.getApInvoiceHeadById(this.id).subscribe(
        data => {
          this.apInvoiceHeadData = data;
          resolve();
        },
        error => {
          console.error(error);
          reject();
        }
      );
    });
  }

  getBillDetailsByHeadId() {
    this.apInvoiceHeadData.apInvoiceDetails = [];
    this.billService.getApInvoiceDetailsByApInvoiceHeadId(this.id).subscribe(
      data => {
        this.apInvoiceHeadData.apInvoiceDetails = data;
        console.log('Updated Invoice Details:', this.apInvoiceHeadData.apInvoiceDetails);
      },
      error => {
        console.error(error);
      }
    );
  }



  fetchGlAccounts() {
    const entityId = +localStorage.getItem('entityId')!;
    this.billService.getActiveCoaLedgerAccountListByEntityBill(entityId).subscribe(
      (glAccounts: CoaLedgerAccount[]) => {
        this.glAccounts = glAccounts;
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching GL Accounts', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load GL Accounts.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Failed to load GL Accounts.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound();
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
  }

  addNewRow() {
    this.apInvoiceHeadData.apInvoiceDetails.push({
      referenceNo: this.apInvoiceHeadData.referenceNo,
      quantity: 0,
      unitPrice: 0,
      itemDescription: '',
      amount: 0,
      dueAmount: 0,
      tax: 0,
      coaLedgerAccountId: 0,
      ledgerAccountName: '',
      ledgerAccountCode: '',
    });
    console.log('Added new row:', this.apInvoiceHeadData.apInvoiceDetails);
  }



  onDescriptionInput(index: number) {
    if (index === this.apInvoiceHeadData.apInvoiceDetails.length - 1) {
      // this.addNewRow();
    }
  }

  // New method to reset taxApplicability
  resetTaxApplicability(index: number): void {
    this.apInvoiceHeadData.apInvoiceDetails[index].taxApplicability = false; // Reset to unchecked
    this.apInvoiceHeadData.apInvoiceDetails[index].tax = 0; // Reset tax value
  }

  calculateSubTotal() {
    let subTotal = 0;
    let totalTax = 0;

    this.apInvoiceHeadData.apInvoiceDetails.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;
      detail.amount = itemAmount; // Always update base amount as unitPrice * quantity
      detail.dueAmount = detail.amount;

      // Add tax if applicable
      if (
        detail.quantity > 0 &&
        detail.taxApplicability &&
        this.taxApplicable
      ) {
        const taxRate = this.businessEntity?.countryId.defaultTaxRate || 0; // Default tax rate
        detail.tax = itemAmount * (taxRate / 100); // Calculate tax
      } else {
        detail.tax = 0; // Reset tax if not applicable
      }

      subTotal += detail.amount; // Add item amount to subtotal
      totalTax += detail.tax; // Add tax to total tax
    });

    this.apInvoiceHeadData.grossAmount = subTotal;
    this.apInvoiceHeadData.totalGst = totalTax;

    this.calculateGrandTotal();
  }

  calculateGrandTotal() {
    const { grossAmount, totalGst } = this.apInvoiceHeadData;
    this.apInvoiceHeadData.netAmount = grossAmount + totalGst; // Total with tax
    this.apInvoiceHeadData.dueAmount = grossAmount + totalGst; // Due amount
  }

  onTaxApplicableChange(index: number): void {
    const detail = this.apInvoiceHeadData.apInvoiceDetails[index];

    // Fetch entityId from localStorage
    const entityId = +(localStorage.getItem('entityId') || 0);

    if (entityId) {
      this.entityService.getBusinessEntityById(entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (this.taxApplicable && detail.taxApplicability) {
            detail.tax = detail.amount * (taxRate / 100); // Apply tax
          } else {
            detail.tax = 0; // Reset tax
          }

          this.calculateSubTotal(); // Recalculate totals
        },
        (error) => {
          console.error('Error fetching entity data:', error);
          detail.tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      detail.tax = 0;
      this.calculateSubTotal();
    }
  }

  updateAmount(index: number): void {
    const detail = this.apInvoiceHeadData.apInvoiceDetails[index];
    detail.amount = detail.quantity * detail.unitPrice; // Calculate base amount

    this.onTaxApplicableChange(index); // Re-evaluate tax based on applicability
  }

  updateQuantity(index: number) {
    this.updateAmount(index); // Ensure amount and tax are updated when quantity changes
  }

  /**removeItem(index: number) {
    this.apInvoiceHeadData.apInvoiceDetails.splice(index, 1); // Remove the item
    this.calculateSubTotal(); // Recalculate totals
  }**/

  

  removeItem(index: number): void {
    const apInvoiceDetailId = this.apInvoiceHeadData.apInvoiceDetails[index]?.apInvoiceDetailId;

    if (apInvoiceDetailId === undefined) {
      this.removeItemLocally(index);
      return;
    }

    this.swalAlertService.showConfirmationDialog(
      'Are you sure?',
      'Do you want to remove this item from the Bill?',
      () => this.confirmRemoveItem(index, apInvoiceDetailId)
    );
  }

  private removeItemLocally(index: number): void {
    this.apInvoiceHeadData.apInvoiceDetails.splice(index, 1);
    this.calculateSubTotal();
  }

  private confirmRemoveItem(index: number, apInvoiceDetailId: number): void {
    this.deletedItemIds.push(apInvoiceDetailId);
    this.removeItemLocally(index);
  }

  private deletedItemIds: number[] = [];

  getDeletedItemIds(): number[] {
    return this.deletedItemIds;
  }


  loadCustomers() {
    const entityId = +(localStorage.getItem('entityId') + '');

    this.businessPartnerService
      .getSupplierListByEntity(entityId)
      .subscribe(
        (customers: BusinessPartner[]) => {
          // Filter customers where businessPartnerType is 'Supplier'
          this.customers = customers.filter(
            (customer) =>
              customer.businessPartnerTypeId?.businessPartnerType === 'Supplier'
          );
        },
        (error: HttpErrorResponse) => {
          console.error('Error fetching Suppliers', error);
          Swal.fire({
            title: 'Error!',
            text: 'Failed to load Supplier.',
            icon: 'error',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#be0032',
            cancelButtonColor: '#007bff',
            showCancelButton: true,
          }).then((result) => {
            if (
              result.isDismissed &&
              result.dismiss === Swal.DismissReason.cancel
            ) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData(
                      'Failed to load customers.'
                    );
                    this.chatBotComponent.responseReceived.subscribe(
                      (response) => {
                        Swal.close();
                        this.chatResponseComponent.showPopup = true;
                        this.chatResponseComponent.responseData = response;
                        this.playLoadingSound();
                        this.stopLoadingSound();
                      }
                    );
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
        }
      );

    // Reset the selected business partner
    this.apInvoiceHeadData.businessPartnerId = '';
  }

  loadBusinessPartnerTypes() {
    this.businessPartnerService.getBusinessPartnerTypesList().subscribe(
      (businessPartnerType: BusinessPartnerType[]) => {
        // Find the "Customer" type from the list
        const customerType = businessPartnerType.find(
          (type) => type.businessPartnerType.toLowerCase() === 'supplier'
        );

        if (customerType) {
          // Assign the customer type to the businessPartner object
          this.businessPartner.businessPartnerTypeId.businessPartnerTypeId =
            customerType.businessPartnerTypeId;
        }

        // Optionally store the filtered list if needed
        this.businessPartnerType = businessPartnerType;
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching Business Partner Type', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load Business Partner Type.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Failed to load Business Partner Type.'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
    );
  }

  setTodayDate() {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const dd = String(today.getDate()).padStart(2, '0');

    this.apInvoiceHeadData.postingDate = today.toISOString().split('T')[0]; // Set posting date to today
    this.updateValidityMinDate();
  }

  updateValidityMinDate() {
    const date = this.apInvoiceHeadData.postingDate;
    if (date) {
      const dateObj = new Date(date);
      const yyyy = dateObj.getFullYear();
      const mm = String(dateObj.getMonth() + 1).padStart(2, '0');
      const dd = String(dateObj.getDate()).padStart(2, '0');

      const minValidUntilDate = `${yyyy}-${mm}-${dd}`;
      (document.getElementById('validityUntil') as HTMLInputElement).min =
        minValidUntilDate;
    }
  }

  
  onQuoteDateChange() {
    this.updateValidityMinDate();
    this.validateDates();
  
    const entityId = +localStorage.getItem('entityId')!;
    const date = this.apInvoiceHeadData.postingDate;
  
    if (date) {
      this.periodClosingService.isDateLocked(entityId, date).subscribe({
        next: (isLocked: boolean) => {
          if (isLocked) {
            Swal.fire({
              icon: 'error',
              title: 'Posting Date is Locked',
              text: 'The selected date falls within a closed accounting period. Please choose another date.',
              confirmButtonColor: '#ff7e5f'
            });
  
            // Reset the posting date
           this.setTodayDate();
          }
        },
        error: (err) => {
          console.error('Error validating posting date lock', err);
        }
      });
    }
  }
  
  

  onValidUntilDateChange(): void {
    this.validateDates();
  }

  validateDates(): boolean {
    const postingDate = new Date(this.apInvoiceHeadData.postingDate);
    const validUntilDate = new Date(this.apInvoiceHeadData.dueDate);

    if (validUntilDate < postingDate) {
      Swal.fire({
        title: 'Invalid Date',
        text: 'The Due date cannot be before the "Posting Date".',
        icon: 'warning',
        confirmButtonText: 'OK',
      });
      return false;
    }
    return true;
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  onSubmit(f: NgForm) {
    // Check if the form is valid
    if (f.valid) {
      // Validate if each row has either a selected  description
      const hasInvalidRows = this.apInvoiceHeadData.apInvoiceDetails.some(
        (detail) =>
          !detail.itemDescription || detail.itemDescription.trim() === ''
      );

        if (this.apInvoiceHeadData.apInvoiceDetails.some(detail => !detail.coaLedgerAccountId)) {
       this.swalAlertService.showErrorWithChimpSupport(
         'Each row must have a GL Account selected.',
          'GL Account is missing in one or more rows after scanning the bill.'
       );
        return; 
     }

    if (hasInvalidRows) {
      this.swalAlertService.showErrorWithChimpSupport(
         'Each row must have a description provided.',
          'Each row must have a description provided.'
         );
          return;
        }

      this.checkForZeroQuantity();
    }
  }

  checkForZeroQuantity() {
    const hasZeroQuantity = this.apInvoiceHeadData.apInvoiceDetails.some(
      (detail) => detail.quantity === 0
    );

     const hasNoUnitPrice = this.apInvoiceHeadData.apInvoiceDetails.some(
      (detail) => detail.unitPrice === 0
    );

    if (hasZeroQuantity || hasNoUnitPrice) {
      Swal.fire({
        title: 'Warning!',
        text: 'Selected item(s) have zero Quantity or Unit Price. Do you want to continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
        footer:
          '<a href="#" id="custom-link" style="background-color: #007bff; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">Ask Chimp</a>',
      }).then((result) => {
        if (result.isConfirmed) {
          // Proceed with saving or drafting
          this.saveApInvoice();
        }
      });

      // Attach chatbot logic to the footer link
      document
        .getElementById('custom-link')
        ?.addEventListener('click', (event) => {
          event.preventDefault(); // Prevent default anchor behavior
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData(
                  'Selected item(s) have zero quantity. Do you want to continue?'
                );
                this.chatBotComponent.responseReceived.subscribe((response) => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        });
    } else if (this.apInvoiceHeadData.apInvoiceDetails.length === 0) {
      Swal.fire({
        title: 'Error!',
        text: 'You must add at least one item.',
        icon: 'error',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
      }).then((result) => {
        if (
          result.isDismissed &&
          result.dismiss === Swal.DismissReason.cancel
        ) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData(
                  'You must add at least one item.'
                );
                this.chatBotComponent.responseReceived.subscribe((response) => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });
      return;
    } else {
      // No zero quantity, proceed with saving or drafting
      this.saveApInvoice();
    }
  }

  saveApInvoice() {
    this.apInvoiceHeadData.status = 'Pending'; // Always set status to 'Pending'
    this.apInvoiceHeadData.userId = +(localStorage.getItem('userid') + '');
    this.apInvoiceHeadData.entityId = +(localStorage.getItem('entityId') + '');


    // Validate dates before proceeding
    if (!this.validateDates()) {
      return; // Stop execution if dates are invalid
    }

      const deletedItemIds = this.getDeletedItemIds();
      this.apInvoiceHeadData.deletedItemIds = deletedItemIds; // Send deleted items list


    this.billService.updateAPInvoiceHead(this.id, this.apInvoiceHeadData).subscribe(
      (response: any) => {
        this.updateBusinessEntityReferenceNumber();

        Swal.fire({
          title: 'Success!',
          text: 'The payable bill has been successfully updated.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/payable-bill-list']);
          }
        });
      },
      (error: HttpErrorResponse) => {
        console.error('Error saving Payable Bill', error);
        Swal.fire({
          title: 'Error!',
          text: 'Unable to Save the Payable Bill. Please fill all required fields and try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Unable to Save the Bill. Please try again'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
    );
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

  updateBusinessEntityReferenceNumber() {
    this.businessEntity.referenceNo = this.apInvoiceHeadData.referenceNo;
    this.entityService
      .updateReferenceNumber(this.businessEntity, this.businessEntityId)
      .subscribe(
        (data) => { },
        (error) => {
          console.error(error);
        }
      );
  }

  @ViewChild('cuspop') cuspop!: NgForm;
  @ViewChild('closeCustomerPopUp') closeCustomerPopUp: any;

  onSubmitCustomerForm() {
    this.saveCustomerForm(this.businessPartner);
  }

  saveCustomerForm(businessPartner: BusinessPartner) {
    this.businessPartner.entityId.entityId = +(
      localStorage.getItem('entityId') + ''
    );
    this.businessPartnerService.saveBusinessPartner(businessPartner).subscribe(
      (response) => {
        Swal.fire({
          title: 'Success!',
          text: 'Supplier added successfully.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        });
        this.loadCustomers();
        this.cuspop.resetForm();
        this.closeCustomerPopUp.nativeElement.click();
      },
      (error) => {
        console.error('Error adding Supplier', error);
        Swal.fire({
          title: 'Error!',
          text: 'Error adding Supplier.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Error adding Supplier.');
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
    );
  }

  onFileSelected(event: any) {
    if (event.target.files && event.target.files[0]) {
      this.selectedFile = event.target.files[0];

      if (this.selectedFile) {
        const reader = new FileReader();

        reader.readAsDataURL(this.selectedFile);

        reader.onload = (event: ProgressEvent<FileReader>) => {
          const result = event.target?.result;
          if (typeof result === 'string') {
            this.url = result;
          }
        };
      }
    }
  }

  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(
      (cust) => cust.businessPartnerId === +selectedCustomerId
    );

    // Set the reference field
    this.apInvoiceHeadData.supplierName = selectedCustomer?.bpName || '';
  }

  onGLChange(event: any, index: number) {
    const selectedAccountId = +event.target.value; // Get the selected ID
    const selectedAccount = this.glAccounts.find(
      (account) => account.coaLedgerAccountId === selectedAccountId
    ); // Find the selected account object

    if (selectedAccount) {
      // Update the corresponding detail with the selected account's information
      this.apInvoiceHeadData.apInvoiceDetails[index].coaLedgerAccountId = selectedAccount.coaLedgerAccountId;
      this.apInvoiceHeadData.apInvoiceDetails[index].ledgerAccountName = selectedAccount.ledgerAccountName;
    }
  }


  
  onCancel() {
    this.router.navigate(['/payable-bill-list']);
  }

}
