<app-payroll-nevigation></app-payroll-nevigation>

<div class="container">
  <div class="actions sub-container">
    <h2>Employee</h2>
  </div>

  <div class="sub-container">
    <div class="payroll-head">
      <div class="payroll-head-icon">
        <div class="circle">
          {{ personalData.firstName[0] | uppercase
          }}{{ personalData.lastName[0] | uppercase }}
        </div>
      </div>

      <div class="payroll-head-content">
        <h2 [ngClass]="{ 'inactive-text': !personalData.status }">
          {{ personalData.firstName }} {{ personalData.lastName }}
        </h2>
        <div class="mt-0" [ngClass]="{ 'inactive-text': !personalData.status }">
          {{ personalData.email }}
        </div>
        <div class="col-md-6 mb-3">
          <input
            id="search"
            class="form-control search-input"
            style="width: 100%"
            type="search"
            placeholder="If you've already added your name, please enter your name and select it."
            [(ngModel)]="searchTerm"
            (input)="onSearchInput()"
            aria-label="Search"
          />

          <ul
            *ngIf="employees.length > 0 && searchTerm.trim().length > 0"
            class="dropdown-menu show"
            style="
              position: absolute;
              width: 550px;
              max-height: 200px;
              overflow-y: auto;
            "
          >
            <li
              *ngFor="let employee of employees"
              class="dropdown-item"
              (click)="selectEmployee(employee)"
              style="cursor: pointer"
            >
              {{ employee.firstName }} {{ employee.lastName }}
            </li>
          </ul>
        </div>
      </div>
      
      <div *ngIf="personalData.firstName" class="status-toggle">
        <button
          class="btn"
          [ngClass]="personalData.status ? 'btn-success' : 'btn-danger'"
          (click)="toggleStatus()"
        >
          {{ personalData.status ? "Active" : "Inactive" }}
        </button>

        <label class="switch">
          <input
            type="checkbox"
            [checked]="personalData.status"
            (change)="toggleStatus()"
          />
          <span class="slider round"></span>
        </label>
      </div>
    </div>
  </div>

  <div class="pay-items-section" *ngIf="isActiveTab('employee')">
    <div class="side-bar">
      <ul class="nav nav_pay_item">
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Personal')"
            (click)="setActiveSide('Personal'); $event.preventDefault()"
          >
            Personal
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Employment')"
            (click)="setActiveSide('Employment'); $event.preventDefault()"
          >
            Employment
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Pay_Template')"
            (click)="setActiveSide('Pay_Template'); $event.preventDefault()"
          >
            Earnings
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Bank_Accounts')"
            (click)="setActiveSide('Bank_Accounts'); $event.preventDefault()"
          >
            Bank Accounts
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Leave')"
            (click)="setActiveSide('Leave'); $event.preventDefault()"
          >
            Leave
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Taxes')"
            (click)="setActiveSide('Taxes'); $event.preventDefault()"
          >
            Taxes
          </a>
        </li>
        <li>
          <a
            href="#"
            [class.active]="isActiveSide('Superannuation')"
            (click)="setActiveSide('Superannuation'); $event.preventDefault()"
          >
            Superannuation
          </a>
        </li>
      </ul>
    </div>

    <div class="Earnings" *ngIf="isActiveSide('Personal')">
      <div class="pay_item_head">
        <h2>Personal</h2>
        <button
          *ngIf="isNameExist"
          class="btn btn-primary add-new-btn"
          (click)="clearEmployeeData()"
        >
          Add New
        </button>
      </div>
      <div class="row position-relative"></div>
      <div class="earnings-container">
        <form #personalForm="ngForm">
          <div class="row">
            <!-- First Name -->
            <div class="col-md-6 mb-3 mt-3">
              <label for="firstName" class="form-label">First Name</label>
              <input
                type="text"
                class="form-control"
                id="firstName"
                [(ngModel)]="personalData.firstName"
                name="firstName"
                required
              />
              <div
                *ngIf="
                  personalForm.controls['firstName']?.invalid &&
                  personalForm.controls['firstName']?.touched
                "
                class="text-danger"
              >
                First Name is required.
              </div>
            </div>

            <!-- Last Name -->
            <div class="col-md-6 mb-3 mt-3">
              <label for="lastName" class="form-label">Last Name</label>
              <input
                type="text"
                class="form-control"
                id="lastName"
                [(ngModel)]="personalData.lastName"
                name="lastName"
                required
              />
              <div
                *ngIf="
                  personalForm.controls['lastName']?.invalid &&
                  personalForm.controls['lastName']?.touched
                "
                class="text-danger"
              >
                Last Name is required.
              </div>
            </div>
          </div>

          <div class="row">
            <!-- Date of Birth -->
            <div class="col-md-6 mb-3">
              <label for="dateOfBirth" class="form-label">Date of Birth</label>
              <input
                type="date"
                class="form-control"
                id="dateOfBirth"
                [(ngModel)]="personalData.dateOfBirth"
                name="dateOfBirth"
                required
              />
              <div
                *ngIf="
                  personalForm.controls['dateOfBirth']?.invalid &&
                  personalForm.controls['dateOfBirth']?.touched
                "
                class="text-danger"
              >
                Date of Birth is required.
              </div>
            </div>

            <!-- Gender -->
            <div class="col-md-6 mb-3">
              <label for="gender" class="form-label">Gender</label>
              <select
                class="form-select"
                id="gender"
                [(ngModel)]="personalData.gender"
                name="gender"
              >
                <option value="" disabled>Select Gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>

          <div class="row">
            <!-- Address -->
            <div class="col-md-6 mb-3 position-relative">
              <label for="address" class="form-label">Address</label>
              <input
                type="text"
                class="form-control"
                id="address"
                [(ngModel)]="personalData.address"
                (keyup)="checkBusinessAddress()"
                name="address"
                required
              />

              <ul
                *ngIf="suggestedAddresses.length > 0"
                class="dropdown-menu show"
                style="
                  position: absolute;
                  width: 100%;
                  max-height: 200px;
                  overflow-y: auto;
                  z-index: 1000;
                "
              >
                <li
                  *ngFor="let address of suggestedAddresses"
                  class="dropdown-item"
                  (click)="selectAddress(address)"
                  style="cursor: pointer"
                >
                  {{ address.full_address }}
                </li>
              </ul>

              <div
                *ngIf="
                  personalForm.controls['address']?.invalid &&
                  personalForm.controls['address']?.touched
                "
                class="text-danger"
              >
                Address is required.
              </div>
            </div>

            <!-- State -->
            <div class="col-md-6 mb-3">
              <label for="state" class="form-label">State</label>
              <select
                class="form-select"
                [(ngModel)]="personalData.state"
                name="state"
                id="state"
                required
              >
                <option [ngValue]="null" disabled>Select State</option>
                <option *ngFor="let state of states" [value]="state.code">
                  {{ state.name }} ({{ state.code }})
                </option>
              </select>
              <div
                *ngIf="
                  personalForm.controls['state']?.invalid &&
                  personalForm.controls['state']?.touched
                "
                class="text-danger"
              >
                State is required.
              </div>
            </div>
            
          </div>

          <div class="row">
            <!-- Postal Code -->
            <div class="col-md-6 mb-3">
              <label for="postalCode" class="form-label">Postal Code</label>
              <input
                type="text"
                class="form-control"
                id="postalCode"
                [(ngModel)]="personalData.postalCode"
                name="postalCode"
                required
              />
              <div
                *ngIf="
                  personalForm.controls['postalCode']?.invalid &&
                  personalForm.controls['postalCode']?.touched
                "
                class="text-danger"
              >
              Postal Code is required.
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <label for="emergencyPhoneNumber" class="form-label"
                >Emergency Phone Number</label
              >
              <div class="d-flex">
                <!-- Country Code Dropdown -->
                <div class="dropdown w-25 me-2">
                  <input
                    type="text"
                    class="form-control"
                    [value]="selectedCountryCode"
                    readonly
                    (focus)="showDropdown()"
                    id="countryCodeInput"
                    placeholder="Code"
                  />
                  <ul
                    class="dropdown-menu show w-200"
                    *ngIf="dropdownVisible"
                    style="max-height: 200px; overflow-y: auto"
                  >
                    <li
                      *ngFor="let country of filteredCountries"
                      (click)="selectCountryCode(country)"
                      class="dropdown-item"
                    >
                      {{ country.name }} ({{ country.code }})
                    </li>
                  </ul>
                </div>

                <!-- Phone Number Input -->
                <input
                  type="number"
                  class="form-control w-75"
                  id="emergencyPhoneNumber"
                  [(ngModel)]="emergencyPhoneNumber"
                  (ngModelChange)="updateEmergencyPhoneNumber()"
                  name="emergencyPhoneNumber"
                  required
                  #phoneNumberInput="ngModel"
                  placeholder="Enter phone number"
                  (keydown)="restrictInput($event)"
                />
              </div>

              <!-- Validation Error Message -->
              <div
                *ngIf="phoneNumberInput.invalid && phoneNumberInput.touched"
                class="text-danger mt-1"
              >
                Emergency Phone Number is required.
              </div>
            </div>
          </div>

          <div class="row">
            <!-- Email -->
            <div class="col-md-6 mb-3">
              <label for="email" class="form-label">Email</label>
              <input
                type="email"
                class="form-control"
                id="email"
                [(ngModel)]="personalData.email"
                name="email"
                required
              />
              <div
                *ngIf="
                  personalForm.controls['email']?.invalid &&
                  personalForm.controls['email']?.touched
                "
                class="text-danger"
              >
                Valid email is required.
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <label for="phoneNumber" class="form-label">Phone Number</label>
              <div class="d-flex">
                <!-- Country Code Dropdown -->
                <div class="dropdown w-25 me-2">
                  <input
                    type="text"
                    class="form-control"
                    [value]="selectCode"
                    readonly
                    (focus)="showDropdown2()"
                    id="countryCodeInput"
                    placeholder="Code"
                  />
                  <ul
                    class="dropdown-menu show w-200"
                    *ngIf="dropdownVisible2"
                    style="max-height: 200px; overflow-y: auto"
                  >
                    <li
                      *ngFor="let country of filteredCountries"
                      (click)="selectedCode(country)"
                      class="dropdown-item"
                    >
                      {{ country.name }} ({{ country.code }})
                    </li>
                  </ul>
                </div>

                <!-- Phone Number Input -->
                <input
                  type="number"
                  class="form-control w-75"
                  id="phoneNumber"
                  [(ngModel)]="phoneNumber"
                  (ngModelChange)="updatePhoneNumber()"
                  name="phoneNumber"
                  #phoneNumberInput="ngModel"
                  placeholder="Enter phone number"
                  (keydown)="restrictInput($event)"
                />
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="emp-open-footer-wrapper">
            <div class="right-buttons">
              <button
                *ngIf="!isNameExist"
                type="submit"
                class="btn btn-primary"
                [disabled]="personalForm.invalid"
                (click)="onAddPersonal()"
              >
                Add
              </button>

              <button
                type="button"
                class="btn btn-primary"
                *ngIf="isNameExist"
                [class.active]="isActiveSide('Employment')"
                (click)="setActiveSide('Employment'); $event.preventDefault()"
              >
                Next
              </button>

              <button
                type="button"
                *ngIf="isNameExist && personalData.status === true"
                class="btn btn-primary"
                (click)="updateEmpolyee()"
                [disabled]="personalForm.pristine"
              >
                Update
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <div class="Earnings" *ngIf="isActiveSide('Employment')">
      <div class="pay_item_head">
        <h2>Employment</h2>
      </div>

      <div class="earnings-container">
        <form #employmentForm="ngForm" (ngSubmit)="addEmploymentData()">
          <div class="row">
            <!-- Start Date -->
            <div class="col-md-6 mb-3">
              <label for="startDate" class="form-label">Start date</label>
              <input
                type="date"
                id="startDate"
                class="form-control"
                [(ngModel)]="employmentData.startDate"
                name="startDate"
                required
              />
              <div
                *ngIf="
                  employmentForm.controls['startDate']?.invalid &&
                  employmentForm.controls['startDate']?.touched
                "
                class="text-danger"
              >
                Start Date is required.
              </div>
            </div>

            <!-- Pay Frequency -->
            <div class="col-md-6 mb-3">
              <label for="payFrequency" class="form-label"
                >Payroll Calendar</label
              >
              <select
                id="payFrequency"
                class="form-select"
                [(ngModel)]="selectedPayCalendar"
                (ngModelChange)="onCalendarTypeChange($event)"
                name="payFrequency"
                required
              >
                <option
                  *ngFor="let type of payCalendars"
                  [value]="type.calendarName"
                >
                  {{ type.calendarName }}
                </option>
              </select>

              <div
                *ngIf="
                  employmentForm.controls['payFrequency']?.invalid &&
                  employmentForm.controls['payFrequency']?.touched
                "
                class="text-danger"
              >
                Payroll Calendar is required.
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <label for="employmentBasis" class="form-label"
                >Employment basis</label
              >
              <select
                id="employmentBasis"
                class="form-control"
                [(ngModel)]="employmentData.employmentBasis"
                name="employmentBasis"
                required
              >
                <option value="" disabled selected>
                  Select employment basis
                </option>
                <option value="full-time">Full-time</option>
           
                <option value="casual">Casual</option>
                </select>
                <div
                  *ngIf="
                    employmentForm.controls['employmentBasis']?.invalid &&
                    employmentForm.controls['employmentBasis']?.touched
                  "
                  class="text-danger"
                >
                  Employment Basis is required.
                </div>
              </div>
              <div class="col-md-6 mb-3">
              <label for="incomeType" class="form-label">Income Type</label>
              <select
                class="form-select"
                [(ngModel)]="employmentData.incomeType"
                name="incomeType"
                id="incomeType"
                required
              >
                <option [ngValue]="null" disabled>Select income type</option>
                <option *ngFor="let incomeType of incomeTypes" [value]="incomeType.code">
                  {{ incomeType.name }} ({{ incomeType.code }})
                </option>
              </select>
              <div
                  *ngIf="
                    employmentForm.controls['incomeType']?.invalid &&
                    employmentForm.controls['incomeType']?.touched
                  "
                  class="text-danger"
                >
                  Income Type is required.
                </div>
              </div>
              
              <hr style="border: 1px solid black; margin: 10px 0" />
              <div class="mb-3">
                <div class="salary-type-selector">
                  <label class="radio-label">
                    <input
                      type="radio"
                      name="salaryType"
                      value="Annual Salary"
                      (change)="clearSalaryInputs()"
                      [(ngModel)]="employmentData.salaryType"
                      style="margin-right: 12px"
                    />
                    Annual Salary
                  </label>
                  <label class="radio-label">
                    <input
                      type="radio"
                      name="salaryType"
                      value="Hourly Rate"
                      [(ngModel)]="employmentData.salaryType"
                      (change)="clearSalaryInputs()"
                      style="margin-right: 12px"
                    />
                    Paid on Hourly Rate
                  </label>
                </div>
              
                <label for="amount" class="form-label">
                  {{ employmentData.salaryType }}
                </label>
            
                <input
                  *ngIf="employmentData.salaryType === 'Annual Salary'"
                  type="number"
                  id="amount"
                  style="width: 430px"
                  class="form-control"
                  [(ngModel)]="employmentData.amount"
                  name="amount"
                  (input)="onAmountChange()"
                  (keydown)="restrictInput($event)"
                  required
                />
            
                <input
                  *ngIf="employmentData.salaryType === 'Hourly Rate'"
                  type="number"
                  id="ordinaryEarningsRate"
                  class="form-control"
                  [(ngModel)]="employmentData.ordinaryEarningsRate"
                  name="ordinaryEarningsRate"
                  (keydown)="restrictInput($event)"
                />
              </div>
              

            <div
              *ngIf="employmentData.salaryType === 'Annual Salary'"
              class="conditional-fields"
            >
              <div class="d-flex justify-content-between">
                <!-- Hours per Week -->
                <div class="form-group me-3 mb-3">
                  <label for="hoursperWeek" class="form-label"
                    >Hours per week</label
                  >
                  <input
                    type="number"
                    id="hoursperWeek"
                    class="form-control"
                    [(ngModel)]="employmentData.hoursperWeek"
                    name="hoursperWeek"
                    (input)="calculateOrdinaryEarningsRate()"
                    (keydown)="restrictInput($event)"
                    required
                  />
                </div>

                <!-- Ordinary Earnings Rate -->
                <div class="form-group mb-3">
                  <label for="ordinaryEarningsRate" class="form-label"
                    >Ordinary earnings rate</label
                  >
                  <input
                    type="number"
                    id="ordinaryEarningsRate"
                    class="form-control"
                    [(ngModel)]="employmentData.ordinaryEarningsRate"
                    name="ordinaryEarningsRate"
                    (keydown)="restrictInput($event)"
                    readonly
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-3 mt-3">
            <div class="d-flex  eligible-row">
              <label
                for="eligibleForLeaveLoadingSuper"
                class="form-label me-3 mb-3"
              >
                Eligibility for Leave Loading
              </label>
              <div class="form-check me-3">
                <input
                  class="form-check-input"
                  type="radio"
                  id="eligibleForLeaveLoadingSuperYes"
                  name="eligibleForLeaveLoadingSuper"
                  [(ngModel)]="employmentData.eligibleForLeaveLoading"
                  [value]="true"
                  required
                />
                <label
                  class="form-check-label mb-0"
                  for="eligibleForLeaveLoadingSuperYes"
                  >Yes</label
                >
              </div>
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="radio"
                  id="eligibleForLeaveLoadingSuperNo"
                  name="eligibleForLeaveLoadingSuper"
                  [(ngModel)]="employmentData.eligibleForLeaveLoading"
                  [value]="false"
                  required
                />
                <label
                  class="form-check-label mb-0"
                  for="eligibleForLeaveLoadingSuperNo"
                  >No</label
                >
              </div>
            </div>
          </div>

          <div
            class="col-md-6 mb-3 mt-3"
            *ngIf="employmentData.eligibleForLeaveLoading === true"
          >
            <div class="d-flex align-items-center">
              <label for="eligibleForLeaveLoading" class="form-label me-3 mb-0">
                Leave Loading Eligibility for Superannuation
              </label>
              <div class="form-check me-3">
                <input
                  class="form-check-input"
                  type="radio"
                  id="eligibleForLeaveLoadingYes"
                  name="eligibleForLeaveLoading"
                  [(ngModel)]="employmentData.eligibleForLeaveLoadingSuper"
                  [value]="true"
                  required
                />
                <label
                  class="form-check-label mb-0"
                  for="eligibleForLeaveLoadingYes"
                  >Yes</label
                >
              </div>
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="radio"
                  id="eligibleForLeaveLoadingNo"
                  name="eligibleForLeaveLoading"
                  [(ngModel)]="employmentData.eligibleForLeaveLoadingSuper"
                  [value]="false"
                  required
                />
                <label
                  class="form-check-label mb-0"
                  for="eligibleForLeaveLoadingNo"
                  >No</label
                >
              </div>
            </div>
          </div>

          <!-- Add Employment Button -->
          <div class="emp-open-footer-wrapper">
            <div class="right-buttons mt-3">
              <button
                type="submit"
                *ngIf="!isEmployementExist"
                class="btn btn-primary"
                (click)="onAddEmployment()"
                [disabled]="employmentForm.invalid || isAddOrUpdateDisabled()"
              >
                Add
              </button>
              <button
                type="button"
                class="btn btn-primary"
                *ngIf="isEmployementExist"
                [class.active]="isActiveSide('Pay_Template')"
                (click)="setActiveSide('Pay_Template'); $event.preventDefault()"
              >
                Next
              </button>

              <button
                type="button"
                *ngIf="isEmployementExist && personalData.status === true"
                class="btn btn-primary"
                (click)="updateEmployement()"
                [disabled]="employmentForm.pristine || isAddOrUpdateDisabled()"
              >
                Update
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <div class="Earnings" *ngIf="isActiveSide('Leave')">
      <div class="pay_item_head">
        <h2>Leave</h2>
      </div>

      <!-- Leave -->

      <div class="earnings-container mt-3">
        <div class="pay_item_head">
          <h2>Leave</h2>
        </div>
        <div class="responsive-table-wrapper">
        <table class="table no-borders">
          <thead>
            <tr>
              <th>Leave Type</th>
              <th>Method</th>
              <th>Accrued Hours Per Week</th>
              <th>Hours Accrued Annually</th>
              <th>Opening Balance</th>
              <th *ngIf="hasLeaveAccrualData()">Leave Balance</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let leave of payTemplateLeaves; let i = index">
              <td>{{ leave.leaveType?.leaveType || null }}</td>
              <td>{{ leave.leaveMethods || "" }}</td>
              <td>{{ leave.accruedHoursPerWeek | number: '1.2-3' }}</td>
              <td>{{ leave.hoursAccruedAnnually | number: '1.2-3' }}</td>
              <td>{{ leave.openingBalance || "0.00" }}</td>
              <td *ngIf="hasLeaveAccrualData()">
                {{ leave.currentBalance | number: '1.2-3' }}
              </td>
              <td>
                <button
                  class="btn btn-danger btn-sm"
                  (click)="deleteLeave(leave.employeeLeaveId)"
                  style="
                    margin-left: 10px;
                    border: none;
                    background: none;
                    padding-left: 10px;
                    font-size: 1.2rem;
                  "
                  title="Delete"
                >
                  <i
                    class="ri-delete-bin-line"
                    style="
                      color: #ff0000;
                      text-shadow: 2px 2px 4px rgba(205, 117, 70, 0.6);
                    "
                  ></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        </div>
        <div class="earning-total">
          <button
            class="btn-add-earning"
            (click)="openLeaveModal()"
            data-bs-toggle="modal"
            data-bs-target="#modal-leave"
          >
            + Add Leave
          </button>
        </div>
      </div>

      <!-- Modal for Adding Leave -->
      <div
        class="modal fade"
        id="modal-leave"
        tabindex="-1"
        aria-labelledby="leaveModalLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="leaveModalLabel">Add Leave</h5>
              <button
                type="button"
                class="custom-close-btn"
                data-bs-dismiss="modal"
                aria-label="Close"
                (click)="closeEarningModal()"
              >
                <i class="bi bi-x-circle"></i>
              </button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label for="leaveType" class="form-label">Leave Type</label>
                <select
                  id="leaveType"
                  class="form-select"
                  [(ngModel)]="selectedLeaveType"
                  (ngModelChange)="onLeaveTypeChange($event)"
                >
                  <option *ngFor="let type of leave" [value]="type.leaveType">
                    {{ type.leaveType }}
                  </option>
                </select>
              </div>
              <div class="mb-3">
                <label for="leaveHours" class="form-label"
                  >Leave Calculating Method</label
                >
                <select
                  id="leaveMethods"
                  class="form-select"
                  [(ngModel)]="newLeave.leaveMethods"
                  (ngModelChange)="onLeaveMethodChange()"
                >
                  <option *ngFor="let type of leaveMethod" [value]="type">
                    {{ type }}
                  </option>
                </select>
              </div>

              <div *ngIf="newLeave.leaveMethods === 'Fixed Sum Per Period'">
                <div>
                  <!-- Hours Accrued Annually  -->
                  <div class="form-group mb-3">
                    <label for="hoursAccruedAnnually" class="form-label"
                      >Hours Accrued Annually</label
                    >
                    <input
                      type="number"
                      id="hoursAccruedAnnually"
                      class="form-control"
                      [(ngModel)]="newLeave.hoursAccruedAnnually"
                      name="hoursAccruedAnnually"
                      (input)="onHoursAccruedAnnuallyChange()"
                      (keydown)="restrictInput($event)"
                    />
                  </div>

                  <!-- Hours per Week -->
                  <div class="form-group mb-3">
                    <label for="hoursPerWeek" class="form-label"
                      >Hours per week</label
                    >
                    <input
                      type="number"
                      id="hoursPerWeek"
                      class="form-control"
                      [(ngModel)]="newLeave.hoursPerWeek"
                      name="hoursPerWeek"
                      required
                      (input)="onHoursPerWeekChange()"
                      (keydown)="restrictInput($event)"
                    />
                  </div>

                  <div class="form-group mb-3">
                    <label for="hoursAccruedAnnually" class="form-label"
                      >Accrued Hours Per Week</label
                    >
                    <input
                      type="number"
                      id="hoursAccruedAnnually"
                      class="form-control"
                      [(ngModel)]="newLeave.accruedHoursPerWeek"
                      name="hoursAccruedAnnually"
                      readonly
                      (keydown)="restrictInput($event)"
                    />
                  </div>
                </div>
              </div>

              <div *ngIf="newLeave.leaveMethods === 'Manually Entered Rate'">
                <div>
                  <!-- Hours per Week -->
                  <div class="form-group mb-3">
                    <label for="hoursperWeek" class="form-label"
                      >Accrued Hours per week</label
                    >
                    <input
                      type="number"
                      id="hoursperWeek"
                      class="form-control"
                      [(ngModel)]="newLeave.accruedHoursPerWeek"
                      name="hoursperWeek"
                      required
                      (keydown)="restrictInput($event)"
                    />
                  </div>
                </div>
              </div>

              <!-- <div *ngIf="newLeave.leaveMethods === 'No Calculation Required'" >
                <div >
                 
                  <div class="form-group mb-3">
                    <label for="hoursperWeek" class="form-label">Hours per week</label>
                    <input
                      type="number"
                      id="hoursperWeek"
                      class="form-control"
                      [(ngModel)]="newLeave.hoursPerWeek"
                      name="hoursperWeek"
                      required
                      (keydown)="restrictInput($event)"
                    />
                  </div>
                </div>
              </div> -->

              <div
                *ngIf="newLeave.leaveMethods === 'Based On Ordinary Earnings'"
              >
                <div>
                  <!-- Hours Accrued Annually  -->
                  <div class="form-group mb-3">
                    <label for="hoursAccruedAnnually" class="form-label"
                      >Hours Accrued Annually</label
                    >
                    <input
                      type="number"
                      id="hoursAccruedAnnually"
                      class="form-control"
                      [(ngModel)]="newLeave.hoursAccruedAnnually"
                      name="hoursAccruedAnnually"
                      (keydown)="restrictInput($event)"
                    />
                  </div>

                  <div class="mb-3">
                    <label for="leaveHours" class="form-label"
                      >Hours Per Week</label
                    >
                    <input
                      type="number"
                      id="leaveHours"
                      class="form-control"
                      [(ngModel)]="newLeave.hoursPerWeek"
                      placeholder="0.00"
                      (keydown)="restrictInput($event)"
                    />
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <label for="openingBalance" class="form-label"
                  >Opening Balance</label
                >
                <input
                  type="number"
                  id="openingBalance"
                  class="form-control"
                  [(ngModel)]="newLeave.openingBalance"
                  name="openingBalance"
                  placeholder="0.00"
                  (keydown)="restrictInput($event)"
                />
              </div>
            </div>
            <div class="emp-open-footer-wrapper">
              <div class="right-buttons">
                <button
                  type="button"
                  class="btn btn-primary"
                  *ngIf="personalData.status === true"
                  (click)="addLeave()"
                  data-bs-dismiss="modal"
                  [disabled]="!isFormValidate()"
                >
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="emp-open-footer-wrapper">
        <div class="right-buttons">
          <button
            type="button"
            class="btn btn-primary"
            [class.active]="isActiveSide('Taxes')"
            (click)="setActiveSide('Taxes'); $event.preventDefault()"
          >
            Next
          </button>
        </div>
      </div>
    </div>

    <!---Tax-->
    <div class="Earnings" *ngIf="isActiveSide('Taxes')">
      <div class="pay_item_head">
        <h2>Taxes</h2>
      </div>

      <div class="earnings-container">
        <form #employeeTaxForm="ngForm" (ngSubmit)="addEmployeeTaxData()">
          <div class="row">
            <!-- taxFileNumber -->
            <div class="col-md-6">
              <label for="taxFileNumber" class="form-label"
                >Tax File Number</label
              >
              <input
                type="text"
                id="taxFileNumber"
                class="form-control"
                [(ngModel)]="employeeTaxData.taxFileNumber"
                name="taxFileNumber"
                required
              />
              <div
                *ngIf="
                  employeeTaxForm.controls['taxFileNumber']?.invalid &&
                  employeeTaxForm.controls['taxFileNumber']?.touched
                "
                class="text-danger"
              >
                Tax File Number is required.
              </div>
            </div>

            <!-- taxScale -->

            <!-- <label for="ordinaryEarningsRate" class="form-label"
                >Tax Scale</label
              >
              <select
                id="taxScale"
                class="form-control"
                [(ngModel)]="employeeTaxData.taxScale"
                name="taxScale"
                required
              >
                <option value="" disabled selected>Select Tax Scale</option>
                <option value="regular">Regular</option>
              </select>
              <div
                *ngIf="
                  employeeTaxForm.controls['taxScale']?.invalid &&
                  employeeTaxForm.controls['taxScale']?.touched
                "
                class="text-danger"
              >
                Tax Scale is required.
              </div> -->

            <!-- Residency Status -->
            <div class="col-md-6">
              <label for="payFrequency" class="form-label"
                >Residency Status</label
              >

              <select
                id="residencyStatus"
                class="form-control"
                [(ngModel)]="employeeTaxData.residencyStatus"
                name="residencyStatus"
                required
              >
                <option value="" disabled selected>
                  Select Residency Status
                </option>
                <option value="Australian Resident">Australian Resident</option>
                <option value="Foreign Resident">Foreign Resident</option>
              </select>
              <div
                *ngIf="
                  employeeTaxForm.controls['residencyStatus']?.invalid &&
                  employeeTaxForm.controls['residencyStatus']?.touched
                "
                class="text-danger"
              >
                Residency Status is required.
              </div>
            </div>
            <div class="col-md-6 mb-3"></div>

            <div class="col-md-6 mb-3 mt-4">
              <div class="d-flex align-items-center"></div>
            </div>

            <div class="col-md-6 mb-3">
              <div class="d-flex eligible-row">
                <label for="studyTrainingLoan" class="form-label me-3 mb-0">
                  Payee has study or training loan
                </label>
                <div class="form-check me-3">
                  <input
                    class="form-check-input"
                    type="radio"
                    id="studyTrainingLoanYes"
                    name="studyTrainingLoan"
                    [(ngModel)]="employeeTaxData.studyTrainingLoan"
                    [value]="true"
                    required
                  />
                  <label
                    class="form-check-label mb-0"
                    for="studyTrainingLoanYes"
                    >Yes</label
                  >
                </div>
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="radio"
                    id="studyTrainingLoanNo"
                    name="studyTrainingLoan"
                    [(ngModel)]="employeeTaxData.studyTrainingLoan"
                    [value]="false"
                    required
                  />
                  <label class="form-check-label mb-0" for="studyTrainingLoanNo"
                    >No</label
                  >
                </div>
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <div class="d-flex eligible-row">
                <label for="taxFreeThreshold" class="form-label me-3 mb-0">
                  Tax-free threshold
                </label>
                <div class="form-check me-3">
                  <input
                    class="form-check-input"
                    type="radio"
                    id="taxFreeThresholdYes"
                    name="taxFreeThreshold"
                    [(ngModel)]="employeeTaxData.taxFreeThreshold"
                    [value]="true"
                    required
                  />
                  <label class="form-check-label mb-0" for="taxFreeThresholdYes"
                    >Yes</label
                  >
                </div>
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="radio"
                    id="taxFreeThresholdNo"
                    name="taxFreeThreshold"
                    [(ngModel)]="employeeTaxData.taxFreeThreshold"
                    [value]="false"
                    required
                  />
                  <label class="form-check-label mb-0" for="taxFreeThresholdNo"
                    >No</label
                  >
                </div>
              </div>
            </div>

            <div class="col-md-6 mb-3 mt-4">
              <div class="d-flex eligible-row">
                <label for="increaseTaxAmount" class="form-label me-3 mb-0">
                  Increase amount of Tax
                </label>
                <div class="form-check me-3">
                  <input
                    class="form-check-input"
                    type="radio"
                    id="increaseTaxAmountYes"
                    name="increaseTaxAmount"
                    [(ngModel)]="employeeTaxData.increaseTaxAmount"
                    [value]="true"
                    required
                  />
                  <label
                    class="form-check-label mb-0"
                    for="increaseTaxAmountYes"
                    >Yes</label
                  >
                </div>
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="radio"
                    id="increaseTaxAmountNo"
                    name="increaseTaxAmount"
                    [(ngModel)]="employeeTaxData.increaseTaxAmount"
                    [value]="false"
                    required
                  />
                  <label class="form-check-label mb-0" for="increaseTaxAmountNo"
                    >No</label
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- Add Employee Tax Button -->
          <div class="emp-open-footer-wrapper">
            <div class="right-buttons" style="width: 100%;">
              <button
                type="submit"
                *ngIf="!isTaxExist"
                class="btn btn-primary"
                [disabled]="employeeTaxForm.invalid"
              >
                Add
              </button>
              <button
                type="button"
                class="btn btn-primary"
                [class.active]="isActiveSide('Superannuation')"
                (click)="
                  setActiveSide('Superannuation'); $event.preventDefault()
                "
              >
                Next
              </button>
              <button
                type="button"
                *ngIf="isTaxExist && personalData.status === true"
                class="btn btn-primary"
                (click)="updateTax()"
                [disabled]="employeeTaxForm.pristine"
              >
                Update
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Bank_Accounts -->

    <div class="Earnings" *ngIf="isActiveSide('Bank_Accounts')">
      <div class="pay_item_head">
        <h2>Bank Accounts</h2>
      </div>
      <div class="earnings-container">
        <!-- First Form -->
        <form #firstBankAccountForm="ngForm" (ngSubmit)="saveBankAccount()">
          <div class="bank-account-row mt-3">
            <h4 class="d-flex justify-content-between align-items-center">
              Primary Bank Account
              <div class="form-actions">
              <button
                  type="button"
                  class="delete-icon-button"
                  *ngIf="isFormSaved"
                  (click)="deleteBankAccount(bankAccountData.bankAccountId)"
                  title="Delete"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </div>
            </h4>
            <div class="form-actions" *ngIf="!isFormSaved">
              <label (click)="toggleBalanceSalary()" class="d-flex align-items-center gap-4">
                Balance Salary Remittance Account
                <input type="checkbox" [checked]="isBalanceSalary" />       
              </label>
            </div> 
            <div class="form-row mt-3">
              <div class="form-group">
                <label for="accountName">Account Name</label>
                <input
                  id="accountName"
                  class="form-control"
                  [(ngModel)]="bankAccountData.accountName"
                  name="accountName"
                  required
                />
                <div
                  *ngIf="
                    firstBankAccountForm.controls['accountName']?.invalid &&
                    firstBankAccountForm.controls['accountName']?.touched
                  "
                  class="text-danger"
                >
                  Account Name is required.
                </div>
              </div>
              <div class="form-group">
                <label for="bsb">BSB</label>
                <input
                  id="bsb"
                  class="form-control"
                  [(ngModel)]="bankAccountData.bsbNumber"
                  name="bsb"
                  required
                />
                <div
                  *ngIf="
                    firstBankAccountForm.controls['bsb']?.invalid &&
                    firstBankAccountForm.controls['bsb']?.touched
                  "
                  class="text-danger"
                >
                  BSB is required.
                </div>
              </div>
              <div class="form-group">
                <label for="accountNumber">Account Number</label>
                <input
                  id="accountNumber"
                  class="form-control"
                  [(ngModel)]="bankAccountData.accountNumber"
                  name="accountNumber"
                  required
                />
                <div
                  *ngIf="
                    firstBankAccountForm.controls['accountNumber']?.invalid &&
                    firstBankAccountForm.controls['accountNumber']?.touched
                  "
                  class="text-danger"
                >
                  Account Number is required.
                </div>
              </div>
              <!-- <div class="form-group">
                <label>Remarks</label>
                <input
                  id="standardText"
                  class="form-control"
                  [(ngModel)]="bankAccountData.remark"
                  name="standardText"
                />
              </div> -->
              <div class="form-group" *ngIf="!isBalanceSalary || !isAmount">
                <label>Amount</label>
                <input
                  type="number"
                  id="amount"
                  class="form-control"
                  [(ngModel)]="bankAccountData.amount"
                  name="amount"
                  placeholder="0.0"
                  required
                  (keydown)="restrictInput($event)"
                />
                <div
                  *ngIf="
                    firstBankAccountForm.controls['amount']?.invalid &&
                    firstBankAccountForm.controls['amount']?.touched
                  "
                  class="text-danger"
                >
                  Amount is required.
                </div>
              </div>
            </div>
            <div class="form-actions">
              <!-- Cancel Button -->
              <button
                type="button"
                class="cancel-icon-button"
                *ngIf="!isFormSaved"
                (click)="clearFirstForm()"
                title="Cancel"
              >
                <i class="fa fa-times-circle"></i>
              </button>

              <!-- Add Button -->
              <button
                type="submit"
                class="add-icon-button"
                *ngIf="!isFormSaved && personalData.status === true"
                title="Add"
                [disabled]="firstBankAccountForm.invalid"
              >
                <i class="fa fa-plus-circle"></i>
              </button>
            </div>
          </div>
        </form>

        <!-- Additional Forms -->
        <div *ngFor="let account of bankAccounts; let i = index">
          <form
            #additionalBankAccountForm="ngForm"
            (ngSubmit)="saveBankAccount2(account)"
          >
            <div class="bank-account-row mt-3 mb-3">
              <div class="form-group" style="position: relative">
                <label style="margin-top: 3px">Bank Account Name</label>
                <input
                  class="form-control"
                  style="width: 400px"
                  [(ngModel)]="account.bankAccountName"
                  [name]="'bankAccountName' + i"
                  placeholder="Enter Bank Account Name"
                  required
                />
                <div
                  *ngIf="
                    additionalBankAccountForm.controls['bankAccountName' + i]
                      ?.invalid &&
                    additionalBankAccountForm.controls['bankAccountName' + i]
                      ?.touched
                  "
                  class="text-danger"
                >
                  Bank Account Name is required.
                </div>
                <div class="form-actions" *ngIf="!account.isSaved">
                  <label (click)="toggleBalanceSalary2(account)" class="d-flex align-items-center gap-4">
                    Balance Salary Remittance Account
                    <input type="checkbox" [checked]="account.isBalanceSalary2" />       
                  </label>
                </div>
                <div class="form-actions-inline">
                  <button
                    type="button"
                    class="delete-icon-button"
                    *ngIf="account.isSaved"
                    (click)="DeleteBankAccountNew(account.bankAccountId)"
                    title="Delete"
                  >
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </div>
              <div class="form-row mt-3">
                <div class="form-group">
                  <label for="accountName{{ i }}">Account Name</label>
                  <input
                    id="accountName{{ i }}"
                    class="form-control"
                    [(ngModel)]="account.accountName"
                    [name]="'accountName' + i"
                    required
                  />
                  <div
                    *ngIf="
                      additionalBankAccountForm.controls['accountName' + i]
                        ?.invalid &&
                      additionalBankAccountForm.controls['accountName' + i]
                        ?.touched
                    "
                    class="text-danger"
                  >
                    Account Name is required.
                  </div>
                </div>
                <div class="form-group">
                  <label for="bsb{{ i }}">BSB</label>
                  <input
                    id="bsb{{ i }}"
                    class="form-control"
                    [(ngModel)]="account.bsbNumber"
                    [name]="'bsb' + i"
                    required
                  />
                  <div
                    *ngIf="
                      additionalBankAccountForm.controls['bsb' + i]?.invalid &&
                      additionalBankAccountForm.controls['bsb' + i]?.touched
                    "
                    class="text-danger"
                  >
                    BSB is required.
                  </div>
                </div>
                <div class="form-group">
                  <label for="accountNumber{{ i }}">Account Number</label>
                  <input
                    id="accountNumber{{ i }}"
                    class="form-control"
                    [(ngModel)]="account.accountNumber"
                    [name]="'accountNumber' + i"
                    required
                  />
                  <div
                    *ngIf="
                      additionalBankAccountForm.controls['accountNumber' + i]
                        ?.invalid &&
                      additionalBankAccountForm.controls['accountNumber' + i]
                        ?.touched
                    "
                    class="text-danger"
                  >
                    Account Number is required.
                  </div>
                </div>
                <!-- <div class="form-group">
                  <label>Remark</label>
                  <input
                    class="form-control"
                    [(ngModel)]="account.remark"
                    [name]="'remark' + i"
                  />
                </div> -->
                <div class="form-group"  *ngIf="!account.isBalanceSalary2 || !account.isAmount2">
                  <label>Amount</label>
                  <input
                    type="number"
                    placeholder=" 0.0"
                    class="form-control"
                    [(ngModel)]="account.amount"
                    name="'amount' + i"
                    required
                    (keydown)="restrictInput($event)"
                  />
                  <div
                    *ngIf="
                      firstBankAccountForm.controls['amount']?.invalid &&
                      firstBankAccountForm.controls['amount']?.touched
                    "
                    class="text-danger"
                  >
                    Amount is required.
                  </div>
                </div>
              </div>
              <div class="form-actions">
                <button
                  type="button"
                  class="cancel-icon-button"
                  *ngIf="!account.isSaved"
                  (click)="removeBankAccount(i)"
                  title="Cancel"
                >
                  <i class="fa fa-times-circle"></i>
                </button>
                <button
                  type="submit"
                  class="add-icon-button"
                  *ngIf="!account.isSaved && personalData.status === true"
                  title="Add"
                  [disabled]="additionalBankAccountForm.invalid"
                >
                  <i class="fa fa-plus-circle"></i>
                </button>
              </div>
            </div>
          </form>
        </div>

        <button
          type="button"
          class="btn btn-link mt-3"
          *ngIf="!isAmount"
          (click)="addBankAccount()"
        >
          + Add Another Bank Account
        </button>
        <div class="emp-open-footer-wrapper">
          <div class="right-buttons">
            <button
              type="button"
              class="btn btn-primary"
              [class.active]="isActiveSide('Leave')"
              (click)="setActiveSide('Leave'); $event.preventDefault()"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="Earnings" *ngIf="isActiveSide('Superannuation')">
      <div class="pay_item_head">
        <h2>Superannuation</h2>
      </div>

      <!-- SuperAnnuation Fund -->

      <div class="earnings-container mt-3">
        <div class="pay_item_head">
          <h2>Superannuation Fund</h2>
        </div>
        <div class="table-responsive-wrapper">
        <table class="table no-borders">
          <thead>
            <tr>
              <th>Superannuation Fund</th>
              <th>Product Name</th>
              <th>Contribution Type</th>
              <th>Calculated As</th>
              <th>Value</th>
              <th>Actions</th>

              <!-- <th>Total</th> -->
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let superannuation of payTemplateSuperannuations;
                let i = index
              "
            >
              <td>{{ superannuation.superannuationFund?.fundName }}</td>
              <td>{{ superannuation.superannuationFund?.productName }}</td>
              <td>{{ superannuation.contributionType || "" }}</td>
              <td>{{ superannuation.type || "" }}</td>
              <td>{{ superannuation.value || "0.00" }}</td>
              <td>
                <button
                  class="btn btn-danger btn-sm"
                  (click)="
                    deleteSuperannuation(superannuation.superannuationId)
                  "
                  style="
                    margin-left: 10px;
                    border: none;
                    background: none;
                    padding-left: 10px;
                    font-size: 1.2rem;
                  "
                  title="Delete"
                >
                  <i
                    class="ri-delete-bin-line"
                    style="
                      color: #ff0000;
                      text-shadow: 2px 2px 4px rgba(205, 117, 70, 0.6);
                    "
                  ></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        </div>
        <div class="earning-total">
          <button
            class="btn-add-earning"
            (click)="openSuperannuationModal()"
            data-bs-toggle="modal"
            data-bs-target="#modal-superannuation"
          >
            + Add Superannuation
          </button>
        </div>
      </div>

      <!-- Modal for Adding Superannuation -->
      <div
        class="modal fade"
        id="modal-superannuation"
        tabindex="-1"
        aria-labelledby="superannuationModalLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Add Superannuation</h5>
              <button
                type="button"
                class="custom-close-btn"
                data-bs-dismiss="modal"
                aria-label="Close"
                (click)="closeSuperannuationModal()"
              >
                <i class="bi bi-x-circle"></i>
              </button>
            </div>

            <!-- Superannuation Forms -->
            <div class="modal-body">
              <form
                #firstSuperannuationForm="ngForm"
                (ngSubmit)="addSuperannuation()"
              >
                <div *ngIf="!hasSGCContribution()">
                  <div class="mb-3">
                    <label for="superannuationType" class="form-label"
                      >Superannuation Guarantee Fund</label
                    >
                    <select
                      id="superannuationType"
                      class="form-select"
                      [(ngModel)]="selectedSuperannuationType"
                      name="superannuationType"
                      (ngModelChange)="
                        onSuperannuationTypeChange($event);
                        setDefaultValue($event)
                      "
                      required
                    >
                      <option
                        *ngFor="let type of superannuations"
                        [value]="type.fundName"
                      >
                        {{ type.fundName }} : {{ type.productName }}
                      </option>
                    </select>

                    <div
                      *ngIf="
                        firstSuperannuationForm.controls['superannuationType']
                          ?.invalid &&
                        firstSuperannuationForm.controls['superannuationType']
                          ?.touched
                      "
                      class="text-danger"
                    >
                      Superannuation Fund is required.
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="usi" class="form-label">USI</label>
                    <input
                      type="text"
                      id="usi"
                      class="form-control"
                      [(ngModel)]="newSuperannuation.usi"
                      name="usi"
                      readonly
                    />
                  </div>

                  <div class="mb-3">
                    <label for="membershipNumber" class="form-label"
                      >Membership Number</label
                    >
                    <input
                      type="text"
                      id="membershipNumber"
                      class="form-control"
                      [(ngModel)]="newSuperannuation.membershipNumber"
                      name="membershipNumber"
                      placeholder="Enter Membership Number"
                      required
                    />

                    <div
                      *ngIf="
                        firstSuperannuationForm.controls['membershipNumber']
                          ?.invalid &&
                        firstSuperannuationForm.controls['membershipNumber']
                          ?.touched
                      "
                      class="text-danger"
                    >
                      Membership Number is required.
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="rate" class="form-label">Rate</label>
                    <input
                      id="rate"
                      type="number"
                      [(ngModel)]="newSuperannuation.value"
                      name="rate"
                      class="form-control"
                      required
                      (keydown)="restrictInput($event)"
                    />
                    <div
                      *ngIf="
                        firstSuperannuationForm.controls['rate']?.invalid &&
                        firstSuperannuationForm.controls['rate']?.touched
                      "
                      class="text-danger"
                    >
                      Rate is required.
                    </div>
                  </div>

                  <div class="modal-footer">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      *ngIf="personalData.status === true"
                      [disabled]="firstSuperannuationForm.invalid"
                      data-bs-dismiss="modal"
                    >
                      Add
                    </button>
                  </div>
                </div>

                <div *ngIf="hasSGCContribution()">
                  <div class="form-group" style="position: relative">
                    <div class="form-actions-inline"></div>
                  </div>

                  <h5 class="mb-3"><b>Additional Contribution</b></h5>

                  <div class="mb-3 mt-6">
                    <label for="contributionType" class="form-label"
                      >Contribution Type</label
                    >
                    <select
                      id="contributionType"
                      class="form-select"
                      [(ngModel)]="newSuperannuation.contributionType"
                      [name]="'contributionType'"
                      (ngModelChange)="onContributionTypeChange($event)"
                      required
                    >
                      <option value="" disabled selected>
                        Select Contribution Type
                      </option>
                      <option value="SGC">SGC</option>
                      <option value="salarySacrificed">Salary Sacrificed</option>
                      <option value="employerContribution">Employer Contribution</option>
                    </select>
                    <div
                      *ngIf="
                        firstSuperannuationForm.controls['contributionType']
                          ?.invalid &&
                        firstSuperannuationForm.controls['contributionType']
                          ?.touched
                      "
                      class="text-danger"
                    >
                      Contribution Type is required.
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="superannuationType" class="form-label"
                      >Superannuation Fund</label
                    >
                    <select
                      id="superannuationType"
                      class="form-select"
                      [(ngModel)]="selectedSuperannuationType"
                      [name]="'superannuationType'"
                      (ngModelChange)="onSuperannuationTypeChange($event)"
                      required
                    >
                      <option
                        *ngFor="let type of superannuations"
                        [value]="type.fundName"
                      >
                        {{ type.fundName }} : {{ type.productName }}
                      </option>
                    </select>
                    <div
                      *ngIf="
                        firstSuperannuationForm.controls['superannuationType']
                          ?.invalid &&
                        firstSuperannuationForm.controls['superannuationType']
                          ?.touched
                      "
                      class="text-danger"
                    >
                      Superannuation Fund is required.
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="usi" class="form-label">USI</label>
                    <input
                      type="text"
                      id="usi"
                      class="form-control"
                      [(ngModel)]="newSuperannuation.usi"
                      [name]="'usi'"
                      readonly
                    />
                  </div>

                  <div class="mb-3">
                    <label for="membershipNumber" class="form-label"
                      >Membership Number</label
                    >
                    <input
                      type="text"
                      id="membershipNumber"
                      class="form-control"
                      [(ngModel)]="newSuperannuation.membershipNumber"
                      [name]="'membershipNumber'"
                      placeholder="Enter Membership Number"
                      required
                    />
                    <div
                      *ngIf="
                        firstSuperannuationForm.controls['membershipNumber']
                          ?.invalid &&
                        firstSuperannuationForm.controls['membershipNumber']
                          ?.touched
                      "
                      class="text-danger"
                    >
                      Membership Number is required.
                    </div>
                  </div>

                  <div class="mb-3">
                    <div class="salary-type-selector">
                      <label class="radio-label">
                        <input
                          type="radio"
                          [name]="'type'"
                          value="Percentage"
                          [(ngModel)]="newSuperannuation.type"
                          style="margin-right: 12px"
                          (change)="clearSuperannuationTypeChange2()"
                          required
                          [checked]="newSuperannuation.type === 'Percentage'"
                        />
                        Percentage
                      </label>

                      <label class="radio-label">
                        <input
                          type="radio"
                          [name]="'type'"
                          value="Amount"
                          [(ngModel)]="newSuperannuation.type"
                          style="margin-right: 12px"
                          (change)="clearSuperannuationTypeChange2()"
                          required
                          [disabled]="newSuperannuation.contributionType === 'SGC'"
                        />
                        Amount
                      </label>
                    </div>

                    <label for="amount" class="form-label">
                      {{ newSuperannuation.type }}
                    </label>

                    <input
                      id="rate"
                      type="number"
                      [(ngModel)]="newSuperannuation.value"
                      name="rate"
                      class="form-control"
                      required
                      (keydown)="restrictInput($event)"
                    />

                    <!-- Validation message for rate -->
                    <div
                      *ngIf="
                        firstSuperannuationForm.controls['rate']?.invalid &&
                        firstSuperannuationForm.controls['rate']?.touched
                      "
                      class="text-danger"
                    >
                      Value is required.
                    </div>
                  </div>

                  <div class="modal-footer">
                    <button
                      type="button"
                      class="btn btn-primary"
                      *ngIf="personalData.status === true"
                      (click)="addSuperannuation2()"
                      [disabled]="firstSuperannuationForm.invalid"
                      data-bs-dismiss="modal"
                    >
                      Add
                    </button>
                  </div>
                </div>
                <button
                  type="button"
                  class="btn btn-link mt-3"
                  (click)="addAnotherSuperannuation()"
                  *ngIf="!hasSGCContribution()"
                  [disabled]="!hasSGCContribution()"
                >
                  + Add Another Superannuation
                </button>
              </form>

              <!-- Additional Superannuation Forms -->
              <!-- <div *ngFor="let superannuation of newSuperannuations; let i = index">
                <hr style="border: 1px solid black; " />
                <form #additionalSuperannuationForm="ngForm">
                    <div class="form-group" style="position: relative">
                      <div class="form-actions-inline">
                        <button
                          type="button"
                          class="cancel-icon-button"
                          (click)="removeSuperannuation(i)"
                          title="Cancel"
                        >
                          <i class="fa fa-times-circle"></i>
                        </button>
                      </div>
                    </div>
                    
                  <h5 class="mb-3"><b>Additional Contribution {{ i + 2 }}</b></h5>

                  <div class="mb-3 mt-6">
                    <label for="contributionType{{ i }}" class="form-label">Contribution Type</label>
                    <select id="contributionType{{ i }}" class="form-select"
                      [(ngModel)]="newSuperannuation.contributionType" [name]="'contributionType'" required>
                      <option value="" disabled selected>Select Contribution Type</option>
                      <option value="salarySacrificed">Salary Sacrificed</option>
                      <option value="employerContribution">Employer Contribution</option>
                    </select>
                    
                    <div *ngIf="additionalSuperannuationForm.controls['contributionType' + i]?.invalid &&
                                additionalSuperannuationForm.controls['contributionType' + i]?.touched" 
                         class="text-danger">
                      Contribution Type is required.
                    </div>
                  </div>
                  

                  <div class="mb-3">
                    <label for="superannuationType{{ i }}" class="form-label">Superannuation Fund</label>
                    <select id="superannuationType{{ i }}" class="form-select"
                    [(ngModel)]="selectedSuperannuationType" [name]="'superannuationType' + i" (ngModelChange)="onSuperannuationTypeChange($event)" required>
                      <option *ngFor="let type of superannuations" [value]="type.fundName">
                        {{ type.fundName }} : {{ type.productName }}
                      </option>
                    </select>
                    <div *ngIf="additionalSuperannuationForm.controls['superannuationType' + i]?.invalid &&
                        additionalSuperannuationForm.controls['superannuationType' + i]?.touched" class="text-danger">
                      Superannuation Fund is required.
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="usi{{ i }}" class="form-label">USI</label>
                    <input type="text" id="usi{{ i }}" class="form-control" [(ngModel)]="newSuperannuation.usi"
                      [name]="'usi' + i" readonly />
                  </div>

                  <div class="mb-3">
                    <label for="membershipNumber{{ i }}" class="form-label">Membership Number</label>
                    <input type="text" id="membershipNumber{{ i }}" class="form-control"
                      [(ngModel)]="newSuperannuation.membershipNumber" [name]="'membershipNumber' + i"
                      placeholder="Enter Membership Number" required />
                      <div *ngIf="additionalSuperannuationForm.controls['membershipNumber' + i]?.invalid &&
                          additionalSuperannuationForm.controls['membershipNumber' + i]?.touched" class="text-danger">
                        Membership Number is required.
                      </div>  
                  </div>

                  <div class="mb-3" *ngFor="let superannuation of newSuperannuations; let i = index">
                    <div class="salary-type-selector">
                      <label class="radio-label">
                        <input type="radio" [name]="'type' + i" value="Percentage" [(ngModel)]="superannuation.type"
                          style="margin-right: 12px;" required (change)="clearSuperannuationTypeChange(i)" />
                        Percentage
                      </label>
                  
                      <label class="radio-label">
                        <input type="radio" [name]="'type' + i" value="Amount" [(ngModel)]="superannuation.type"
                          style="margin-right: 12px;" required (change)="clearSuperannuationTypeChange(i)" />
                        Amount
                      </label>
                    </div>
                  
                    <label for="amount{{ i }}" class="form-label">
                      {{ superannuation.type }}
                    </label>
                  
                    <input type="number" id="amount{{ i }}" class="form-control" [(ngModel)]="superannuation.value"
                      [name]="'amount' + i" required (keydown)="restrictInput($event)" />
                    <div *ngIf="additionalSuperannuationForm.controls['amount' + i]?.invalid &&
                        additionalSuperannuationForm.controls['amount' + i]?.touched" class="text-danger">
                      Value is required.
                    </div>
                  </div>
                  

                  <div class="modal-footer">
                    <button type="button" class="btn btn-primary" *ngIf="personalData.status === true"
                      (click)="addSuperannuation2()" [disabled]="additionalSuperannuationForm.invalid" data-bs-dismiss="modal">
                      Add
                    </button>
                  </div>
               </form>
              </div>  -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pay_Template -->

    <div class="Earnings" *ngIf="isActiveSide('Pay_Template')">
      <!-- <div class="pay_item_head">
        <h2>Pay Template</h2>
      </div> -->
      <!-- Earnings Container -->

      <div class="earnings-container mt-3">
        <div class="pay_item_head">
          <h2>Earnings</h2>
        </div>
        <div class="table-responsive-wrapper">
        <table class="table no-borders mt-3">
          <thead>
            <tr>
              <th>Salary Type</th>
              <th *ngIf="employmentData.salaryType === 'Annual Salary'">Annual Salary</th>
              <th *ngIf="employmentData.salaryType === 'Hourly Rate'">Hourly Rate</th>
              <th *ngIf="employmentData.salaryType === 'Annual Salary'">Hours per Week</th>
              <th *ngIf="employmentData.salaryType === 'Annual Salary'">Ordinary Earnings Rate</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>{{ employmentData.salaryType }}</td>
              <td *ngIf="employmentData.salaryType === 'Annual Salary'">{{ employmentData.amount }}</td>
              <td *ngIf="employmentData.salaryType === 'Hourly Rate'">{{ employmentData.ordinaryEarningsRate }}</td>
              <td *ngIf="employmentData.salaryType === 'Annual Salary'">{{ employmentData.hoursperWeek }}</td>
              <td *ngIf="employmentData.salaryType === 'Annual Salary'">{{ employmentData.ordinaryEarningsRate }}</td>
            </tr>
          </tbody>
        </table> 
        </div>
        
        <div class="table-responsive-custom">
        <table class="table no-borders mt-3">
          <thead>
            <tr>
              <th>Earning Type</th>
              <!-- <th>Hours</th> -->
              <th>Rate</th>
              <th>Amount</th>
              <th>Actions</th>
              <!-- <th>Total</th> -->
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let earning of payTemplateEarnings; let i = index">
              <td>{{ earning.earning?.earningsName }}</td>
              <!-- <td>{{ earning.hours || "0.00" }}</td> -->
              <td>{{ earning.rate || "0.00" }}</td>
              <td>{{ earning.amount || "0.00" }}</td>
              <td>
                <button
                  class="btn btn-danger btn-sm"
                  (click)="deleteEarning(earning.employeeEarningId)"
                  style="
                    margin-left: 10px;
                    border: none;
                    background: none;
                    padding-left: 10px;
                    font-size: 1.2rem;
                  "
                  title="Delete"
                >
                  <i
                    class="ri-delete-bin-line"
                    style="
                      color: #ff0000;
                      text-shadow: 2px 2px 4px rgba(205, 117, 70, 0.6);
                    "
                  ></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        </div>

        <div class="earning-total">
          <button
            class="btn-add-earning"
            (click)="openEarningModal()"
            data-bs-toggle="modal"
            data-bs-target="#modal-two"
          >
            + Add Earnings
          </button>
          <div class="value">Total</div>
          <span>{{ totalEarningAmount | currency }}</span>
        </div>
      </div>

      <!-- Modal for Adding Earnings -->
      <div
        class="modal fade"
        id="modal-two"
        tabindex="-1"
        aria-labelledby="earningModalLabel"
        aria-hidden="true"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="earningModalLabel">Add Earning</h5>
              <button
                type="button"
                class="custom-close-btn"
                data-bs-dismiss="modal"
                aria-label="Close"
                (click)="closeEarningModal()"
              >
                <i class="bi bi-x-circle"></i>
              </button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <div class="mb-3">
                  <label for="Category" class="form-label">Category</label>
                  <select
                    name="earningsCategory"
                    class="form-control"
                    id="Category"
                    [(ngModel)]="earning.category"
                    (change)="onCategoryChange()"
                    required
                    #earningsCategory="ngModel"
                  >
                    <option value="" disabled selected>
                      Select a earning Category
                    </option>
                    <option *ngFor="let type of earningCategory" [value]="type">
                      {{ type }}
                    </option>
                  </select>
                  <div
                    *ngIf="earningsCategory.invalid && earningsCategory.touched"
                    class="text-danger"
                  >
                    Category is required.
                  </div>
                </div>

                <label for="earningType" class="form-label">Earning Type</label>
                <select
                  id="earningType"
                  class="form-select"
                  [(ngModel)]="selectedEarningsName"
                  (ngModelChange)="onEarningTypeChange($event)"
                >
                  <option
                    *ngFor="let earning of filteredEarnings"
                    [value]="earning.earningsName"
                  >
                    {{ earning.earningsName }}
                  </option>
                </select>
              </div>
              <div class="mb-3" *ngIf="newEarning.typeOfUnits === 'Rate'">
                <label for="earningRate" class="form-label">Rate</label>
                <input
                  type="number"
                  id="earningRate"
                  class="form-control"
                  [(ngModel)]="newEarning.rate"
                  placeholder="0.00"
                  (keydown)="restrictInput($event)"
                />
              </div>
              <div class="mb-3" *ngIf="newEarning.typeOfUnits === 'Fixed Amount'">
                <label for="earningAmount" class="form-label">Amount</label>
                <input
                  type="number"
                  id="earningAmount"
                  class="form-control"
                  [(ngModel)]="newEarning.amount"
                  placeholder="0.00"
                  (keydown)="restrictInput($event)"
                />
              </div>
            </div>
            <div class="modal-footer">
              <button
                type="button"
                *ngIf="personalData.status === true"
                class="btn btn-primary"
                (click)="addEarning()"
                data-bs-dismiss="modal"
                [disabled]="
                  !selectedEarningsName ||
                  (!newEarning.amount && !newEarning.rate) || 
                  (newEarning.amount !== 0.0 && newEarning.amount <= 0) || 
                  (newEarning.rate !== 0.0 && newEarning.rate <= 0)
                "
              >
                Add
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Deductions Container -->
      <div class="earnings-container mt-3">
        <div class="pay_item_head">
          <h2 style="color: red">Deductions</h2>
        </div>
        <div class="table-responsive-custom">
        <table class="table no-borders">
          <thead>
            <tr>
              <th>Deduction Type</th>
              <th>Amount</th>
              <th>Actions</th>
              <!-- <th>Total</th> -->
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let deduction of payTemplateDeductions; let i = index">
              <td>{{ deduction.deduction?.deductionName }}</td>
              <td>{{ deduction.amount || "0.00" }}</td>
              <td>
                <button
                  class="btn btn-danger btn-sm"
                  (click)="deleteDeduction(deduction.employeeDeductionId)"
                  style="
                    margin-left: 10px;
                    border: none;
                    background: none;
                    padding-left: 10px;
                    font-size: 1.2rem;
                  "
                  title="Delete"
                >
                  <i
                    class="ri-delete-bin-line"
                    style="
                      color: #ff0000;
                      text-shadow: 2px 2px 4px rgba(205, 117, 70, 0.6);
                    "
                  ></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        </div>
        <div class="earning-total">
          <button
            class="btn-add-earning"
            (click)="openDeductionModal()"
            data-bs-toggle="modal"
            data-bs-target="#modal-deduction"
          >
            + Add Deduction
          </button>
          <div class="value">Total</div>
          <span>{{ totalDeductionAmount | currency }}</span>
        </div>
      </div>

      <!-- Modal for Adding Deduction -->
      <div
        class="modal fade"
        id="modal-deduction"
        tabindex="-1"
        aria-labelledby="deductionModalLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="deductionModalLabel">
                Add Deduction
              </h5>
              <button
                type="button"
                class="custom-close-btn"
                data-bs-dismiss="modal"
                aria-label="Close"
                (click)="closeDeductionModal()"
              >
                <i class="bi bi-x-circle"></i>
              </button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label for="deductionType" class="form-label"
                  >Deduction Type</label
                >
                <select
                  id="deductionType"
                  class="form-select"
                  [(ngModel)]="selectedDeductionsName"
                  (ngModelChange)="onDeductionTypeChange($event)"
                >
                  <option
                    *ngFor="let type of deductions"
                    [value]="type.deductionName"
                  >
                    {{ type.deductionName }}
                  </option>
                </select>
              </div>
              <div class="mb-3">
                <label for="deductionAmount" class="form-label">Amount</label>
                <input
                  type="number"
                  id="deductionAmount"
                  class="form-control"
                  [(ngModel)]="newDeduction.amount"
                  placeholder="0.00"
                  (keydown)="restrictInput($event)"
                />
              </div>
            </div>
            <div class="modal-footer">
              <button
                type="button"
                *ngIf="personalData.status === true"
                class="btn btn-primary"
                (click)="addDeduction()"
                data-bs-dismiss="modal"
                [disabled]="
                  !selectedDeductionsName ||
                  !newDeduction.amount ||
                  newDeduction.amount <= 0
                "
              >
                Add
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="emp-open-footer-wrapper">
        <div class="right-buttons">
          <button
            type="button"
            class="btn btn-primary"
            [class.active]="isActiveSide('Bank_Accounts')"
            (click)="setActiveSide('Bank_Accounts'); $event.preventDefault()"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
