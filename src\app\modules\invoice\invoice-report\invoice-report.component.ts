import { Component, ElementRef, ViewChild } from '@angular/core';
import { InvoiceService } from '../invoice.service';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { BusinessPartner } from '../../business-partner/business-partner';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { InvoiceHead } from '../invoice';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';
import * as bootstrap from 'bootstrap';

@Component({
  selector: 'app-invoice-report',
  templateUrl: './invoice-report.component.html',
  styleUrls: ['./invoice-report.component.css']
})
export class InvoiceReportComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  fromDate: string = '';
  toDate: string = '';
  status: string = '%';
  typef: string = 'pdf';
  reportType: string = '';
  overdueOption: string = '';
  overdueFromDate: string = '';
  overdueToDate: string = '';
  minAmount: number = 0;
  maxAmount: number = 0;
  reference: string = '';
  isLoading = false;
  customers: BusinessPartner[] = [];
  invoiceHead: InvoiceHead = new InvoiceHead();
  getAllCustomers = false;
  base64String: any;


  @ViewChild('invoiceListPreviewFrame') invoiceListPreviewFrame!: ElementRef;
 

  constructor(private invoiceService: InvoiceService, private router: Router, public sanitizer: DomSanitizer,  private swalAlerts: SwalAlertsService , private businessPartnerService: BusinessPartnerService) { }

  
  ngOnInit() {
     this.invoiceHead.businessPartnerId = '0';
    this.loadCustomers();
  }


  
  previewInvoiceList(fromDate: string, toDate: string, status: string, businessPartnerId: any) {
  if (!fromDate || !toDate) {

    this.swalAlerts.showSwalWarning('Warning!', 'Please select Date Range', 'Missing date range for Invoice report.');
    return;
  }
  this.isLoading = true;
  const entityId = +localStorage.getItem('entityId')!;
  const entityUUID = localStorage.getItem('entityUuid')!;
  const bpId = (businessPartnerId === 0 || businessPartnerId === '' || businessPartnerId == null) ? null : +businessPartnerId;

   // Only send minAmount and maxAmount if status is 'overdue'
    let minAmount = 0;
    let maxAmount = 0;

    
    if (status === 'overdue') {
      // Handle different overdue options selected by the user
      if (this.overdueOption === 'amounts' || this.overdueOption === 'both') {
        minAmount = this.minAmount || 0;
        maxAmount = this.maxAmount || 0;
      }
    }

  const requestData = {
    fromDate,
    toDate,
    status,
    entityId,
    businessPartnerId: bpId,
    minAmount,
    maxAmount,
    entityUUID
  };

  this.invoiceService.getInvoiceListReport(requestData).subscribe(
    data => {
      const base64String = data.response;
      if (base64String) {
        this.loadPdfIntoIframe(base64String);
      } else {
        this.isLoading = false;
        alert('No Invoice data for preview.');
      }
    },
    error => {
      this.isLoading = false;
      alert('Error loading Invoice preview.');
    }
  );
}

  
// The download report function now uses the stored base64String
downloadReport(base64String: string, fileName: string, reportType: string) {
  const link = document.createElement('a');
  let source;

  if (reportType === 'pdf') {
    source = `data:application/pdf;base64,${base64String}`;
  } else if (reportType === 'excel') {
    source = `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${base64String}`;
  } else {
    return;
  }

  link.href = source;
  link.download = `${fileName}.${reportType === 'pdf' ? 'pdf' : 'xlsx'}`;
  link.click();
}

downloadAs(reportType: 'pdf' | 'excel') {
  if (!this.base64String) {
    Swal.fire({
      title: 'Error',
      text: 'No data available for download.',
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#be0032',
    });
    return;
  }

  const fileName = 'InvoiceListReport';
  this.downloadReport(this.base64String, fileName, reportType);
}


    private loadPdfIntoIframe(base64String: string) {
      if (base64String && base64String.trim().length >= 50) {
        const pdfData = 'data:application/pdf;base64,' + base64String;
        const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
        const iframe = this.invoiceListPreviewFrame.nativeElement;
    
        iframe.onload = () => {
          this.isLoading = false;
        };
    
        iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
    
        // Open modal manually using Bootstrap JS
        const modalElement = document.getElementById('simpleModal');
        const modal = new bootstrap.Modal(modalElement!);
        modal.show();
      } else {
         this.isLoading = false;
         this.swalAlerts.showSwalWarning('No Data', 'No Invoice data for preview.', 'No Invoice data was returned for the selected range.');
    
      }
    }
    
    
 loadCustomers() {
  const entityId = +((localStorage.getItem('entityId')) + '');
  this.businessPartnerService.getCustomerListByEntity(entityId).subscribe(
    (customers: BusinessPartner[]) => {
      this.customers = customers;
    },
    (error: HttpErrorResponse) => {
      console.error('Error fetching customers', error);

      //  Use SwalAlertsService for error with Chimp support
      this.swalAlerts.showErrorWithChimpSupport(
        'Failed to load customers.',
        'Unable to fetch customer list for this entity. Please check if the customer service is responding.'
      );
    }
  );
}



  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(cust => cust.businessPartnerId === +selectedCustomerId);

    // Set the reference field
    this.invoiceHead.reference = selectedCustomer?.bpName || '';
  }


  // Method to toggle all customers' data
  toggleAllCustomers() {
    this.getAllCustomers = !this.getAllCustomers;
    if (this.getAllCustomers) {
      this.invoiceHead.businessPartnerId = '';  // Clear selected customer when showing all
    }
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}
