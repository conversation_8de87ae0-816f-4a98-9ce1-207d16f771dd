import { Component, OnInit, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { BotService } from '../bot.service';


interface ChatMessage {
  sender: string;
  text: string;
}

@Component({
  selector: 'app-chat-assistant',
  templateUrl: './chat-assistant.component.html',
  styleUrls: ['./chat-assistant.component.css']
})
export class ChatAssistantComponent implements OnInit, AfterViewChecked {
  @ViewChild('conversationContainer') conversationContainer!: ElementRef;

  showPopup = true;
  isChatVisible = false;
  conversation: ChatMessage[] = [];
  input: string = '';
  response: string = '';
  isWaitingForResponse = false;
  private audio!: HTMLAudioElement;

  constructor(private botService: BotService) {} 
  
  ngOnInit(): void {
    setTimeout(() => {
      this.showPopup = false;
    }, 10000);
  }
  

  sendData(): void {
    if (this.input && this.input.trim() && !this.isWaitingForResponse) {
      this.conversation.push({ sender: 'User', text: this.input });
      this.isWaitingForResponse = true;   
      const params = { prompt: this.input };
      this.botService.request('GET', '/chat-assistant', null, params).subscribe(
        (response_assistant: any) => {
          this.conversation.push({ sender: 'Assistant', text: response_assistant });
          this.playLoadingSound();
          this.response = response_assistant;
          this.isWaitingForResponse = false;
          this.stopLoadingSound()   
        },
        error => {
          console.error('Error:', error);
          const errorMessage = 'ChatBot is not available!';
          this.response = errorMessage;
          this.conversation.push({ sender: 'Assistant', text: errorMessage });
          this.isWaitingForResponse = false;
          this.stopLoadingSound()   
        }
      );
      this.input = ''; 
      this.scrollToBottom(); 
    }
  }

  closePopup(): void {
    this.showPopup = false;
  }

  closePopup_2(): void {
    this.isChatVisible = false;
  }

  toggleChat(): void {
    this.isChatVisible = !this.isChatVisible;
    this.showPopup = false;
  }

  scrollToBottom(): void {
    try {
      this.conversationContainer.nativeElement.scrollTop = this.conversationContainer.nativeElement.scrollHeight;
    } catch (err) {}
  }

  ngAfterViewChecked(): void {
    this.scrollToBottom();
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }
}
