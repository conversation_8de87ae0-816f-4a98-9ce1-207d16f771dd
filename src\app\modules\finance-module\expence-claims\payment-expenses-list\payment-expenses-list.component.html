<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <!-- Heading And Button -->
  <div class="actions">
    <h1>Expenses Payments</h1>
  </div>

 
  <!-- Tabs -->
  <div>
  <ul class="nav nav-tabs mb-3 justify-content-start">
    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'all'"
        (click)="setActiveTab('all')"
        >All</a
      >
    </li>
    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'pending'"
        (click)="setActiveTab('pending')"
        >Pending</a
      >
    </li>
    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'paid'"
        (click)="setActiveTab('paid')"
        >Paid</a
      >
    </li>
    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'overdue'"
        (click)="setActiveTab('overdue')"
        >Overdue</a
      >
    </li>
  </ul>
</div>

  <div class="Card">
    <!-- Filter and Search Section -->
    <div class="row1">
      <!-- Input for number, reference -->
      <div class="row1_col1">
        <label for="search-input">Payment Expenses Number</label>
        <div class="input-container">
          <input
            type="text"
            class="search-input"
            id="search-input"
            [(ngModel)]="searchTerm"
          />
          <i class="bi bi-search"></i>
        </div>
      </div>

      <div class="row1_col3">
        <label for="StartDate">Start Date</label>
        <input
          type="date"
          class="date-picker"
          id="StartDate"
          [(ngModel)]="startDate"
        />
      </div>

      <div class="row1_col4">
        <label for="EndDate">End Date</label>
        <input
          type="date"
          class="date-picker"
          id="EndDate"
          [(ngModel)]="endDate"
        />
      </div>
    </div>

    <div class="row2">
      <div class="row2_col3">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>
      <div class="row2_col1">
        <button type="button" class="primary-button" (click)="filterInvoices()">
          Search
        </button>
      </div>
    </div>
  </div>

  <div class="table-responsive">
    <table>
      <thead>
        <tr class="table-head">
          <th scope="col" class="valueCheckbox">
            <input
              type="checkbox"
              [checked]="isAllSelected"
              (change)="selectAll($event)"
            />
          </th>
          <th scope="col" class="valuehead">Payment Expenses Number</th>
          <th scope="col" class="valuehead">Date</th>
          <th scope="col" class="valuehead">Expenses Numbers</th>
          <th style="text-align: center" scope="col" class="valuehead">
            Total Paid Amount
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let invoice of filteredInvoices"
         
        >
          <td class="valueCheckbox">
            <input
              type="checkbox"
              [checked]="selectedInvoices.has(invoice)"
              (change)="toggleSelection(invoice, $event)"
            />
          </td>
          <td class="value">{{ invoice.paymentExpensesNumber }}</td>
          <td class="value">{{ invoice.documentDate | date : "dd-MM-yyyy" }}</td>
          <td class="value">{{ invoice.expensesNumbers }}</td>
          <td style="text-align: right" class="value">
            {{ invoice.totalPaidAmount | currency }}
          </td>
          
        </tr>
      </tbody>
    </table>
  </div>
</div>
