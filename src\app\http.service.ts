import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class HttpService {
  private readonly baseURL = environment.apiUrl;
  private readonly baseURLSales = environment.salesApiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.sessionStorage.getItem('auth_token');
  }

  setAuthToken(token: string | null): void {
    if (token !== null) {
      
      window.sessionStorage.setItem('auth_token', token);
    } else {
      
      window.sessionStorage.removeItem('auth_token');
    }
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    const authToken = this.getAuthToken();

  if (authToken) {
    headers = headers.set('Authorization', 'Bearer ' + authToken);
  } else {
    // Add secure API key for protected-but-public endpoints
    headers = headers.set('X-API-KEY', environment.secureApiKey);
  }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  requestSales(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURLSales + url, options);
      case 'POST':
        return this.http.post(this.baseURLSales + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURLSales + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURLSales + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }
}
