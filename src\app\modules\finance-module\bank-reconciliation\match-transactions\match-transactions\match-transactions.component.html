<!-- <app-finance-navbar></app-finance-navbar> -->
<!-- <app-bot-controller></app-bot-controller> -->
<!-- <app-chat-response></app-chat-response> -->
<div class="container pt-5 px-5 position-relative">
  <button
    class="btn btn-sm fs-6 bg-transparent position-absolute"
    style="right: 5px; top: 5px;"
    (click)="dialogRef.close()"
  >
    <i class="fa fa-times"></i>
  </button>
  <div
    class="col-12 d-flex justify-content-between align-items-center py-2"
  >
    <h1 class="fs-3 fw-bold">Matched Transactions</h1>
    <button
      class="primary-button"
      *ngIf="filteredMatchedDetails.length > 0 && pendingTransactionCount == 0"
      (click)="reconsileAll()"
    >
      Reconsile All
    </button>
  </div>
  <div class="col-12 py-3">
    <div class="row">
      <div class="col-6">
        <label for="searchTerm">Statement Details / Document Details</label>
        <input
          type="text"
          id="searchTerm"
          class="w-100 custom-input"
          [(ngModel)]="searchFilter.searchTerm"
        />
      </div>
      <div class="col-3 d-grid">
        <label for="transactionType">Transaction Type</label>
        <select
          class="custom-select"
          id="transactionType"
          [(ngModel)]="searchFilter.transactionTypeTerm"
        >
          <option value="All">All</option>
          <option value="CREDIT">Credit</option>
          <option value="DEBIT">Debit</option>
        </select>
      </div>
      <div class="col-3 d-grid">
        <label for="documentType">Document Type</label>
        <select
          class="custom-select"
          id="documentType"
          [(ngModel)]="searchFilter.documentTypeTerm"
        >
          <option value="All">All</option>
          <option *ngFor="let docType of documentTypes" [value]="docType.label">
            {{ docType.label }}
          </option>
        </select>
      </div>
    </div>
  </div>
  <div class="col-12 pb-0 pt-3" *ngIf="filteredMatchedDetails.length > 0">
    <table class="matched-transaction-table">
      <colgroup>
        <col width="10%" />
        <col width="18%" />
        <col width="27%" />
        <col width="10%" />
        <col width="10%" />
        <col width="15%" />
        <col width="10%" />
      </colgroup>
      <thead>
        <tr class="bg-secondary text-white">
          <th colspan="2">Statement Line</th>
          <th colspan="4">Matched Transaction</th>
          <th></th>
        </tr>
        <tr class="bg-secondary-light">
          <th>Date</th>
          <th>Description</th>
          <th>Description</th>
          <th class="text-center">Account</th>
          <th class="text-center">Tax</th>
          <th class="text-end">Amount</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let detail of filteredMatchedDetails">
          <tr *ngFor="let doc of detail.documentDetailList; let i = index">
            <!-- Only render these columns once using rowspan -->
            <td
              *ngIf="i === 0"
              [attr.rowspan]="detail.documentDetailList.length"
              class="text-black-50"
            >
              {{ detail.recordDate | date : "dd MMM yyyy" }}
            </td>
            <td
              *ngIf="i === 0"
              [attr.rowspan]="detail.documentDetailList.length"
            >
              <div>
                <div class="d-flex">
                  <span
                    class="mini-transaction-id-label"
                    [ngClass]="{
                      'bg-success': detail.transactionType === 'CREDIT',
                      'bg-danger': detail.transactionType === 'DEBIT'
                    }"
                    style="width: fit-content"
                  >
                    {{ detail.transactionId?.id }} </span
                  >&nbsp;
                  <span
                    *ngIf="detail.bankRecMatchMode"
                    class="mini-transaction-id-label"
                    >{{
                      detail.bankRecMatchMode.toString()
                        | replaceUnderscoreWithSpace
                    }}</span
                  >
                </div>
                <p class="mt-1" style="font-size: 14px">
                  {{ detail.transactionId?.description }}
                </p>
              </div>
            </td>

            <!-- These are repeated per document detail -->
            <td>
              <p>
                <span
                  *ngIf="detail.bankRecDocumentType"
                  [ngClass]="{
                    'receipt-mini-label':
                      detail.bankRecDocumentType.toString() ===
                      'PAYMENT_RECEIPT',
                    'invoice-mini-label':
                      detail.bankRecDocumentType.toString() === 'INVOICE',
                    'bill-mini-label':
                      detail.bankRecDocumentType.toString() === 'BILL',
                    'voucher-mini-label':
                      detail.bankRecDocumentType.toString() ===
                      'PAYMENT_VOUCHER',
                    'gl-income-mini-label':
                      detail.bankRecDocumentType.toString() === 'GL_INCOME',
                    'gl-expense-mini-label':
                      detail.bankRecDocumentType.toString() === 'GL_EXPENSE',
                    'bank-transfer-mini-label':
                      detail.bankRecDocumentType.toString() === 'BANK_TRANSFER',
                    'bank-rule-mini-label':
                      detail.bankRecDocumentType.toString() === 'BANK_RULE'
                  }"
                  >{{
                    detail.bankRecDocumentType.toString()
                      | replaceUnderscoreWithSpace
                  }}</span
                >
                <span *ngIf="doc.documentId">
                  -
                  <span class="mini-transaction-id-label">{{
                    doc.documentId
                  }}</span>
                </span>
              </p>
              <p class="text-dark mt-1" style="font-size: 14px">
                {{ doc.transactionDescription || "N/A" }}
              </p>
            </td>
            <td class="text-center">
              <p>{{ doc.glAccountId }}</p>
            </td>
            <td class="text-center">
              <p>
                {{
                  doc.gstApplicability == null
                    ? "-"
                    : doc.gstApplicability
                    ? "Applied"
                    : "Not Applied"
                }}
              </p>
            </td>
            <td class="text-end">{{ doc.transactionAmount | currency }}</td>
            <td
              class="text-center"
              *ngIf="i === 0"
              [attr.rowspan]="detail.documentDetailList.length"
            >
              <button
                *ngIf="detail.groupId"
                class="btn btn-sm mini-debit-label"
                (click)="undoMatching(detail.groupId)"
              >
                Undo
              </button>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
  <div
    class="col-12 pb-0 pt-3"
    *ngIf="!filteredMatchedDetails || filteredMatchedDetails.length == 0"
  >
    <div class="row">
      <div class="col-8 py-5 mx-auto g-0 d-inline-flex">
        <div class="col-4 text-center my-auto">
          <img
            src="assets/images/no-data-bank-rec.svg"
            alt="data fetching image"
            style="height: 180px"
          />
        </div>
        <div class="col-8 text-start ps-4 my-auto" style="line-height: 20px">
          <h5 style="font-weight: 600">
            No Transactions Available for Reconciliation
          </h5>
          <p class="text-black-50" style="font-size: 14px">
            There are no transactions to reconcile at the moment. Please check
            if your bank account is connected correctly, or try adjusting the
            selected date range to view available transactions.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
