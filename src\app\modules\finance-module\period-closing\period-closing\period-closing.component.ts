import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { PeriodClosingService } from '../period-closing.service';
import { PeriodClosingHead } from '../period-closing';
import Swal from 'sweetalert2';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-period-closing',
  templateUrl: './period-closing.component.html',
  styleUrls: ['./period-closing.component.css']
})
export class PeriodClosingComponent implements OnInit {
  constructor(
      private periodClosingService: PeriodClosingService,
      private router: Router
    ) {}

    periodClosingHead: PeriodClosingHead = new PeriodClosingHead();


  /**ngOnInit(): void {
  const entityId = +(localStorage.getItem('entityId') || '0');

  this.periodClosingService.getLastClosedPeriod(entityId).subscribe({
    next: (lastPeriod) => {
        console.log('Last Closed Period:', lastPeriod);
      if (lastPeriod?.toDate) {
        const nextFromDate = this.addOneDay(lastPeriod.toDate);
        this.periodClosingHead.fromDate = nextFromDate;
        this.periodClosingHead.toDate = this.getTodayDate(); // You can modify this to month-end, etc.
      } else {
        // If no last period exists, maybe this is first close
        const today = this.getTodayDate();
        this.periodClosingHead.fromDate = today;
        this.periodClosingHead.toDate = today;
      }
    },
    error: (err) => {
      console.error('Failed to fetch last closed period:', err);
    },
  });
}

getTodayDate(): string {
  return new Date().toISOString().split('T')[0]; // yyyy-MM-dd
}

addOneDay(dateStr: string): string {
  const date = new Date(dateStr);
  date.setDate(date.getDate() + 1);
  return date.toISOString().split('T')[0];
}

    savePeriodClosing(): void {
      this.periodClosingHead.entityId = +(localStorage.getItem('entityId') + '');
      this.periodClosingHead.userId = +(localStorage.getItem('userid') + '');
    
      this.periodClosingService.addPeriodClosing(this.periodClosingHead).subscribe(
        (response) => {
          Swal.fire({
            title: 'Success!',
            text: 'Period closing successfully.',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then(() => {
            this.router.navigate(['/payable-bill-list']);
          });
        },
        (error: HttpErrorResponse) => {
          console.error('Error period closing', error);
          Swal.fire({
            title: 'Error!',
            text: 'Error period closing.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#be0032',
          });
        }
      );
    }**/


ngOnInit(): void {
  const entityId = +(localStorage.getItem('entityId') || '0');
  const today = new Date();

  this.periodClosingService.getLastClosedPeriod(entityId).subscribe({
    next: (lastPeriod) => {
      console.log('Last Closed Period:', lastPeriod);

      let fromYear: number;
      let toYear: number;

      if (lastPeriod?.toDate) {
        // Closed before → set fromDate as next day after last toDate
        const lastToDate = new Date(lastPeriod.toDate);
        const nextFromDate = new Date(lastToDate);
        nextFromDate.setDate(nextFromDate.getDate() + 1);

        fromYear = nextFromDate.getFullYear();
        if (nextFromDate.getMonth() < 6) {
          // Before July → still part of previous fiscal year
          fromYear -= 1;
        }
        toYear = fromYear + 1;

        this.periodClosingHead.fromDate = new Date(`${fromYear}-07-01`).toISOString().split('T')[0];
        this.periodClosingHead.toDate = new Date(`${toYear}-06-30`).toISOString().split('T')[0];
      } else {
        // First-time close: infer fiscal year
        const currentYear = today.getFullYear();
        const currentMonth = today.getMonth(); // 0 = Jan, 6 = July

        if (currentMonth >= 6) {
          fromYear = currentYear;
          toYear = currentYear + 1;
        } else {
          fromYear = currentYear - 1;
          toYear = currentYear;
        }

        this.periodClosingHead.fromDate = new Date(`${fromYear}-07-01`).toISOString().split('T')[0];
        this.periodClosingHead.toDate = new Date(`${toYear}-06-30`).toISOString().split('T')[0];
      }

      // Disallow early closing
      const toDate = new Date(this.periodClosingHead.toDate);
      if (today < toDate) {
        Swal.fire({
          title: 'Period Closing Not Allowed',
          text: `You can only close the fiscal year after ${toDate.toDateString()}.`,
          icon: 'warning',
          confirmButtonText: 'OK',
          confirmButtonColor: '#ff7e5f',
        });
      }
    },
    error: (err) => {
      console.error('Failed to fetch last closed period:', err);
    }
  });
}



savePeriodClosing(): void {
  const toDate = new Date(this.periodClosingHead.toDate);
  const today = new Date();

  if (today < toDate) {
    Swal.fire({
      title: 'Too Early to Close',
      text: `You can only close the fiscal year after ${toDate.toDateString()}.`,
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ff7e5f',
    });
    return;
  }

  this.periodClosingHead.entityId = +(localStorage.getItem('entityId') || '0');
  this.periodClosingHead.userId = +(localStorage.getItem('userid') || '0');

  this.periodClosingService.addPeriodClosing(this.periodClosingHead).subscribe(
    () => {
      Swal.fire({
        title: 'Success!',
        text: 'Period closed successfully.',
        icon: 'success',
        confirmButtonText: 'OK',
        confirmButtonColor: '#28a745',
      }).then(() => {
        this.router.navigate(['/payable-bill-list']);
      });
    },
    (error: HttpErrorResponse) => {
      console.error('Error period closing', error);
      Swal.fire({
        title: 'Error!',
        text: 'Error while closing the period.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      });
    }
  );
}

    
}
