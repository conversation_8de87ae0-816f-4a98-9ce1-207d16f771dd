import { Component, OnInit, ViewChild } from '@angular/core';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { PaymentExpensesHead } from '../expence-claims';
import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';
import { Entity, EntityTradingName } from 'src/app/modules/entity/entity';
import { ExpenceClaimsService } from '../expence-claims.service';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { ActivatedRoute, Router } from '@angular/router';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-payment-expenses-list',
  templateUrl: './payment-expenses-list.component.html',
  styleUrls: ['./payment-expenses-list.component.css']
})
export class PaymentExpensesListComponent implements OnInit {

  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  activeTab = 'all';
  currentPage = 1;
  pageSize = 10;
  invoiceHeadList: PaymentExpensesHead[] = [];
  selectedInvoices: Set<PaymentExpensesHead> = new Set<PaymentExpensesHead>();
  isAllSelected: boolean = false;
  searchTerm: string = ''; // Store the search term
  filteredInvoices: PaymentExpensesHead[] = []; 
  startDate: string | null = null; // Bind to the start date input
  endDate: string | null = null;   // Bind to the end date input
  entity: Entity = new Entity();
  invoices: any[] = [];
  templateHtmlContent: string = '';
  recipientEmail: string = ''; // Loaded from the database
  TempRecipientEmail: string =''; // User-entered email (optional)
  invoiceHead: PaymentExpensesHead = new PaymentExpensesHead();

  constructor(private invoiceService: ExpenceClaimsService, private entityService: EntityService,  private route: ActivatedRoute, private router: Router, public sanitizer: DomSanitizer) {
  }

  ngOnInit(): void {
 
    this.getInvoiceHeadListByEntity();
    
  }

    
  

  private getInvoiceHeadListByEntity() {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.invoiceService.getAllPaymentExpensesHeadList(entityId).subscribe(data => {
      this.invoiceHeadList = data;
      this.filterInvoices(); // Filter invoices initially
    });
  }


  // Function to filter invoices based on the search term and active tab
  filterInvoices() {
    const searchTermLower = this.searchTerm.toLowerCase().trim();
    let filtered = this.invoiceHeadList;

    

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(invoice =>
        invoice.paymentExpensesNumber.toString().toLowerCase().includes(searchTermLower) 
      );
    }

    // Filter by date range
    if (this.startDate) {
      const startDate = new Date(this.startDate);
      filtered = filtered.filter(invoice => new Date(invoice.documentDate) >= startDate);
    }

    if (this.endDate) {
      const endDate = new Date(this.endDate);
      filtered = filtered.filter(invoice => new Date(invoice.documentDate) <= endDate);
    }

    this.filteredInvoices = filtered;
  }

  // Reset filters
  resetFilters() {
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.activeTab = 'all'; // Reset the active tab to 'all'
    this.filteredInvoices = this.invoiceHeadList;
  }

  // Function to handle changes in the search input
  onSearchChange() {
    this.filterInvoices(); // Call filter function on input change
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.filterInvoices(); // Filter invoices when tab changes
  }


  toggleSelection(invoice: PaymentExpensesHead, event: any): void {
    if (event.target.checked) {
      this.selectedInvoices.add(invoice);
    } else {
      this.selectedInvoices.delete(invoice);
    }
  }

  selectAll(event: any): void {
    this.isAllSelected = event.target.checked;

    if (this.isAllSelected) {
      this.invoiceHeadList.forEach(invoice => {
        this.selectedInvoices.add(invoice);
      });
    } else {
      this.selectedInvoices.clear();
    }
  }

  clearSelectedCheckboxes() {
    // Deselect all checkboxes
    this.selectedInvoices.clear();
  }



}

