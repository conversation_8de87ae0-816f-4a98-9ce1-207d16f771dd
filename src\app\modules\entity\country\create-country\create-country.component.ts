import { Component, OnInit, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Country } from '../country';
import { CountryService } from '../country.service';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';

@Component({
  selector: 'app-create-country',
  templateUrl: './create-country.component.html',
  styleUrls: ['./create-country.component.css'],
})
export class CreateCountryComponent implements OnInit {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  countries: Country[] = [];
  country: Country = new Country();

  constructor(private countryService: CountryService, private router: Router) {}

  ngOnInit(): void {
    this.countries = this.getAllCountries();
  }

  onSubmit(form: NgForm): void {
    if (form.valid) {
      this.country.taxCategoryId = 1;
      this.countryService.createCountry(this.country).subscribe(
        () => {
          Swal.fire({
            title: 'Success!',
            text: 'Country created successfully.',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
          }).then((result) => {
            if (result.isConfirmed) {
              this.router.navigate(['/country']);
            }
          });
        },
        (error) => {
          console.error('Error creating country:', error);
          Swal.fire({
            title: 'Error!',
            text: 'Failed to create country.',
            icon: 'error',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
                confirmButtonColor: '#be0032',
                cancelButtonColor: '#007bff',
                showCancelButton: true, 
                }).then((result) => {
                  if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                    if (this.chatBotComponent) {
                      Swal.fire({
                        title: 'Processing...',
                        text: 'Please wait while Chimp processes your request.',
                        allowOutsideClick: false,
                        didOpen: () => {
                          Swal.showLoading();
                          this.chatBotComponent.setInputData('Failed to create country.');
                          this.chatBotComponent.responseReceived.subscribe(response => {
                            Swal.close();
                            this.chatResponseComponent.showPopup = true;
                            this.chatResponseComponent.responseData = response;
                            this.playLoadingSound();
                            this.stopLoadingSound() 
                          });
                        },
                      });
                    } else {
                      console.error('ChatBotComponent is not available.');
                    }
                  }
          });
        }
      );
    } else {
      Swal.fire({
        title: 'Warning!',
        text: 'Please fill in all required fields.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f',
      });
    }
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  private getAllCountries(): Country[] {
    return [
      new Country('Afghanistan'),
      new Country('Albania'),
      new Country('Algeria'),
      new Country('Andorra'),
      new Country('Angola'),
      new Country('Antigua and Barbuda'),
      new Country('Argentina'),
      new Country('Armenia'),
      new Country('Australia'),
      new Country('Austria'),
      new Country('Azerbaijan'),
      new Country('Bahamas'),
      new Country('Bahrain'),
      new Country('Bangladesh'),
      new Country('Barbados'),
      new Country('Belarus'),
      new Country('Belgium'),
      new Country('Belize'),
      new Country('Benin'),
      new Country('Bhutan'),
      new Country('Bolivia'),
      new Country('Bosnia and Herzegovina'),
      new Country('Botswana'),
      new Country('Brazil'),
      new Country('Brunei'),
      new Country('Bulgaria'),
      new Country('Burkina Faso'),
      new Country('Burundi'),
      new Country('Cabo Verde'),
      new Country('Cambodia'),
      new Country('Cameroon'),
      new Country('Canada'),
      new Country('Central African Republic'),
      new Country('Chad'),
      new Country('Chile'),
      new Country('China'),
      new Country('Colombia'),
      new Country('Comoros'),
      new Country('Congo (Congo-Brazzaville)'),
      new Country('Costa Rica'),
      new Country('Croatia'),
      new Country('Cuba'),
      new Country('Cyprus'),
      new Country('Czechia (Czech Republic)'),
      new Country('Democratic Republic of the Congo'),
      new Country('Denmark'),
      new Country('Djibouti'),
      new Country('Dominica'),
      new Country('Dominican Republic'),
      new Country('Ecuador'),
      new Country('Egypt'),
      new Country('El Salvador'),
      new Country('Equatorial Guinea'),
      new Country('Eritrea'),
      new Country('Estonia'),
      new Country('Eswatini (fmr. "Swaziland")'),
      new Country('Ethiopia'),
      new Country('Fiji'),
      new Country('Finland'),
      new Country('France'),
      new Country('Gabon'),
      new Country('Gambia'),
      new Country('Georgia'),
      new Country('Germany'),
      new Country('Ghana'),
      new Country('Greece'),
      new Country('Grenada'),
      new Country('Guatemala'),
      new Country('Guinea'),
      new Country('Guinea-Bissau'),
      new Country('Guyana'),
      new Country('Haiti'),
      new Country('Honduras'),
      new Country('Hungary'),
      new Country('Iceland'),
      new Country('India'),
      new Country('Indonesia'),
      new Country('Iran'),
      new Country('Iraq'),
      new Country('Ireland'),
      new Country('Israel'),
      new Country('Italy'),
      new Country('Ivory Coast'),
      new Country('Jamaica'),
      new Country('Japan'),
      new Country('Jordan'),
      new Country('Kazakhstan'),
      new Country('Kenya'),
      new Country('Kiribati'),
      new Country('Kuwait'),
      new Country('Kyrgyzstan'),
      new Country('Laos'),
      new Country('Latvia'),
      new Country('Lebanon'),
      new Country('Lesotho'),
      new Country('Liberia'),
      new Country('Libya'),
      new Country('Liechtenstein'),
      new Country('Lithuania'),
      new Country('Luxembourg'),
      new Country('Madagascar'),
      new Country('Malawi'),
      new Country('Malaysia'),
      new Country('Maldives'),
      new Country('Mali'),
      new Country('Malta'),
      new Country('Marshall Islands'),
      new Country('Mauritania'),
      new Country('Mauritius'),
      new Country('Mexico'),
      new Country('Micronesia'),
      new Country('Moldova'),
      new Country('Monaco'),
      new Country('Mongolia'),
      new Country('Montenegro'),
      new Country('Morocco'),
      new Country('Mozambique'),
      new Country('Myanmar (formerly Burma)'),
      new Country('Namibia'),
      new Country('Nauru'),
      new Country('Nepal'),
      new Country('Netherlands'),
      new Country('New Zealand'),
      new Country('Nicaragua'),
      new Country('Niger'),
      new Country('Nigeria'),
      new Country('North Korea'),
      new Country('North Macedonia'),
      new Country('Norway'),
      new Country('Oman'),
      new Country('Pakistan'),
      new Country('Palau'),
      new Country('Panama'),
      new Country('Papua New Guinea'),
      new Country('Paraguay'),
      new Country('Peru'),
      new Country('Philippines'),
      new Country('Poland'),
      new Country('Portugal'),
      new Country('Qatar'),
      new Country('Romania'),
      new Country('Russia'),
      new Country('Rwanda'),
      new Country('Saint Kitts and Nevis'),
      new Country('Saint Lucia'),
      new Country('Saint Vincent and the Grenadines'),
      new Country('Samoa'),
      new Country('San Marino'),
      new Country('Sao Tome and Principe'),
      new Country('Saudi Arabia'),
      new Country('Senegal'),
      new Country('Serbia'),
      new Country('Seychelles'),
      new Country('Sierra Leone'),
      new Country('Singapore'),
      new Country('Slovakia'),
      new Country('Slovenia'),
      new Country('Solomon Islands'),
      new Country('Somalia'),
      new Country('South Africa'),
      new Country('South Korea'),
      new Country('South Sudan'),
      new Country('Spain'),
      new Country('Sri Lanka'),
      new Country('Sudan'),
      new Country('Suriname'),
      new Country('Sweden'),
      new Country('Switzerland'),
      new Country('Syria'),
      new Country('Taiwan'),
      new Country('Tajikistan'),
      new Country('Tanzania'),
      new Country('Thailand'),
      new Country('Timor-Leste'),
      new Country('Togo'),
      new Country('Tonga'),
      new Country('Trinidad and Tobago'),
      new Country('Tunisia'),
      new Country('Turkey'),
      new Country('Turkmenistan'),
      new Country('Tuvalu'),
      new Country('Uganda'),
      new Country('Ukraine'),
      new Country('United Arab Emirates'),
      new Country('United Kingdom'),
      new Country('United States'),
      new Country('Uruguay'),
      new Country('Uzbekistan'),
      new Country('Vanuatu'),
      new Country('Vatican City'),
      new Country('Venezuela'),
      new Country('Vietnam'),
      new Country('Yemen'),
      new Country('Zambia'),
      new Country('Zimbabwe'),
    ];
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }
}
