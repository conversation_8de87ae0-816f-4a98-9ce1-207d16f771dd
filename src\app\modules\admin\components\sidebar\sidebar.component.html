<div class="container">
  <input type="checkbox" id="check" class="check-box" />
  <label for="check">
    <i class="fas fa-bars" id="btn"></i>
    <i class="fas fa-times" id="cancel"></i>
  </label>

  <div class="sidebar">
    <div class="sidebar-content">
      <a
        (click)="navigateSalesDashboard()"
        *ngIf="!isSystemAdmin && !isPayrollOnlyPlan"
      >
        <i class="mdi mdi-finance"></i>
        <span>Sales</span>
      </a>

      <a
        (click)="navigateFinanceDashboard()"
        *ngIf="!isSystemAdmin && (isBasicPlan || isPremiumPlan)"
      >
        <i class="fas fa-wallet"></i>
        <span>Finance</span>
      </a>

      <a
        (click)="payItems()"
        *ngIf="!isSystemAdmin && (isPremiumPlan || isPayrollOnlyPlan)"
      >
        <i class="fas fa-file-invoice-dollar" style="margin-right: 5px;"></i>
        <span>Payroll</span>
      </a>

      <a (click)="toggleSystemSettingMenu()">
        <i class="fas fa-wallet"></i>
        <span>System Settings</span>
        <i
          class="fas fa-caret-down"
          [ngClass]="{ rotate: showSystemSettingsSubmenu }"
          style="margin-left: 40px;"
        ></i>
      </a>

      <div *ngIf="showSystemSettingsSubmenu" class="submenu1">
        <a
          (click)="toggleFinanceMenu()"
          *ngIf="
            !isSystemAdmin && (isBasicPlan || isPremiumPlan || isPayrollOnlyPlan)
          "
        >
          <i class="fas fa-briefcase" style="margin-right: 5px;"></i>
          <span>Finance Master</span>
          <i
            class="fas fa-caret-down"
            [ngClass]="{ rotate: showFinanceSubmenu }"
            style="margin-left: 40px;"
          ></i>
        </a>

        <div *ngIf="showFinanceSubmenu" class="submenu2">
          <a (click)="navigateGlAccount()">
            <i class="fas fa-book" style="margin-right: 5px;"></i>
            <span style="font-size: 12px">GL Accounts</span>
          </a>

          <a (click)="navigateBusinessPartner()">
            <i class="fas fa-handshake"></i>
            <span style="font-size: 12px">Supplier</span>
          </a>

          <a (click)="navigateBankAccounts()">
            <i class="fas fa-university" style="margin-right: 3px;"></i>
            <span style="font-size: 12px">Bank Accounts</span>
          </a>

          <a (click)="navigatePeriodClosing()">
            <i class="fas fa-piggy-bank" style="margin-right:3px;"></i>
            <span style="font-size: 12px">Period Closing</span>
          </a>
        </div>

        <a (click)="navigateCountry()" *ngIf="isSystemAdmin">
          <i class="fas fa-user-shield"></i>
          <span>Admin</span>
        </a>

        <a (click)="navigateSubscription()" *ngIf="isPrimaryUser">
          <i class="fas fa-receipt" style="margin-right: 8px;"></i>
          <span>Subscription</span>
        </a>

        <a (click)="navigateManageUsers()" *ngIf="isPrimaryUser || isSystemAdmin">
          <i class="fas fa-users-cog"></i>
          <span>Manage Users</span>
        </a>

        <a (click)="navigateAccountantInvite()" *ngIf="isAccountant">
          <i class="fas fa-user-plus"></i>
          <span style="font-size: 13px">Add Accountant</span>
        </a>

        <a (click)="navigateRequestEntity()" *ngIf="isAccountant">
          <i class="fas fa-paper-plane"></i>
          <span style="font-size: 13px">Request to Entity</span>
        </a>

        <a (click)="navigateInviteEntity()" *ngIf="isAccountant">
          <i class="fas fa-envelope-open-text"></i>
          <span style="font-size: 13px">Invite to Entity</span>
        </a>

        <a (click)="toggleMasterSubmenu()" *ngIf="isPrimaryUser">
          <i class="fas fa-briefcase" style="margin-right: 5px;"></i>
          <span>Master</span>
          <i
            class="fas fa-caret-down"
            [ngClass]="{ rotate: showMasterSubMenu }"
            style="margin-left: 40px;"
          ></i>
        </a>

        <div *ngIf="showMasterSubMenu" class="submenu2">
          <a (click)="navigateBusinessPartnerList()">
            <i class="fas fa-handshake"></i>
            <span style="font-size: 12px">Business Partner</span>
          </a>
          <a (click)="navigateSalesItem()">
            <i class="fas fa-box-open"></i>
            <span style="font-size: 13px">Sales Item</span>
          </a>
        </div>

        <a (click)="navigateEntitySettings()" *ngIf="isPrimaryUser">
          <i class="fas fa-cogs"></i>
          <span>Settings</span>
        </a>
      </div>
    </div>
  </div>
</div>
