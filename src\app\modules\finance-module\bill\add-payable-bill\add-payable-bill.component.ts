import {
  Component,
  ElementRef,
  Inject,
  Optional,
  ViewChild,
} from '@angular/core';
import { ApInvoiceDetail, ApInvoiceHead, CoaLedgerAccount, SaveApInvoiceHeadDTO } from '../bill';
import { Entity } from 'src/app/modules/entity/entity';
import {
  BusinessPartner,
  BusinessPartnerType,
} from 'src/app/modules/business-partner/business-partner';
import { HttpService } from 'src/app/http.service';
import { BillService } from '../bill.service';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from 'src/app/modules/admin/components/user/user.service';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { BankReconciliationService } from '../../bank-reconciliation/bank-reconciliation.service';
import { HttpErrorResponse } from '@angular/common/http';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { NgForm } from '@angular/forms';
import { SwalAlertsService } from 'src/app/modules/swal-alerts/swal-alerts.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import * as pdfjsLib from 'pdfjs-dist';
import { PeriodClosingService } from '../../period-closing/period-closing.service';
import { DateAdapter } from '@angular/material/core';
(pdfjsLib as any).GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;


@Component({
  selector: 'app-add-payable-bill',
  templateUrl: './add-payable-bill.component.html',
  styleUrls: ['./add-payable-bill.component.css'],
})
export class AddPayableBillComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent)
  chatResponseComponent!: ChatResponseComponent;
  @ViewChild('resultSection') resultSection!: ElementRef;
  @ViewChild('canvas', { static: false }) canvasRef!: ElementRef<HTMLCanvasElement>;

  apInvoiceHeadData: ApInvoiceHead = new ApInvoiceHead();
  apInvoiceDetail: ApInvoiceDetail = new ApInvoiceDetail();
  businessEntity: Entity = new Entity();
  customers: BusinessPartner[] = [];
  glAccounts: CoaLedgerAccount[] = [];
  businessPartnerType: BusinessPartnerType[] = [];
  unitPriceInvalid: boolean = false;
  taxApplicable: boolean = false;
  businessPartner: BusinessPartner = new BusinessPartner();
  private audio!: HTMLAudioElement;
  lastReferenceNumber: string = '';
  businessEntityId: number = 0;
  selectedFile: File | null = null;
  url = '';
  isLoading: boolean = false;
  previewImageSrc: string | null = null;
  selectedGlAccount: string | null = null;
  text: string | null = null;
  isPreview: boolean = false;
  transactionData: any;
  totalBalance: number = 0.0;
  balanceError: string | null = null;
  taxApplicabilityEnabled = true;
  defaultTaxRate = 0;
  id: number = 0;
  showPreview: boolean = false;
  minDate = new Date();

  constructor(
    private billService: BillService,
    private entityService: EntityService,
    private router: Router,
    private businessPartnerService: BusinessPartnerService,
    private bankReconciliationService: BankReconciliationService,
    private swalAlertService: SwalAlertsService,
    private periodClosingService: PeriodClosingService,
    private dateAdapter: DateAdapter<Date>,
    @Optional() private dialogRef: MatDialogRef<AddPayableBillComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public transactionDetails: any
  ) {
    this.dateAdapter.setLocale('en-GB'); // This will format dates as dd/mm/yyyy
  }

  ngOnInit() {
    this.setTodayDate();
    this.loadCustomers();
    this.getBusinessEntityById();
    this.addEmptyRows(1);
    this.fetchGlAccounts();
  }

  togglePreview(): void {
  this.showPreview = !this.showPreview;
  }

  fetchGlAccounts() {
    const entityId = +localStorage.getItem('entityId')!;
    this.billService
      .getActiveCoaLedgerAccountListByEntityBill(entityId)
      .subscribe(
        (glAccounts: CoaLedgerAccount[]) => {
          this.glAccounts = glAccounts;
        },
        (error: HttpErrorResponse) => {
          console.error('Error fetching GL Accounts', error);
          Swal.fire({
            title: 'Error!',
            text: 'Failed to load GL Accounts.',
            icon: 'error',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#be0032',
            cancelButtonColor: '#007bff',
            showCancelButton: true,
          }).then((result) => {
            if (
              result.isDismissed &&
              result.dismiss === Swal.DismissReason.cancel
            ) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData(
                      'Failed to load gl Accounts.'
                    );
                    this.chatBotComponent.responseReceived.subscribe(
                      (response) => {
                        Swal.close();
                        this.chatResponseComponent.showPopup = true;
                        this.chatResponseComponent.responseData = response;
                        this.playLoadingSound();
                        this.stopLoadingSound();
                      }
                    );
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
        }
      );

    // Initialize ledgerAccountName for each item in apInvoiceDetails
    this.apInvoiceHeadData.apInvoiceDetails.forEach((detail) => {
      detail.ledgerAccountName = '';
    });
  }

  onScanBillClick(fileInput: HTMLInputElement): void {
    fileInput.click();
  }

  fileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];

      const fileType = this.selectedFile.type;

    if (fileType.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        this.previewImageSrc = e.target?.result as string;
      };
      reader.readAsDataURL(this.selectedFile);
    } else if (fileType === 'application/pdf') {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const typedarray = new Uint8Array(e.target!.result as ArrayBuffer);
        const pdf = await pdfjsLib.getDocument({ data: typedarray }).promise;
        const page = await pdf.getPage(1);
        const canvas = this.canvasRef.nativeElement;
        const context = canvas.getContext('2d')!;
        const viewport = page.getViewport({ scale: 1.5 });
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        await page.render({ canvasContext: context, viewport }).promise;

        this.previewImageSrc = canvas.toDataURL('image/png');
     

      };
      reader.readAsArrayBuffer(this.selectedFile);
    } else {
      this.previewImageSrc = null; // Or show a message if needed
    }

      this.uploadImage();
    }
  }

  uploadImage(): void {
    if (this.selectedFile) {
      this.isLoading = true;
      this.isPreview = true;

      this.billService.uploadImage(this.selectedFile).subscribe({
        next: (response: any[]) => {
          this.isLoading = false;
          this.populateInvoiceDetails(response);
          this.calculateSubTotal();
          this.apInvoiceHeadData.scanned = true;
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Backend Error:', error);

          let errorMessage = 'Unknown processing error';

          if (error?.error) {
            if (typeof error.error === 'string') {
              errorMessage = error.error;
            } else if (error.error?.error) {
              errorMessage = error.error.error;
            } else if (error.error?.message) {
              errorMessage = error.error.message;
            }
          }

          Swal.fire({
            icon: 'error',
            title: 'Processing Error',
            text: errorMessage,
            confirmButtonColor: '#be0032',
          });
        },
      });
    } else {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'No file selected.',
      });
    }
  }

  populateInvoiceDetails(data: any[]): void {
    this.apInvoiceHeadData.apInvoiceDetails = [];

    data.forEach((item: any) => {
      this.apInvoiceHeadData.supplierReferenceNumber = item.referenceNo || '';
      this.apInvoiceHeadData.apInvoiceDetails.push({
        itemDescription: item.description || '',
        quantity: item.quantity || '',
        unitPrice: item.unitPrice || '',
        referenceNo: item.referenceNo || '',
        amount: item.amount || 0.0,
        dueAmount: item.dueAmount || 0.0,
        tax: item.tax || 0.0,
        coaLedgerAccountId: item.coaLedgerAccountId || '',
        ledgerAccountName: item.ledgerAccountName || '',
        ledgerAccountCode: item.ledgerAccountCode || '',
      });
    });
  }

  scrollToResult(): void {
    this.resultSection.nativeElement.scrollIntoView({ behavior: 'smooth' });
  }

  addEmptyRows(count: number) {
    if (this.transactionDetails) {
      this.addNewDetailedRow(this.transactionDetails);
      this.updateAmount(0);
    } else {
      for (let i = 0; i < count; i++) {
        this.apInvoiceHeadData.apInvoiceDetails.push({
          referenceNo: this.apInvoiceHeadData.referenceNo,
          quantity: 0,
          unitPrice: 0,
          itemDescription: '',
          amount: 0,
          dueAmount: 0,
          tax: 0,
          coaLedgerAccountId: 0,
          ledgerAccountName: '',
          ledgerAccountCode: '',
        });
      }
    }
  }

  addNewRow() {
    this.apInvoiceHeadData.apInvoiceDetails.push({
      referenceNo: this.apInvoiceHeadData.referenceNo,
      quantity: 0,
      unitPrice: 0,
      itemDescription: '',
      amount: 0,
      dueAmount: 0,
      tax: 0,
      coaLedgerAccountId: 0,
      ledgerAccountName: '',
      ledgerAccountCode: '',
    });
  }

  addNewDetailedRow(transactionDetails: any) {
    this.apInvoiceHeadData.apInvoiceDetails.push(
      this.createTransactionDetailsRow(transactionDetails)
    );
  }

  createTransactionDetailsRow(transactiondetails: any) {
    return {
      referenceNo: this.apInvoiceHeadData.referenceNo,
      quantity: 1,
      unitPrice: transactiondetails.pendingBalance,
      itemDescription: transactiondetails.description,
      amount: 0,
      dueAmount: 0,
      tax: 0,
      coaLedgerAccountId: 0,
      ledgerAccountName: '',
      ledgerAccountCode: '',
    };
  }

  onDescriptionInput(index: number) {
    if (index === this.apInvoiceHeadData.apInvoiceDetails.length - 1) {
      // this.addNewRow();
    }
  }

  calculateSubTotal() {
    let subTotal = 0;
    let totalTax = 0;

    this.apInvoiceHeadData.apInvoiceDetails.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;
      detail.amount = itemAmount; // Always update base amount as unitPrice * quantity
      detail.dueAmount = detail.amount;
      
      // Add tax if applicable
      if (
        detail.quantity > 0 &&
        detail.taxApplicability &&
        this.taxApplicable
      ) {
        const taxRate = this.businessEntity?.countryId.defaultTaxRate || 0; // Default tax rate
        detail.tax = itemAmount * (taxRate / 100); // Calculate tax
      } else {
        detail.tax = 0; // Reset tax if not applicable
      }

      subTotal += detail.amount; // Add item amount to subtotal
      totalTax += detail.tax; // Add tax to total tax
    });

    this.apInvoiceHeadData.grossAmount = subTotal;
    this.apInvoiceHeadData.totalGst = totalTax;

    this.calculateGrandTotal();
  }

  calculateGrandTotal() {
    const { grossAmount, totalGst } = this.apInvoiceHeadData;
    this.apInvoiceHeadData.netAmount = grossAmount + totalGst; // Total with tax
    this.apInvoiceHeadData.dueAmount = grossAmount + totalGst; // Due amount
  }

  onTaxApplicableChange(index: number): void {
    const detail = this.apInvoiceHeadData.apInvoiceDetails[index];

    // Fetch entityId from localStorage
    const entityId = +(localStorage.getItem('entityId') || 0);

    if (entityId) {
      this.entityService.getBusinessEntityById(entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (this.taxApplicable && detail.taxApplicability) {
            detail.tax = detail.amount * (taxRate / 100); // Apply tax
          } else {
            detail.tax = 0; // Reset tax
          }

          this.calculateSubTotal(); // Recalculate totals
        },
        (error) => {
          console.error('Error fetching entity data:', error);
          detail.tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      detail.tax = 0;
      this.calculateSubTotal();
    }
  }

  updateAmount(index: number): void {
    const detail = this.apInvoiceHeadData.apInvoiceDetails[index];
    detail.amount = detail.quantity * detail.unitPrice; // Calculate base amount

    this.onTaxApplicableChange(index); // Re-evaluate tax based on applicability
  }

  updateQuantity(index: number) {
    this.updateAmount(index); // Ensure amount and tax are updated when quantity changes
  }

  removeItem(index: number) {
    Swal.fire({
      title: 'Remove Item',
      text: 'Are you sure you want to remove this item?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, remove it!',
      cancelButtonText: 'No, cancel!',
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
    }).then((result) => {
      if (result.isConfirmed) {
        try {
          // Remove the item from the array
          this.apInvoiceHeadData.apInvoiceDetails.splice(index, 1);
          this.calculateSubTotal(); // Recalculate totals
          
          Swal.fire({
            title: 'Removed!',
            text: 'Item has been removed.',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#3085d6',
          });
        } catch (error) {
          console.error('Error removing item:', error);
          Swal.fire({
            title: 'Error!',
            text: 'Failed to remove item.',
            icon: 'error',
            confirmButtonText: 'OK',
            cancelButtonText: 'Ask Chimp',
            confirmButtonColor: '#be0032',
            cancelButtonColor: '#007bff',
            showCancelButton: true,
          }).then((result) => {
            if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
              if (this.chatBotComponent) {
                Swal.fire({
                  title: 'Processing...',
                  text: 'Please wait while Chimp processes your request.',
                  allowOutsideClick: false,
                  didOpen: () => {
                    Swal.showLoading();
                    this.chatBotComponent.setInputData('Failed to remove item.');
                    this.chatBotComponent.responseReceived.subscribe(response => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound() 
                    });
                  },
                });
              } else {
                console.error('ChatBotComponent is not available.');
              }
            }
          });
        }
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        Swal.fire({
          title: 'Cancelled',
          text: 'Item removal cancelled.',
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#3085d6',
        });
      }
    });
  }

  loadCustomers() {
    const entityId = +(localStorage.getItem('entityId') + '');

    this.businessPartnerService.getSupplierListByEntity(entityId).subscribe(
      (customers: BusinessPartner[]) => {
        // Filter customers where businessPartnerType is 'Supplier'
        this.customers = customers.filter(
          (customer) =>
            customer.businessPartnerTypeId?.businessPartnerType === 'Supplier'
        );
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching Suppliers', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load Supplier.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Failed to load customers.'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );

    // Reset the selected business partner
    this.apInvoiceHeadData.businessPartnerId = '';
  }

  loadBusinessPartnerTypes() {
    this.businessPartnerService.getBusinessPartnerTypesList().subscribe(
      (businessPartnerType: BusinessPartnerType[]) => {
        // Find the "Customer" type from the list
        const customerType = businessPartnerType.find(
          (type) => type.businessPartnerType.toLowerCase() === 'supplier'
        );

        if (customerType) {
          // Assign the customer type to the businessPartner object
          this.businessPartner.businessPartnerTypeId.businessPartnerTypeId =
            customerType.businessPartnerTypeId;
        }

        // Optionally store the filtered list if needed
        this.businessPartnerType = businessPartnerType;
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching Business Partner Type', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load Business Partner Type.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Failed to load Business Partner Type.'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
    );
  }

  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

  getBusinessEntityById() {
    this.businessEntityId = +(localStorage.getItem('entityId') + '');

    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
      (data) => {
        this.businessEntity = data;
        this.taxApplicabilityEnabled = data.taxApplicability === 'yes';
        this.defaultTaxRate = data.countryId?.defaultTaxRate || 0;
        this.taxApplicabilityEnabled = data.taxApplicability === 'yes';
        this.lastReferenceNumber = this.incrementReferenceNumber(
          this.businessEntity.referenceNo
        );

        this.apInvoiceHeadData.referenceNo = this.lastReferenceNumber;
      },
      (error) => console.error(error)
    );
  }

  incrementReferenceNumber(referenceNo: string): string {
    // If the referenceNo  is null or empty, initialize it to 'B000001'
    if (!referenceNo) {
      return 'B000001';
    }

    // Assuming the referenceNo is in the format 'I000039'
    const prefix = referenceNo.charAt(0); // Extract the 'I'
    const numericPart = referenceNo.slice(1); // Extract the '000039'

    // Increment the numeric part and pad with leading zeros to maintain the format
    const incrementedNumber = (Number(numericPart) + 1)
      .toString()
      .padStart(numericPart.length, '0');

    // Combine the prefix and the incremented number
    return prefix + incrementedNumber;
  }

  setTodayDate() {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const dd = String(today.getDate()).padStart(2, '0');

    this.apInvoiceHeadData.postingDate = today.toISOString().split('T')[0]; // Set posting date to today
    this.updateValidityMinDate();
  }

  updateValidityMinDate() {
    const date = this.apInvoiceHeadData.postingDate;
    if (date) {
      const dateObj = new Date(date);
      const yyyy = dateObj.getFullYear();
      const mm = String(dateObj.getMonth() + 1).padStart(2, '0');
      const dd = String(dateObj.getDate()).padStart(2, '0');

      const minValidUntilDate = `${yyyy}-${mm}-${dd}`;
      (document.getElementById('validityUntil') as HTMLInputElement).min =
        minValidUntilDate;
    }
  }

  onQuoteDateChange() {
    this.updateValidityMinDate();
    this.validateDates();

    const entityId = +localStorage.getItem('entityId')!;
    const date = this.apInvoiceHeadData.postingDate;

    if (date) {
      this.periodClosingService.isDateLocked(entityId, date).subscribe({
        next: (isLocked: boolean) => {
          if (isLocked) {
            Swal.fire({
              icon: 'error',
              title: 'Posting Date is Locked',
              text: 'The selected date falls within a closed accounting period. Please choose another date.',
              confirmButtonColor: '#ff7e5f'
            });

            // Reset the posting date
            this.setTodayDate();
          }
        },
        error: (err) => {
          console.error('Error validating posting date lock', err);
        }
      });
    }
  }

  onValidUntilDateChange(): void {
    this.validateDates();
  }

  validateDates(): boolean {
    if (!this.apInvoiceHeadData.postingDate || !this.apInvoiceHeadData.dueDate) {
      return false;
    }
    const postingDate = new Date(this.apInvoiceHeadData.postingDate);
    const validUntilDate = new Date(this.apInvoiceHeadData.dueDate);

    if (validUntilDate < postingDate) {
      Swal.fire({
        title: 'Invalid Date',
        text: 'The Due date cannot be before the "Posting Date".',
        icon: 'warning',
        confirmButtonText: 'OK',
      });
      return false;
    }
    return true;
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  onSubmit(f: NgForm) {
    // Check if the form is valid
    if (f.valid) {
      // Validate if each row has either a selected  description
      const hasInvalidRows = this.apInvoiceHeadData.apInvoiceDetails.some(
        (detail) =>
          !detail.itemDescription || detail.itemDescription.trim() === ''
      );

      if (
        this.apInvoiceHeadData.apInvoiceDetails.some(
          (detail) => !detail.coaLedgerAccountId
        )
      ) {
        this.swalAlertService.showErrorWithChimpSupport(
          'Each row must have a GL Account selected.',
          'GL Account is missing in one or more rows after scanning the bill.'
        );
        return;
      }

      if (hasInvalidRows) {
        this.swalAlertService.showErrorWithChimpSupport(
          'Each row must have a description provided.',
          'Each row must have a description provided.'
        );
        return;
      }

      // Check transaction balance validation
      // if (this.transactionData) {
      //   if (
      //     this.totalBalance >
      //     Math.round(this.apInvoiceHeadData.netAmount * 100) / 100
      //   ) {
      //     Swal.fire({
      //       title: 'Balance Error!',
      //       text: `This is less than the balance of ${this.totalBalance}`,
      //       icon: 'error',
      //       confirmButtonText: 'OK',
      //       confirmButtonColor: '#be0032',
      //     });
      //     return;
      //   } else if (
      //     this.totalBalance <
      //     Math.round(this.apInvoiceHeadData.netAmount * 100) / 100
      //   ) {
      //     Swal.fire({
      //       title: 'Balance Warning!',
      //       text: `This is greater than the balance of $${Number(
      //         this.totalBalance
      //       ).toFixed(2)} and $${Number(
      //         this.apInvoiceHeadData.netAmount
      //       ).toFixed(2)}`,
      //       icon: 'warning',
      //       confirmButtonText: 'OK',
      //       confirmButtonColor: '#ff7e5f',
      //     });
      //     return;
      //   }
      // }

      this.checkForZeroQuantity();
    }
  }

  checkForZeroQuantity() {
    const hasZeroQuantity = this.apInvoiceHeadData.apInvoiceDetails.some(
      (detail) => detail.quantity === 0
    );

    const hasNoUnitPrice = this.apInvoiceHeadData.apInvoiceDetails.some(
      (detail) => detail.unitPrice === 0
    );

    if (hasZeroQuantity || hasNoUnitPrice) {
      Swal.fire({
        title: 'Warning!',
        text: 'Selected item(s) have zero quantity or Unit Price. Do you want to continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
        footer:
          '<a href="#" id="custom-link" style="background-color: #007bff; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">Ask Chimp</a>',
      }).then((result) => {
        if (result.isConfirmed) {
          // Proceed with saving or drafting
          this.saveApInvoice();
        }
      });

      // Attach chatbot logic to the footer link
      document
        .getElementById('custom-link')
        ?.addEventListener('click', (event) => {
          event.preventDefault(); // Prevent default anchor behavior
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData(
                  'Selected item(s) have zero quantity. Do you want to continue?'
                );
                this.chatBotComponent.responseReceived.subscribe((response) => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        });
    } else if (this.apInvoiceHeadData.apInvoiceDetails.length === 0) {
      Swal.fire({
        title: 'Error!',
        text: 'You must add at least one item.',
        icon: 'error',
        confirmButtonText: 'OK',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#be0032',
        cancelButtonColor: '#007bff',
        showCancelButton: true,
      }).then((result) => {
        if (
          result.isDismissed &&
          result.dismiss === Swal.DismissReason.cancel
        ) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData(
                  'You must add at least one item.'
                );
                this.chatBotComponent.responseReceived.subscribe((response) => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });
      return;
    } else {
      // No zero quantity, proceed with saving or drafting
      this.saveApInvoice();
    }
  }

  saveApInvoice() {
    this.apInvoiceHeadData.status = 'Pending'; // Always set status to 'Pending'
    this.apInvoiceHeadData.userId = +(localStorage.getItem('userid') + '');
    this.apInvoiceHeadData.entityId = +(localStorage.getItem('entityId') + '');

    if (!this.validateDates()) {
      return;
    }

    // Add the DTO Here
    const saveApInvoiceHeadDTOInstance:SaveApInvoiceHeadDTO ={
      apInvoice:this.apInvoiceHeadData,
      autoPaymentVoucher:this.dialogRef ? true : false,
    }

    console.log(saveApInvoiceHeadDTOInstance);


    this.billService.saveApInvoiceHeadData(saveApInvoiceHeadDTOInstance).subscribe(
      (response: any) => {
        this.apInvoiceHeadData = response;
        this.id = this.apInvoiceHeadData.apInvoiceHeadId;

        if (this.selectedFile) {
          this.billService
            .saveApiInvoiceHeadFile(
              this.apInvoiceHeadData.apInvoiceHeadId,
              this.selectedFile
            )
            .subscribe((response) => {
              console.log(response);
              this.apInvoiceHeadData = response;
            });
        }

        // if (this.transactionData) {
        //   const matchedDetail = {
        //     selectedRecord: this.apInvoiceHeadData,
        //     selectedTransaction: this.transactionData,
        //     entityId: this.apInvoiceHeadData.entityId,
        //   };

        //   this.bankReconciliationService
        //     .saveSuggestAndDebitDebitTransaction(matchedDetail)
        //     .subscribe(
        //       () => {
        //         console.log('BankStatementMatchedDetail saved successfully.');
        //       },
        //       (error) => {
        //         console.error('Error saving BankStatementMatchedDetail', error);
        //       }
        //     );
        // }

        // All Updated

        this.updateBusinessEntityReferenceNumber();

        Swal.fire({
          title: 'Success!',
          text: 'The Payable Bill has been Successfully saved.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then((result) => {
          if (result.isConfirmed) {
            if (this.dialogRef) {
              this.closeDialog(true,this.apInvoiceHeadData);
            } else {
             // this.router.navigate([`/view-payable-bill/${this.id}`]);
               this.router.navigate(['/payable-bill-list']);
            }
          }
        });
      },
      (error: HttpErrorResponse) => {
        console.error('Error saving Payable Bill', error);
        Swal.fire({
          title: 'Error!',
          text: 'Unable to Save the Payable Bill. Please fill all required fields and try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData(
                    'Unable to Save the Bill. Please try again'
                  );
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });

        if (this.dialogRef) {
          this.closeDialog(true);
        }

        return;
      }
    );
  }

  onCancel() {
    this.router.navigate(['/payable-bill-list']);
  }

  updateBusinessEntityReferenceNumber() {
    this.businessEntityId = +(localStorage.getItem('entityId') + '');
    this.businessEntity.referenceNo = this.apInvoiceHeadData.referenceNo;
    this.entityService
      .updateReferenceNumber(this.businessEntity, this.businessEntityId)
      .subscribe(
        (data) => {},
        (error) => {
          console.error(error);
        }
      );
  }

  @ViewChild('cuspop') cuspop!: NgForm;
  @ViewChild('closeCustomerPopUp') closeCustomerPopUp: any;

  onSubmitCustomerForm() {
    this.saveCustomerForm(this.businessPartner);
  }

  saveCustomerForm(businessPartner: BusinessPartner) {
    this.businessPartner.entityId.entityId = +(
      localStorage.getItem('entityId') + ''
    );
    this.businessPartnerService.saveBusinessPartner(businessPartner).subscribe(
      (response) => {
        Swal.fire({
          title: 'Success!',
          text: 'Supplier added successfully.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        });
        this.loadCustomers();
        this.cuspop.resetForm();
        this.closeCustomerPopUp.nativeElement.click();
      },
      (error) => {
        console.error('Error adding Supplier', error);
        Swal.fire({
          title: 'Error!',
          text: 'Error adding Supplier.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (
            result.isDismissed &&
            result.dismiss === Swal.DismissReason.cancel
          ) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Error adding Supplier.');
                  this.chatBotComponent.responseReceived.subscribe(
                    (response) => {
                      Swal.close();
                      this.chatResponseComponent.showPopup = true;
                      this.chatResponseComponent.responseData = response;
                      this.playLoadingSound();
                      this.stopLoadingSound();
                    }
                  );
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
        return;
      }
    );
  }

  onFileSelected(event: any) {
    if (event.target.files && event.target.files[0]) {
      this.selectedFile = event.target.files[0];

      if (this.selectedFile) {
        const reader = new FileReader();

        reader.readAsDataURL(this.selectedFile);

        reader.onload = (event: ProgressEvent<FileReader>) => {
          const result = event.target?.result;
          if (typeof result === 'string') {
            this.url = result;
          }
        };
      }
    }
  }

  onBillFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (e: any) => (this.url = e.target.result);
    }
  }

  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(
      (cust) => cust.businessPartnerId === +selectedCustomerId
    );

    // Set the reference field
    this.apInvoiceHeadData.supplierName = selectedCustomer?.bpName || '';
  }

  onGLChange(event: any, index: number) {
    const selectedAccountId = +event.target.value; // Get the selected ID
    const selectedAccount = this.glAccounts.find(
      (account) => account.coaLedgerAccountId === selectedAccountId
    ); // Find the selected account object

    if (selectedAccount) {
      // Update the corresponding detail with the selected account's information
      this.apInvoiceHeadData.apInvoiceDetails[index].coaLedgerAccountId =
        selectedAccount.coaLedgerAccountId;
      this.apInvoiceHeadData.apInvoiceDetails[index].ledgerAccountName =
        selectedAccount.ledgerAccountName;
    }
  }

  closeDialog(status: boolean, apInvoiceHead?: ApInvoiceHead) {
    this.dialogRef.close({ success: status, apInvoice: apInvoiceHead });
  }
}
