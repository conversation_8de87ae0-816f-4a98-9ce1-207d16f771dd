import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class HomeService {
  private selectedSubscriptionFeeId: number | null = null;
  private selectedSubscribtionAmout: string | null = null;

  constructor() {}

  setSelectedSubscriptionFeeId(id: number) {
    this.selectedSubscriptionFeeId = id;
  }

  getSelectedSubscriptionFeeId(): number | null {
    return this.selectedSubscriptionFeeId;
  }

  setSelectedSubscriptionamount(amount: string) {
    this.selectedSubscribtionAmout = amount;
  }

  getSelectedSubscriptionAmount(): string | any {
    return this.selectedSubscribtionAmout;
  }
}
