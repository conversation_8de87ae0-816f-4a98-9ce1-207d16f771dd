<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="bill-container">
    <form class="row g-1">
        <div class="heading" (keydown)="preventSubmit($event)">
            <h3>Payable Bill -
                    <span
      [ngClass]="{
             'text-pending': apInvoiceHeadData.status === 'Pending',
              'text-overdue': apInvoiceHeadData.status === 'Overdue',
              'text-paid': apInvoiceHeadData.status === 'Paid',
              'text-canceled': apInvoiceHeadData.status === 'Canceled',
              'text-awaiting-payment': apInvoiceHeadData.status === 'Awaiting Payment',   
      }"
    >
      {{ apInvoiceHeadData.status }}
    </span>
      <ng-container *ngIf="apInvoiceHeadData.scanned">
    <i class="bi bi-check2-circle" title="Scanned via OCR" style="color: #007bff; font-size: 1.2rem; margin-right: 4px;"></i>
  </ng-container>
 </h3>
        
     <div class="d-flex align-items-center gap-2">
          <!-- Edit Button -->
         <button class="btn btn-outline-primary gradient-btn d-flex align-items-center" (click)="editBill(apInvoiceHeadData.apInvoiceHeadId)">
           <i class="bi bi-pencil-square me-2"></i> Edit Bill
           </button>
          
          
           <div class="btn-group"  #dropdownRef [class.show]="isDropdownOpen">
            <button
              type="button"
              class="btn btn-secondary dropdown-toggle gradient-btn"
              data-bs-toggle="dropdown"
              aria-expanded="false"
              (click)="toggleDropdown()"
            >
              <i class="bi bi-three-dots-vertical"></i>
            </button>
            <ul
              class="dropdown-menu dropdown-menu-end"
              [class.show]="isDropdownOpen"
            >
        <li>
          <a class="dropdown-item" (click)="recordBatchPayment(apInvoiceHeadData.apInvoiceHeadId)">Record Payment</a>
        </li>
        <li>
          <a class="dropdown-item" (click)="onCreditNoteMultiple(apInvoiceHeadData.apInvoiceHeadId)">Credit Note</a>
        </li>
        <li>
          <a class="dropdown-item" (click)="cancelSelectedBills()">Cancel</a>
        </li>        
            </ul>
          </div>
        </div>
      </div>


        

        <div class="bd">
            <div class="form-section">
                <div class="form-row" style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                    <div class="form-group" style="display: flex; flex-grow: 1">
                        <label style="margin-right: 10px; white-space: nowrap">Bill Number</label>
                        <input class="input-style" type="text" [value]="apInvoiceHeadData.referenceNo" style="background-color: #ffffff" readonly disabled />
                    </div>
                    <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1">
                        <div style="display: flex" class="form-dataInput">
                            <label style="margin-right: 40px; white-space: nowrap">Due Date</label>
                            <input 
                                class="input-style" 
                                type="text" 
                                [value]="apInvoiceHeadData.dueDate | date: 'dd/MM/yyyy'" 
                                readonly 
                                disabled 
                            />
                        </div>
                    </div>
                </div>

                <div class="form-row" style="display: flex; justify-content: space-between">
                    <div class="form-group" style="flex-grow: 1">
                        <div style="display: flex" class="form-dataInput">
                            <label style="margin-right: 32px; white-space: nowrap" class="InputLabel">Supplier</label>
                            <select class="form-select" disabled>
                                <option *ngFor="let customer of customers" [value]="customer.businessPartnerId">{{ customer.bpName }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1">
                        <div style="display: flex" class="form-dataInput">
                            <label style="margin-right: 20px; white-space: nowrap">Posting Date</label>
                            <input 
                                class="input-style" 
                                type="text" 
                                [value]="apInvoiceHeadData.postingDate | date: 'dd/MM/yyyy'" 
                                readonly 
                                disabled 
                            />
                        </div>
                    </div>
                </div>

                <div class="form-rows">
                    <div class="form-group" style="flex-grow: 1;">
                        <div class="reff">
                            <label style="margin-right: 20px; white-space: nowrap" class="InputLabel">Reference</label>
                            <input class="input-style" type="text" [value]="apInvoiceHeadData.referenceNo"  style="width: 100%" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-section">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 33%">Description</th>
                                <th style="width: 7%">Quantity</th>
                                <th style="width: 17%; text-align: center">Unit Price</th>
                                <th style="width: 13%">Account</th>
                                <th style="width: 9%">{{ businessEntity.countryId.defaultTaxType }}({{ businessEntity.countryId.defaultTaxRate }}%)</th>
                                <th style="width: 6%; text-align: right">Tax</th>
                                <th style="width: 15%; text-align: right; padding-right: 0%;">Amount ({{ businessEntity.countryId.defaultCurrency}})</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="apInvoiceHeadData.apInvoiceDetails.length > 0">
                            <tr *ngFor="let detail of apInvoiceHeadData.apInvoiceDetails; let i = index">
                                <td><input type="text" class="form-control" [value]="detail.itemDescription" readonly disabled /></td>
                                <td><input type="number" class="form-control" [value]="detail.quantity" readonly disabled /></td>
                                <td>
                                    <div class="input-group">
                                        <div class="input-group-prepend"><span class="input-group-text">$</span></div>
                                        <input type="number" class="form-control" [value]="detail.unitPrice" readonly disabled />
                                    </div>
                                </td>
                                <td>
                                    <select class="form-control" [value]="detail.coaLedgerAccountId" disabled>
                                        <option *ngFor="let account of glAccounts" [value]="account.coaLedgerAccountId" [selected]="account.coaLedgerAccountId === detail.coaLedgerAccountId">{{ account.ledgerAccountName }}</option>
                                    </select>
                                </td>
                                <td>
                                    <div class="form-check-tax">
                                        <label>
                                            In.Tax <input type="checkbox" [disabled]="!taxApplicabilityEnabled" [checked]="detail.taxApplicability" disabled style="margin-left: 5px" />
                                        </label>
                                    </div>
                                </td>
                                <td style="text-align: right">{{ apInvoiceHeadData.apInvoiceDetails[i].tax | currency }}</td>
                                <td style="text-align: right">{{ apInvoiceHeadData.apInvoiceDetails[i].amount | currency }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="main-row-note">
                <div class="notes-totals-section">
                    <div class="notes-section" style="padding: 0 10px 0 10px;">
                        <label for="remarks">Note</label>
                        <textarea id="remarks" class="form-control" name="note" rows="10" readonly></textarea>
                    </div>

                    <div class="totals-section" style="margin-top: 25px;">
                        <div class="totals-row">
                            <span class="totals-row1">Sub Total Amount </span>
                            <span class="totals-row2">{{ apInvoiceHeadData.grossAmount | currency }}</span>
                        </div>
                        <div class="totals-row">
                            <span class="totals-row1">Total {{ businessEntity.countryId.defaultTaxType }}</span>
                            <span class="totals-row2">{{ apInvoiceHeadData.totalGst | currency }}</span>
                        </div>
                        <div class="totals-row">
                            <strong class="totals-row1">Grand Total</strong>
                            <strong class="totals-row2">{{ apInvoiceHeadData.netAmount | currency }}</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
  