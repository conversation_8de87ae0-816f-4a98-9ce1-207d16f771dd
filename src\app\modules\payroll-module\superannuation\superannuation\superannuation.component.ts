import { Component, OnInit } from '@angular/core';
import { EmployeeMaster } from '../../payroll-settings/empolyee/employee';
import { EmployeeService } from '../../payroll-settings/services/employee.service';
import { BeamServiceService } from '../../payroll-settings/services/beam-service.service';
import Swal from 'sweetalert2';
import { PayRunDetail } from '../../payroll-settings/payroll-setting';
import { ValidationResponse } from '../superannuation';
import * as bootstrap from 'bootstrap';


@Component({
  selector: 'app-superannuation',
  templateUrl: './superannuation.component.html',
  styleUrls: ['./superannuation.component.css']
})
export class SuperannuationComponent implements OnInit {
  
  payRunDetailList: PayRunDetail[] = [];
  selectedPayRunDetail: Set<PayRunDetail> = new Set<PayRunDetail>();
  isAllSelected: boolean = false;
  isSubmitting: boolean = false;
  validationErrors: any[] = [];
  validationWarnings: any[] = [];
  fromDate: string = '';
  toDate: string = '';
  filteredPayRunDetailList: any[] = [];
  pageSize = 10;
  currentPage = 0;
  totalPages = 0;
  pagedPayRunDetails: any[] = [];
  pageNumbers: number[] = [];
  maxVisiblePages = 3;  
  visiblePages: number[] = [];

  notifications: any[] = []; 
  isLoadingNotifications: boolean = false;
  isLoadingTable: boolean = false;

  constructor(private employeeService: EmployeeService, private beamServiceService: BeamServiceService) { }

  ngOnInit(): void {
    this.getAndProcessNotifications(); // Fetch notifications first
  }

  selectAll(event: any): void {
    this.isAllSelected = event.target.checked;
    console.log("Select All Toggled:", this.isAllSelected);

    if (this.isAllSelected) {
      this.payRunDetailList.forEach(payRunDetail => {
        this.selectedPayRunDetail.add(payRunDetail);
      });
    } else {
      this.selectedPayRunDetail.clear();
    }

    console.log("Selected Pay Run Details:", Array.from(this.selectedPayRunDetail));
  }

  toggleSelection(payRunDetail: PayRunDetail, event: any): void {
      if (event.target.checked) {
          this.selectedPayRunDetail.add(payRunDetail);
      } else {
          this.selectedPayRunDetail.delete(payRunDetail);
      }

      console.log("Toggled Selection for:", payRunDetail);
      console.log("Current Selected Pay Run Details:", Array.from(this.selectedPayRunDetail));
  }

  private getPayRunDetailsByEntityIdAndType() {
    this.isLoadingTable = true; // Show spinner
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.employeeService.getPayRunDetailsByEntityIdAndType(entityId).subscribe(data => {
      this.payRunDetailList = data;

      this.filteredPayRunDetailList = [...data].sort((a, b) => {
        if (a.status === 'PENDING' && b.status !== 'PENDING') return -1;
        if (a.status !== 'PENDING' && b.status === 'PENDING') return 1;
        return 0;
      });

      this.totalPages = Math.ceil(this.filteredPayRunDetailList.length / this.pageSize);
      this.updatePagedData();
      this.updateVisiblePages();
      this.filteredPayRunDetailList.forEach(row => {
        if (row.beamStatus == 'PENDING') {
          this.getBeamStatus(row.beamProcessId);
        }
      });

      this.isLoadingTable = false; // Hide spinner
    }, error => {
      console.error('Error fetching pay run details:', error);
      this.isLoadingTable = false; // Hide spinner
    });
  }

  updatePagedData() {
    const start = this.currentPage * this.pageSize;
    const end = start + this.pageSize;
    this.pagedPayRunDetails = this.filteredPayRunDetailList.slice(start, end);
  }

  goToPage(page: number) {
    if (page >= 0 && page < this.totalPages) {
      this.currentPage = page;
      this.updatePagedData();
      this.updateVisiblePages();
    }
  }

  prevPage() {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.updatePagedData();
      this.updateVisiblePages();
    }
  }

  nextPage() {
    if (this.currentPage + 1 < this.totalPages) {
      this.currentPage++;
      this.updatePagedData();
      this.updateVisiblePages();
    }
  }

  // ✅ Sliding window logic
  updateVisiblePages() {
    const half = Math.floor(this.maxVisiblePages / 2);
    let start = Math.max(0, this.currentPage - half);
    let end = start + this.maxVisiblePages;

  
    if (end > this.totalPages) {
      end = this.totalPages;
      start = Math.max(0, end - this.maxVisiblePages);
    }

    this.visiblePages = [];
    for (let i = start; i < end; i++) {
      this.visiblePages.push(i);
    }
  }


  private getBeamStatus(beamProcessId: string) {
  const entityId = +((localStorage.getItem('entityId')) + '');
  this.employeeService.getBeamStatus(beamProcessId, entityId).subscribe(data => {
    const index = this.payRunDetailList.findIndex(row => row.beamProcessId === beamProcessId);
    if (index !== -1) {
      this.payRunDetailList[index].beamStatus = data.beamStatus;
      this.getPayRunDetailsByEntityIdAndType();
    }
  });
}


  refreshStatus(): void {

  this.getPayRunDetailsByEntityIdAndType();
  this.filteredPayRunDetailList.forEach(row => {
    if (row.beamStatus === 'PENDING') {
      this.getBeamStatus(row.beamProcessId);
    }
  });
}


  search(fromDate: string, toDate: string): void {
    if (!fromDate || !toDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Dates',
        text: 'Please select both dates to proceed.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff',
      });
      return;
    }

    const from = new Date(fromDate);
    const to = new Date(toDate);

    // Parse DB date string: '2025-05-27 00:00:00.0000000'
    this.filteredPayRunDetailList = this.payRunDetailList.filter(detail => {
      const detailTimestamp = detail.payRun?.date;
      if (!detailTimestamp) return false;

      // Replace space with 'T' and remove extra fractional seconds for ISO compatibility
      let isoString = detailTimestamp.replace(' ', 'T').replace(/\.\d+$/, '');
      // If still not valid, fallback to just the date part
      let detailDate = new Date(isoString);
      if (isNaN(detailDate.getTime())) {
        detailDate = new Date(detailTimestamp.split(' ')[0]);
      }

      // Set time to 0:00:00 for from and 23:59:59 for to to include the full days
      const fromDay = new Date(from);
      fromDay.setHours(0,0,0,0);
      const toDay = new Date(to);
      toDay.setHours(23,59,59,999);

      return detailDate >= fromDay && detailDate <= toDay;
    });

    // Reset pagination after filtering
    this.currentPage = 0;
    this.totalPages = Math.ceil(this.filteredPayRunDetailList.length / this.pageSize);
    this.updatePagedData();
    this.updateVisiblePages();

    console.log("Filtered Results:", this.filteredPayRunDetailList);
  }
  
  setDateRange(event: Event) {
    const target = event.target as HTMLSelectElement; // Explicitly cast event.target
    const months = target.value;
  
    if (!months) {
      this.fromDate = '';
      this.toDate = '';
      return;
    }
  
    const monthsAgo = Number(months);
    const today = new Date();
    const pastDate = new Date();
    
    pastDate.setMonth(today.getMonth() - monthsAgo);
  
    this.fromDate = pastDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    this.toDate = today.toISOString().split('T')[0];
  }

  submitSuperannuations() {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.isSubmitting = true; // Disable button while processing
    this.validationErrors = []; // Reset previous errors
    this.validationWarnings = []; // Reset previous warnings

    // Convert selectedPayRunDetail Set to an array
    const selectedPayRunDetailsArray = Array.from(this.selectedPayRunDetail);

    if (selectedPayRunDetailsArray.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'No Superannuation Selected',
            text: 'Please select at least one superannuation to proceed.',
        });
        this.isSubmitting = false;
        return;
    }

    // Validate the selected pay run details
    this.beamServiceService.validateContributions(selectedPayRunDetailsArray, entityId).subscribe({
        next: (responses: ValidationResponse[]) => {  // Correctly typing the response as an array
            console.log('Validation Responses:', responses);

            if (!responses || responses.length === 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'Validation Failed',
                    text: 'No response received from the validation service.',
                });
                this.isSubmitting = false;
                return;
            }

            // Process each validation response
            let shouldSubmit = true;  // Flag to track if submission should proceed

            responses.forEach(response => {
                if (Array.isArray(response.validations)) {
                    this.validationErrors = response.validations.filter(v => v.severity === 'Error');
                    this.validationWarnings = response.validations.filter(v => v.severity === 'Warning');
                }

                // Handle errors
                if (this.validationErrors.length > 0) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Errors',
                        text: 'Please check the errors listed below.',
                    });
                    this.isSubmitting = false;
                    shouldSubmit = false;  // Set flag to false if errors exist
                }

                // Handle warnings
                if (this.validationWarnings.length > 0 && shouldSubmit) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Validation Warnings',
                        text: 'There are warnings you should review before proceeding.',
                        showCancelButton: true,
                        confirmButtonText: 'Proceed Anyway',
                        cancelButtonText: 'Cancel',
                    }).then((result) => {
                        if (result.isConfirmed) {
                            this.submitContributions(selectedPayRunDetailsArray); // Proceed with submission
                        } else {
                            this.isSubmitting = false; // Cancel submission
                        }
                    });
                }
            });

            // If there were no errors or warnings, submit directly
            if (shouldSubmit && this.validationErrors.length === 0 && this.validationWarnings.length === 0) {
                this.submitContributions(selectedPayRunDetailsArray);
            }

        },
        error: (err) => {
            console.error('Error:', err);
            Swal.fire({
                icon: 'error',
                title: 'Submission Failed',
                text: 'An error occurred while submitting contributions. Please try again.',
            });
            this.isSubmitting = false;
        }
    });
}




/**
 * Function to submit the selected pay run details
 */
submitContributions(selectedPayRunDetailsArray: PayRunDetail[]) {
  const entityId = +((localStorage.getItem('entityId')) + "");
    this.beamServiceService.submitContributions(selectedPayRunDetailsArray,entityId).subscribe({
        next: (response) => {
            Swal.fire({
                icon: 'success',
                title: 'Submission Successful',
                text: 'Superannuation contributions submitted successfully!',
            });

            // Clear selected pay run details after successful submission
            this.selectedPayRunDetail.clear(); // Clear the selection
            this.getAndProcessNotifications(); // Refresh the list of pay run details
            this.isSubmitting = false;
        },
        error: (err) => {
            console.error('Submission Error:', err);
            Swal.fire({
                icon: 'error',
                title: 'Submission Failed',
                text: 'An error occurred while submitting the contributions. Please try again.',
            });
            this.isSubmitting = false;
        }
    });
}
cancelProcessed(beamProcessId: string): void {
  const entityId = +((localStorage.getItem('entityId')) + '');
  this.beamServiceService.cancel(beamProcessId, entityId).subscribe(
    (_response: any) => {
      console.log('Cancel API response:', _response);

      if (_response === 'OK') {
        Swal.fire({
          title: 'Success!',
          text: 'Superannuation cancelled successfully!',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then(() => {
          this.getPayRunDetailsByEntityIdAndType();
        });
      } else {
        Swal.fire({
          title: 'Error!',
          text: _response,
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    },
    (_error: any) => {
      console.error('Cancel API error:', _error);
      Swal.fire({
        title: 'Error!',
        text: 'Failed to cancel superannuation. Please try again.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#be0032',
      });
    }
  );
}


  fetchNotifications() {
    this.isLoadingNotifications = true;
    const entityId = +(localStorage.getItem('entityId') + '');
    this.beamServiceService.getNotifications(entityId).subscribe({
      next: (response: any[]) => {
        console.log('Notifications:', response);

        this.notifications = response.map(notification => {
          let payloadMessage = '';
          if (notification.payload) {
            try {
              const payload = JSON.parse(notification.payload);
              payloadMessage = payload.statusMessage || 'No status message available';
            } catch (error) {
              console.error('Error parsing payload:', error);
              payloadMessage = 'Invalid payload format';
            }
          }

          return {
            ...notification,
            parsedPayloadMessage: payloadMessage,
          };
        });

        this.isLoadingNotifications = false; // Hide notification spinner
      },
      error: (err) => {
        console.error('Error fetching notifications:', err);
        this.isLoadingNotifications = false; // Hide notification spinner
      }
    });
  }

  getAndProcessNotifications() {
    this.isLoadingTable = true; // Show table spinner during notification fetch
    const entityId = +(localStorage.getItem('entityId') + '');
    this.beamServiceService.getAndProcessNotifications(entityId).subscribe({
      next: (response: any[]) => {
        console.log('Notifications:', response);

        this.notifications = response.map(notification => {
          let payloadMessage = '';
          if (notification.payload) {
            try {
              const payload = JSON.parse(notification.payload);
              payloadMessage = payload.statusMessage || 'No status message available';
            } catch (error) {
              console.error('Error parsing payload:', error);
              payloadMessage = 'Invalid payload format';
            }
          }

          return {
            ...notification,
            parsedPayloadMessage: payloadMessage,
          };
        });


      this.getPayRunDetailsByEntityIdAndType(); // Run after processing notifications
      },
      error: (err) => {
        console.error('Error fetching notifications:', err);
        this.isLoadingTable = false; // Hide table spinner
      }
    });
  }

}

