
<!-- Customer creation Popup -->
<div
  class="modal fade"
  id="customerPopUpModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="simpleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" style="max-width: 740px">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="simpleModalLabel">Add New Customer</h5>
        <div class="icon-group ms-auto">
          <i
            class="bi bi-x-circle close-icon"
            #closeCustomerPopUp
            data-bs-dismiss="modal"
            aria-label="Close"
            title="Close"
          ></i>
        </div>
      </div>

      <div class="modal-body">
        <form
          #cuspop="ngForm"
          name="cuspop"
          (ngSubmit)="cuspop.form.valid && onSubmitCustomerForm()"
          class="row g-1"
          novalidate="feedback-form"
          (keydown)="preventSubmit($event)"
        >
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="partner-type">Business Partner Type</label>
                <select
                  class="input-style"
                  id="partner-type"
                  [(ngModel)]="
                    businessPartner.businessPartnerTypeId.businessPartnerTypeId
                  "
                  name="businessPartnerType"
                  style="
                    border: 1px solid #c7c7c7;
                    border-radius: 8px;
                    width: 100%;
                    height: 43.41px;
                  "
                  disabled
                >
                  <option value="" selected disabled>
                    Select Partner Type
                  </option>
                  <option
                    *ngFor="let customer of businessPartnerType"
                    [value]="customer.businessPartnerTypeId"
                  >
                    {{ customer.businessPartnerType }}
                  </option>
                </select>

                <div
                  *ngIf="
                    cuspop.submitted &&
                    cuspop.controls['businessPartnerType'].invalid
                  "
                  class="text-danger"
                ></div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6" style="width: 100%;">
              <div class="form-group">
                <label for="bp-name">Business Partner Name <span class="text-danger">*</span></label>
                <input
                  type="text"
                  id="bp-name"
                  [(ngModel)]="businessPartner.bpName"
                  name="bpName"
                  required
                  #bpName="ngModel"
                />

                <div
                  *ngIf="cuspop.submitted && bpName.invalid"
                  class="text-danger"
                >
                  <div *ngIf="bpName.errors?.['required']">
                    Business Partner Name is required.
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="email">Email</label>
                <input
                  type="email"
                  id="email"
                  [(ngModel)]="businessPartner.email"
                  name="email"
                  pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                  #email="ngModel"
                />

                <div *ngIf="cuspop.submitted && email.invalid" class="text-danger">
                  <!-- <div *ngIf="email.errors?.['required']">
                    Email is required.
                  </div> -->
                  <div *ngIf="email.errors?.['pattern']">
                    Invalid email format.
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <label for="contactPhoneNumber">Contact Number</label>
                <input
                  type="text"
                  id="contactPhoneNumber"
                  [(ngModel)]="businessPartner.contactPhoneNumber"
                  name="contactPhoneNumber"
                  #contactPhoneNumber="ngModel"
                  pattern="^\+?[0-9\s-]{7,15}$"
                />
                <div *ngIf="(contactPhoneNumber.dirty || cuspop.submitted) && contactPhoneNumber.invalid" class="text-danger">
                  <div *ngIf="contactPhoneNumber.errors?.['pattern']">
                    Invalid contact number format.
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="abn-number"
                  >ABN Number <small>(Optional)</small></label
                >
                <input
                  type="text"
                  id="abn-number"
                  [(ngModel)]="businessPartner.abnNumber"
                  name="abnNumber"
                />
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="acn-number">ACN Number</label>
                <input
                  type="text"
                  id="acn-number"
                  [(ngModel)]="businessPartner.acnNumber"
                  name="acnNumber"
                />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="default-payment-terms"
                  >Default Payment Terms <small>(Optional)</small></label
                >
                <select
                  class="input-style"
                  id="default-payment-terms"
                  [(ngModel)]="businessPartner.defaultPaymentTerms"
                  name="defaultPaymentTerms"
                  style="
                    border: 1px solid #c7c7c7;
                    border-radius: 8px;
                    width: 100%;
                    height: 43.41px;
                  "
                >
                  <option value="" disabled selected>
                    Select default payment terms
                  </option>
                  <option value="net30">Net 30</option>
                  <option value="net15">Net 15</option>
                  <option value="net10">Net 10</option>
                  <option value="dueOnReceipt"> Due on Receipt</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="business-address">Business Address</label>

                <input
                type="text"
                id="business-address"
                [(ngModel)]="businessPartner.businessAddress"
                name="businessAddress"
                (keyup)="checkBusinessAddress()"
                class="form-control"
              />
              <ul
                *ngIf="suggestedAddresses.length > 0"
                class="dropdown-menu show"
                style="position: absolute; width: 100%; max-height: 200px; overflow-y: auto; z-index: 1000;"
              >
                <li
                  *ngFor="let address of suggestedAddresses"
                  class="dropdown-item"
                  (click)="selectAddress(address)"
                  style="cursor: pointer;"
                >
                  {{ address.full_address }}
                </li>
              </ul>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="form-group">
                <label for="delivery-address">Delivery Address</label>
                <input
                type="text"
                id="delivery-address"
                [(ngModel)]="businessPartner.deliveryAddress"
                name="deliveryAddress"
                (keyup)="checkDeliveryAddress()"
                class="form-control"
                [disabled]="isDeliveryAddressSynced"
              />
              <ul
                *ngIf="suggestedDeliveryAddresses.length > 0"
                class="dropdown-menu show"
                style="position: absolute; width: 100%; max-height: 200px; overflow-y: auto; z-index: 1000;"
              >
                <li
                  *ngFor="let address of suggestedDeliveryAddresses"
                  class="dropdown-item"
                  (click)="selectDeliveryAddress(address)"
                  style="cursor: pointer;"
                >
                  {{ address.full_address }}
                </li>
              </ul>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group"></div>
              </div>
            <div class="col-md-6">
                 <div class="form-check-address ">
              <label [class.text-muted]="!businessPartner.businessAddress || businessPartner.businessAddress.trim() === ''">
                <input
                type="checkbox"
                (change)="syncDeliveryAddress($event)"
                [disabled]="!businessPartner.businessAddress || businessPartner.businessAddress.trim() === ''"
                />
                Same as Business Address
              </label>
            </div>
            </div>
          </div>
          
          <hr />
          <h3>Additional</h3>
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="bank-account-name">Bank Account Name</label>
                <input
                  type="text"
                  id="bank-account-name"
                  [(ngModel)]="businessPartner.bankAccountName"
                  name="bankAccountName"
                />
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="bsb">BSB</label>
                <input
                  type="text"
                  id="bsb"
                  [(ngModel)]="businessPartner.bsb"
                  name="bsb"
                />
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="bank-account-number">Bank Account Number</label>
                <input
                  type="text"
                  id="bank-account-number"
                  [(ngModel)]="businessPartner.accountNumber"
                  name="accountNumber"
                />
              </div>
            </div>
          </div>
          <!-- Footer Buttons -->
          <div class="popup-footer">
            <div class="left-section">
              <a *ngIf="showUpdateLink" href="/business-partner" class="cancel-btn1" style="padding: 10px 20px;">Update Customer</a>
            </div>  
            <div class="right-section">
              <button type="button" class="cancel-btn1" #closeCustomerPopUp data-bs-dismiss="modal">Cancel</button>
              <button type="submit" class="add-btn1">Add</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>