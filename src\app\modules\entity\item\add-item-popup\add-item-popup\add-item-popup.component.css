
body {
  font-family: Arial, sans-serif;
  background-color: transparent;
  margin: 0;
  padding: 0;
}

.container {
  width: 100%;
  max-width: 1200px; 
  margin: 0 auto;
  padding: 10px;
  background-color: transparent;
}

/* Heading styles */

.heading {
  display: flex;
  background-color: transparent;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: none;
  border: none;
  gap: 5px;
  
}

.heading h3 {
  flex: 1;
  margin-bottom: 0;
  font-family: Inter;
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  color: #4262ff;
  top: 264px;
}

.heading .transparent-button {
  top: 256px;
  border: 1px solid #4262ff;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 12px;
  font-weight: bold;
  background-color: white;
  color: #4262ff;
  font-family: Inter;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

.heading .transparent-button:hover {
  background-color: #4262ff;
  color: white;
}

.bd {
  border: 2px solid #cec9c980;
  border-radius: 12px;
  margin: 0;
  width: 100%;
}
/* Form styles */

.form-section {
  display: flex;
  font-family: Arial, sans-serif;
  flex-direction: column;
  padding: 20px;
  background-color: #f7f7f7; 
}

.form-row {
  display: flex;
  gap: 32px;
  padding-right: 20px;
  margin-bottom: 5px;
}

.form-group {
  flex: 1;
}

.input-style {
  height: 49px;
  top: 656px;
  left: 111px;
  padding: 10px;
  font-size: 14px;
}

.create-customer-container {
  display: flex;
  justify-content: flex-end;
}

label {
  display: block;
  font-weight: bold;
  color: #333;
}



#checkBox {
  display: inline-block;
  vertical-align: middle;
  margin-left: 20px;
  
}

#markas-1 {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0px;
  
}

.update-link {
  font-size: 15px;
  font-family: Segoe UI, sans-serif;
  font-weight: 600;
  color: #4a4ae2;
  margin-top: 20px;
  margin-left: 10px;
  display: inline-block; 
  white-space: nowrap; 
  text-align: left;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.form-group {
  margin-bottom: 10px;
  text-align: left;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 25.41px;
  color: #444343;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: normal;
}

.form-group input {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  font-size: 14px;
}

.popup-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.left-section {
  display: flex;
  justify-content: flex-start;
}

.right-section {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-btn {
  padding: 10px 60px;
  border-radius: 17px;
  cursor: pointer;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  background: #ffffff;
  color: #6822ff;
  border: 2px solid #4262ff;
  margin-top: 15px;
  /* margin-left: 250px; */
}

.cancel-btn:hover {
  background: #4262ff;
  color: white;
}

.add-btn {
  padding: 10px 70px;
  border: none;
  border-radius: 17px;
  cursor: pointer;
  font-family: Segoe UI, sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
  margin-top: 15px;
}

.add-btn:hover {
  background: linear-gradient(to right, #512ca2, #4262ff);
}

.form-check {
  display: inline-flex;
  align-items: center;
}

.form-check-input {
  margin-right: 5px;
  margin-left: 5px;
}

.form-check-label {
  margin-right: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  text-transform: capitalize;
}

.form-check-tax {
  display: inline-flex;
  align-items: center;
}

.form-check-tax-input {
  margin-right: 5px;
}

.form-check-tax-label {
  margin-right: 2px;
}

.close-icon {
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.close-icon:hover {
  color: #000;
}

.table-responsive {
  overflow-x: auto;
}

.new-item {
  background-color: #4262ff;
  color: white;
}

.new-item:hover {
  background-color: #512ca2;
}

@media (max-width: 768px) {
  .heading {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .heading h3 {
    font-size: 26px;
    margin-bottom: 10px;
  }

  .heading .button-group {
    width: 100%;
  }

  .heading .transparent-button {
    width: 100%;
    text-align: center;
  }

  .form-row {
    flex-direction: column;
    gap: 20px;
    padding-right: 0;
  }

  .form-group {
    width: 100%;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
  }

  #tradingName {
    background-color: #fff;
  }

  .form-dataInput {
    display: flex;
    flex-direction: column;
  }

  .InputBoxes {
    flex-direction: column;
  }

  .search-bar {
    justify-content: flex-start;
    padding-left: 0;
    margin-bottom: 15px;
  }

  .search-bar input.input-style {
    width: 100%;
  }

  .table-section {
    padding-left: 0;
    padding-right: 0;
    overflow-x: auto;
  }

  table {
    min-width: 800px; 
  }

  .notes-totals-section {
    flex-direction: column;
    gap: 15px;
  }

  .notes-section,
  .totals-section {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    padding: 20px 15px;
    height: auto;
  }

  .totals-row {
    width: 100%;
    flex-direction: row;
    align-items: center;
    gap: 10px;
  }

  .totals-row1,
  .totals-row2 {
    width: auto;
    flex: 1;
    text-align: left;
  }

  .totals-row1 {
    text-align: left;
    margin-right: 10px;
  }

  .totals-row2 {
    text-align: right;
  }

  .popup-footer {
    flex-direction: column;
    gap: 10px;
  }


  .cancel-btn,
  .add-btn {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .update-link {
    text-align: center;
    margin-left: 0;
  }

  .form-check {
    flex-direction: column;
    align-items: flex-start;
  }

  .form-check-input {
    margin-right: 0;
    margin-bottom: 5px;
  }

  .form-check-label {
    margin-right: 0;
  }
}

@media (max-width: 480px) {
  .heading h3 {
    font-size: 22px;
  }

  .heading .transparent-button {
    font-size: 16px;
    padding: 8px 16px;
  }

  .form-group input,
  .input-style {
    font-size: 14px;
    padding: 8px;
  }

  .cancel-btn,
  .add-btn {
    font-size: 14px;
    padding: 8px 16px;
  }

  .update-link {
    font-size: 14px;
  }
}
