<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
    <!-- Title -->

    <div class="container">
        <div class="actions">
            <h1>Payment Receipts</h1>

        </div>
        <div class="search-create">
            <button class="btn btn-outline-primary" (click)="createPaymentReceipt()">
            Create Payment Receipt
        </button>
        <button (click)="exportToExcel()" class="export-btn">Export to Excel</button>

        </div>
        <div>
        </div>

        <div class="Card">
            <!-- Filter and Search Section -->
            <div class="row1">
                <!-- Input for number, reference -->
                <div class="row1_col1">
                    <label for="search-input">Receipts No. or Customer Name</label>
                    <div class="input-container">
                        <input type="text" class="search-input" id="search-input" [(ngModel)]="searchTerm" />
                        <i class="bi bi-search"></i>
                    </div>
                </div>

                <div class="row1_col3">
                    <label for="StartDate">Start Date</label>
                    <input type="date" class="date-picker" pattern="dd/mm/yyyy" id="StartDate" [(ngModel)]="startDate" />
                </div>

                <div class="row1_col4">
                    <label for="EndDate">End Date</label>
                    <input type="date" class="date-picker" id="EndDate" [(ngModel)]="endDate" />
                </div>
            </div>

            <div class="row2">
                <div class="row2_col3">
                    <button type="button" class="secondary-button" (click)="resetFilters()">Reset</button>
                </div>
                <div class="row2_col1">
                    <button type="button" class="primary-button" (click)="filterPaymentReceiptsHeads()">Search</button>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <!-- Table headers -->
                <thead>
                    <tr class="table-head">
                        <th scope="col" class="valueCheckbox"><input type="checkbox" /></th>
                        <th scope="col" class="valuehead">Receipt Number</th>
                        <th scope="col" class="valuehead">Invoice Number(s)</th>
                        <th scope="col" class="valuehead">Customer Name</th>
                        <th scope="col" class="valuehead">Document Date</th>
                        <th style="text-align: right;" scope="col" class="valuehead">Total Paid Amount</th>
                        <th style="text-align: center;" scope="col" class="valuehead">Actions</th>

                    </tr>
                </thead>
                <!-- Table body -->
                <tbody>
                    <tr *ngFor="let receipt of filteredPaymentReceiptsHeads">
                        <td class="valueCheckbox"><input type="checkbox" [(ngModel)]="receipt.selected" /></td>
                        <td class="value">{{ receipt.paymentNumber }}</td>
                        <td class="value">{{ receipt.invoiceNumbers }}</td>
                        <td class="value">{{ receipt.customerName }}</td>
                        <td class="value">{{ receipt.documentDate | date:'dd MMM yyyy' }}</td>
                        <td style="text-align: right;" class="value">{{ receipt.totalPaidAmount | currency}}</td>

                        <td style="text-align: center;" class="value">
                            <button
                             (click)="deleteCreditNote(receipt.paymentReceiptId,receipt.paymentNumber)"
                             class="btn btn-danger btn-sm"
                              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
                              data-bs-toggle="tooltip"
                                [disabled]="receipt.isLocked"
                              title="Delete Receipt">
                            <i class="ri-delete-bin-line"
                             style="color: #FF0000;"
                             [style.color]=" receipt.isLocked
                             ? '#aaa'
                             : '#ff0000'
                             "></i>

                        </button>



                        </td>

                    </tr>
                </tbody>
            </table>
        </div>
    </div>
