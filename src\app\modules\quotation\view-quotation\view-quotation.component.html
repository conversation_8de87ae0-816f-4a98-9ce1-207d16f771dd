<app-sales-navigation></app-sales-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
    <form #f="ngForm" class="row g-1" novalidate="feedback-form">
        <div class="heading">
            <h3>Sales Quote -
                    <span
      [ngClass]="{
        'text-danger': quotationData.status === 'Expired',
      }"
    >
      {{ quotationData.status }}
    </span></h3>
            <div class="btn-group" [class.show]="isDropdownOpen">
                <button type="button" class="btn btn-secondary dropdown-toggle gradient-btn" data-bs-toggle="dropdown"
                    aria-expanded="false" (click)="toggleDropdown()">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen">
                    <li>
                        <a class="dropdown-item" data-bs-target="#sendQuoteModal1" data-bs-toggle="modal"
                            (click)="loadEmailTemplate()">Send</a>
                    </li>
                    <li><a class="dropdown-item" (click)="handleReviseQuotation(quotationData.quoteId)">Revise Quote</a>
                    </li>
                    <li><a class="dropdown-item" (click)="editQuotation(quotationData.quoteId)">Edit Quote</a></li>
                    <li><a class="dropdown-item" (click)="handleCopyFromQuote(quotationData.quoteId)"> Copy Quote</a>
                    </li>
                    <li><a class="dropdown-item" (click)="handleCreateInvoice(quotationData.quoteId)">Create Invoice</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="bd">
            <div class="form-section">

                <!-- First row: Quotation Number and Valid Until -->
                <div class="form-row" style="display: flex; justify-content: space-between;">
                    <!-- Quotation Number -->
                    <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1;">
                        <div class="form-dataInput" style="display: flex;">
                            <label for="quotationNo" id="quotation" style="margin-right: 10px; white-space: nowrap;">Quotation Number</label>
                            <input class="input-style" type="text" id="quotationNo" [(ngModel)]="quotationData.quoteNumber"
                                name="quoteNumber" #quoteNumber="ngModel" readonly />
                        </div>
                    </div>

                    <!-- Valid Until -->
                    <div class="form-group" style="display: flex; flex-grow: 1;">
                        <label for="validityUntil" style="margin-right: 45px; white-space: nowrap;">Valid Until</label>
                        <input class="input-style" type="date" id="validityUntil"
                            [(ngModel)]="quotationData.validUntilDate" name="validityUntil" readonly />
                    </div>
                </div>

                <!-- Second row: Customer and Quotation Date -->
                <div class="form-row" style="display: flex; justify-content: space-between;">
                    <!-- Customer -->
                    <div class="form-group" style="flex-grow: 1;">
                        <div style="display: flex;" class="form-dataInput">
                            <label for="customer" style="margin-right: 70px; white-space: nowrap;">Customer</label>
                            <select class="form-select" id="customer" [(ngModel)]="quotationData.businessPartnerId"
                                name="customerName" disabled>
                                <option value="" selected disabled>Select Customer</option>
                                <option *ngFor="let customer of customers" [value]="customer.businessPartnerId">
                                    {{ customer.bpName }}
                                </option>
                            </select>
                        </div>
                    </div>

                    <!-- Quotation Date -->
                    <div class="form-group" style="display: flex; flex-grow: 1;">
                        <label for="quotationDate" style="margin-right: 10px; white-space: nowrap;">Quotation
                            Date</label>
                        <input class="input-style" type="date" id="quotationDate" [(ngModel)]="quotationData.quoteDate"
                            name="quotationDate" readonly />
                    </div>

                </div>

                <!-- Third row: Trading Name -->
                <div class="form-row" style="display: flex; justify-content: space-between;">
                    <!-- Trading Name -->
                    <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1;">
                        <div style="display: flex;" class="form-dataInput">
                            <label for="tradingName" style="margin-right: 40px; white-space: nowrap;">Trading Name</label>
                            <select class="form-select" id="tradingName" [(ngModel)]="quotationData.entityTradingNameId"
                                name="tradingName" [disabled]="entityTradingNames.length <= 1" readonly>
                                <option value="" selected disabled>Select Trading Name</option>
                                <option *ngFor="let tradingName of entityTradingNames"
                                    [value]="tradingName.entityTradingNameId">
                                    {{ tradingName.tradingName }}
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group" style="display: flex; flex-grow: 1;"></div>
                </div>
            </div>

            <div class="table-section">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 35%;">Item</th>
                                <th style="width: 25%;">Description</th>
                                <th style="width: 7%;">Quantity</th>
                                <th style="width: 17%;">Unit Price</th>
                                <th style="width: 5%;">Discount</th>
                                <th style="width: 8%; text-align: center;">Disc. Type</th>
                                <th style="width: 8%;">{{ businessEntity.countryId.defaultTaxType }}({{
                                    businessEntity.countryId.defaultTaxRate }}%)</th>
                                <th style="width: 5%; text-align: right;">Tax</th>
                                <th style="width: 14%; text-align: right; padding-right: 0;">Amount ({{
                                    businessEntity.countryId.defaultCurrency}})</th>
                                <th style="width: 3%;"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let detail of quotationData.details; let i = index">

                                <td>
                                    <!-- ng-select will only show if itemCode is not 'SISSERVICE' -->
                                    <ng-select *ngIf="quotationData.details[i].salesItemId?.itemCode !== 'SISSERVICE'"
                                        name="salesItem-{{ i }}" [appendTo]="'body'" [items]="allSalesItems"
                                        bindLabel="description" [(ngModel)]="quotationData.details[i].salesItemId"
                                        [searchable]="true" [clearable]="false" [disabled]="true"
                                        placeholder="Select item">
                                        <ng-template ng-option-tmp let-item="item">
                                            {{ item.itemCode }} - {{ item.description }}
                                        </ng-template>
                                    </ng-select>
                                </td>


                                <td>
                                    <input type="text" [(ngModel)]="quotationData.details[i].description"
                                        name="description-{{ i }}" class="form-control" placeholder="Enter description"
                                        [disabled]="quotationData.details[i].salesItemId && quotationData.details[i].salesItemId.itemCode !== 'SISSERVICE' && quotationData.details[i].salesItemId.itemCode !== ''"
                                        readonly />
                                </td>

                                <td>
                                    <input type="number" [(ngModel)]="quotationData.details[i].quantity"
                                        name="quantity-{{ i }}" class="form-control" min="0" readonly />
                                </td>

                                <td>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="number" [(ngModel)]="quotationData.details[i].unitPrice"
                                            name="unitPrice-{{ i }}" class="form-control" min="0" step="0.01"
                                            [disabled]="quotationData.details[i].salesItemId && quotationData.details[i].salesItemId.itemCode !== 'SISSERVICE' && quotationData.details[i].salesItemId.itemCode !== ''"
                                            readonly />
                                    </div>
                                </td>

                                <td>
                                    <input type="number" [(ngModel)]="quotationData.details[i].discount"
                                        name="discount-{{ i }}" class="form-control" min="0" readonly />
                                </td>

                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="discountType{{ i }}"
                                            id="percent{{ i }}" value="B"
                                            [(ngModel)]="quotationData.details[i].discountType" disabled />
                                        <label class="form-check-label" for="percent{{ i }}">%</label>
                                        <input class="form-check-input" type="radio" name="discountType{{ i }}"
                                            id="dollar{{ i }}" value="$"
                                            [(ngModel)]="quotationData.details[i].discountType" disabled />
                                        <label class="form-check-label" for="dollar{{ i }}">$</label>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-check-tax">
                                        <label>
                                            Incl.GST
                                            <input type="checkbox"
                                                [(ngModel)]="quotationData.details[i].taxApplicability"
                                                name="taxApplicable-{{ i }}" [disabled]="true"
                                                style="margin-left: 5px;" />
                                        </label>
                                    </div>
                                </td>

                                <td style="text-align: right">{{ quotationData.details[i].tax | currency }}</td>
                                <td style="text-align: right">{{ quotationData.details[i].amount | currency }}</td>

                                <td>

                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- note text area -->
            <div class="main-row-note">
                <div class="notes-totals-section">
                    <div class="notes-section">
                        <label for="notes">Notes</label>
                        <textarea id="notes" class="form-control" [(ngModel)]="quotationData.note" name="note"></textarea>
                    </div>

                    <div class="totals-section" style="margin-top: 25px;">
                        <div class="totals-row">
                            <span class="totals-row1">Sub Total Amount </span>
                            <span class="totals-row2">{{ quotationData.subTotal | currency }}</span>
                        </div>
                        <div class="totals-row">
                            <span class="totals-row1">Total Discount</span>
                            <span class="totals-row2">{{calculateTotalDiscount()| currency}}</span>
                        </div>
                        <div class="totals-row">
                            <span class="totals-row1">Total {{ businessEntity.countryId.defaultTaxType }} </span>
                            <span class="totals-row2">{{ quotationData.totalGst | currency }}</span>
                        </div>
                        <div class="totals-row">
                            <strong class="totals-row1">Grand Total</strong>
                            <strong class="totals-row2">{{ quotationData.grandTotal | currency }}</strong>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>

<!-- Send Quote -->
<div class="modal fade" id="sendQuoteModal1" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 640px; max-height: 1080px;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="simpleModalLabel">Send Quotes</h5>
                <button type="button" class="btn-close custom-close-btn" aria-label="Close" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body">
                <form #sendQuote="ngForm" name="sendQuote" (ngSubmit)="sendQuote.form.valid && sendSelectedQuotes()" novalidate="feedback-form">
                    <div class="form-group">
                        <label for="recipientEmail">Recipient Email</label>
                        <input type="email" 
                         id="TempRecipientEmail2"
                         name="TempRecipientEmail"
                         [(ngModel)]="TempRecipientEmail" 
                         placeholder="Enter a temporary email or  loaded email"
                         class="form-control" 
                         required
                         email
                         #TempRecipientEmailRef="ngModel"
                          />
                        <p *ngIf="!TempRecipientEmail">Using stored email: {{ recipientEmail }}</p>
                        <!--<div *ngIf="sendQuote.submitted && sendQuote.controls['TempRecipientEmail'].invalid" class="text-danger">
                            <div *ngIf="sendQuote.controls['TempRecipientEmail'].errors?.['required']">Email is required.</div>
                            <div *ngIf="sendQuote.controls['TempRecipientEmail'].errors?.['email']">Enter a valid email address.</div>
                        </div>-->
                        <div *ngIf="sendQuote.submitted && TempRecipientEmailRef.invalid" class="text-danger">
                        <div *ngIf="TempRecipientEmailRef.errors?.['required']">Email is required.</div>
                        <div *ngIf="TempRecipientEmailRef.errors?.['email']">Enter a valid email address.</div>
                        </div>

                    </div>

                    <div class="class-name">
                        <div class="form-group">
                            <label for="subject">Subject</label>
                            <input type="text" id="subject2" name="subject" [(ngModel)]="subject" required />
                            <div *ngIf="sendQuote.submitted && sendQuote.controls['subject'].invalid" class="text-danger">
                                <div *ngIf="sendQuote.controls['subject'].errors?.['required']">Subject is required.</div>
                            </div>
                        </div>
                    </div>

                    <div class="class-name">
                        <div class="form-group">
                            <label for="message">Message</label>
                            <textarea id="message2" name="message" rows="4"
                                style="min-height: 350px; height: fit-content; width: 100%;" [(ngModel)]="content"
                                required></textarea>
                            <div *ngIf="sendQuote.submitted && sendQuote.controls['message'].invalid" class="text-danger">
                                <div *ngIf="sendQuote.controls['message'].errors?.['required']">Message is required.</div>
                            </div>
                        </div>
                    </div>

                    <div class="popup-footer">
                        <button type="submit" class="add-btn" [disabled]="isSending">
                            {{ isSending ? 'Sending...' : 'Send' }}
                        </button>
                        <button type="button" #closeSendQuote class="cancel-btn" data-bs-dismiss="modal">Close</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>