<app-admin-navigation></app-admin-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="">
  <!-- Tab Navigation -->
  <div class="search-create">
    <ul class="nav nav-tabs" id="myTab" role="tablist">
      <li class="nav-item" role="presentation">
        <button
          class="modern-tab-btn"
          id="entity-tab"
          data-bs-toggle="tab"
          data-bs-target="#entity"
          type="button"
          role="tab"
          aria-controls="entity"
          aria-selected="true"
        >
          <i class="bi bi-building me-2"></i>
          Business Entity
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="modern-tab-btn-outline"
          id="template-tab"
          data-bs-toggle="tab"
          data-bs-target="#template"
          type="button"
          role="tab"
          aria-controls="template"
          aria-selected="false"
        >
          <i class="bi bi-envelope-paper me-2"></i>
          Email Template
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button type="button" class="modern-tab-btn" (click)="connectStripe()">
          <i class="bi bi-credit-card-2-front me-2"></i>
          Connect Stripe account
        </button>
      </li>
      <li class="nav-item" role="presentation" *ngIf="isPremiumOrEnterpriseUser">
        <button
          type="button"
          class="modern-tab-btn"
          data-bs-toggle="modal"
          data-bs-target="#basiqModal"
        >
          <i class="bi bi-bank2 me-2"></i>
          Connect Basiq Account
        </button>
      </li>
      <li class="nav-item" role="presentation"  *ngIf="isPremiumOrEnterpriseUser">
        <button type="button" class="modern-tab-btn" (click)="connectBeam()">
          <i class="bi bi-lightning-charge me-2"></i>
          Connect Beam account
        </button>
      </li>
      <!-- <li class="nav-item" role="presentation">
        <button
          type="button"
          class="btn btn-info"
          data-bs-toggle="modal"
          data-bs-target="#transactionsModal"
          (click)="basiqTransactions()"
        >
          Show Transactions
        </button>
      </li> -->
      <!-- <li class="nav-item" role="presentation">
        <button
          type="button"
          class="btn btn-success"
          data-bs-toggle="modal"
          data-bs-target="#accountsModal"
          (click)="basiqAccounts()"
        >
          Show Accounts
        </button>
      </li> -->
    </ul>
  </div>

  <!-- Tab Content -->
  <div class="tab-content" id="myTabContent">
    <!-- Business Entity Tab Content -->
    <div
      class="tab-pane fade show active"
      id="entity"
      role="tabpanel"
      aria-labelledby="entity-tab"
    >
      <div class="header">
        <h3>Edit Business Entity</h3>
      </div>
      <form #businessEntityForm="ngForm" class="body-form">
        <div class="main-form">
          <div class="middle-form">
            <div class="form-headline">
              <h6 class="subhead">Business Entity Details</h6>
            </div>

            <div class="form-body">
              <div class="row" style="margin-bottom: 30px;">
                <div class="col-lg-4 col-sm-12 detail">
                  <label class="input-headline">ABN Number</label>
                  <input
                    type="text"
                    class="form-control abn-input"
                    name="abn"
                    [(ngModel)]="entity.abn"
                    aria-label="Recipient's username"
                    aria-describedby="button-addon2"
                    readonly
                  />
                  <!--<button
                    class="btn btn-outline-secondary abn-button"
                    type="button"
                    id="button-addon2"
                  >
                    Lookup ABN
                  </button>-->
                </div>

                <div class="col-lg-4 col-sm-12 detail">
                  <label class="input-headline">Business Structure</label>
                  <select
                    class="form-select en-input"
                    id="businessStructure"
                    name="businessStructure"
                    [(ngModel)]="entity.businessStructure"
                    required
                  >
                    <option value="Sole trader">Sole trader</option>
                    <option value="Company">Company</option>
                    <option value="Partnership">Partnership</option>
                    <option value="Trust">Trust</option>
                    <option value="Co-operative">Co-operative</option>
                    <option value="Indigenous corporation">Indigenous corporation</option>
                  </select>
                </div>

                <div class="col-lg-4 col-sm-12 detail">
                  <label class="input-headline">Entity Name</label>
                  <input
                    type="text"
                    class="form-control abn-input"
                    name="en"
                    [(ngModel)]="entity.entityName"
                    aria-label="Recipient's username"
                    aria-describedby="button-addon2"
                    readonly
                  />
                </div>
              </div>

              <div class="row" style="margin-bottom: 30px;">
                <div class="col-lg-8 col-sm-12 detail position-relative">
                  <label class="input-headline">Business Address</label>
                  <input
                    type="text"
                    class="form-control abn-input"
                    [(ngModel)]="entity.businessAddress"
                    name="businessAddress"
                    (keyup)="checkBusinessAddress()"
                    aria-describedby="button-addon2"
                  />
                  <ul
                    *ngIf="suggestedAddresses.length > 0"
                    class="dropdown-menu show"
                    style="position: absolute; width: 100%; max-height: 200px; overflow-y: auto; z-index: 1000;"
                  >
                    <li
                      *ngFor="let address of suggestedAddresses"
                      class="dropdown-item"
                      (click)="selectAddress(address)"
                      style="cursor: pointer;"
                    >
                      {{ address.full_address }}
                    </li>
                  </ul>
                </div>
                <div class="col-lg-4 col-sm-12 detail"></div>
                <div class="col-lg-4 col-sm-12 detail"></div>
              </div>

              <div class="row" style="margin-bottom: 30px;">
                <div class="col-lg-4 col-sm-12 detail">
                  <label class="input-headline" for="defaultCreditPeriod"
                    >Default Credit Period (days)</label
                  >
                  <input
                    type="number"
                    id="defaultCreditPeriod"
                    name="defaultCreditPeriod"
                    [(ngModel)]="entity.defaultCreditPeriod"
                    class="form-control abn-input"
                    required
                  />
                </div>

                <div  class="col-lg-4 col-sm-12 detail">
                  <label class="input-headline">BAS Period </label>
                  <select
                    class="form-select en-input"
                    id="basPeriod"
                    name="basPeriod"
                    type="text"
                    [(ngModel)]="entity.basPeriod"
                    required
                  >
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="annually">Annually</option>
                  </select>
                </div>
                <div class="col-lg-4 col-sm-12 detail">
                   <div class="check-box">
                    <input
                    type="checkbox"
                    name="gst-registration"
                    [checked]="entity.taxApplicability === 'yes'"
                    (change)="onGSTCheckboxChanged($event)"
                    />
          <label>GST Applicable</label>
        </div> 
                </div>
              </div>

              <div class="row">
                <div class="col-lg-4 col-sm-12 detail"  *ngIf="entity.taxApplicability === 'yes'">
                  <label class="input-headline">GST Return Frequency</label>
                  <select
                    class="form-select en-input"
                    id="gstReturnFrequency"
                    name="gstReturnFrequency"
                    type="text"
                    [(ngModel)]="entity.gstReturnFrequency"
                    required
                  >
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="annually">Annually</option>
                  </select>
                </div>

                <div class="col-lg-4 col-sm-12 detail">
                  <label for="logo" class="input-headline">Upload Logo</label>
                  <input
                    type="file"
                    id="logo"
                    name="logo"
                    (change)="onFileSelected($event)"
                    class="form-control abn-input"
                    accept="image/*"
                  />
                </div>

                <div class="col-lg-4 col-sm-12 detail">
                  <img
                    [src]="logoUrl"
                    alt="logo"
                    width="100"
                    height="100"
                    class="entity-logo"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="middle-form">
            <div class="form-headline">
              <h6 class="subhead">Entity Trading Details</h6>
            </div>

            <!-- <div class="row">
              <form #entityForm="ngForm">
                <div class="row">
                  <div class="col-lg-4 col-sm-12 detail">
                    <label for="tradingName">Trading Name</label>
                    <input
                      type="text"
                      id="tradingName"
                      class="form-control"
                      [(ngModel)]="entityTradingName.tradingName"
                      name="tradingName"
                      required
                    />
                  </div>

                  <div class="col-lg-4 col-sm-12 detail">
                    <label for="industryClassification"
                      >Industry Classification</label
                    >
                    <div class="input-group">
                      <select
                        id="industryClassification"
                        class="form-control en-input"
                        name="industryClassification"
                        required
                      >
                        <option value="Agriculture, Forestry and Fishing">
                          Agriculture, Forestry and Fishing
                        </option>
                        <option value="Manufacturing">Manufacturing</option>
                        <option
                          value="Electricity, Gas, Water and Waste Services"
                        >
                          Electricity, Gas, Water and Waste Services
                        </option>
                        <option value="Construction">Construction</option>
                        <option value="Wholesale Trade">Wholesale Trade</option>
                        <option value="Retail Trade">Retail Trade</option>
                        <option value="Accommodation and Food Services">
                          Accommodation and Food Services
                        </option>
                        <option value="Transport, Postal and Warehousing">
                          Transport, Postal and Warehousing
                        </option>
                        <option
                          value="Information Media and Telecommunications"
                        >
                          Information Media and Telecommunications
                        </option>
                        <option value="Financial and Insurance Services">
                          Financial and Insurance Services
                        </option>
                        <option value="Rental, Hiring and Real Estate Services">
                          Rental, Hiring and Real Estate Services
                        </option>
                        <option value="Administrative and Support Services">
                          Administrative and Support Services
                        </option>
                        <option value="Education and Training">
                          Education and Training
                        </option>
                        <option value="Health Care and Social Assistance">
                          Health Care and Social Assistance
                        </option>
                        <option value="Arts and Recreation Services">
                          Arts and Recreation Services
                        </option>
                        <option value="Other Services">Other Services</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="btn-group">
                  <button class="cancel-btn" (click)="onCancel()">Cancel</button>
                  <button class="save-btn" (click)="onSubmit()">Save</button>
                </div>
              </form>
            </div> -->

            <div class="edit-entity-container">
              <button
                type="button"
                class="edit-entity-btn"
                (click)="toggleEditTradingDetails()"
              >
                Edit Entity Trading Details
              </button>
            </div>

            <div class="modal-overlay" *ngIf="showEditTradingDetails">
              <div class="modal-content">
                <div class="container" *ngIf="showEditTradingDetails">
                  <div class="form-container">
                    <div class="form-headline">
                      <h2>Update Trading Name</h2>
                      <div class="icon-group ms-auto">
                        <i
                          class="bi bi-x-circle close-icon"
                          (click)="toggleEditTradingDetails()"
                          aria-label="Close"
                        ></i>
                      </div>
                    </div>
                    <div class="radio-group">
                      <label>
                        <input
                          type="radio"
                          name="tradingNameOption"
                          value="single"
                          [(ngModel)]="tradingNameOption"
                          (click)="singleTradingName()"
                        />
                        Single Trading Name
                      </label>
                      <label>
                        <input
                          type="radio"
                          name="tradingNameOption"
                          value="multiple"
                          [(ngModel)]="tradingNameOption"
                        />
                        Multiple Trading Names
                      </label>
                      <label>
                        <input
                          type="radio"
                          name="tradingNameOption"
                          value="none"
                          [(ngModel)]="tradingNameOption"
                          (click)="noTradingName()"
                        />
                        No Trading Name
                      </label>
                    </div>
                    <form>
                      <div class="form-group">
                        <label for="tradingName">Trading Name</label>
                        <input
                          type="text"
                          id="tradingName"
                          name="tradingName"
                          [(ngModel)]="entityTradingName.tradingName"
                          [readonly]="tradingNameOption === 'none'"
                          [class.read-only]="tradingNameOption === 'none'"
                          required
                        />
                        <button
                          *ngIf="tradingNameOption === 'multiple'"
                          type="button"
                          class="verify-code"
                          (click)="addAdditionalTradingName()"
                        >
                          Add Additinal Trading Name
                        </button>
                      </div>

                      <div
                        *ngFor="
                          let name of newTradingName;
                          let i = index;
                          trackBy: trackByIndex
                        "
                        class="form-group"
                      >
                        <label for="addAdditionalTradingName-{{ i }}">
                          Additional Trading Name {{ i + 1 }}
                        </label>
                        <input
                          type="text"
                          id="addAdditionalTradingName-{{ i }}"
                          name="addAdditionalTradingName-{{ i }}"
                          [(ngModel)]="newTradingName[i]"
                          [readonly]="tradingNameOption === 'none'"
                          [class.read-only]="tradingNameOption === 'none'"
                          required
                        />
                        <button
                          type="button"
                          class="remove-btn"
                          (click)="removeTradingName(i)"
                        >
                          Remove
                        </button>
                      </div>

                      <div class="form-group">
                        <label for="industryClassification"
                          >Industry Classification</label
                        >
                        <select
                          id="industryClassification"
                          name="industryClassification"
                          class="form-control"
                          [(ngModel)]="entityTradingName.industryId"
                          [disabled]="tradingNameOption === 'none'"
                          [class.read-only]="tradingNameOption === 'none'"
                        >
                          <option [ngValue]="null">
                            -- Select Industry --
                          </option>
                          <option
                            *ngFor="let industry of industryList"
                            [ngValue]="industry"
                          >
                            {{ industry.industryName }}
                          </option>
                        </select>
                      </div>
                      <button
                        type="submit"
                        class="update-entity-btn"
                        (click)="submitTradingNames()"
                      >
                        {{
                          tradingNameOption === "none"
                            ? "Proceed"
                            : "Update Entity"
                        }}
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Save Button -->
          <div class="btn-group">
            <button class="cancel-btn" (click)="onCancel()">Cancel</button>
            <button class="save-btn" (click)="onSubmit()">Save</button>
          </div>
        </div>
      </form>
    </div>

    <!-- Email Template Tab Content -->
    <!-- Email Template Tab Content with Nested Tabs -->
    <div
      class="tab-pane fade"
      id="template"
      role="tabpanel"
      aria-labelledby="template-tab"
    >
      <div class="header">
        <h3>Select Email Template</h3>
      </div>

      <div class="search-create">
        <!-- Nested Tab Navigation for Sales Quote and Invoice -->
        <ul class="nav nav-tabs" id="nestedTab" role="tablist">
          <!-- Tab for Sales Quote -->
          <li class="nav-item" role="presentation">
            <button
              class="nav-link active"
              id="sales-tab"
              data-bs-toggle="tab"
              data-bs-target="#salesQuote"
              type="button"
              role="tab"
              aria-controls="salesQuote"
              aria-selected="true"
            >
              Sales Quote
            </button>
          </li>

          <!-- Tab for Invoice -->
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              id="invoice-tab"
              data-bs-toggle="tab"
              data-bs-target="#invoice"
              type="button"
              role="tab"
              aria-controls="invoice"
              aria-selected="false"
            >
              Invoice
            </button>
          </li>
        </ul>
      </div>

      <!-- Nested Tab Content for Sales Quote and Invoice -->
      <div class="tab-content" id="nestedTabContent">
        <div
          class="tab-pane fade show active"
          id="salesQuote"
          role="tabpanel"
          aria-labelledby="sales-tab"
        >
          <form
            id="quoteForm"
            [formGroup]="quoteTemplateForm"
            (ngSubmit)="onSubmitEmail(quoteTemplateForm)"
            class="body-form"
          >
            <div class="main-form">
              <div class="middle-form">
                <div class="form-headline">
                  <h6 class="subhead">Sales Quote Email Template</h6>
                </div>
                <div class="form-body">
                  <!-- Template Name -->
                  <div class="mb-3">
                    <label for="quoteTemplateName" class="form-label"
                      >Template Name</label
                    >
                    <input
                      type="text"
                      id="templateName"
                      class="form-control"
                      formControlName="quoteTemplateName"
                      placeholder="Enter template name"
                    />
                  </div>

                  <!-- Email Subject -->
                  <div class="mb-3">
                    <label for="emailSubject" class="form-label">Subject</label>
                    <input
                      type="text"
                      id="emailSubject"
                      class="form-control"
                      formControlName="quoteSubject"
                      placeholder="Enter email subject"
                      readonly
                    />
                  </div>

                  <!-- Email Content -->
                  <div class="mb-3">
                    <label for="templateContent" class="form-label"
                      >Email Content</label
                    >
                    <textarea
                      id="templateContent"
                      formControlName="quoteContent"
                      class="form-control"
                      style="min-height: 350px; height: fit-content"
                      placeholder="Enter email content"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="btn-group-1">
              <button
                type="button" class="email-cancel-btn"
                (click)="onCancel()"
              >
                Close
              </button>
              <!--<button
                type="button" class="email-save-btn"
                (click)="deleteTemplate()"
              >
                Delete
              </button>-->
              <button
                type="submit" class="email-save-btn"
                (click)="onSaveIt(quoteTemplateForm)"
              >
                Save
              </button>
            </div>
          </form>
        </div>

        <!-- Invoice Tab Content -->
        <div
          class="tab-pane fade"
          id="invoice"
          role="tabpanel"
          aria-labelledby="invoice-tab"
        >
          <form
            id="invoiceForm"
            [formGroup]="invoiceTemplateForm"
            (ngSubmit)="onSubmitEmail(invoiceTemplateForm)"
            class="body-form"
          >
            <div class="main-form">
              <div class="middle-form">
                <div class="form-headline">
                  <h6 class="subhead">Invoice Email Template</h6>
                </div>
                <div class="form-body">
                  <!-- Template Name -->
                  <div class="mb-3">
                    <label for="templateName" class="form-label"
                      >Template Name</label
                    >
                    <input
                      type="text"
                      id="templateName"
                      class="form-control"
                      formControlName="invoiceTemplateName"
                      placeholder="Enter template name"
                    />
                    <div
                      *ngIf="
                        invoiceTemplateForm.get('templateName')?.invalid &&
                        invoiceTemplateForm.get('templateName')?.touched
                      "
                    >
                      <small class="text-danger"
                        >Template Name is required.</small
                      >
                    </div>
                  </div>
                  <!-- Email Subject -->
                  <div class="mb-3">
                    <label for="invoiceSubject" class="form-label"
                      >Subject</label
                    >
                    <input
                      type="text"
                      id="invoiceSubject"
                      class="form-control"
                      formControlName="invoiceSubject"
                      placeholder="Enter email subject"
                      readonly
                    />
                  </div>

                  <!-- Email Content -->
                  <div class="mb-3">
                    <label for="invoiceContent" class="form-label"
                      >Email Content</label
                    >
                    <textarea
                      id="invoiceContent"
                      formControlName="invoiceContent"
                      class="form-control"
                      style="min-height: 350px; height: fit-content"
                      placeholder="Enter email content"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="btn-group-1">
              <button
                type="button"
                class="email-cancel-btn"
                (click)="onCancel()"
              >
                Close
              </button>
              <button
                type="button"
                class="email-save-btn"
                (click)="deleteTemplate()"
              >
                Delete
              </button>
              <button
                type="submit"
                class="email-save-btn"
                (click)="onSaveIt(invoiceTemplateForm)"
              >
                Save
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Basiq Modal -->
<div
  class="modal fade"
  id="basiqModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="basiqModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered modern-basiq-modal" style="max-width: 500px">
    <div class="modal-content modern-basiq-modal-content">
      <div class="modal-header modern-basiq-modal-header">
        <h5 class="modal-title" id="connectBasiqModalLabel">
          <i class="bi bi-bank2 me-2" style="color:#4262ff"></i>
          Connect Basiq
        </h5>
        <button
          type="button"
          class="btn-close modern-basiq-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body modern-basiq-modal-body">
        <!-- Form to connect if not connected -->
        <form
          *ngIf="!entity.basiqId"
          #basiqForm="ngForm"
          (ngSubmit)="basiqForm.form.valid && connectBasiq()"
          novalidate
        >
          <div class="mb-3">
            <label for="mobileNumber" class="form-label modern-basiq-label">Mobile Number</label>
            <input
              type="tel"
              id="mobileNumber"
              name="mobileNumber"
              [(ngModel)]="mobileNumber"
              class="form-control modern-basiq-input"
              placeholder="Enter your mobile number"
              pattern="^\+?[1-9]\d{1,14}$"
              required
            />
            <div
              *ngIf="basiqForm.submitted && basiqForm.controls['mobileNumber']?.invalid"
              class="text-danger"
            >
              <div *ngIf="basiqForm.controls['mobileNumber']?.errors?.['required']">
                Mobile number is required.
              </div>
              <div *ngIf="basiqForm.controls['mobileNumber']?.errors?.['pattern']">
                Enter a valid mobile number (e.g., +1234567890).
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-end">
            <button
              type="submit"
              class="modern-basiq-btn"
              [disabled]="isConnecting"
            >
              <span *ngIf="isConnecting">
                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                Connecting...
              </span>
              <span *ngIf="!isConnecting">
                <i class="bi bi-link-45deg me-2"></i>
                Connect Basiq
              </span>
            </button>
          </div>
        </form>
        <!-- Connected State -->
        <div *ngIf="entity.basiqId" class="modern-basiq-connected">
          <p class="text-success fw-bold mb-4 modern-basiq-success">
            <i class="bi bi-check-circle-fill me-2"></i>
            Basiq account is connected.
          </p>
          <div class="modern-basiq-accounts">
            <h5 class="fw-semibold mb-3">
              <i class="bi bi-bank me-2 text-primary"></i> Connected Accounts
            </h5>
            <ul class="list-group list-group-flush rounded">
              <li *ngFor="let account of accounts" class="list-group-item d-flex justify-content-between align-items-start modern-basiq-account-item">
                <div>
                  <div class="fw-semibold">
                    {{ account.institution || 'Unknown Institution' }}
                  </div>
                  <div class="text-muted small">
                    {{ account.name || 'Unnamed Account' }}
                  </div>
                </div>
                <div class="text-end">
                  <span 
                    class="badge fs-6"
                    [ngClass]="{
                      'bg-success-subtle text-success': account.balance >= 0,
                      'bg-danger-subtle text-danger': account.balance < 0
                    }"
                  >
                    {{ account.balance | currency }}
                  </span>
                </div>
              </li>
            </ul>
          </div>
          <div class="d-flex flex-column gap-3 mt-4">
            <div class="input-group modern-basiq-action-group">
              <select class="form-select" [(ngModel)]="selectedAction" name="action">
                <option value="connect">Add</option>
                <option value="manage">Manage</option>
                <option value="extend">Extend</option>
                <option value="update">Update</option>
                <option value="renew">Renew</option>
              </select>
              <button
                type="button"
                class="modern-basiq-btn-outline"
                (click)="connectAdditionalAccount(selectedAction)"
                [disabled]="isConnecting"
              >
                <span *ngIf="isConnecting">
                  <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Connecting...
                </span>
                <span *ngIf="!isConnecting">
                  <i class="bi bi-link-45deg me-2"></i>
                  Account
                </span>
              </button>
            </div>
            <button
              type="button"
              class="modern-basiq-btn-danger"
              (click)="confirmDeleteUser()"
              [disabled]="isDeleting"
              aria-label="Delete Basiq account"
            >
              <span *ngIf="isDeleting">
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Deleting...
              </span>
              <span *ngIf="!isDeleting">
                <i class="bi bi-trash me-2"></i>
                Delete User Account
              </span>
            </button>
          </div>
        </div>
      </div>
      <div class="modal-footer modern-basiq-footer" *ngIf="entity.basiqId">
        <button #closebasiqModal type="button" class="modern-basiq-btn-outline" data-bs-dismiss="modal">
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Entity Modal -->
<div
  class="modal fade"
  id="editEntityModal"
  tabindex="-1"
  aria-labelledby="editEntityModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <!-- use modal-lg for wider form -->
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title" id="editEntityModalLabel">
          Update Trading Name
        </h2>
        <div class="icon-group ms-auto">
          <i
            class="bi bi-x-circle close-icon"
            data-bs-dismiss="modal"
            aria-label="Close"
            title="Close"
            #closeCustomerPopUp
          ></i>
        </div>
      </div>
      <div class="modal-body">
        <!-- Your existing form goes here -->
        <div class="container">
          <div class="form-container">
            <div class="radio-group">
              <label>
                <input
                  type="radio"
                  name="tradingNameOption"
                  value="single"
                  [(ngModel)]="tradingNameOption"
                  (click)="singleTradingName()"
                />
                Single Trading Name
              </label>
              <label>
                <input
                  type="radio"
                  name="tradingNameOption"
                  value="multiple"
                  [(ngModel)]="tradingNameOption"
                />
                Multiple Trading Names
              </label>
              <label>
                <input
                  type="radio"
                  name="tradingNameOption"
                  value="none"
                  [(ngModel)]="tradingNameOption"
                  (click)="noTradingName()"
                />
                No Trading Name
              </label>
            </div>
            <form>
              <div class="form-group">
                <label for="tradingName">Trading Name</label>
                <input
                  type="text"
                  id="tradingName"
                  name="tradingName"
                  [(ngModel)]="entityTradingName.tradingName"
                  [readonly]="tradingNameOption === 'none'"
                  [class.read-only]="tradingNameOption === 'none'"
                  required
                />
                <button
                  *ngIf="tradingNameOption === 'multiple'"
                  type="button"
                  class="verify-code"
                  (click)="addAdditionalTradingName()"
                >
                  Add Additinal Trading Name
                </button>
              </div>

              <div
                *ngFor="
                  let name of newTradingName;
                  let i = index;
                  trackBy: trackByIndex
                "
                class="form-group"
              >
                <label for="addAdditionalTradingName-{{ i }}">
                  Additional Trading Name {{ i + 1 }}
                </label>
                <input
                  type="text"
                  id="addAdditionalTradingName-{{ i }}"
                  name="addAdditionalTradingName-{{ i }}"
                  [(ngModel)]="newTradingName[i]"
                  [readonly]="tradingNameOption === 'none'"
                  [class.read-only]="tradingNameOption === 'none'"
                  required
                />
                <button
                  type="button"
                  class="remove-btn"
                  (click)="removeTradingName(i)"
                >
                  Remove
                </button>
              </div>

              <div class="form-group">
                <label for="industryClassification"
                  >Industry Classification</label
                >
                <select
                  id="industryClassification"
                  name="industryClassification"
                  class="form-control"
                  [(ngModel)]="entityTradingName.industryId"
                  [disabled]="tradingNameOption === 'none'"
                  [class.read-only]="tradingNameOption === 'none'"
                >
                  <option [ngValue]="null">-- Select Industry --</option>
                  <option
                    *ngFor="let industry of industryList"
                    [ngValue]="industry"
                  >
                    {{ industry.industryName }}
                  </option>
                </select>
              </div>
              <button type="submit" class="btn">
                {{ tradingNameOption === "none" ? "Proceed" : "Update Entity" }}
              </button>
            </form>
          </div>
          <!-- keep your form content exactly as it is -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Transactions Modal -->
<div
  class="modal fade"
  id="transactionsModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="transactionsModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" style="max-width: 1000px;">
    <div class="modal-content">
      <!-- Modal Header -->
      <div class="modal-header">
        <h5 class="modal-title" id="transactionsModalLabel">Transactions</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body">
        <div *ngIf="transactions && transactions.length > 0">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Date</th>
                <th>Description</th>
                <th>Amount</th>            
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let transaction of transactions">
                <td>{{ transaction.postDate | date: "dd MMM yyyy" }}</td>
                <td>{{ transaction.description }}</td>
                <td>{{ transaction.amount | currency }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div *ngIf="transactions?.length === 0">
          <p>No transactions available.</p>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer">
        <button
          type="button"
          class="basiq-cancel-btn"
          data-bs-dismiss="modal"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Accounts Modal -->
<div
  class="modal fade"
  id="accountsModal"
  tabindex="-1"
  aria-labelledby="accountsModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered modal-xl">
    <div class="modal-content">
      
      <!-- Header -->
      <div class="modal-header">
        <h5 class="modal-title" id="accountsModalLabel">Accounts</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>

      <!-- Body -->
      <div class="modal-body">

        <!-- Accounts Table -->
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Account Name</th>
              <th>Account No</th>
              <th>Currency</th>
              <th>Balance</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="let account of accounts"
              (click)="onAccountClick(account)"
              style="cursor: pointer;"
              [class.table-active]="account.id === selectedAccountId"
            >
              <td>{{ account.name }}</td>
              <td>{{ account.accountNo }}</td>
              <td>{{ account.currency }}</td>
              <td>{{ account.balance | currency }}</td>
              <td>{{ account.status }}</td>
            </tr>
          </tbody>
        </table>

        <!-- Transactions Section -->
        <ng-container *ngIf="selectedAccountId">
          <ng-container *ngIf="transactions.length > 0; else noTransactions">
            <h6 class="mt-4">Transactions for selected account</h6>
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Description</th>
                  <th>Amount</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let transaction of transactions">
                  <td>{{ transaction.postDate | date: 'dd MMM yyyy' }}</td>
                  <td>{{ transaction.description }}</td>
                  <td>{{ transaction.amount | currency }}</td>
                </tr>
              </tbody>
            </table>
          </ng-container>

          <ng-template #noTransactions>
            <p class="mt-3">No transactions available for this account.</p>
          </ng-template>
        </ng-container>

      </div>

      <!-- Footer -->
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-secondary"
          data-bs-dismiss="modal"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Stripe Modal -->
<div
  class="modal fade"
  id="stripeModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="stripeModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" style="max-width: 500px">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Connect Stripe</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>

      <div class="modal-body">
        <!-- Connected message and actions -->
        <div *ngIf="entity.stripeAccountId" class="text-center py-4">
          <p class="mb-3 fw-semibold text-success">
            <i class="bi bi-check-circle-fill me-2"></i>
            Stripe account is connected.
          </p>

          <div class="d-flex justify-content-center gap-2">
            <button
              type="button"
              class="btn btn-outline-danger d-flex align-items-center"
              (click)="removeStripeAccount()"
              [disabled]="isRemovingStripe"
            >
              <span *ngIf="isRemovingStripe" class="spinner-border spinner-border-sm me-2"></span>
              <i *ngIf="!isRemovingStripe" class="bi bi-trash me-2"></i>
              <span>{{ isRemovingStripe ? 'Removing...' : 'Remove Account' }}</span>
            </button>

            <button
              type="button"
              class="btn btn-outline-primary"
              (click)="goToStripeDashboard()"
            >
              Go to Stripe Dashboard
            </button>
          </div>
        </div>
      </div>

      <div class="modal-footer" *ngIf="entity.stripeAccountId">
        <button #closeStripeModal type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Close
        </button>
      </div>
    </div>
  </div>
</div>

