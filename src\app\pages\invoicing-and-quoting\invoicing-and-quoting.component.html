<app-home-header></app-home-header>

<div class="container">
    <h2 class="title">Turn Quotes into Cash – Instantly with LEDGER CHIMP!</h2>
    <p class="subtitle">
        Stop waiting—<span class="highlight2">start getting paid faster!</span> With LEDGER CHIMP, sending quotes to customers is effortless, and converting them into invoices takes just a click.
    </p>

    <div class="feature-grid">
        <div class="feature-card" *ngFor="let feature of features">
            <div class="icon">{{ feature.icon }}</div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
        </div>
    </div>

    <p class="footertitle">
        Why wait? <span class="highlight2">Try LEDGER CHIMP today and experience effortless invoicing!</span><br />
        Let me know if you’d like any tweaks!
    </p>
</div>
