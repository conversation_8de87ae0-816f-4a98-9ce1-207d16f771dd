#check {
  display: none;
}

#check:checked ~ label #btn {
  margin-left: -60px;
  opacity: 0;
  visibility: hidden;
}

#check:checked ~ label #cancel {
  margin-left: 245px;
  opacity: 1;
  visibility: visible;
}

label {
  position: fixed;
  top: 15px;
  left: 70px;
  z-index: 1000;
}

label #btn,
label #cancel {
  cursor: pointer;
  color: #f7f7f7;
  font-size: 29px;
  height: 30px;
  width: 30px;
  text-align: center;
  line-height: 45px;
  transition: all 0.5s ease;
}

label #cancel {
  opacity: 0;
  visibility: hidden;
}

#check:checked ~ .sidebar {
  left: 0;
}

.sidebar {
  position: fixed;
  width: 310px;
  left: -310px;
  height: 100vh;
  background-color: #fff;
  transition: all 0.5s ease;
  box-shadow: 3px 0 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 40px;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.sidebar-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.sidebar a {
  display: block;
  height: 65px;
  width: 100%;
  color: #4262ff;
  line-height: 65px;
  padding-left: 30px;
  box-sizing: border-box;
  border-left: 5px solid transparent;
  font-family: "Inter", sans-serif;
  transition: all 0.5s ease;
  cursor: pointer;
  font-size: 17px;
}

/* a.active, */
.sidebar a:hover {
  border-left: 4px solid var(--accent-color);
  color: #324acc;
  background: linear-gradient(
    to left,
    var(--accent-color),
    var(--gradient-color)
  );
}

.sidebar a:hover {
  --accent-color: #455cff;
  --gradient-color: #ffffff;
  border-left: 4px solid #455cff;
}

.sidebar a i {
  transition: color 0.3s ease;
}

.sidebar a span {
  letter-spacing: 1px;
  margin-left: 10px;
}

.submenu1 {
  background-color: #ffffff;
}

.submenu1 a {
  padding-left: 50px;
  font-size: 15px;
}

.submenu1 a:hover {
  --accent-color: #455cff;
  --gradient-color: #ffffff;
  border-left: 4px solid #455cff;
}

.submenu2 {
  background-color: #ffffff;
}

.submenu2 a {
  padding-left: 100px;
  font-size: 13px;
}

.submenu2 a:hover {
  --accent-color: #455cff;
  --gradient-color: #ffffff;
  border-left: 4px solid #455cff;
}

@media (max-width: 767px) {
  .sidebar {
    width: 0;
    height: 100%;
    left: -250px;
    transition: all 0.5s ease;
  }

  #check:checked ~ .sidebar {
    width: 250px;
    left: 0;
  }

  /* Toggle button visibility */
  label #btn,
  label #cancel {
    display: inline-block;
    position: fixed;
    top: 15px;
    left: 50px;
    z-index: 1000;

  }

  header,
  #btn,
  #cancel {
    display: none;
  }

  .sidebar a {
    height: 60px;
  }

  .sidebar a span {
    opacity: 1;
    visibility: visible;
  }

  .sidebar a:hover span {
    opacity: 1;
    visibility: visible;
  }

  #check:checked ~ label #cancel {
    margin-left: 0;
  }

  .sidebar {
    padding: 10px;
  }

  .sidebar a {
    font-size: 15px;
    padding-left: 20px;
  }

  .submenu1 a {
    font-size: 13px;
    padding-left: 40px;
  }

  .submenu2 a {
    font-size: 11px;
    padding-left: 80px;
  }
}
