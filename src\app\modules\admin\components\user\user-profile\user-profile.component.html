<app-admin-navigation></app-admin-navigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<body>
  <div class="form-container">
    <div>
      <h2>User Profile</h2>
      <form (ngSubmit)="onUserUpdate()">
        <div class="user-name">
          <div class="form-group">
            <label for="first-name">First Name</label>
            <input
              type="text"
              id="first-name"
              name="firstName"
              [(ngModel)]="user.firstName"
              required
            />
          </div>
          <div class="form-group">
            <label for="last-name">Last Name</label>
            <input
              type="text"
              id="last-name"
              name="lastName"
              [(ngModel)]="user.lastName"
              required
            />
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email</label>
          <input
            type="email"
            id="email"
            name="email"
            [(ngModel)]="user.username"
            required
            readonly
            disabled
          />
        </div>
        <div class="form-group">
          <label for="user-type">User Type</label>
          <input
            type="user-type"
            id="user-type"
            name="user-type"
            [(ngModel)]="user.userTypeId.userType"
            readonly
          />
        </div>

        <div class="check-box">
          <input
            type="checkbox"
            name="change-password"
            [(ngModel)]="changePassword"
            (click)="enableChangePassword()"
          />
          <label>Change Password</label>
        </div>
        <div class="" *ngIf="changePassword">
          <div class="form-group">
            <label for="password">Password</label>
            <div class="input-group d-flex align-items-center">
              <input
                [type]="isPasswordVisible ? 'text' : 'password'"
                id="password"
                name="password"
                [(ngModel)]="userPassword"
                (ngModelChange)="validatePassword()"
                style="flex: 1;"
                required
              />
              <button
                type="button"
                class="btn btn-outline-secondary input-group-text d-flex align-items-center"
                (click)="isPasswordVisible = !isPasswordVisible"
                tabindex="-1"
                style="padding: 1; width: 40px; height: 48px;">
                <i [class]="isPasswordVisible ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
              </button>
            </div>
            <small class="text-danger" *ngIf="passwordError">{{ passwordError }}</small>
          </div>

          <div class="form-group">
            <label for="re-password">Re-enter Password</label>
            <div class="input-group d-flex align-items-center">
              <input
                [type]="isPasswordVisible ? 'text' : 'password'"
                id="re-password"
                name="rePassword"
                [(ngModel)]="rePassword"
                (ngModelChange)="checkPasswordsMatch()"
                style="flex: 1;"
                required
              />
              <button
                type="button"
                class="btn btn-outline-secondary input-group-text d-flex align-items-center"
                (click)="isPasswordVisible = !isPasswordVisible"
                tabindex="-1"
                style="padding: 1; width: 40px; height: 48px; display: flex;">
                <i [class]="isPasswordVisible ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
              </button>
            </div>
              <small class="text-danger" *ngIf="passwordMismatchError">{{ passwordMismatchError }}</small>
        
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="invite-button">Save Changes</button>
        </div>
      </form>
    </div>
  </div>
</body>
