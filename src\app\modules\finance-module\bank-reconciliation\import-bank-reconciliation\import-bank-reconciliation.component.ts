import { BusinessPartner } from 'src/app/modules/business-partner/business-partner';
import munkres from 'munkres-js';
import {
  BankRecDocumentType,
  BankRecMatchMode,
  TransactionRequestBankTransfer,
  TransactionRequestCreditListDTO,
  TransactionRequestIncomeOrExpense,
  BankRecTransactionType,
  BankRuleDetail,
  BankRuleDTO,
  BankRuleType,
  BankRuleTransactionMatchDTO,
} from './../bank-reconciliation';
import { Component, NgZone, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { BankReconciliationService } from '../bank-reconciliation.service';
import { ApInvoiceHead, PaymentVoucherHeader } from '../../bill/bill';
import { BillService } from '../../bill/bill.service';
import {
  InvoiceHead,
  PaymentReceiptsHead,
} from 'src/app/modules/invoice/invoice';
import { BankAccount } from '../../bank/bank';
import {
  BankStatementHeader,
  BankStatementDetail,
} from '../bank-reconciliation';
import Swal from 'sweetalert2';
import { MatDialog } from '@angular/material/dialog';
import { CreateInvoiceComponent } from 'src/app/modules/invoice/create-invoice/create-invoice.component';
import { AddPayableBillComponent } from '../../bill/add-payable-bill/add-payable-bill.component';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CoaLedgerAccount } from '../../gl-account/gl-account';
import { GlAccountService } from '../../gl-account/gl-account.service';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { Entity } from 'src/app/modules/entity/entity';
import { forkJoin } from 'rxjs';
import { BusinessPartnerService } from 'src/app/modules/business-partner/business-partner.service';
import { HttpErrorResponse } from '@angular/common/http';
import { BankService } from '../../bank/bank.service';
import { MatchTransactionsComponent } from '../match-transactions/match-transactions/match-transactions.component';
@Component({
  selector: 'app-import-bank-reconciliation',
  templateUrl: './import-bank-reconciliation.component.html',
  styleUrls: ['./import-bank-reconciliation.component.css'],
})
export class ImportBankReconciliationComponent {
  findrecordsFilter: any = {
    searchTerm: '',
    amountterm: '',
  };

  @ViewChild('fileInput') fileInput: any;
  selectedSource: 'statement' | 'bankfeed' = 'statement';
  selectedFile: File | null = null;
  uploadStatus: string = '';
  transactions: BankStatementDetail[] = [];
  allTransactions: BankStatementDetail[] = [];

  // For Find
  selectedPaymentReceipt: PaymentReceiptsHead | null = null;
  selectedInvoiceHead: InvoiceHead | null = null;
  selectedVoucherHead: PaymentVoucherHeader | null = null;
  selectedBillHead: ApInvoiceHead | null = null;

  filteredPaymentVoucherHeadersAndBills: any[] = [];
  selectedFilter: string = 'all';

  selectedBankAccount: BankAccount | null = null;
  filteredTransactions: any[] = [];
  amountTolerance: number = 0.01;

  isLoadingBankFeed = false;
  noDataFromFeed: boolean = false;
  isCreditPopup = false;
  isDebitPopup = false;
  isExpenseOpen = false;
  isBankTransferOpen = false;
  selectedOption: string = '';
  state: any = null;
  selectedDateFilter: string | false = false;

  tranasctionCount: number = 0;
  transactionData: any = null;
  isViewDetails = false;
  selectedRecordType: string = '';
  selectedDetails: any = null;
  isUploading = false;
  isFindPopup = false;
  selectedTransactionType: string = '';
  selectedTransactionForFind: BankStatementDetail | null = null;
  findRecords: any[] = [];
  totalSelectedAmount: number = 0.0;
  selectedTransactionAmount: number = 0.0;
  selectedRecords: any[] = [];
  matchedDebitPairs: any[] = [];
  matchedCreditPairs: any[] = [];

  // for Suggest
  suggestedInvoiceHead: InvoiceHead | null = null;
  suggestedBillHead: ApInvoiceHead | null = null;
  suggestedPaymentReceipt: PaymentReceiptsHead | null = null;
  suggestedPaymentVoucherHeader: PaymentVoucherHeader | null = null;

  bankStatementHeader: BankStatementHeader | null = null;

  // Updated Ones
  filteredPaymentReceiptsAndInvoiceHeads: any[] = [];
  expenseForm!: FormGroup;
  incomeForm!: FormGroup;
  bankTransferForm!: FormGroup;
  expenseLedgerAccountsList: CoaLedgerAccount[] = [];
  incomeLedgerAccountsList: CoaLedgerAccount[] = [];
  bankAccountsList: BankAccount[] = [];
  supplierList: BusinessPartner[] = [];
  customerList: BusinessPartner[] = [];
  spendMoneyBankRules: BankRuleDTO[] = [];
  receiveMoneyBankRules: BankRuleDTO[] = [];
  filteredSuppliers: BusinessPartner[] = [];
  filteredCustomers: BusinessPartner[] = [];
  isExpenseGstCheckboxVisible: boolean = true;
  isIncomeGstCheckboxVisible: boolean = true;

  expandedRowIndex: number | null = null;

  constructor(
    private bankReconciliationService: BankReconciliationService,
    private billService: BillService,
    private router: Router,
    private dialog: MatDialog,
    private fb: FormBuilder,
    private glAccountService: GlAccountService,
    private entityService: EntityService,
    private businessPartnerService: BusinessPartnerService,
    private bankService: BankService
  ) {}

  ngOnInit() {
    this.state = history.state;
    this.selectedBankAccount = this.state.bankAccount;

    if (!this.selectedBankAccount) {
      this.router.navigate(['/bank_account_list']);
      return;
    }

    this.getAllPaymentVoucherHeadersAndApInvoices();
    this.getPaymentReceiptListAndPendingInvoicesByEntity();
    this.loadLedgerAccounts();
    this.loadAllBankAccounts();
    this.loadBankRules();
    this.updateViewInit();
    this.loadBusinessPartners();
    this.expenseFormInit();
    this.incomeFormInit();
    this.bankTransferFormInit();
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;

    if (input?.files?.length) {
      this.selectedFile = input.files[0];
      this.uploadStatus = '';
      this.transactions = [];
    }
  }

  loadLedgerAccounts() {
    const entityId = +(localStorage.getItem('entityId') || 0);
    this.glAccountService.getCoaLedgerAccountListByEntity(entityId).subscribe({
      next: (response: CoaLedgerAccount[]) => {
        console.log('Ledger accounts loaded:', response);

        this.incomeLedgerAccountsList = response.filter(
          (ledgerAccount) =>
            ledgerAccount.coaHeaderId.accountHeaderType === 'Income' ||
            ledgerAccount.coaHeaderId.accountHeaderType === 'Other Income' ||
            ledgerAccount.coaHeaderId.accountHeaderType ===
              'Current liabilities' ||
            ledgerAccount.coaHeaderId.accountHeaderType ===
              'Non Current liabilities'
        );

        this.expenseLedgerAccountsList = response.filter(
          (ledgerAccount) =>
            ledgerAccount.coaHeaderId.accountHeaderType === 'Expenses' ||
            ledgerAccount.coaHeaderId.accountHeaderType === 'Current Assets' ||
            ledgerAccount.coaHeaderId.accountHeaderType === 'Non current assets'
        );
      },
      error: (err) => {
        console.error('Failed to load ledger accounts:', err);
      },
    });
  }

  loadAllBankAccounts() {
    const entityId = +(localStorage.getItem('entityId') || 0);
    if (!entityId) {
      console.warn('EntityId not Found');
      return;
    }

    this.bankService.getBankAccountsByEntityId(entityId).subscribe(
      (data: BankAccount[]) => {
        this.bankAccountsList = data;
      },
      (error) => {
        console.error('Error fetching quotations:', error);
      }
    );
  }

  loadBusinessPartners() {
    const entityId = +(localStorage.getItem('entityId') || 0);
    this.businessPartnerService
      .getBusinessPartnersListByEntity(entityId)
      .subscribe(
        (partners: BusinessPartner[]) => {
          // Suppliers
          this.supplierList = partners.filter(
            (partner) =>
              partner.businessPartnerTypeId?.businessPartnerType === 'Supplier'
          );

          // Customers
          this.customerList = partners.filter(
            (partner) =>
              partner.businessPartnerTypeId?.businessPartnerType === 'Customer'
          );
        },
        (error: HttpErrorResponse) => {
          console.error('Error fetching Business Partner Type', error);
        }
      );
  }

  loadBankRules() {
    const entityId = +(localStorage.getItem('entityId') || 0);

    if (!entityId) {
      console.warn('EntityId not Found');
      return;
    }

    if (!this.selectedBankAccount) {
      console.warn('BankAccount not Found');
      return;
    }

    this.bankReconciliationService
      .loadBankRulesByBankAccountIdAndEntityId(
        entityId,
        this.selectedBankAccount.bankAccountId
      )
      .subscribe(
        (bankRuleDTOList: BankRuleDTO[]) => {
          if (bankRuleDTOList) {
            this.spendMoneyBankRules = [];
            this.receiveMoneyBankRules = [];

            console.log('BankRuleDTO List : ', bankRuleDTOList);

            bankRuleDTOList.forEach((bankRuleDTO) => {
              const ruleType = bankRuleDTO.bankRuleHead.bankRuleType;
              if (ruleType === BankRuleType.SPEND) {
                this.spendMoneyBankRules.push(bankRuleDTO);
              } else if (ruleType === BankRuleType.RECEIVE) {
                this.receiveMoneyBankRules.push(bankRuleDTO);
              }
            });
          }
        },
        (error) => {
          console.error(error);
        }
      );
  }

  filterByDateChange() {
    console.log('Working Here Filtere By date');
    if (!this.selectedBankAccount) {
      console.log('Selected Date Not Valid');
      return;
    }

    if (!this.selectedDateFilter) {
      console.log('Selected Date Not Valid');
      return;
    }

    this.noDataFromFeed = false;
    this.isLoadingBankFeed = true;

    this.loadAllTransactionsFromBankFeed(
      this.selectedBankAccount,
      this.selectedDateFilter
    );
  }

  loadAllTransactionsFromBankFeed(
    bankAccount: BankAccount,
    selectedDate: string
  ) {
    console.log('loadAllTransactionsFromBankFeed');
    console.log('selected Date : ', selectedDate);

    const entityId = +(localStorage.getItem('entityId') || 0);
    this.tranasctionCount = 0;

    this.bankReconciliationService
      .getAllTransactionsByBankAccountFeed(
        bankAccount.bankAccountId,
        entityId,
        selectedDate
      )
      .subscribe({
        next: (response: BankStatementDetail[]) => {
          if (response) {
            console.info('Completed Returned BankSatementDetails');
            console.info(response);
            this.selectedFilter = 'debit';
            this.transactions = response;
            this.filterTransactions();
            this.bankStatementHeader =
              this.transactions.length > 0
                ? this.transactions[0].bankStatementHeader
                : null;
            this.isLoadingBankFeed = false;
          } else {
            this.noDataFromFeed = true;
            this.isLoadingBankFeed = false;
          }
        },
        error: (er) => {
          console.error(er);
          this.noDataFromFeed = true;
          this.isLoadingBankFeed = false;
        },
      });
  }

  uploadReport() {
    this.isUploading = true;

    console.log(this.selectedFile);

    if (!this.selectedFile) {
      this.uploadStatus = 'Please select a file first!';
      this.isUploading = false;
      return;
    }

    if (!this.selectedBankAccount) {
      this.uploadStatus = 'BankAccount Not Updated!';
      this.isUploading = false;
      return;
    }

    const formData = new FormData();
    formData.append('file', this.selectedFile);
    const entityId = +(localStorage.getItem('entityId') || 0);

    this.bankReconciliationService
      .uploadBankStatement(
        entityId,
        this.selectedBankAccount.bankAccountId,
        formData
      )
      .subscribe(
        (response: any) => {
          this.allTransactions = response || [];
          this.transactions = this.allTransactions.filter(
            (transaction) => transaction.matchStatus === 'Pending'
          );
          this.bankStatementHeader =
            this.allTransactions.length > 0
              ? this.allTransactions[0].bankStatementHeader
              : null;
          this.selectedFilter = 'credit';
          this.filterTransactions();
          this.isUploading = false;
        },
        (error: any) => {
          alert('Upload failed. Please try again.');
          this.transactions = [];
          this.isUploading = false;
        }
      );
  }

  onTabChange(selectedSourceOpt: 'statement' | 'bankfeed'): void {
    if (selectedSourceOpt === 'bankfeed') {
      if (!this.selectedBankAccount?.basiqAccountId) {
        Swal.fire({
          title: 'Bank Feed Not Configured',
          text: 'This bank account is not connected to a bank feed. Please configure it before proceeding.',
          icon: 'warning',
          background: '#1e1e2f',
          color: '#ffffff',
          confirmButtonColor: '#4262ff',
          confirmButtonText: 'Ok',
          customClass: {
            popup: 'swal-custom-popup',
            title: 'swal-custom-title',
            confirmButton: 'swal-custom-button',
          },
        });
        return;
      }
    }

    this.selectedSource = selectedSourceOpt;
    this.allTransactions = [];
    this.transactions = [];
    this.filteredTransactions = [];
    this.filterTransactions();
  }

  updateViewInit() {
    if (this.state && 'bankAccount' in this.state) {
      this.selectedBankAccount = this.state['bankAccount'];
      if (this.selectedBankAccount?.basiqAccountId) {
        this.selectedSource = 'bankfeed';
        this.noDataFromFeed = true;
      } else {
        this.selectedSource = 'statement';
      }
    } else {
      this.router.navigate(['/bank-account-list']);
    }
  }

  clear() {
    this.bankStatementHeader = null;
    this.selectedFile = null;
    this.uploadStatus = '';
    this.transactions = [];
    this.filteredTransactions = [];
    this.selectedFilter = 'all';

    this.matchedCreditPairs = [];
    this.matchedDebitPairs = [];
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
    this.resetMatchingFlags();
  }

  expenseFormInit() {
    this.expenseForm = this.fb.group({
      payeeName: ['', Validators.required],
      amount: [null, [Validators.required, Validators.min(0.01)]],
      ledgerAccount: [null, Validators.required],
      gstCheck: [false],
    });

    this.expenseForm
      .get('payeeName')
      ?.valueChanges.subscribe((value: string) => {
        if (value != null) {
          const filterValue = value.toLowerCase();
          this.filteredSuppliers = this.supplierList.filter((supplier) =>
            supplier.bpName.toLowerCase().includes(filterValue)
          );
        }
      });

    this.expenseForm
      .get('ledgerAccount')
      ?.valueChanges.subscribe((selectedId: number) => {
        const selectedAccount = this.expenseLedgerAccountsList.find(
          (acc) => acc.coaLedgerAccountId == selectedId
        );
        if (selectedAccount?.defaultTaxCode == 'GST Free Expenses') {
          this.isExpenseGstCheckboxVisible = false;
          this.expenseForm.get('gstCheck')?.setValue(false);
        } else {
          this.isExpenseGstCheckboxVisible = true;
          this.expenseForm.get('gstCheck')?.setValue(false);
        }
      });
  }

  incomeFormInit() {
    this.incomeForm = this.fb.group({
      incomeDescription: ['', Validators.required],
      incomeAmount: [null, [Validators.required, Validators.min(0.01)]],
      incomeLedgerAccount: [null, Validators.required],
      incomeGstCheck: [false],
    });

    this.incomeForm
      .get('incomeDescription')
      ?.valueChanges.subscribe((value: string) => {
        if (value != null) {
          const filterValue = value.toLowerCase();
          this.filteredCustomers = this.customerList.filter((customer) =>
            customer.bpName.toLowerCase().includes(filterValue)
          );
        }
      });

    this.incomeForm
      .get('incomeLedgerAccount')
      ?.valueChanges.subscribe((selectedId: number) => {
        const selectedAccount = this.incomeLedgerAccountsList.find(
          (acc) => acc.coaLedgerAccountId == selectedId
        );
        if (selectedAccount?.defaultTaxCode == 'GST Free Income') {
          this.isIncomeGstCheckboxVisible = false;
          this.incomeForm.get('incomeGstCheck')?.setValue(false);
        } else {
          this.isIncomeGstCheckboxVisible = true;
          this.incomeForm.get('incomeGstCheck')?.setValue(false);
        }
      });
  }

  bankTransferFormInit() {
    this.bankTransferForm = this.fb.group({
      transferBank: [null, Validators.required],
      transferDescription: ['', Validators.required],
      transferAmount: [null, [Validators.required, Validators.min(0.01)]],
    });
  }

  onExpenseSubmit() {
    if (this.expenseForm.invalid) return;

    const expenseFormValue = this.expenseForm.value;
    console.log('Expense Form Value:', expenseFormValue);

    const entityId = +(localStorage.getItem('entityId') || 0);
    const userId = +(localStorage.getItem('userid') || 0);

    if (entityId === 0 || userId === 0) {
      console.error('Invalid entityId or userId');
      return;
    }

    if (this.transactionData == null) {
      console.error('Transaction Data Is null');
      return;
    }

    // Find ledger account by ID from form value
    const selectedLedgerAccount = this.expenseLedgerAccountsList.find(
      (acc) => acc.coaLedgerAccountId === Number(expenseFormValue.ledgerAccount)
    );
    if (!selectedLedgerAccount) {
      console.error('Ledger Account Not Found');
      return;
    }

    const transactionRequestIncome: TransactionRequestIncomeOrExpense = {
      entityId: entityId,
      userId: userId,
      matchedMode: BankRecMatchMode.CREATE,
      selectedType: BankRecTransactionType.DEBIT,
      bankRecDocumentType: BankRecDocumentType.GL_EXPENSE,
      selectedTransaction: this.transactionData,
      transactionAmount: expenseFormValue.amount,
      transactionReference: expenseFormValue.payeeName,
      ledgerAccountId: selectedLedgerAccount,
      gstApplicability: expenseFormValue.gstCheck,
    };

    this.bankReconciliationService
      .saveCreateIncomeOrExpense(transactionRequestIncome)
      .subscribe(
        (data: boolean) => {
          console.log('Expense Created');
          if (data) {
            this.swalPopup(
              true,
              'Expense Creation Success',
              'Expense Creation Success',
              this.transactionData,
              this.closeExpenseIncomeDialog()
            );
          }
        },
        (error) => {
          console.error(error);
          this.swalPopup(
            false,
            'Expense Creation Failed',
            'Expense Creation Failed',
            this.transactionData,
            this.closeExpenseIncomeDialog()
          );
        }
      );
  }

  onIncomeSubmit() {
    if (this.incomeForm.invalid) return;

    const incomeFormValue = this.incomeForm.value;
    console.log('Form Value:', incomeFormValue);

    const entityId = +(localStorage.getItem('entityId') || 0);
    const userId = +(localStorage.getItem('userid') || 0);

    if (entityId === 0 || userId === 0) {
      console.error('Invalid entityId or userId');
      return;
    }

    if (this.transactionData == null) {
      console.error('Transaction Data Is null');
      return;
    }

    // Find ledger account by ID from form value
    const selectedLedgerAccount = this.incomeLedgerAccountsList.find(
      (acc) =>
        acc.coaLedgerAccountId === Number(incomeFormValue.incomeLedgerAccount)
    );
    if (!selectedLedgerAccount) {
      console.error('Ledger Account Not Found');
      return;
    }

    const transactionRequestIncome: TransactionRequestIncomeOrExpense = {
      entityId: entityId,
      userId: userId,
      matchedMode: BankRecMatchMode.CREATE,
      selectedType: BankRecTransactionType.CREDIT,
      bankRecDocumentType: BankRecDocumentType.GL_INCOME,
      selectedTransaction: this.transactionData,
      transactionAmount: incomeFormValue.incomeAmount,
      transactionReference: incomeFormValue.incomeDescription,
      ledgerAccountId: selectedLedgerAccount,
      gstApplicability: incomeFormValue.incomeGstCheck,
    };

    this.bankReconciliationService
      .saveCreateIncomeOrExpense(transactionRequestIncome)
      .subscribe(
        (data: boolean) => {
          if (data) {
            console.log('Income Created');
            this.swalPopup(
              true,
              'Income Creation Success',
              'Income Creation Success',
              this.transactionData,
              this.closeExpenseIncomeDialog()
            );
          }
        },
        (error) => {
          console.error(error);
          this.swalPopup(
            false,
            'Income Creation Failed',
            'Income Creation Failed',
            this.transactionData,
            this.closeExpenseIncomeDialog()
          );
        }
      );
  }

  onBankTransferSubmit() {
    if (this.bankTransferForm.invalid) return;

    const bankTransferFormValue = this.bankTransferForm.value;
    console.log('Form Value:', bankTransferFormValue);

    const entityId = +(localStorage.getItem('entityId') || 0);
    const userId = +(localStorage.getItem('userid') || 0);

    if (entityId === 0 || userId === 0) {
      console.error('Invalid entityId or userId');
      return;
    }

    if (this.transactionData == null) {
      console.error('Transaction Data Is null');
      return;
    }

    if (this.selectedBankAccount == null) {
      console.error('No bank account Found');
      return;
    }

    let transactionType;

    if (this.transactionData.credit != null) {
      transactionType = BankRecTransactionType.CREDIT;
    } else if (this.transactionData.debit != null) {
      transactionType = BankRecTransactionType.DEBIT;
    }

    if (transactionType == undefined) {
      console.error('Selected TransactionType is null');
      return;
    }

    const transactionRequestBankTransfer: TransactionRequestBankTransfer = {
      entityId: entityId,
      userId: userId,
      bankRecDocumentType: BankRecDocumentType.BANK_TRANSFER,
      matchedMode: BankRecMatchMode.TRANSFER,
      selectedTransaction: this.transactionData,
      selectedType: transactionType,
      transactionAmount: bankTransferFormValue.transferAmount,
      transactionReference: bankTransferFormValue.transferDescription,
      fromBankAccountId: this.selectedBankAccount.bankAccountId,
      toBankAccountId: bankTransferFormValue.transferBank,
    };

    this.bankReconciliationService
      .createTransactionBankTransfer(transactionRequestBankTransfer)
      .subscribe(
        (data: boolean) => {
          console.log('BankTransfer Created');
          this.swalPopup(
            true,
            'Bank Transfer Completed',
            'Bank Transfer Completed',
            this.transactionData,
            this.closeBankTransferDialog()
          );
        },
        (error) => {
          this.swalPopup(
            false,
            'Bank Transfer Failed',
            'Bank Transfer Failed',
            this.transactionData,
            this.closeBankTransferDialog()
          );
        }
      );
  }

  updateBusinessEntityReferenceNumber(businessEntity: Entity) {
    this.entityService
      .updateExpensesNumber(businessEntity, businessEntity.entityId)
      .subscribe(
        (data) => {
          return true;
        },
        (error) => {
          console.error(error);
          return false;
        }
      );
  }

  incrementExpenseNumber(expensesNumber: string): string {
    // If the expensesNumber  is null or empty, initialize it to 'B000001'
    if (!expensesNumber) {
      return 'E000001';
    }
    const prefix = expensesNumber.charAt(0); // Extract the 'I'
    const numericPart = expensesNumber.slice(1); // Extract the '000039'
    const incrementedNumber = (Number(numericPart) + 1)
      .toString()
      .padStart(numericPart.length, '0');
    return prefix + incrementedNumber;
  }

  closeExpenseIncomeDialog() {
    this.closeDialog();
    this.expenseForm.reset();
    this.incomeForm.reset();
  }

  openBankTransferDialog(transactionDetails: any) {
    this.transactionData = transactionDetails;
    if (this.transactionData) {
      this.bankTransferForm.patchValue({
        transferDescription: this.transactionData.description,
        transferAmount: this.transactionData.pendingBalance,
      });

      // Add IncomeAmount Validation Here
      const pendingBalance = this.transactionData.pendingBalance;
      this.bankTransferForm
        .get('transferAmount')
        ?.setValidators([Validators.required, Validators.max(pendingBalance)]);
      this.bankTransferForm.get('transferAmount')?.updateValueAndValidity();
      this.isBankTransferOpen = true;
    }
  }

  closeBankTransferDialog() {
    this.transactionData = null;
    this.isBankTransferOpen = false;
    this.bankTransferForm.reset();
  }

  getPaymentReceiptListAndPendingInvoicesByEntity() {
    this.filteredPaymentReceiptsAndInvoiceHeads = [];
    console.info(
      'Starts getPaymentReceiptListByEntity() : ',
      this.filteredPaymentReceiptsAndInvoiceHeads
    );
    const entityId = +(localStorage.getItem('entityId') || 0);

    forkJoin({
      receipts:
        this.bankReconciliationService.getPaymnetReceiptsListByEntity(entityId),
      invoices:
        this.bankReconciliationService.getInvoiceHeadListByEntity(entityId),
    }).subscribe(
      ({ receipts, invoices }) => {
        const pendingReceipts = receipts.filter(
          (r) => r.matchStatus === 'Pending'
        );
        const pendingInvoices = invoices.filter(
          (i) =>
            i.invoiceStatus === 'Pending' ||
            i.invoiceStatus === 'Awaiting Payment'
        );

        this.filteredPaymentReceiptsAndInvoiceHeads = [
          ...pendingInvoices.map((i) => ({ ...i, type: 'invoice' })),
          ...pendingReceipts.map((r) => ({ ...r, type: 'receipt' })),
        ];

        this.filterTransactions();
        console.info(this.filteredPaymentReceiptsAndInvoiceHeads);
      },
      (error) => console.error('Error fetching data:', error)
    );
  }

  getAllPaymentVoucherHeadersAndApInvoices() {
    const entityId = +(localStorage.getItem('entityId') || 0);
    forkJoin({
      vouchers: this.billService.getAllPaymentVoucherHeaderList(entityId),
      bills: this.billService.getAllApInvoiceHeadList(entityId),
    }).subscribe(
      ({ vouchers, bills }) => {
        const pendingVouchers = vouchers.filter(
          (paymentVoucherHeader) =>
            paymentVoucherHeader.matchStatus === 'Pending'
        );
        const pendingBills = bills.filter(
          (bill) =>
            bill.status === 'Pending' || bill.status === 'Awaiting Payment'
        );
        this.filteredPaymentVoucherHeadersAndBills = [
          ...pendingVouchers.map((pv) => ({ ...pv, type: 'voucher' })),
          ...pendingBills.map((pb) => ({ ...pb, type: 'bill' })),
        ];

        this.filterTransactions();
        console.info(this.filteredPaymentReceiptsAndInvoiceHeads);
      },
      (error) => console.error('Error fetching data:', error)
    );
  }

  get filteredFindRecords() {
    const keyword = this.findrecordsFilter.searchTerm?.toLowerCase() || '';
    const amountInput = parseFloat(this.findrecordsFilter.amountterm);

    return this.findRecords.filter((record: any) => {
      let matchesKeyword;

      if (this.selectedTransactionType === 'credit') {
        matchesKeyword =
          !keyword ||
          record.customerName?.toLowerCase().includes(keyword) ||
          record.paymentNumber?.toLowerCase().includes(keyword) ||
          record.reference?.toLowerCase().includes(keyword) ||
          record.invoiceNumber?.toLowerCase().includes(keyword);
      } else if (this.selectedTransactionType === 'debit') {
        matchesKeyword =
          !keyword ||
          record.payeeName?.toLowerCase().includes(keyword) ||
          record.voucherNumber?.toLowerCase().includes(keyword);
      }

      const recBalance =
        record.totalPaidAmount - (record.recAmount || 0) ||
        record.balanceAmount;

      const matchesAmount = isNaN(amountInput) || recBalance == amountInput;

      return matchesKeyword && matchesAmount;
    });
  }

  loadTransactionDetails(bankStatementHeaderId: string) {
    console.log('Load Transactional Details Start');
    this.bankReconciliationService
      .getAllTransactionsByHeader(bankStatementHeaderId)
      .subscribe((response: any) => {
        console.log(response);
        this.allTransactions = response || [];
        this.transactions = this.allTransactions.filter(
          (transaction) => transaction.matchStatus === 'Pending'
        );
        this.filterTransactions();
      });

    console.log('Load Transactional Details End');
  }

  filterTransactions() {
    console.log('Starts filterTransactions() : ');
    this.filteredTransactions = [];

    this.resetMatchingFlags(); // Reset before filtering
    if (this.selectedFilter === 'all') {
      this.filteredTransactions = [...this.transactions];
    } else if (this.selectedFilter === 'credit') {
      this.filteredTransactions = this.transactions.filter((t) => t.credit > 0);
      this.matchCreditTransactions(); // Match credit transactions with InvoiceHead and Payment Receipts
    } else if (this.selectedFilter === 'debit') {
      this.filteredTransactions = this.transactions.filter((t) => t.debit > 0);
      this.matchDebitTransactions(); // Match debit transactions with Vouchers and ApInvoiceHead
    }
    this.tranasctionCount = this.filteredTransactions.length;
    console.log('Ends filterTransactions() : ', this.filteredTransactions);
  }

  ngAfterViewInit() {
    console.log('Transactions:', this.transactions);
    console.log('Filtered Transactions:', this.filteredTransactions);
  }

  protected getApRecbalance(voucherHead: any) {
    const balance = voucherHead.totalPaidAmount - voucherHead.recAmount;
    return balance;
  }

  protected getPaymentReceiptRecbalance(paymentReceipt: any) {
    const balance = paymentReceipt.totalPaidAmount - paymentReceipt.recAmount;
    return balance;
  }

  matchDebitTransactions() {
    console.info('Start matchDebitTransactions()');

    const matchedItems = new Set<string>();
    this.matchedDebitPairs = [];

    // Loop all of them to asign to the rule
    if (this.spendMoneyBankRules && this.spendMoneyBankRules.length > 0) {
      this.filteredTransactions.forEach((transaction) => {
        for (const spendMoneyRule of this.spendMoneyBankRules) {
          const ruleFilterOptions: BankRuleDetail[] =
            spendMoneyRule.bankRuleDetailList.filter(
              (spendRuleDetail) => spendRuleDetail.ruleDetailType === 'F'
            );

          if (
            this.ruleAssignemtForTransaction(transaction, ruleFilterOptions)
          ) {
            transaction.matched = 'rule';
            this.matchedDebitPairs.push({
              transaction,
              type: 'RR',
              assignedRule: spendMoneyRule.bankRuleHead,
              assignedRuleGLDetails: spendMoneyRule.bankRuleDetailList.filter(
                (ruleDetail) => ruleDetail.ruleDetailType === 'G'
              ),
            });
            break;
          }
        }
      });
    }

    const NAME_WEIGHT = 2000;
    const MAX_SCORE_THRESHOLD = 5000;

    // --- Green Matches ---
    this.filteredTransactions.forEach((transaction) => {
      if (!transaction.debit) return;

      for (const item of this.filteredPaymentVoucherHeadersAndBills) {
        const { id, name, amount, type } =
          this.getNormalizedMatchDataForDebit(item);
        const matchKey = `${type}_${id}`;
        if (matchedItems.has(matchKey)) continue;

        const matchesDescription = transaction.description
          .toLowerCase()
          .includes(name);
        const matchesAmount =
          Math.abs(amount - transaction.debit) < this.amountTolerance;

        if (matchesDescription && matchesAmount) {
          item.matched = 'green';
          transaction.matched = 'green';
          matchedItems.add(matchKey);

          this.matchedDebitPairs.push({
            transaction,
            type: 'VB',
            voucherOrBill: item,
          });
          return;
        }
      }
    });

    // --- Yellow / Orange Matches via Hungarian Algorithm ---
    const unmatchedTransactions = this.filteredTransactions.filter(
      (t) => t.debit && t.matched !== 'green' && t.matched !== 'rule'
    );
    const unmatchedItems = this.filteredPaymentVoucherHeadersAndBills.filter(
      (item) => {
        const { id, type } = this.getNormalizedMatchDataForDebit(item);
        return !matchedItems.has(`${type}_${id}`);
      }
    );

    const costMatrix: number[][] = unmatchedTransactions.map((transaction) =>
      unmatchedItems.map((item) => {
        const { name, amount } = this.getNormalizedMatchDataForDebit(item);
        const nameSim = this.getNameSimilarity(transaction.description, name);
        const amountDiff = Math.abs(amount - transaction.debit);
        const score = amountDiff - nameSim * NAME_WEIGHT;
        return Math.max(score, 0);
      })
    );

    if (costMatrix.length && costMatrix[0].length) {
      const indices = munkres(costMatrix);

      // Identify worst match for orange
      let worstScore = -1;
      let worstMatchIndex: number | null = null;

      indices.forEach(([tIdx, rIdx], i) => {
        if (
          tIdx < costMatrix.length &&
          rIdx < costMatrix[0].length &&
          costMatrix[tIdx][rIdx] < MAX_SCORE_THRESHOLD
        ) {
          const score = costMatrix[tIdx][rIdx];
          if (score > worstScore) {
            worstScore = score;
            worstMatchIndex = i;
          }
        }
      });

      indices.forEach(([tIdx, rIdx], i) => {
        if (
          tIdx < costMatrix.length &&
          rIdx < costMatrix[0].length &&
          costMatrix[tIdx][rIdx] < MAX_SCORE_THRESHOLD
        ) {
          const transaction = unmatchedTransactions[tIdx];
          const item = unmatchedItems[rIdx];
          const { id, type } = this.getNormalizedMatchDataForDebit(item);
          const matchKey = `${type}_${id}`;
          matchedItems.add(matchKey);

          const matchType = i === worstMatchIndex ? 'orange' : 'yellow';
          item.matched = matchType;
          transaction.matched = matchType;

          this.matchedDebitPairs.push({
            transaction,
            type: 'VB',
            voucherOrBill: item,
          });
        }
      });
    }

    console.info('matchDebitTransactions', this.matchedDebitPairs);
    console.info('End matchDebitTransactions()');

    return this.matchedDebitPairs;
  }

  private getNormalizedMatchDataForDebit(item: any): {
    id: number;
    name: string;
    amount: number;
    type: 'voucher' | 'bill';
  } {
    return {
      id:
        item.type === 'voucher'
          ? item.paymentVoucherHeaderId
          : item.type === 'bill'
          ? item.apInvoiceHeadId
          : null,
      name:
        item.type === 'voucher'
          ? item.payeeName?.toLowerCase() || ''
          : item.type === 'bill'
          ? item.supplierName?.toLowerCase() || ''
          : '',
      amount:
        item.type === 'voucher'
          ? this.getApRecbalance(item)
          : item.type === 'bill'
          ? item.dueAmount
          : 0,
      type: item.type,
    };
  }

  private getNormalizedMatchDataForCredit(item: any): {
    id: number;
    name: string;
    amount: number;
    type: 'receipt' | 'invoice';
  } {
    return {
      id:
        item.type === 'receipt'
          ? item.paymentReceiptId
          : item.type === 'invoice'
          ? item.invoiceHeadId
          : null,
      name:
        item.type === 'receipt'
          ? item.customerName?.toLowerCase() || ''
          : item.type === 'invoice'
          ? item.reference?.toLowerCase() || ''
          : '',
      amount:
        item.type === 'receipt'
          ? this.getPaymentReceiptRecbalance(item)
          : item.type === 'invoice'
          ? item.balanceAmount
          : 0,
      type: item.type,
    };
  }

  private ruleAssignemtForTransaction(
    transaction: any,
    ruleDetails: BankRuleDetail[]
  ): boolean {
    let hasDescriptionCondition = false;
    let descriptionMatch = false;
    let amountMatch = true;

    for (const detail of ruleDetails) {
      const field = detail.ruleMatchField;
      const filter = detail.ruleFilterText?.toLowerCase() ?? '';

      if (field === 'Descrption') {
        hasDescriptionCondition = true;
        const txDesc = transaction.description?.toLowerCase() ?? '';

        switch (detail.ruleFilterBy) {
          case 'Equals':
            if (txDesc === filter) descriptionMatch = true;
            break;
          case 'Contains':
            if (txDesc.includes(filter) || filter.includes(txDesc))
              descriptionMatch = true;
            break;
          case 'Starts with':
            if (txDesc.startsWith(filter)) descriptionMatch = true;
            break;
        }
      }

      if (field === 'Amount') {
        const txAmount = Number(transaction.pendingBalance);
        const ruleAmount = Number(filter);
        if (txAmount !== ruleAmount) {
          amountMatch = false;
          break;
        }
      }
    }

    return (!hasDescriptionCondition || descriptionMatch) && amountMatch;
  }

  matchCreditTransactions() {
    console.info('Start matchCreditTransactions() ', this.filteredTransactions);
    const matchedItems = new Set<string>();
    this.matchedCreditPairs = [];

    // Loop all of them to asign to the rule
    if (this.receiveMoneyBankRules && this.receiveMoneyBankRules.length > 0) {
      this.filteredTransactions.forEach((transaction) => {
        for (const receiveMoneyRule of this.receiveMoneyBankRules) {
          const ruleFilterOptions: BankRuleDetail[] =
            receiveMoneyRule.bankRuleDetailList.filter(
              (receiveRuleDetail) => receiveRuleDetail.ruleDetailType === 'F'
            );

          if (
            this.ruleAssignemtForTransaction(transaction, ruleFilterOptions)
          ) {
            transaction.matched = 'rule';
            this.matchedCreditPairs.push({
              transaction,
              type: 'SR',
              assignedRule: receiveMoneyRule.bankRuleHead,
              assignedRuleGLDetails: receiveMoneyRule.bankRuleDetailList.filter(
                (ruleDetail) => ruleDetail.ruleDetailType === 'G'
              ),
            });
            break;
          }
        }
      });
    }

    const NAME_WEIGHT = 2000;
    const MAX_SCORE_THRESHOLD = 5000;

    // --- Green Matches ---
    this.filteredTransactions.forEach((transaction) => {
      for (const item of this.filteredPaymentReceiptsAndInvoiceHeads) {
        const { id, name, amount, type } =
          this.getNormalizedMatchDataForCredit(item);
        const matchKey = `${type}_${id}`;

        if (matchedItems.has(matchKey)) continue;

        const matchesDescription = transaction.description
          .toLowerCase()
          .includes(name);
        const matchesAmount =
          Math.abs(amount - transaction.pendingBalance) < this.amountTolerance;

        if (matchesDescription && matchesAmount) {
          item.matched = 'green';
          transaction.matched = 'green';
          matchedItems.add(matchKey);
          this.matchedCreditPairs.push({
            transaction,
            type: 'PRI',
            paymentReceiptOrInvoice: item,
          });
          return;
        }
      }
    });

    // --- Yellow Matches with Hungarian Algorithm ---
    const unmatchedTransactions = this.filteredTransactions.filter(
      (t) => t.matched !== 'green' && t.matched !== 'rule'
    );

    console.info('Unmatched Transactions : ', unmatchedTransactions);

    const unmatchedItems = this.filteredPaymentReceiptsAndInvoiceHeads.filter(
      (r) => {
        const { id, type } = this.getNormalizedMatchDataForCredit(r);
        return !matchedItems.has(`${type}_${id}`);
      }
    );

    const costMatrix: number[][] = unmatchedTransactions.map((transaction) =>
      unmatchedItems.map((item) => {
        const { name, amount } = this.getNormalizedMatchDataForCredit(item);
        const nameSim = this.getNameSimilarity(transaction.description, name);
        const amountDiff = Math.abs(amount - transaction.pendingBalance);
        const score = amountDiff - nameSim * NAME_WEIGHT;
        return Math.max(score, 0);
      })
    );

    if (costMatrix.length && costMatrix[0].length) {
      const indices = munkres(costMatrix);

      // Find worst match for orange
      let worstScore = -1;
      let worstMatchIndex: number | null = null;

      indices.forEach(([tIdx, rIdx], i) => {
        if (
          tIdx < costMatrix.length &&
          rIdx < costMatrix[0].length &&
          costMatrix[tIdx][rIdx] < MAX_SCORE_THRESHOLD
        ) {
          const score = costMatrix[tIdx][rIdx];
          if (score > worstScore) {
            worstScore = score;
            worstMatchIndex = i;
          }
        }
      });

      indices.forEach(([tIdx, rIdx], i) => {
        if (
          tIdx < costMatrix.length &&
          rIdx < costMatrix[0].length &&
          costMatrix[tIdx][rIdx] < MAX_SCORE_THRESHOLD
        ) {
          const transaction = unmatchedTransactions[tIdx];
          const item = unmatchedItems[rIdx];
          const { id, type } = this.getNormalizedMatchDataForCredit(item);
          const matchKey = `${type}_${id}`;
          matchedItems.add(matchKey);

          const matchType = i === worstMatchIndex ? 'orange' : 'yellow';
          item.matched = matchType;
          transaction.matched = matchType;

          this.matchedCreditPairs.push({
            transaction,
            type: 'PRI',
            paymentReceiptOrInvoice: item,
          });
        }
      });
    }

    console.info('matchCreditTransactions', this.matchedCreditPairs);
    console.info('End matchCreditTransactions()');

    return this.matchedCreditPairs;
  }

  getNameSimilarity(a: string, b: string): number {
    a = a.toLowerCase();
    b = b.toLowerCase();

    if (a.includes(b) || b.includes(a)) return 1;

    // Simple string similarity fallback (can be enhanced)
    let commonLength = 0;
    for (let i = 0; i < Math.min(a.length, b.length); i++) {
      if (a[i] === b[i]) commonLength++;
      else break;
    }

    return commonLength / Math.max(a.length, b.length); // returns between 0 and 1
  }

  runHungarianAlgorithm(costMatrix: number[][]): [number[], number[]] {
    const indices = munkres(costMatrix); // returns pairs of indices [row, col]

    const transactionIndices: number[] = [];
    const receiptIndices: number[] = [];

    for (const [tIdx, rIdx] of indices) {
      if (
        tIdx >= 0 &&
        tIdx < costMatrix.length &&
        rIdx >= 0 &&
        rIdx < costMatrix[0].length
      ) {
        transactionIndices.push(tIdx);
        receiptIndices.push(rIdx);
      }
    }

    return [transactionIndices, receiptIndices];
  }

  resetMatchingFlags() {
    this.filteredTransactions.forEach((t) => {
      t.matched = false;
    });

    this.filteredPaymentVoucherHeadersAndBills.forEach((voucherHead) => {
      voucherHead.matched = false;
    });

    this.filteredPaymentReceiptsAndInvoiceHeads.forEach((paymentReceipt) => {
      paymentReceipt.matched = false;
    });
  }

  viewDetails(type: string, data: any): void {
    console.log('Opening popup for:', type, data);
    if (data) {
      this.isViewDetails = true;
      this.selectedRecordType = type;
      this.selectedDetails = data;
    }
  }

  closeViewDetails() {
    this.isViewDetails = false;
    this.selectedRecordType = ' ';
    this.selectedDetails = null;
  }

  openDialog(transaction: any): void {
    this.bankReconciliationService.setFile(this.selectedFile);
    this.bankReconciliationService.setTransactions(this.transactions);
    console.log(
      'Stored:',
      this.bankReconciliationService.getFile(),
      this.bankReconciliationService.getTransactions()
    );

    // Here add the Create Credit Invoice
    if (
      transaction.credit !== null &&
      transaction.credit !== undefined &&
      transaction.pendingBalance !== null &&
      transaction.pendingBalance !== undefined &&
      transaction.pendingBalance > 0
    ) {
      this.transactionData = transaction;
      if (this.transactionData) {
        this.incomeForm.patchValue({
          incomeDescription: this.transactionData.description,
          incomeAmount: this.transactionData.pendingBalance,
        });

        // Add IncomeAmount Validation Here
        const pendingBalance = this.transactionData.pendingBalance;
        this.incomeForm
          .get('incomeAmount')
          ?.setValidators([
            Validators.required,
            Validators.max(pendingBalance),
          ]);
        this.expenseForm.get('incomeAmount')?.updateValueAndValidity();

        this.isExpenseOpen = true;
        this.isCreditPopup = true;
        this.isDebitPopup = false;
      }

      return;
    }

    // Here add the Create Debit Bill
    if (
      transaction.debit !== null &&
      transaction.debit !== undefined &&
      transaction.pendingBalance !== null &&
      transaction.pendingBalance !== undefined &&
      transaction.pendingBalance > 0
    ) {
      this.transactionData = transaction;
      if (this.transactionData) {
        this.expenseForm.patchValue({
          payeeName: this.transactionData.description,
          amount: this.transactionData.pendingBalance,
        });

        // Add Amount Validation Here
        const pendingBalance = this.transactionData.pendingBalance;
        this.expenseForm
          .get('amount')
          ?.setValidators([
            Validators.required,
            Validators.max(pendingBalance),
          ]);
        this.expenseForm.get('amount')?.updateValueAndValidity();

        this.isExpenseOpen = true;
        this.isCreditPopup = false;
        this.isDebitPopup = true;
      }
    }
  }

  closeDialog(): void {
    this.isExpenseOpen = false;
    this.isCreditPopup = false;
    this.isDebitPopup = false;
    this.transactionData = null;
  }

  saveSuggestedTransaction(transaction: any) {
    const entityId = +(localStorage.getItem('entityId') || 0);
    const userId = +(localStorage.getItem('userid') || 0);

    if (transaction.credit !== null && transaction.credit !== undefined) {
      const foundReceiptOrInvoice = this.matchedCreditPairs.find(
        (pair) => pair.transaction.id === transaction.id
      )?.paymentReceiptOrInvoice;

      // If its a Receipt
      if (foundReceiptOrInvoice.type === 'receipt') {
        this.suggestedPaymentReceipt = foundReceiptOrInvoice;
        console.log('processing as confirm - receipt');
        if (this.suggestedPaymentReceipt) {
          console.log(this.suggestedPaymentReceipt);

          // Create the TranscationDetailDTO
          const transactionRequestCreditListDTO: TransactionRequestCreditListDTO =
            {
              selectedType: BankRecTransactionType.CREDIT,
              paymentType: BankRecDocumentType.PAYMENT_RECEIPT,
              matchedMode: BankRecMatchMode.SYSTEM_MATCH,
              selectedTransaction: transaction,
              transactionAmount: transaction.pendingBalance,
              paymentTypeId: this.suggestedPaymentReceipt.paymentReceiptId,
              paymentTypeAmount: this.getPaymentReceiptRecbalance(
                this.suggestedPaymentReceipt
              ),
              paymentTypeTotalRecBalanceAmount:
                this.getPaymentReceiptRecbalance(this.suggestedPaymentReceipt),
              paymentTypeReference: this.suggestedPaymentReceipt.customerName,
              paymentTypeDate: this.suggestedPaymentReceipt.documentDate,
              entityId: entityId,
              userId: userId,
            };

          this.bankReconciliationService
            .saveSuggestCreditTransaction(transactionRequestCreditListDTO)
            .subscribe(
              (response: string) => {
                console.log('Transaction saved successfully', response);
                this.swalPopup(
                  true,
                  'Trasaction Matched',
                  'Transaction Matched',
                  transaction
                );
              },
              (error) => {
                console.error('Error saving transaction', error);
                this.swalPopup(
                  false,
                  'Trasaction Matched Error',
                  'Transaction Matched Error',
                  transaction
                );
              }
            );
        }

        return;
      }

      // If its a Invoice
      if (foundReceiptOrInvoice.type === 'invoice') {
        console.log('processing as confirm - invoice');
        this.suggestedInvoiceHead = foundReceiptOrInvoice;

        if (this.suggestedInvoiceHead) {
          // Create the TranscationDetailDTO - Invoice
          const transactionRequestCreditListDTO: TransactionRequestCreditListDTO =
            {
              selectedType: BankRecTransactionType.CREDIT,
              paymentType: BankRecDocumentType.INVOICE,
              matchedMode: BankRecMatchMode.SYSTEM_MATCH,
              selectedTransaction: transaction,
              transactionAmount: transaction.pendingBalance,
              paymentTypeId: this.suggestedInvoiceHead.invoiceHeadId,
              paymentTypeAmount: this.suggestedInvoiceHead.balanceAmount,
              paymentTypeTotalRecBalanceAmount:
                this.suggestedInvoiceHead.balanceAmount,
              paymentTypeReference: this.suggestedInvoiceHead.reference,
              paymentTypeDate: this.suggestedInvoiceHead.date,
              entityId: entityId,
              userId: userId,
            };

          this.bankReconciliationService
            .saveFindCreditTransaction(transactionRequestCreditListDTO)
            .subscribe(
              (response: string) => {
                console.log('Transaction saved successfully', response);
                this.swalPopup(
                  true,
                  'Trasaction Matched',
                  'Transaction Matched',
                  transaction
                );
              },
              (error) => {
                console.error('Error saving transaction', error);
                this.swalPopup(
                  false,
                  'Trasaction Matched Error',
                  'Transaction Matched Error',
                  transaction
                );
              }
            );
        }

        return;
      }

      return;
    }

    if (transaction.debit !== null && transaction.debit !== undefined) {
      const foundVoucherOrBill = this.matchedDebitPairs.find(
        (pair) => pair.transaction.id === transaction.id
      )?.voucherOrBill;

      // If its a Voucher
      if (foundVoucherOrBill.type === 'voucher') {
        console.log('processing as confirm - voucher');
        this.suggestedPaymentVoucherHeader = foundVoucherOrBill;

        if (!this.suggestedPaymentVoucherHeader) {
          console.log('SuggestedPaymentVoucherHeader ins null');
          return;
        }

        const matchDetailRequest: TransactionRequestCreditListDTO = {
          selectedType: BankRecTransactionType.DEBIT,
          paymentType: BankRecDocumentType.PAYMENT_VOUCHER,
          matchedMode: BankRecMatchMode.SYSTEM_MATCH,
          selectedTransaction: transaction,
          transactionAmount: transaction.pendingBalance,
          paymentTypeId:
            this.suggestedPaymentVoucherHeader.paymentVoucherHeaderId,
          paymentTypeAmount: this.getApRecbalance(
            this.suggestedPaymentVoucherHeader
          ),
          paymentTypeTotalRecBalanceAmount: this.getApRecbalance(
            this.suggestedPaymentVoucherHeader
          ),
          paymentTypeReference: this.suggestedPaymentVoucherHeader.payeeName,
          paymentTypeDate: this.suggestedPaymentVoucherHeader.date,
          entityId: entityId,
          userId: userId,
        };

        this.bankReconciliationService
          .saveFindDebitTransaction(matchDetailRequest)
          .subscribe(
            (response: string) => {
              console.log('Transaction saved successfully', response);
              this.swalPopup(
                true,
                'Trasaction Matched',
                'Transaction Matched',
                transaction
              );
            },
            (error) => {
              console.error('Error saving transaction', error);
              this.swalPopup(
                false,
                'Trasaction Matched Error',
                'Transaction Matched Error',
                transaction
              );
            }
          );

        return;
      }

      if (foundVoucherOrBill.type === 'bill') {
        console.log('processing as confirm - bill');

        this.suggestedBillHead = foundVoucherOrBill;

        if (!this.suggestedBillHead) {
          console.log('SuggestedBillHead in null');
          return;
        }

        const matchDetailRequest: TransactionRequestCreditListDTO = {
          selectedType: BankRecTransactionType.DEBIT,
          paymentType: BankRecDocumentType.BILL,
          matchedMode: BankRecMatchMode.SYSTEM_MATCH,
          selectedTransaction: transaction,
          transactionAmount: transaction.pendingBalance,
          paymentTypeId: this.suggestedBillHead.apInvoiceHeadId,
          paymentTypeAmount: this.suggestedBillHead.dueAmount,
          paymentTypeTotalRecBalanceAmount: this.suggestedBillHead.dueAmount,
          paymentTypeReference: this.suggestedBillHead.supplierName,
          paymentTypeDate: this.suggestedBillHead.postingDate,
          entityId: entityId,
          userId: userId,
        };

        this.bankReconciliationService
          .saveFindDebitTransaction(matchDetailRequest)
          .subscribe(
            (response: string) => {
              console.log('Transaction saved successfully', response);
              this.swalPopup(
                true,
                'Trasaction Matched',
                'Transaction Matched',
                transaction
              );
            },
            (error) => {
              console.error('Error saving transaction', error);
              this.swalPopup(
                false,
                'Trasaction Matched Error',
                'Transaction Matched Error',
                transaction
              );
            }
          );
      }
    }
  }

  openFindPopup(transaction: any): void {
    this.isFindPopup = true;
    this.selectedTransactionForFind = transaction;

    if (transaction.credit !== null && transaction.credit !== undefined) {
      this.selectedTransactionType = 'credit';
      this.findRecords = this.filteredPaymentReceiptsAndInvoiceHeads;
      this.selectedTransactionAmount = transaction.pendingBalance;
      console.log('find records credit: ', this.findRecords);
      return;
    }

    if (transaction.debit !== null && transaction.debit !== undefined) {
      this.selectedTransactionType = 'debit';
      this.findRecords = this.filteredPaymentVoucherHeadersAndBills;
      this.selectedTransactionAmount = transaction.pendingBalance;
      console.info('find records debit: ', this.findRecords);
    }
  }

  closeFindPopup(): void {
    this.isFindPopup = false;
    this.selectedTransactionForFind = null;
    this.findRecords = [];
    this.selectedRecords = [];
    this.totalSelectedAmount = 0;
    this.findrecordsFilter = {
      searchTerm: '',
      amountterm: '',
    };
  }

  saveFindTransaction(selectedDetail: any, recAmountValue: number) {
    // Assign the selected records based on transaction type
    let selectedDetailAsignableAmount: number = -1;
    this.selectedPaymentReceipt = null;
    this.selectedInvoiceHead = null;
    this.selectedVoucherHead = null;
    this.selectedBillHead = null;

    if (this.selectedTransactionType === 'debit') {
      if (selectedDetail.type === 'voucher') {
        selectedDetailAsignableAmount =
          selectedDetail.totalPaidAmount - selectedDetail.recAmount;
        this.selectedVoucherHead = selectedDetail;
      } else if (selectedDetail.type === 'bill') {
        selectedDetailAsignableAmount = selectedDetail.dueAmount;
        this.selectedBillHead = selectedDetail;
      } else {
        return;
      }
    } else if (this.selectedTransactionType === 'credit') {
      // Receipt
      if (selectedDetail.type === 'receipt') {
        selectedDetailAsignableAmount =
          selectedDetail.totalPaidAmount - selectedDetail.recAmount;
        this.selectedPaymentReceipt = selectedDetail;
      } else if (selectedDetail.type === 'invoice') {
        selectedDetailAsignableAmount = selectedDetail.balanceAmount;
        this.selectedInvoiceHead = selectedDetail;
      } else {
        return;
      }
    } else {
      return;
    }

    if (
      selectedDetailAsignableAmount == null ||
      selectedDetailAsignableAmount < 0
    ) {
      console.warn(
        'selectedDetailAsignableAmount is not valid : ',
        selectedDetailAsignableAmount
      );

      return;
    }

    const maxApplicableAmount = Math.min(
      this.selectedTransactionAmount,
      selectedDetailAsignableAmount
    );

    if (recAmountValue <= 0) {
      console.warn('Entered amount must be greater than 0.');
      return;
    }

    if (recAmountValue > maxApplicableAmount) {
      console.warn(
        'Entered amount exceeds the maximum applicable amount: ' +
          maxApplicableAmount
      );
      return;
    }

    const entityId = +(localStorage.getItem('entityId') || 0);
    const userId = +(localStorage.getItem('userid') || 0);

    // Credit - Receipt
    if (
      this.selectedTransactionType === 'credit' &&
      this.selectedTransactionForFind &&
      this.selectedPaymentReceipt
    ) {
      // Create the TranscationDetailDTO - Receipt
      console.log('Working Find - Credit - Receipt');

      const transactionRequestCreditListDTO: TransactionRequestCreditListDTO = {
        selectedType: BankRecTransactionType.CREDIT,
        paymentType: BankRecDocumentType.PAYMENT_RECEIPT,
        matchedMode: BankRecMatchMode.FIND,
        selectedTransaction: this.selectedTransactionForFind,
        transactionAmount: this.selectedTransactionForFind.pendingBalance,
        paymentTypeId: this.selectedPaymentReceipt.paymentReceiptId,
        paymentTypeAmount: recAmountValue,
        paymentTypeTotalRecBalanceAmount: this.getPaymentReceiptRecbalance(
          this.selectedPaymentReceipt
        ),
        paymentTypeReference: this.selectedPaymentReceipt.customerName,
        paymentTypeDate: this.selectedPaymentReceipt.documentDate,
        entityId: entityId,
        userId: userId,
      };

      this.bankReconciliationService
        .saveFindCreditTransaction(transactionRequestCreditListDTO)
        .subscribe(
          (response: string) => {
            console.log('Transaction saved successfully', response);
            if (response) {
              this.swalPopup(
                true,
                'Credit-Find Success',
                'Credit-Find Success',
                this.selectedTransactionForFind,
                () => {
                  this.isFindPopup = false;
                }
              );
            } else {
              this.swalPopup(
                false,
                'Credit-Find Failed',
                'Credit-Find Failed',
                this.selectedTransactionForFind,
                () => {
                  this.isFindPopup = false;
                }
              );
            }
          },
          (error) => {
            console.error('Error saving transaction', error);
            this.swalPopup(
              true,
              'Credit-Find Success',
              'Credit-Find Success',
              this.selectedTransactionForFind,
              () => {
                this.isFindPopup = false;
              }
            );
          }
        );

      return;
    }

    // Credit - InvoiceHead
    if (
      this.selectedTransactionType === 'credit' &&
      this.selectedTransactionForFind &&
      this.selectedInvoiceHead
    ) {
      // Create the TranscationDetailDTO - Invoice
      console.log('Working Find - Credit - Invoice');
      const transactionRequestCreditListDTO: TransactionRequestCreditListDTO = {
        selectedType: BankRecTransactionType.CREDIT,
        paymentType: BankRecDocumentType.INVOICE,
        matchedMode: BankRecMatchMode.FIND,
        selectedTransaction: this.selectedTransactionForFind,
        transactionAmount: this.selectedTransactionForFind.pendingBalance,
        paymentTypeId: this.selectedInvoiceHead.invoiceHeadId,
        paymentTypeAmount: recAmountValue,
        paymentTypeTotalRecBalanceAmount:
          this.selectedInvoiceHead.balanceAmount,
        paymentTypeReference: this.selectedInvoiceHead.reference,
        paymentTypeDate: this.selectedInvoiceHead.date,
        entityId: entityId,
        userId: userId,
      };

      this.bankReconciliationService
        .saveFindCreditTransaction(transactionRequestCreditListDTO)
        .subscribe(
          (response: string) => {
            console.log('Transaction saved successfully', response);
            if (response) {
              this.swalPopup(
                true,
                'Credit-Find Success',
                'Credit-Find Success',
                this.selectedTransactionForFind,
                () => {
                  this.isFindPopup = false;
                }
              );
            } else {
              this.swalPopup(
                false,
                'Credit-Find Failed',
                'Credit-Find Failed',
                this.selectedTransactionForFind,
                () => {
                  this.isFindPopup = false;
                }
              );
            }
          },
          (error) => {
            console.error('Error saving transaction', error);
            this.swalPopup(
              false,
              'Debit-Find Error',
              'Debit-Find Error',
              this.selectedTransactionForFind,
              () => {
                this.isFindPopup = false;
              }
            );
          }
        );

      return;
    }

    if (
      this.selectedTransactionType === 'debit' &&
      this.selectedTransactionForFind &&
      this.selectedVoucherHead
    ) {
      console.log('Working Find - Debit - Voucher');
      const transactionRequestCreditListDTO: TransactionRequestCreditListDTO = {
        selectedType: BankRecTransactionType.DEBIT,
        paymentType: BankRecDocumentType.PAYMENT_VOUCHER,
        matchedMode: BankRecMatchMode.FIND,
        selectedTransaction: this.selectedTransactionForFind,
        transactionAmount: this.selectedTransactionForFind.pendingBalance,
        paymentTypeId: this.selectedVoucherHead.paymentVoucherHeaderId,
        paymentTypeAmount: recAmountValue,
        paymentTypeTotalRecBalanceAmount: this.getApRecbalance(
          this.selectedVoucherHead
        ),
        paymentTypeReference: this.selectedVoucherHead.payeeName,
        paymentTypeDate: this.selectedVoucherHead.date,
        entityId: entityId,
        userId: userId,
      };

      this.bankReconciliationService
        .saveFindDebitTransaction(transactionRequestCreditListDTO)
        .subscribe(
          (response: string) => {
            console.log('Transaction saved successfully', response);
            if (response) {
              this.swalPopup(
                true,
                'Debit-Find Success',
                'Debit-Find Success',
                this.selectedTransactionForFind,
                () => {
                  this.isFindPopup = false;
                }
              );
            } else {
              this.swalPopup(
                false,
                'Debit-Find Failed',
                'Debit-Find Failed',
                this.selectedTransactionForFind,
                () => {
                  this.isFindPopup = false;
                }
              );
            }
          },
          (error) => {
            console.error('Error saving transaction', error);
            this.swalPopup(
              false,
              'Debit-Find Error',
              'Debit-Find Error',
              this.selectedTransactionForFind,
              () => {
                this.isFindPopup = false;
              }
            );
          }
        );
      return;
    }

    if (
      this.selectedTransactionType === 'debit' &&
      this.selectedTransactionForFind &&
      this.selectedBillHead
    ) {
      console.log('Working Find - Debit - Bill');
      const transactionRequestCreditListDTO: TransactionRequestCreditListDTO = {
        selectedType: BankRecTransactionType.DEBIT,
        paymentType: BankRecDocumentType.BILL,
        matchedMode: BankRecMatchMode.FIND,
        selectedTransaction: this.selectedTransactionForFind,
        transactionAmount: this.selectedTransactionForFind.pendingBalance,
        paymentTypeId: this.selectedBillHead.apInvoiceHeadId,
        paymentTypeAmount: recAmountValue,
        paymentTypeTotalRecBalanceAmount: this.selectedBillHead.dueAmount,
        paymentTypeReference: this.selectedBillHead.supplierName,
        paymentTypeDate: this.selectedBillHead.postingDate,
        entityId: entityId,
        userId: userId,
      };

      this.bankReconciliationService
        .saveFindDebitTransaction(transactionRequestCreditListDTO)
        .subscribe(
          (response: string) => {
            console.log('Transaction saved successfully', response);
            if (response) {
              this.swalPopup(
                true,
                'Debit-Find Success',
                'Debit-Find Success',
                this.selectedTransactionForFind,
                () => {
                  this.isFindPopup = false;
                }
              );
            } else {
              this.swalPopup(
                false,
                'Debit-Find Failed',
                'Debit-Find Failed',
                this.selectedTransactionForFind,
                () => {
                  this.isFindPopup = false;
                }
              );
            }
          },
          (error) => {
            console.error('Error saving transaction', error);
            this.swalPopup(
              false,
              'Debit-Find Error',
              'Debit-Find Error',
              this.selectedTransactionForFind,
              () => {
                this.isFindPopup = false;
              }
            );
          }
        );
      return;
    }
  }

  navigateToMatchTransactions() {
    if (this.bankStatementHeader && this.bankStatementHeader.bankStatementId) {
      const bankStatementId = this.bankStatementHeader.bankStatementId;
      const dialogRef = this.dialog.open(MatchTransactionsComponent, {
        width: '90%',
        height: '90%',
        data: { bankStatementId },
        disableClose: false,
        panelClass: 'p-0',
      });

      dialogRef.afterClosed().subscribe((result) => {
        this.loadBankRules();
        this.getAllPaymentVoucherHeadersAndApInvoices();
        this.getPaymentReceiptListAndPendingInvoicesByEntity();
        this.loadTransactionDetails(bankStatementId);
        if (this.selectedBankAccount?.basiqConnectionId) {
          const today = new Date().toISOString().split('T')[0];
          this.selectedDateFilter = today;
          this.loadAllTransactionsFromBankFeed(this.selectedBankAccount,today);
        }
      });
    } else {
      console.error('bankStatementHeader or bankStatementId is missing');
    }
  }

  navigateToBankRule() {
    if (this.selectedBankAccount) {
      this.router.navigate(['/bank-rule-list'], {
        state: { selectedBankAccount: this.selectedBankAccount },
      });
    }
  }

  navigateToRecReport() {
    if (this.selectedBankAccount) {
      this.router.navigate(['/final-bank-rec']);
    }
  }


  openCreateInvoiceDialog(transactionDetails: any) {
    const dialogRef = this.dialog.open(CreateInvoiceComponent, {
      width: '1000px',
      height: '600px',
      data: transactionDetails,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (result.success && result.invoice) {
          // Save it Automatically as a create-rec
          this.saveCreateCreditTransaction(transactionDetails, result.invoice);
          this.getPaymentReceiptListAndPendingInvoicesByEntity();
        }
      }
    });
  }

  saveCreateCreditTransaction(transactionDetail: any, invoice: InvoiceHead) {
    const entityId = +(localStorage.getItem('entityId') || 0);
    const userId = +(localStorage.getItem('userid') || 0);

    this.bankReconciliationService
      .getPaymentReceiptByInvoiceId(invoice.invoiceNumber, entityId)
      .subscribe(
        (data: PaymentReceiptsHead) => {
          console.info(data);

          if (data) {
            const transactionRequestCreditListDTO: TransactionRequestCreditListDTO =
              {
                selectedType: BankRecTransactionType.CREDIT,
                paymentType: BankRecDocumentType.PAYMENT_RECEIPT,
                matchedMode: BankRecMatchMode.CREATE,
                selectedTransaction: transactionDetail,
                transactionAmount: transactionDetail.pendingBalance,
                paymentTypeId: data.paymentReceiptId,
                paymentTypeAmount: this.getPaymentReceiptRecbalance(data),
                paymentTypeTotalRecBalanceAmount:
                  this.getPaymentReceiptRecbalance(data),
                paymentTypeReference: data.customerName,
                paymentTypeDate: data.documentDate,
                entityId: entityId,
                userId: userId,
              };

            this.bankReconciliationService
              .saveSuggestCreditTransaction(transactionRequestCreditListDTO)
              .subscribe(
                (response: string) => {
                  console.log('Transaction saved successfully', response);
                  if (response) {
                    this.swalPopup(
                      true,
                      'System Matching Completed',
                      'System Matching Completed',
                      transactionDetail,
                      () => {
                        this.isFindPopup = false;
                      }
                    );
                  } else {
                    this.swalPopup(
                      false,
                      'System Matching Failed',
                      'System Matching Failed',
                      transactionDetail,
                      () => {
                        this.isFindPopup = false;
                      }
                    );
                  }
                },
                (error) => {
                  console.error('Error saving transaction', error);
                  this.swalPopup(
                    false,
                    'System Matching Error',
                    'System Matching Error',
                    transactionDetail,
                    () => {
                      this.isFindPopup = false;
                    }
                  );
                }
              );
          } else {
            alert('Payment Receipt Not Found');
          }
        },
        (error) => {
          console.error('Error fetching PaymentReceiptsHead:', error);
          alert('Eoor Pops out');
        }
      );
  }

  openCreateAPInvoiceDialog(transactionDetails: any) {
    const dialogRef = this.dialog.open(AddPayableBillComponent, {
      width: '1000px',
      height: '600px',
      data: transactionDetails,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (result.success && result.apInvoice) {
          console.info(result);
          // Save it Automatically as a create-rec
          this.getAllPaymentVoucherHeadersAndApInvoices();
        }
      }
    });
  }

  protected getAllocatbleAmount(
    transactionAmount: number,
    selectedDetail: any
  ) {
    const selectedDetailBalance: number =
      selectedDetail.totalPaidAmount - selectedDetail.recAmount;

    const value: number =
      transactionAmount > selectedDetailBalance
        ? selectedDetailBalance
        : transactionAmount;

    return Math.round((value + Number.EPSILON) * 100) / 100;
  }

  protected getAllocatbleAmountForInvoice(
    transactionAmount: number,
    selectedDetail: any
  ) {
    const value: number =
      transactionAmount > selectedDetail.balanceAmount
        ? selectedDetail.balanceAmount
        : transactionAmount;

    return Math.round((value + Number.EPSILON) * 100) / 100;
  }

  protected getAllocatbleAmountForBill(
    transactionAmount: number,
    selectedDetail: any
  ) {
    const value: number =
      transactionAmount > selectedDetail.dueAmount
        ? selectedDetail.dueAmount
        : transactionAmount;

    return Math.round((value + Number.EPSILON) * 100) / 100;
  }

  private swalPopup(
    success: boolean,
    title: string,
    body: string,
    transaction: any,
    callbackFn?: any
  ) {
    const icon = success ? 'success' : 'error';
    const confirmButtonText = success ? 'OK' : 'Retry';

    Swal.fire({
      title: title,
      text: body,
      icon: icon,
      background: '#ffffff',
      color: '#000000',
      confirmButtonColor: '#4262ff',
      confirmButtonText: confirmButtonText,
      customClass: {
        popup: 'swal-custom-popup',
        title: 'swal-custom-title',
        confirmButton: 'swal-custom-button',
      },
    }).then(() => {
      this.loadBankRules();
      this.getAllPaymentVoucherHeadersAndApInvoices();
      this.getPaymentReceiptListAndPendingInvoicesByEntity();
      this.loadTransactionDetails(
        transaction.bankStatementHeader.bankStatementId
      );
      if (callbackFn) {
        callbackFn();
      }
      console.log('Handle Popup Working..................... : END');
    });
  }

  // BankRule Card
  toggleRowExpand(index: number): void {
    this.expandedRowIndex = this.expandedRowIndex === index ? null : index;
  }

  protected valueFromPercentage(
    transactionDetail: BankStatementDetail,
    percentage: number
  ): number {
    const pendingBalance = transactionDetail.pendingBalance || 0;
    const value = (pendingBalance * percentage) / 100;
    return Math.round(value * 100) / 100;
  }

  submitBankRuleMatch(
    ruleDetailsList: any[],
    transaction: BankStatementDetail
  ): void {
    console.info(ruleDetailsList);

    const entityId = +(localStorage.getItem('entityId') || 0);
    const userId = +(localStorage.getItem('userid') || 0);

    if (!entityId || entityId == null) {
      console.error('EntityId is NULL');
      return;
    }

    if (!userId || userId == null) {
      console.error('UserId is NULL');
      return;
    }

    if (!transaction || transaction == null) {
      console.error('Selected Transaction is NULL');
      return;
    }

    if (
      !ruleDetailsList ||
      ruleDetailsList == null ||
      ruleDetailsList.length == 0
    ) {
      console.error('Selected BankRuleDetails are NULL');
      return;
    }

    if (!ruleDetailsList[0].bankRuleHead) {
      console.error('Selected BankRuleHead is NULL');
      return;
    }

    const bankRuleTransactionMatchDTO: BankRuleTransactionMatchDTO = {
      userId: userId,
      entityId: entityId,
      selectedType:
        this.selectedFilter == 'credit'
          ? BankRecTransactionType.CREDIT
          : BankRecTransactionType.DEBIT,
      bankRuleDetail: ruleDetailsList,
      bankRuleHead: ruleDetailsList[0].bankRuleHead,
      matchedMode: BankRecMatchMode.BANK_RULE,
      transaction: transaction,
      totalAllocationPercentage: 5,
    };

    this.bankReconciliationService
      .submitBankRuleMatch(bankRuleTransactionMatchDTO)
      .subscribe(
        (response: boolean) => {
          if (response) {
            this.swalPopup(
              true,
              'Creation Completed',
              'Bank Rule Match Completed',
              transaction
            );
          } else {
            this.swalPopup(
              false,
              'Creation Failed',
              `Bank Rule Match Failed.`,
              transaction
            );
          }
        },
        (err) => {
          console.error('submitBankRuleMatch() : ', err);
          this.swalPopup(
            false,
            'Creation Failed',
            `Bank Rule Match Failed. Error : ${err}`,
            transaction
          );
        }
      );
  }

  removeRuleDetail(ruleDetail: any): void {
    // const index = this.ruleDetailsList.findIndex(
    //   (r) => r.bankRuleDetailId === ruleDetail.bankRuleDetailId
    // );
    // if (index !== -1) {
    //   this.ruleDetailsList.splice(index, 1);
    // }
  }

  isSpendRuleValid(ruleDetailsList: any[]): boolean {
    if (!ruleDetailsList || ruleDetailsList.length === 0) return false;

    const total = ruleDetailsList.reduce((sum, detail) => {
      let value = Number(detail.ruleDetailGlAccountPercentage || 0);
      if (value < 0.01 || value > 100) return NaN; // Invalidate sum
      return sum + value;
    }, 0);

    return Math.round(total * 100) / 100 === 100;
  }
}
