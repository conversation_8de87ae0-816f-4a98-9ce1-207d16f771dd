<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <form #f="ngForm" (ngSubmit)="onUpdate()" class="row g-1" novalidate>
    <div class="heading" (keydown)="preventSubmit($event)">
      <h3>Update Expense</h3>
    </div>

    <div class="bd">
      <div class="form-section">
        <!-- First Row: Expense Number -->
        <div class="form-rows" style="display: flex; margin-bottom: 20px;">
          <div class="form-group">
            <label for="expensesNumber" style="margin-right: 15px; white-space: nowrap">Expense Number</label>
            <input
              type="text"
              id="expensesNumber"
              class="input-style"
              [(ngModel)]="otherExpensesHeadData.expensesNumber"
              name="expensesNumber"
              readonly
              disabled
            />
          </div>
        </div>

        <!-- Second Row: Spent On & Spent At -->
        <div class="form-row" style="display: flex; justify-content: space-between">
          <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1">
            <div style="display: flex" class="form-dataInput">
              <label for="spentOn" style="margin-right: 75px; white-space: nowrap">Spent On</label>
              <input
                type="date"
                class="input-style"
                id="spentOn"
                [(ngModel)]="otherExpensesHeadData.spentOn"
                name="spentOn"
                required
              />
            </div>
          </div>

          <div class="form-group" style="display: flex; flex-direction: column; flex-grow: 1">
            <div class="form-dataInput"> 
              <label for="spentAt" style="margin-right: 35px; white-space: nowrap">Spent At</label>
                <input
                  type="text"
                  class="input-style"
                  id="spentAt"
                  [(ngModel)]="otherExpensesHeadData.spentAt"
                  name="spentAt"
                  required
                />
              </div>
          </div>
        </div>
      </div>

      <!-- Table Section -->
      <div class="table-section">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>Description</th>
                <th>Amount</th>
                <th>GL Account</th>
                <th>
                  {{ businessEntity?.countryId?.defaultTaxType }} ({{
                    businessEntity?.countryId?.defaultTaxRate
                  }}%)
                </th>
                <th style="text-align: right;">Tax</th>
                <th style="text-align: right;">Total Amount</th>
                <!-- <th style="width: 3%;"></th> -->
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let detail of otherExpensesHeadData.otherExpensesDetails; let i = index">
                <td>
                  <input
                    type="text"
                    [(ngModel)]="detail.description"
                    name="description-{{ i }}"
                    class="form-control"
                    placeholder="Enter Description"
                    required
                  />
                </td>

                <td>
                  <input
                    type="number"
                    [(ngModel)]="detail.amount"
                    (input)="updateAmount(i)"
                    name="amount-{{ i }}"
                    class="form-control"
                    min="0"
                    step="0.01"
                    required
                  />
                </td>

                <td>
                  <select
                    class="form-control"
                    [(ngModel)]="detail.coaLedgerAccountId"
                    name="coaLedgerAccountId-{{ i }}"
                    required
                  >
                    <option value="" disabled selected>Select GL Account</option>
                    <option *ngFor="let account of glAccounts" [value]="account.coaLedgerAccountId">
                      {{ account.ledgerAccountName }}
                    </option>
                  </select>
                </td>

                <td>
                  <input
                    type="checkbox"
                    [(ngModel)]="detail.taxApplicability"
                    name="taxApplicable-{{ i }}"
                    (change)="onTaxApplicableChange(i)"
                  />
                </td>

                <td style="text-align: right;">{{ detail.tax | currency }}</td>

                <td style="text-align: right;">{{ detail.total_amount | currency }}</td>

                <!-- <td>
                  <button type="button" class="btn btn-link" (click)="removeItem(i)">
                    <i class="fa fa-trash" style="color: red;"></i>
                  </button>
                </td> -->
              </tr>
            </tbody>
            
          </table>

          <button type="button" class="btns btn-primarys" (click)="addNewRow()"><i class="bi bi-plus-circle-fill"></i> Add New Row</button>
        </div>
      </div>

      <!-- Notes & Totals -->
      <div class="notes-totals-section">
        <div class="notes-section">
          <label for="remarks" style="padding-bottom: 6px;">Notes</label>
          <textarea
            id="remarks"
            class="form-control"
            [(ngModel)]="otherExpensesHeadData.remarks"
            name="remarks"
            rows="10"
          ></textarea>
        </div>

        <div class="totals-section" style="margin-top: 25px;">
          <div class="totals-row">
            <span class="totals-row1">Sub Total</span>
            <span class="totals-row2">{{ otherExpensesHeadData.grossAmount | currency }}</span>
          </div>

          <div class="totals-row">
            <span class="totals-row1">Total {{ businessEntity?.countryId?.defaultTaxType }}</span>
            <span class="totals-row2">{{ otherExpensesHeadData.totalGst| currency }}</span>
          </div>

          <div class="totals-row grand-total">
            <strong class="totals-row1">Grand Total</strong>
            <strong class="totals-row2">{{ otherExpensesHeadData.netAmount | currency }}</strong>
          </div>
        </div>
      </div>

      <div class="footer-section" style="margin-bottom: 50px">
      <!-- Footer Buttons -->
        <div class="footer-btn-section">
          <button type="submit" class="add-btn">Update Expense</button>
          <button type="button" class="cancel-btn" (click)="onCancel()">Cancel</button>
        </div>
      </div>
    </div>
  </form>
</div>
