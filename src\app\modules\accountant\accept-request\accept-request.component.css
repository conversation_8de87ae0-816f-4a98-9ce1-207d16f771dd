* {
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

.main-container {
    background-color: rgb(241, 241, 241);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.actions {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.actions h1 {
    flex: 1;
    margin-bottom: 20px;
    font-family: Inter;
    font-size: 40px;
    font-weight: 700;
    text-align: left;
    color: #4262FF;
}


table {
    width: 100%;
    margin-bottom: 20px;
    border-radius: 10px;
    overflow: hidden;
}

.table-responsive {
    border-radius: none;
}

.table-head th {
    position: relative;
    padding: 10px;
    background-color: #d7dbf5;
    text-align: left;
    color: rgb(0, 0, 0);
    font-size: 14px;
    font-weight: bold;
}

.table-head th:first-child {
    border-top-left-radius: 10px;
}

.table-head th:last-child {
    border-top-right-radius: 10px;
}

tbody tr {
    background-color: white;
    border-bottom: rgb(171, 171, 171) 1px solid;
    font-size: small;
    color: rgb(102, 102, 102);
}

tbody tr:hover {
    background-color: #f1f1f1;
    /* Light grey color on hover */
}

td,
th {
    padding: 10px;
}

td.valueCheckbox,
th.valueCheckbox {
    width: 5%;
}

td.value,
th.valuehead {
    width: 12%;
}

th.valuehead {
    text-align: center;
}
