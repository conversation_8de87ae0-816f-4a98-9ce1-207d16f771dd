export class Bill {}

export class ApInvoiceHead {
  apInvoiceHeadId: number = 0;
  businessPartnerId: any = 0;
  entityId: number = 0;
  userId: number = 0;
  referenceNo: string = '';
  postingDate: string = '';
  dueDate: string = '';
  supplierName: string = '';
  remarks: string = '';
  totalGst: number = 0.0;
  grossAmount: number = 0.0;
  netAmount: number = 0.0;
  dueAmount: number = 0.0;
  status: string = '';
  apInvoiceDetails: ApInvoiceDetail[] = [];
  selected: boolean = false;
  balanceAmount: number = 0.0;
  creditAmount: number = 0.0;
  bankRecStatus: string = '';
  supplierReferenceNumber: string = '';
  matched?: 'green' | 'yellow' | 'orange' | false;
  partialMatched?: boolean;
  matchStatus: string = '';
  reconcileStatus: string = '';
  pendingBalance: number = 0.0;
  deletedItemIds: number[] = [];
  recAmount:number = 0.0;
  scanned?: boolean = false;
  isLocked?: boolean;
}

export class ApInvoiceDetail {
  apInvoiceDetailId?: number = 0;
  apInvoiceHead?: ApInvoiceHead = new ApInvoiceHead();
  itemDescription: string = '';
  referenceNo: string = '';
  quantity: number = 0;
  unitPrice: number = 0.0;
  amount: number = 0.0;
  dueAmount: number = 0.0;
  tax: number = 0.0;
  coaLedgerAccountId: number = 0;
  ledgerAccountName: string = '';
  ledgerAccountCode: string = '';
  taxApplicability?: boolean = false;
}

export class CoaLedgerAccount {
  coaLedgerAccountId: number = 0;
  ledgerAccountCode: string = '';
  ledgerAccountName: string = '';
}

export class PaymentVoucherHeader {
  paymentVoucherHeaderId: number = 0;
  details: PaymentVoucherDetail[] = [];
  businessPartnerId: any = 0;
  entityId: number = 0;
  userId: number = 0;
  voucherNumber: string = '';
  date: string = '';
  bankAccount: string = '';
  payeeName: string = '';
  balance: number = 0;
  status: string = '';
  netAmount: number = 0;
  referenceNos: string = '';
  selected: boolean = false;
  remarks: string = '';
  totalPaidAmount: number = 0;
  coaLedgerAccountId: number =0;
  ledgerAccountName: string = '';
  bankName:string ='';
	recStatus:string='';
	matchStatus:string='Pending';
	recAmount:number = 0;
  matched:'green'| 'yellow' |'orange' | false = false;
}

export class PaymentVoucherDetail {
  paymentVoucherDetailId: number = 0;
  paymentVoucherHeader: PaymentVoucherHeader = new PaymentVoucherHeader();
  apInvoiceHeadId: number = 0;
  referenceNo: string = '';
  billDate: string = '';
  netAmount: number = 0.0;
  dueAmount: number = 0.0;
  paidAmount: number = 0.0;
  balanceAmount: number = 0.0;
  coaLedgerAccountId: number = 0;
  ledgerAccountName: string = '';
  tax: number = 0.0;
  accountHeaderType: string = '';
  glAccountIncome:  number = 0.0;
}

export class CreditNoteBillHead {
  creditNoteBillHeadId: number = 0;
  apInvoiceHeadId: number = 0;
  details: CreditNoteBillDetails[] = [];
  businessPartnerId: any = 0;
  entityId: number = 0;
  userId: number = 0;
  creditNoteNumberBill: string = '';
  documentDate: string = '';
  balanceDue: number = 0;
  documentStatus: string = '';
  remarks: string = '';
  totalCreditAmount: number = 0;
  supplierName: string = '';
  referenceNos: string = '';
  selected: boolean = false;
}

export class CreditNoteBillDetails {
  creditNoteBillDetailsId: number = 0;
  creditNoteBillHead: CreditNoteBillHead = new CreditNoteBillHead();
  apInvoiceHeadId: number = 0;
  apInvoiceDetailId: number = 0;
  referenceNo: string = '';
  netAmount: number = 0.0;
  balanceAmount: number = 0.0;
  creditAmount: number = 0.0;
  coaLedgerAccountId: number = 0;
  ledgerAccountName: string = '';
}

export interface SaveApInvoiceHeadDTO{
  apInvoice:ApInvoiceHead;
  autoPaymentVoucher:boolean;
}
