
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { HttpErrorResponse } from '@angular/common/http';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import Swal from 'sweetalert2';
import { BillService } from '../../bill/bill.service';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';

@Component({
  selector: 'app-bs-report',
  templateUrl: './bs-report.component.html',
  styleUrls: ['./bs-report.component.css']
})
export class BsReportComponent implements OnInit{

  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
    @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
    @ViewChild('balanceSheetPreviewFrame') balanceSheetPreviewFrame!: ElementRef;
   
    fromDate: string='';
    toDate: string='';
    isLoading = false;
    private audio!: HTMLAudioElement;
  isCustom: boolean = false;
  comparisonOptions: { value: string, label: string }[] = [];
  selectedComparison: string = '';
  showComparisonDropdown: boolean = false;
  divideByMonths: boolean = false; // Checkbox state
  showDivideByMonths: boolean = false; // Determines if checkbox should be visible




constructor(
   private billService: BillService,
    public sanitizer: DomSanitizer
    
  ) { }

  ngOnInit() {
  }


  updateDateRange(event: Event) {
    const selectedValue = (event.target as HTMLSelectElement).value;
    const today = new Date();
    let fromDate = new Date();
    let toDate = new Date();
    this.showComparisonDropdown = false;
    this.comparisonOptions = [];
    this.showDivideByMonths = false; // Hide checkbox by default
    this.divideByMonths = false; // Reset checkbox state

    switch (selectedValue) {
      case 'endOfThisMonth':
        fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
        toDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        this.showComparisonDropdown = true;
        this.comparisonOptions = [
          { value: 'monthly', label: 'Last Month' }, // Update to 'monthly'

        ];
        break;

      case 'endOfThisQuarter':
        const quarter = Math.floor(today.getMonth() / 3);
        fromDate = new Date(today.getFullYear(), quarter * 3, 1);
        toDate = new Date(today.getFullYear(), quarter * 3 + 3, 0);
        this.showComparisonDropdown = true;
        this.comparisonOptions = [
          { value: 'quarterly', label: 'Last Quarter' } // Update to 'quarterly'
        ];
        this.showDivideByMonths = true;
        break;

      /**case 'endOfThisYear':
        fromDate = new Date(today.getFullYear(), 0, 1);
        toDate = new Date(today.getFullYear(), 11, 31);
        this.showComparisonDropdown = true;
        this.comparisonOptions = [
          { value: 'yearly', label: 'Last Year' }, // Update to 'yearly'

        ];
        this.showDivideByMonths = true;
        break;**/

        case 'endOfThisYear':
  if (today.getMonth() >= 6) { // From July to December, current FY started this year
    fromDate = new Date(today.getFullYear(), 6, 1); // July 1st
    toDate = new Date(today.getFullYear() + 1, 5, 30); // June 30th next year
  } else { // From January to June, current FY started last year
    fromDate = new Date(today.getFullYear() - 1, 6, 1); // July 1st last year
    toDate = new Date(today.getFullYear(), 5, 30); // June 30th this year
  }
  this.showComparisonDropdown = true;
  this.comparisonOptions = [
    { value: 'yearly', label: 'Last Fiscal Year' }
  ];
  this.showDivideByMonths = true;
  break;

      case 'endOfLastMonth':
        fromDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        toDate = new Date(today.getFullYear(), today.getMonth(), 0);
        this.showComparisonDropdown = false; // No dropdown for last month, last quarter, last year
        break;

      case 'endOfLastQuarter':
        const quarter1 = Math.floor(today.getMonth() / 3);
        fromDate = new Date(today.getFullYear(), quarter1 * 3 - 3, 1);
        toDate = new Date(today.getFullYear(), quarter1 * 3, 0);
        this.showComparisonDropdown = false; // No dropdown for last month, last quarter, last year
        break;

    /**case 'endOfLastYear':
        fromDate = new Date(today.getFullYear() - 1, 0, 1);
        toDate = new Date(today.getFullYear() - 1, 11, 31);
        this.showComparisonDropdown = false; // No dropdown for last month, last quarter, last year
        break;**/

        case 'endOfLastYear':
  if (today.getMonth() >= 6) { // July to December: last FY was Jul last year – Jun this year
    fromDate = new Date(today.getFullYear() - 1, 6, 1);
    toDate = new Date(today.getFullYear(), 5, 30);
  } else { // January to June: last FY was Jul 2 years ago – Jun last year
    fromDate = new Date(today.getFullYear() - 2, 6, 1);
    toDate = new Date(today.getFullYear() - 1, 5, 30);
  }
  this.showComparisonDropdown = false;
  break;

      case 'custom':
        this.isCustom = true;
        this.fromDate = '';
        this.toDate = '';
        return;

      default:
        return;
    }

    this.fromDate = fromDate.toISOString().split('T')[0];
    this.toDate = toDate.toISOString().split('T')[0];
  }


  onComparisonChange() {
    if (this.selectedComparison) {
      this.divideByMonths = false; // Uncheck the checkbox when comparison is selected
    }
  }


previewBalanceSheet(fromDate: string, toDate: string, selectedComparison: string,) {
   //  fromDate = '2025-01-01';

    // Validation checks for required fields
    if (!fromDate || !toDate) {
      Swal.fire({
        title: 'Warning!',
        text: 'Please select Date Range',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ff7e5f'
      });
      return;
    }
    this.isLoading = true;
    const entityId = +((localStorage.getItem('entityId')) + "");
    let reportObservable;


     if (this.showComparisonDropdown && selectedComparison) {
        reportObservable = this.billService.getBalanceSheetListReportWithComparison(fromDate, toDate, entityId, selectedComparison);
      } else {
        reportObservable = this.billService.getBalanceSheetListReport(fromDate, toDate, entityId);
      }



    reportObservable.subscribe(
      data => {
        const base64String = data.response;

        if (base64String) {
          this.loadPdfIntoIframe(base64String);
        } else {
          this.isLoading = false;
          alert('No Balance sheet data for preview.');
        }
      },
      error => {
        this.isLoading = false;
        alert('Error loading balance sheet preview.');
      }
    );
  }


 private loadPdfIntoIframe(base64String: string) {
    let dataLoaded = false; // Flag to track if data was loaded successfully

    // Check if base64String is valid
    if (base64String && base64String.trim().length >= 50) { // Adjust the length check as needed
      const pdfData = 'data:application/pdf;base64,' + base64String;
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
      const iframe = this.balanceSheetPreviewFrame.nativeElement;

      iframe.onload = () => {
        this.isLoading = false;
        dataLoaded = true; // Set flag to true when data loads
      };

      iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);
    }

    // Check load status after a short delay
    setTimeout(() => {
      if (!dataLoaded) {
        this.isLoading = false;
        Swal.fire({
          title: 'No Data',
          text: 'No balance sheet data for preview.',
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#007bff'
        });
      }
    }, 2000);
  
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = '../assets/google_chat.mp3';
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }



}

