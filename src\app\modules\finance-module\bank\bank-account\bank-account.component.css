.form{
    padding-left: 100px;
    padding-right: 100px;
    padding-bottom: 30px;
  }
  .text-header{
    padding-top: 30px;
    padding-bottom: 30px;
    color: #4262FF;
    font-size: 36px;
    font-weight: 700;
  }
  .primary-acc-text{
    font-weight: 600;
    font-size: 28px;
    color: #000000;
    
  }
  .hr-div{
    width: 100%;
  }
  .hr-line{
    width: 100%;
    background-color: #4262FF;
    color: #4262FF;
    border: solid 1px;
  }
  .date-div{
    margin-bottom: 60px;
  }
  .dates{
    font-weight: 600;
    font-size: 24px;
    color: #000000;
  }
  .table-container {
    font-family: Arial, sans-serif;
    width: 90%;
    margin: auto;
  }
  
  .title {
    color: #4A54FF;
    font-size: 24px;
    margin-bottom: 5px;
  }
  
  .fiscal-year, .date-range {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
  }
  
  .account-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
  }
  
  .account-table thead th {
    /* background-color: #4A54FF; */
    font-weight: 600;
    font-size: 24px;
    color: #000000;
    padding: 10px;
    text-align: left;
  }
  
  .account-table tbody td {
    padding: 15px;
    border-bottom: 1px solid #ddd;
  }
  
  .section-header td {
    /* background-color: #F3F4F6; */
    font-weight: 700;
    font-size: 24px;
    color: #4262FF;
  }

  .equity-row{
    border-top: 1px solid #C7C7C799; /* Green top horizontal line */
  border-bottom: 1px solid #C7C7C799; /* Green bottom horizontal line */

  }

  .total-row{
    border-top: 2px solid #444343; /* Green top horizontal line */
  border-bottom: 2px solid #444343; /* Green bottom horizontal line */
  }
  
  .total-row td {
    font-weight: 400;
    font-size: 20px;
    color: #000000;
  }
  .total-row .total{
    font-weight: 700;
    font-size: 24px;
    color: #000000;
  }
  td{
    color: #000000;
    font-weight: 400;
    font-size: 20px;
  }
  .delete-btn {
    background-color: #C7C7C7;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
    border-radius: 50%;
  }
  
  .button-container {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 40px;
    margin-top: 60px;
  }
  
  .cancel-btn, .save-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
  }
  
  .save-btn {
    background: linear-gradient(90deg, #4262FF 8.41%, #512CA2 91.11%);
    border: #6822FF;
    width: 279.25px;
    height: 50px;
    border-radius: 17px;
    font-weight: 600;
    font-size: 20px;
    color:#FFFFFF;
  }
  
  .cancel-btn {
    border: 2px solid #6822FF;
    color: #6822FF;
    font-weight: 600;
    font-size: 20px;
    width: 279.25px;
    height: 50px;
    border-radius: 17px;
    background-color: #FFFFFF;
  }
  .maginn{
    margin-top: 20px;
  }
  
  
  @media(max-width:1300px){
    .input-and-lable{
      flex-wrap: wrap;
    }
  }
  @media(max-width:928px){

    .text-header{
    font-size: 28px;
    font-weight: 700;
  }
    .primary-acc-box{
      display: flex;
      flex-direction: column;
    }
    .input-and-lable{
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 15px;
      width: 100%;
    }
    .button-container {
      align-items: center;
      justify-content: center;
    }
  }
  @media(max-width:700px){

    .text-header{
    font-size: 28px;
    font-weight: 700;
  }

    .form{
      padding-left: 50px;
      padding-right: 50px;
    }
    .button-container {
      flex-direction: column;
    }
  }
  @media(max-width:450px){
    .form{
      padding-left: 20px;
      padding-right: 20px;
    }
    .box-div{
      padding: 15px;
    }
    
  }