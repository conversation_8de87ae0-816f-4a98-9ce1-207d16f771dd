<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
  <!-- Heading And Button -->
  <div class="actions">
    <h1>List of Expenses</h1>
    <div class="btn-group" [class.show]="isDropdownOpen">
      <!-- Dropdown button with vertical dots -->
      <button
      type="button"
      class="btn btn-secondary dropdown-toggle gradient-btn"
      data-bs-toggle="dropdown"
      aria-expanded="false"
      (click)="toggleDropdown()"
      >
      <i class="bi bi-three-dots-vertical"></i>
      <!-- Vertical dots icon -->
    </button>
    <ul class="dropdown-menu dropdown-menu-end"  [class.show]="isDropdownOpen">
      <!-- <li>
        <a class="dropdown-item" (click)="onPaymentreceiptMultiple()">Payment</a>
      </li> -->
      <li>
        <a class="dropdown-item" (click)="cancelSelectedExpenses()">Cancel</a>
      </li>
    </ul>
      
      
    </div>
  </div>

  <div class="search-create">
    <button type="button" (click)="addExpences()" class="create-invoice">
      Add Expenses
    </button>
  
  </div>
  <!-- Tabs -->
  <div>
  <ul class="nav nav-tabs mb-3 justify-content-start">
    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'all'"
        (click)="setActiveTab('all')"
        >All</a
      >
    </li>
    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'pending'"
        (click)="setActiveTab('pending')"
        >Pending</a
      >
    </li>
   <!-- <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'paid'"
        (click)="setActiveTab('paid')"
        >Paid</a
      >
    </li>-->
    <li class="nav-item">
      <a
        class="nav-link"
        [class.active]="activeTab === 'overdue'"
        (click)="setActiveTab('overdue')"
        >Overdue</a
      >
    </li>
  </ul>
</div>

  <div class="Card">
    <!-- Filter and Search Section -->
    <div class="row1">
      <!-- Input for number, reference -->
      <div class="row1_col1">
        <label for="search-input">Expenses Number</label>
        <div class="input-container">
          <input
            type="text"
            class="search-input"
            id="search-input"
            [(ngModel)]="searchTerm"
          />
          <i class="bi bi-search"></i>
        </div>
      </div>

      <div class="row1_col3">
        <label for="StartDate">Start Date</label>
        <div style="position: relative; width: 100%;">
          <input
            matInput
            [matDatepicker]="startDatePicker"
            class="date-picker"
            id="StartDate"
            [(ngModel)]="startDate"
            placeholder="dd/mm/yyyy"
          />
          <mat-datepicker-toggle matSuffix [for]="startDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
        </div>
        <mat-datepicker #startDatePicker></mat-datepicker>
      </div>

      <div class="row1_col4">
        <label for="EndDate">End Date</label>
        <div style="position: relative; width: 100%;">
          <input
            matInput
            [matDatepicker]="endDatePicker"
            class="date-picker"
            id="EndDate"
            [(ngModel)]="endDate"
            placeholder="dd/mm/yyyy"
          />
          <mat-datepicker-toggle matSuffix [for]="endDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
        </div>
        <mat-datepicker #endDatePicker></mat-datepicker>
      </div>
    </div>

    <div class="row2">
      <div class="row2_col3">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
      </div>
      <div class="row2_col1">
        <button type="button" class="primary-button" (click)="filterExpenses()">
          Search
        </button>
      </div>
    </div>
  </div>

  <div class="table-responsive">
    <table>
      <thead>
        <tr class="table-head">
          <th scope="col" class="valueCheckbox">
            <input
              type="checkbox"
              [checked]="isAllSelected"
              (change)="selectAll($event)"
            />
          </th>
          <th scope="col" class="valuehead">Reference</th>
          <th scope="col" class="valuehead">Spent On</th>
          <th scope="col" class="valuehead">Spent At</th>
          <th style="text-align: right" scope="col" class="valuehead">
            Balance Amount
          </th>
          <th style="text-align: right" scope="col" class="valuehead">
            Bill Amount
          </th>
          <th scope="col" style="text-align: center" class="valuehead">
            Status
          </th>
          <th scope="col" style="text-align: center" class="valuehead">
            Action
          </th>
         <!--<th scope="col" class="valuehead">Actions</th>-->
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let expense of filteredExpenses"
          [ngStyle]="{
            color: expense.status === 'Overdue' ? 'red' : 'inherit'
          }"
        >
          <td class="valueCheckbox">
            <input
              type="checkbox"
              [checked]="selectedExpenses.has(expense)"
              (change)="toggleSelection(expense, $event)"
            />
          </td>
          <td class="value">{{ expense.expensesNumber }}</td>
          <td class="value">{{ expense.spentOn | date : "dd-MM-yyyy" }}</td>
          <td class="value">{{ expense.spentAt }}</td>
          <td style="text-align: right" class="value">
            {{ expense.balanceAmount | currency }}
          </td>
          <td style="text-align: right" class="value">
            {{ expense.netAmount | currency }}
          </td>
          <td
            style="padding-left: 15px"
            class="value"
            [ngClass]="{
              'text-pending': expense.status === 'Pending',
              'text-paid': expense.status === 'Paid',
              'text-overdue': expense.status === 'Overdue',
              'text-canceled': expense.status === 'Canceled'
            }"
          >
            <span
              class="lable"
              [ngClass]="{
                'border-pending': expense.status === 'Pending',
                'border-paid': expense.status === 'Paid',
                'border-overdue': expense.status === 'Overdue',
                'border-canceled': expense.status === 'Canceled'
              }"
              >{{ expense.status }}</span
            >
          </td>

          <td>
            <!-- Delete Button -->
              <button
              (click)="deleteExpences(expense.otherExpensesId)"
              title="Delete Expence"
              style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;"
              >
                <i class="ri-delete-bin-line" style="color: #ff0000"
               [style.color]="
                expense.status === 'Pending' ||
                expense.status === 'Paid' ||
                expense.status === 'Overdue' ||
                expense.status === 'Canceled'
              "></i>
              </button>
          </td>


         <!-- <td class="value">
           <button
            class="btn btn-orange btn-sm"
            title="Edit"
            (click)="navigateToEditExpense(expense.otherExpensesId)"
          >
            <i class="ri-edit-box-line" style="color: #4262ff"></i>
          </button>
          
        <button class="btn btn-danger btn-sm" style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" data-bs-toggle="tooltip" title="Delete">
                <i class="ri-delete-bin-line" style="color: #FF0000;"></i>
            </button>
          </td>-->
        </tr>
      </tbody>
    </table>
  </div>
</div>
