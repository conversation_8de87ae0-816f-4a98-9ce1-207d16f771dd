<app-home-header></app-home-header>

<div class="container">
  <h2>User Agreement & Terms and Conditions</h2>
  <div class="terms-box">
    <p>
      Lorem ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups. Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups. Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.Lorem
      ipsum is placeholder text commonly used in the graphic, print, and
      publishing industries for previewing layouts and visual mockups.
    </p>
  </div>
  <div class="checkboxes">
    <label>
      <input
        type="checkbox"
        [(ngModel)]="isUserAgreementMarked"
        (change)="isBothChecked()"
      />
      I Agree to the <a>User Agreement</a>.
    </label>
    <label>
      <input
        type="checkbox"
        [(ngModel)]="isTermsMarked"
        (change)="isBothChecked()"
      />
      I Agree to the <a>Terms and Conditions</a>.
    </label>
  </div>
  <div class="proceed-btn-container">
    <button
      class="proceed-btn"
      [class.disabled]="!canProceed"
      [disabled]="!canProceed"
      (click)="navigateUserRegistration()"
    >
      Proceed
    </button>
  </div>
</div>
