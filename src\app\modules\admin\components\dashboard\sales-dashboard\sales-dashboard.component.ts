import { Component, OnInit } from '@angular/core';
import { InvoiceService } from 'src/app/modules/invoice/invoice.service';
import { QuotationService } from 'src/app/modules/quotation/quotation.service';
import { catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import Swal from 'sweetalert2';

interface Summary {
  count: number;
  amount: number;
}

interface InvoiceSummary {
  awaiting: Summary;
  overdue: Summary;
  paid: Summary;
  pending: Summary;
  revised: Summary;
  canceled: Summary;
  sent: Summary;
 
}

interface QuoteSummary {
  pending: Summary;
  sent: Summary;
  revised: Summary;
  accepted: Summary;
  expired: Summary;
  declined: Summary;
  toInvoice: Summary;
}

/**interface AgedDebtors {
  current: number;
  days30: number;
  days90: number;
  days120: number;
}**/

@Component({
  selector: 'app-sales-dashboard',
  templateUrl: './sales-dashboard.component.html',
  styleUrls: ['./sales-dashboard.component.css'],
})
export class SalesDashboardComponent implements OnInit {
  invoiceData: InvoiceSummary = {
    awaiting: { count: 0, amount: 0 },
    overdue: { count: 0, amount: 0 },
    paid: { count: 0, amount: 0 },
    pending: { count: 0, amount: 0 },
    revised: { count: 0, amount: 0 },
    canceled: { count: 0, amount: 0 },
    sent: { count: 0, amount: 0 },
    
  };

  quoteData: QuoteSummary = {
    pending: { count: 0, amount: 0 },
    sent: { count: 0, amount: 0 },
    revised: { count: 0, amount: 0 },
    accepted: { count: 0, amount: 0 },
    expired: { count: 0, amount: 0 },
    declined: { count: 0, amount: 0 },
    toInvoice: { count: 0, amount: 0 },
  };

  /**agedDebtors: AgedDebtors = {
  current: 0,
  days30: 0,
  days90: 0,
  days120: 0
};**/

  constructor(
    private invoiceService: InvoiceService,
    private quotationService: QuotationService
  ) {}

  ngOnInit(): void {
    const entityId = Number(localStorage.getItem('entityId') || '0');
    this.fetchInvoiceSummary(entityId);
    this.fetchQuoteSummary(entityId);
  //  this.fetchAgedDebtors(entityId);
  }

  private fetchInvoiceSummary(entityId: number): void {
    this.invoiceService.getInvoiceSummary(entityId)
      .pipe(
        catchError(error => {
          // this.showError('Failed to load invoice summary');
          console.error('Invoice Summary Error:', error);
          return of(null);
        })
      )
      .subscribe(data => {
        if (data) {
          this.invoiceData = data;
        }
      });
  }

  private fetchQuoteSummary(entityId: number): void {
    this.quotationService.getQuoteSummary(entityId)
      .pipe(
        catchError(error => {
          // this.showError('Failed to load quote summary');
          console.error('Quote Summary Error:', error);
          return of(null);
        })
      )
      .subscribe(data => {
        if (data) {
          this.quoteData = data;
        }
      });
  }

 /**  private fetchAgedDebtors(entityId: number): void {
  this.invoiceService.getAgedDebtorsSummary(entityId)
    .pipe(
      catchError(error => {
        this.showError('Failed to load aged debtors data');
        console.error('Aged Debtors Error:', error);
        return of(null);
      })
    )
    .subscribe(data => {
      if (data) {
        this.agedDebtors = data;
      }
    });
}
**/
  private showError(message: string): void {
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: message
    });
  }
}
