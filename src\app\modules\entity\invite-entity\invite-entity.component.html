<app-admin-navigation></app-admin-navigation>
<div class="container">
  <div class="invite-entity-form">
    <h2>Invite to Entity</h2>
    <form [formGroup]="entityInviteForm" (ngSubmit)="onSubmit($event)">
      <div class="form-group">
        <label for="email">Enter user email</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          (keyup)="checkUser()"
          [(ngModel)]="recipientEmail"
          required
        />
        <div
          *ngIf="
            entityInviteForm.get('email')?.invalid &&
            (entityInviteForm.get('email')?.dirty ||
              entityInviteForm.get('email')?.touched)
          "
        >
          <small
            class="text-danger"
            *ngIf="entityInviteForm.get('email')?.errors?.['required']"
          >
            Email is required.
          </small>
          <small
            class="text-danger"
            *ngIf="entityInviteForm.get('email')?.errors?.['customEmail']"
          >
            Invalid email format.
          </small>
        </div>
        <small *ngIf="isUsernameExits" class="text-danger">
          {{ usernameExistsMessage }}
        </small>
      </div>
      <div class="form-actions">
        <!-- <button type="button" class="cancel" (click)="navigateToDashboard()">
          Cancel
        </button> -->
        <button type="submit" class="add-entity">Invite</button>
      </div>
    </form>
  </div>
</div>
