//Angular Core & Router
import { Component, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule, NgForm } from '@angular/forms';

//Angular HTTP & RxJS
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, map, catchError, of } from 'rxjs';


//Services
import { HttpService } from 'src/app/http.service';
import { QuotationService } from '../quotation.service';
import { EntityService } from '../../entity/entity.service';
import { UserService } from '../../admin/components/user/user.service';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { StorageService } from '../../entity/storage.service';

//Models & Interfaces
import { Entity, EntityTradingName } from '../../entity/entity';
import { BusinessPartner, BusinessPartnerType } from '../../business-partner/business-partner';
import { QuotationDetail, QuoteHead, SalesItem } from '../quotation';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';
import { DateAdapter } from '@angular/material/core';

@Component({
  selector: 'app-create-quotation',
  templateUrl: './create-quotation.component.html',
  styleUrls: ['./create-quotation.component.css'],
})

export class CreateQuotationComponent {

  salesItems: SalesItem[] = [];
  quotationData: QuoteHead = new QuoteHead();
  quotationDetail: QuotationDetail = new QuotationDetail();
  itemPopUp: boolean = false;
  businessEntity: Entity = new Entity();
  customers: BusinessPartner[] = [];
  businessPartnerType: BusinessPartnerType[] = [];
  selectedType: string = '';

  newItem: SalesItem = {
    salesItemId: 0,
    entityId: 1,
    userId: 1,
    itemTypeId: 1,
    itemCode: '',
    itemName: '',
    sellingPrice: 0,
    standardDiscount: 0,
    salesAccount: '',
    taxApplicability: '',
    itemStatus: 'active',
    description: '',
    unitPrice: 0,
    amount: 0,
    transactionDate: '2023-07-15',
  };

  unitPriceInvalid: boolean = false;
  itemCode: string = '';
  taxApplicable: boolean = false;
  businessPartner: BusinessPartner = new BusinessPartner();
  selectedFile: File | null = null;
  
  url = '';
  businessEntityId: number = 0;
  filteredSalesItems: SalesItem[] = [];
  allSalesItems: SalesItem[] = [];
  entityTradingNames: EntityTradingName[] = [];
  showTaxApplicabilityDropdown: boolean = true;
  quotationStatus: any;
  entityId: number = 0;
  userId: number = 0;
  entityUuid: string = ''; 
  
  showUpdateLink = true;
  isSaving: boolean = false;
 
  constructor(
    private quotationService: QuotationService,
    private entityService: EntityService,
    private router: Router,
    private businessPartnerService: BusinessPartnerService,
    private storageService: StorageService,
    private swalAlertsService: SwalAlertsService,
    private dateAdapter: DateAdapter<Date>
  ) {
    this.entityId = this.storageService.getEntityId();
    this.userId = this.storageService.getUserId();
    this.entityUuid = this.storageService.getEntityUUID();
    this.dateAdapter.setLocale('en-GB');

  }

  ngOnInit() {
    this.setTodayDate();
    this.loadCustomers();
    this.getBusinessEntityById();
    this.getEntityTradingNamesByEntityId();
    this.fetchAllSalesItems();
    this.addEmptyRows(1);

    
    if (!this.entityId) {
      this.showTaxApplicabilityDropdown = false;
      this.newItem.taxApplicability = 'no';
      return;
    }

    this.entityService.getBusinessEntityById(this.entityId).subscribe(
      (entity: Entity) => {
        this.showTaxApplicabilityDropdown = entity.taxApplicability === 'yes';
        this.newItem.taxApplicability = entity.taxApplicability || 'no';
      },
      error => {
        console.error('Error fetching entity:', error);
        this.showTaxApplicabilityDropdown = false;
        this.newItem.taxApplicability = 'no';
      }
    );

  
    const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      const userType = user.userType; // assuming userType is here

      if (userType === 'General user' || userType === 'Accountant') {
        this.showUpdateLink = false;
      }
    }
  }

  customSearchFn(term: string, item: any) {
    term = term.toLowerCase();
    const itemCode = (item.itemCode || '').toLowerCase(); // Default to empty string if undefined
    const description = (item.description || '').toLowerCase(); // Default to empty string if undefined
    return itemCode.includes(term) || description.includes(term);
  }

  addEmptyRows(count: number) {
    for (let i = 0; i < count; i++) {
      this.addNewRow();
    }
  }

  addNewRow() {
    this.quotationData.details.push(this.createEmptyRow());
  }

  // Helper method to create an empty row
  createEmptyRow() {
    return {
      salesItemId: new SalesItem(),
      taxCategoryId: 0,
      quoteNumber: '',
      quantity: 0,
      unitPrice: 0,
      description: '',
      discount: 0,
      tax: 0,
      amount: 0,
      discountType: '$',
      itemName: ''
    };
  }

  
  resetTaxApplicability(index: number): void {
    this.quotationData.details[index].taxApplicability = false; 
    this.quotationData.details[index].tax = 0; 
  }


  updateQuotationDetails(selectedItem: SalesItem, index: number) {

    this.quotationData.details[index].salesItemId = selectedItem;
    this.quotationData.details[index].unitPrice = selectedItem.unitPrice;
    this.quotationData.details[index].amount = this.quotationData.details[index].quantity * selectedItem.unitPrice;

    this.checkTaxApplicability(index);

    this.calculateSubTotal(); 

    // Check if this is the last row, if so, add a new row
    if (index === this.quotationData.details.length - 1) {
    }
  }

  onDescriptionInput(index: number) {
    if (index === this.quotationData.details.length - 1) {
    }
  }

  onTypeChange(event: Event): void {
    const selectedValue = (event.target as HTMLSelectElement).value;
    this.selectedType = selectedValue;

  }


  applyDiscount(detail: QuotationDetail, itemAmount: number) {
    if (detail.quantity === 0) {
      detail.amount = 0;
    } else {
      if (detail.discountType === 'B') {
        detail.amount = itemAmount - (itemAmount * (detail.discount / 100));
      } else if (detail.discountType === '$') {
        detail.amount = itemAmount - detail.discount;
      } else {
        detail.amount = itemAmount;
      }

      // Ensure amount doesn't go below zero
      detail.amount = Math.max(0, detail.amount);
    }
  }

  updateAmount(index: number): void {
    const detail = this.quotationData.details[index];
    const itemAmount = detail.quantity * detail.unitPrice;

    if (detail.quantity < 0 || detail.unitPrice < 0 || detail.discount < 0) {
      this.swalAlertsService.showWarning("Negative values are not allowed.", () => {
        this.resetInvalidInput(detail);
      });
      return;
    }

    if (detail.quantity === 0 && (detail.discount > 0 || detail.discountType !== '')) {
      this.swalAlertsService.showWarning("Please specify a quantity first.", () => {
        detail.discount = 0;
        detail.discountType = '$';
        detail.amount = 0;
        detail.tax = 0;
      });
      return;
    }

    this.applyDiscount(detail, itemAmount);

    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (!this.quotationData.details[index].taxApplicability) {
            this.checkTaxApplicability(index);
          }

          if (this.quotationData.details[index].taxApplicability && this.taxApplicable) {
            this.applyFlatTax(index, taxRate);
          } else {
            detail.tax = 0;
          }

          this.calculateSubTotal();
        },
        error => {
          console.error('Error fetching entity data:', error);
          detail.tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      detail.tax = 0;
      this.calculateSubTotal();
    }
  }

  resetInvalidInput(detail: QuotationDetail) {
    if(detail.quantity < 0 ){
      detail.quantity = 0; 
    }

    if(detail.discount < 0 ){
      detail.discount = 0; 
    }

    if(detail.unitPrice < 0 ){
      detail.unitPrice = 0;
    }
    detail.discount = detail.discount; 
    detail.discountType = detail.discountType; 
    detail.amount = detail.amount; 
    detail.unitPrice = detail.unitPrice;
    detail.quantity = detail.quantity;
    detail.tax = detail.tax; 
   
  }

  updateDiscountType(index: number, value: string) {
    const detail = this.quotationData.details[index];
    const itemAmount = detail.quantity * detail.unitPrice;
    detail.discountType = value;
    detail.discount = 0;

    if (detail.quantity === 0) {
      this.swalAlertsService.showWarning("Please add a quantity before applying a discount", () => {
        detail.discountType = '$';
      });
    } else {
      this.updateAmount(index);
    }

    this.calculateSubTotal();
  }


  updateQuantity(index: number) {
    this.updateAmount(index);
  }

  calculateTotalDiscount(): number {
    let totalDiscount = 0;
    this.quotationData.details.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;
      let discountAmount = 0;

      if (detail.discountType === 'B') {
        discountAmount = itemAmount * (detail.discount / 100);
      } else if (detail.discountType === '$') {
        discountAmount = detail.discount;
      }

      totalDiscount += discountAmount;
    });

    this.quotationData.totalDiscAmount = totalDiscount;
    return totalDiscount;
  }

  checkTaxApplicability(index: number): void {
     if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          this.updateDetailsTaxApplicability(index, taxRate);
          this.calculateSubTotal();
        },
        error => {
          console.error('Error fetching entity data:', error);
          this.updateDetailsTaxApplicability(index, 0);
          this.calculateSubTotal();
        }
      );
    } else {
      this.updateDetailsTaxApplicability(index, 0);
      this.calculateSubTotal();
    }
  }

  updateDetailsTaxApplicability(index: number, taxRate: number): void {
    const detail = this.quotationData.details[index];
    const itemTaxApplicable = detail.salesItemId.taxApplicability === 'yes';

    if (this.taxApplicable && itemTaxApplicable) {
      detail.tax = detail.amount * (taxRate / 100);
    } else {
      detail.tax = 0;
    }
  }

  onTaxApplicableChange(index: number): void {
    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (entity: Entity) => {
          this.taxApplicable = entity.taxApplicability === 'yes';
          const taxRate = entity.countryId.defaultTaxRate || 0;

          if (this.taxApplicable && this.quotationData.details[index].taxApplicability) {
            this.applyFlatTax(index, taxRate);
          } else {
            this.quotationData.details[index].tax = 0;
          }

          this.calculateSubTotal();
        },
        error => {
          console.error('Error fetching entity data:', error);
          this.quotationData.details[index].tax = 0;
          this.calculateSubTotal();
        }
      );
    } else {
      this.quotationData.details[index].tax = 0;
      this.calculateSubTotal();
    }
  }

  applyFlatTax(index: number, taxRate: number): void {
    const detail = this.quotationData.details[index];
    detail.tax = detail.amount * (taxRate / 100);
    this.calculateSubTotal();
  }

  removeItem(index: number) {
    this.salesItems.splice(index, 1);
    this.quotationData.details.splice(index, 1);
    this.calculateSubTotal();
  }

  calculateSubTotal() {
    let subTotal = 0;
    let totalTax = 0;
    let totalDiscount = 0;

    this.quotationData.details.forEach((detail) => {
      const itemAmount = detail.quantity * detail.unitPrice;

      if (detail.quantity > 0) {
        this.applyDiscount(detail, itemAmount);
        subTotal += detail.amount;
        totalTax += detail.tax;
        totalDiscount += (detail.discountType === 'B') ? (itemAmount * (detail.discount / 100)) : detail.discount;
      }
    });

    this.quotationData.subTotal = subTotal;
    this.quotationData.totalGst = totalTax;
    this.quotationData.totalDiscAmount = totalDiscount;
    this.calculateGrandTotal();
  }

  calculateGrandTotal() {
    this.quotationData.grandTotal = this.quotationData.subTotal + this.quotationData.totalGst;
  }

  /**Date Validation **/
  setTodayDate() {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const dd = String(today.getDate()).padStart(2, '0');

    this.quotationData.quoteDate = `${yyyy}-${mm}-${dd}`;
    this.updateValidityMinDate();
  }

  updateValidityMinDate() {
    const quoteDate = this.quotationData.quoteDate;
    if (quoteDate) {
      const quoteDateObj = new Date(quoteDate);
      const yyyy = quoteDateObj.getFullYear();
      const mm = String(quoteDateObj.getMonth() + 1).padStart(2, '0');
      const dd = String(quoteDateObj.getDate()).padStart(2, '0');

      const minValidUntilDate = `${yyyy}-${mm}-${dd}`;
      (document.getElementById('validityUntil') as HTMLInputElement).min = minValidUntilDate;
    }
  }
  onQuoteDateChange() {
    this.updateValidityMinDate();
    this.validateDates();
  }

  onValidUntilDateChange(): void {
    this.validateDates();
  }

  validateDates(): boolean {
    const quoteDate = new Date(this.quotationData.quoteDate);
    const validUntilDate = new Date(this.quotationData.validUntilDate);

    if (validUntilDate < quoteDate) {
      // Pass the message to showWarning and handle the callback (if needed)
      this.swalAlertsService.showWarning('The "Valid Until" date cannot be before the "Quotation Date".', () => {
      });
      return false;  // Prevent form submission
    }
    return true;  // Proceed with saving
  }

  onSubmit(f: NgForm) {
    if (!f.valid) return;
    if (this.hasInvalidRows()) {
      this.isSaving = false;  
      this.handleApiError('Each row must include either a selected item or a provided description.');
      return;
    }

    // If all rows are valid, proceed with the form submission
    this.isSaving = true;
    this.quotationData.status = this.quotationStatus;
    this.checkForZeroQuantity(this.quotationStatus);
  }

  hasInvalidRows(): boolean {
    return this.quotationData.details.some(detail =>
      (!detail.salesItemId || !detail.salesItemId.itemCode) && (!detail.description || detail.description.trim() === '')
    );
  }

  checkForZeroQuantity(status: string) {
    if (this.hasZeroQuantity()) {
      this.swalAlertsService.showWarningWithCancel('Selected item(s) have zero quantity. Do you want to continue?',
         () => this.saveQuotation(status),
         () => { this.isSaving = false; }
          );
      return;
    }

    if (this.isEmptyQuotation(status)) {
       this.isSaving = false; 
      this.handleApiError('You must add at least one item.');
      return;
    }

    this.saveQuotation(status);
  }

  hasZeroQuantity(): boolean {
    return this.quotationData.details.some(detail => detail.quantity === 0);
  }

  isEmptyQuotation(status: string): boolean {
    return (status === 'Pending' || status === 'Draft') && this.quotationData.details.length === 0;
  }

  saveQuotation(status: string): void {
    this.quotationData.status = status;
    this.quotationData.userId = this.userId;
    this.quotationData.entityId = this.entityId;
    this.quotationData.entityUuid = this.entityUuid;

    if (!this.validateDates()) {
      this.isSaving = false;
      return;
    }

    this.quotationService.saveSalesQuotesHead(this.quotationData).subscribe(
      (savedQuote: QuoteHead) => {
        this.isSaving = false; // Reset saving state immediately after success
        this.router.navigate([`/view-quotation/${savedQuote.quoteId}`]); 
        this.swalAlertsService.showSuccessDialog(
          'Success!',
          'The Sales Quote has been successfully saved.'
        );
      },
      (error: HttpErrorResponse) => {
        this.isSaving = false; 
        this.swalAlertsService.showErrorDialog('Unable to Save the Quotation. Please try again.');
      }
    );
  }

  /**Entity**/

  getEntityTradingNamesByEntityId() {
    this.businessEntityId = this.storageService.getEntityId(); // Always a valid number

    if (!this.businessEntityId) {
      console.error("No valid entity ID found.");
      return;
    }

    this.entityService.getEntityTradingNamesByEntityId(this.businessEntityId).subscribe(
      (data) => {
        this.entityTradingNames = data;
        this.quotationData.entityTradingNameId = data.length === 1
          ? data[0].entityTradingNameId
          : undefined; 
      },
      (error) => console.error("Error fetching entity trading names:", error)
    );
  }

  getBusinessEntityById() {

    this.businessEntityId = +((localStorage.getItem('entityId')) + "");

    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(data => {
      this.businessEntity = data;
    }, error => console.error(error));

  }

  /** Item **/

  fetchAllSalesItems() {
    if (!this.entityId) {
      console.error("No valid entity ID found.");
      this.swalAlertsService.showErrorDialog("No valid entity ID found. Please check your settings.");
      return;
    }

    this.quotationService.getAllSalesItemsByEntity(this.entityId).subscribe(
      (items: SalesItem[]) => {
        this.allSalesItems = items.filter(item => item.itemCode !== 'SISSERVICE'); // Store all items
        this.filteredSalesItems = items; // Initialize filtered items with all items
      },
      (error: any) => {
        console.error("Error fetching items", error);
        this.swalAlertsService.showErrorDialog("Unable to fetch sales items. Please try again.");
      }
    );
  }

  onItemSelected(selectedItem: SalesItem, index: number) {
    if (!selectedItem || !selectedItem.itemCode) {
      this.swalAlertsService.showErrorDialog('Please select a valid item.');
      return;
    }

    const existingItemIndex = this.findExistingItemIndex(selectedItem, index);

    if (existingItemIndex !== -1) {
      this.askToAddItemAgain(selectedItem, index);
    } else {
      this.updateQuotationDetails(selectedItem, index);
      this.resetTaxApplicability(index);
    }
  }

  // Helper method to find an existing item in the quotation details
  findExistingItemIndex(selectedItem: SalesItem, index: number): number {
    return this.quotationData.details.findIndex(
      (detail, i) => detail.salesItemId.itemCode === selectedItem.itemCode && i !== index
    );
  }

  askToAddItemAgain(selectedItem: SalesItem, index: number) {
    this.swalAlertsService.showConfirmationDialog(
      'Item already exists',
      'Do you want to add this item again?',
      () => {
        this.updateQuotationDetails(selectedItem, index);
        this.resetTaxApplicability(index);
      },
      () => {
        // Remove the entire row if the user clicks "No"
        this.quotationData.details.splice(index, 1);
      }
    );
  }

onItemAdded(savedItem: SalesItem) {
  this.fetchAllSalesItems(); 
  this.itemCode = savedItem.itemCode; 
}

  /**Customer**/

  loadCustomers() {
    this.businessPartnerService.getCustomerListByEntity(this.entityId).subscribe(
      (customers: BusinessPartner[]) => {
        this.customers = customers;
      },
      (error: HttpErrorResponse) => {
        this.handleApiError("Failed to load customers.");
      }
    );
    this.quotationData.businessPartnerId = "";
  }

  loadBusinessPartnerTypes() {
    this.businessPartnerService.getBusinessPartnerTypesList().subscribe(
      (businessPartnerType: BusinessPartnerType[]) => {
        // Find the "Customer" type from the list
        const customerType = businessPartnerType.find(
          (type) => type.businessPartnerType.toLowerCase() === "customer"
        );

        if (customerType) {
          // Assign the customer type to the businessPartner object
          this.businessPartner.businessPartnerTypeId.businessPartnerTypeId =
            customerType.businessPartnerTypeId;
        }
        // Optionally store the filtered list if needed
        this.businessPartnerType = businessPartnerType;
      },
      (error: HttpErrorResponse) => {
        this.handleApiError("Failed to load Business Partner Type.");
      }
    );
  }

  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(cust => cust.businessPartnerId === +selectedCustomerId);

    // Set the reference field
    this.quotationData.customerName = selectedCustomer?.bpName || '';
  }

  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  setStatus(status: string) {
    this.quotationStatus = status;
  }
  
  handleApiError(errorMessage: string, error: any = null) {
    this.isSaving = false; // Reset saving state when showing error
    this.swalAlertsService.showErrorDialog(errorMessage);
  }

}
