<app-sales-navigation></app-sales-navigation>

<div class="container">
  <div class="header">
    <h3>Create Credit Note</h3>
  </div>

  <form #f="ngForm" class="styled-form" novalidate>
    <div class="bd">
      <div class="form-section">
        <!-- credit note no -->
        <div class="form-row">
          <div class="form-group">
            <label for="creditNoteNumber">Credit Note No:</label>
            <input
              class="input-style"
              id="creditNoteNumber"
              type="text"
              required
              [(ngModel)]="creditNote.creditNoteNumber"
              name="creditNoteNumber"
              style="background-color: #ffffff"
              readonly
              disabled
            />
          </div>
        </div>

        <!-- document date -->
        <div class="form-row">
          <div class="form-group">
            <label for="documentDate">Document Date:</label>
            <input
              id="documentDate"
              type="date"
              class="input-style"
              required
              [(ngModel)]="creditNote.documentDate"
              name="documentDate"
            />
            <div
              *ngIf="
                f.controls['documentDate']?.touched &&
                f.controls['documentDate']?.invalid
              "
              class="text-danger"
            >
              Date is required.
            </div>
          </div>
        </div>

        <!-- customer name -->
        <div class="form-row">
          <div class="form-group">
            <label for="customer">Customer Name:</label>
            <select
              class="form-select"
              id="customer"
              [(ngModel)]="creditNote.businessPartnerId"
              (change)="onCustomerChange($event)"
              name="customerName"
              [disabled]="isCustomerSelected"
              required
            >
              <option value="" selected disabled>Select Customer</option>
              <option *ngFor="let customer of customers" [value]="customer.businessPartnerId">
                {{ customer.bpName }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- Add Invoice Button -->
      <div class="form-row">
        <div class="search-bar d-flex justify-content-start">
          <!-- <button type="button" class="add_invoice" (click)="addInvoiceRow()">
            Add Invoice
          </button> -->
          <button type="button" class="btn btn-primary new-item" (click)="addInvoiceRow()">
            <i class="bi bi-plus-circle-fill"></i> Add New Row
          </button>
        </div>
      </div>

      <div class="table-section">
        <div class="table-responsive">
          <table class="table-section">
            <thead>
              <tr>
                <th>#</th>
                <th>Search</th>
                <th>Invoice No</th>
                <th style="text-align: right;">Amount</th>
                <th style="text-align: right;">Invoice Balance</th>
                <th style="text-align: right;">Credit Amount</th>
                <th style="text-align: right;">New Balance</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <!-- Loop through selected invoices -->
              <tr *ngFor="let invoice of enteredInvoices; let i = index">
                <td>{{ i + 1 }}</td>

                <!-- search -->
                <td>
                  <button
                    type="button"
                    class="btn btn-link"
                    data-bs-toggle="modal"
                    data-bs-target="#invoiceSearchModal"
                    (click)="onSearchInvoiceClick(i)"
                  >
                    <i class="fa fa-search"></i>
                  </button>
                </td>

                <!-- invoice no -->
                <td>
                  <input
                    type="text"
                    class="form-control1"
                    required
                    [(ngModel)]="enteredInvoices[i].invoiceNumber"
                    (ngModelChange)="onInvoiceNumberChange($event, i)"
                    name="invoiceNumber{{ i }}"
                    placeholder="Enter Invoice No"
                  />
                </td>

                <!-- Amount -->
                <td>
                  <input
                    type="text"
                    class="form-control"
                    [value]="invoice.grandTotal | currency : 'USD' : 'symbol' : '1.2-2'"
                    disabled
                  />
                  <input
                    type="hidden"
                    [(ngModel)]="invoice.grandTotal"
                    name="grandTotal{{ i }}"
                  />
                </td>

                <!-- Balance -->
                <td>
                  <input
                    type="text"
                    class="form-control"
                    [value]="invoice.balanceAmount ? (invoice.balanceAmount | currency : 'USD' : 'symbol' : '1.2-2'): '0.00'"
                    disabled
                  />
                  <input
                    type="hidden"
                    [(ngModel)]="invoice.balanceAmount"
                    name="balanceAmount{{ i }}"
                  />
                </td>

                <!-- Credit Amount -->
                <td>
                  <input
                    type="number"
                    class="form-control"
                    required
                    [(ngModel)]="invoice.creditAmount"
                    name="creditAmount{{ i }}"
                    (keydown)="preventEnter($event)"
                    (input)="validateCreditAmount(invoice)"
                    min="0"
                    [ngClass]="{ 'is-invalid': f.submitted && f.controls['creditAmount' + i].invalid }"
                  />
                  <div
                    *ngIf="
                      f.controls['creditAmount' + i]?.touched &&
                      f.controls['creditAmount' + i]?.invalid
                    "
                    class="text-danger"
                  >
                    Credit Amount is required.
                  </div>
                </td>

                <!-- New Invoice Balance -->
                <td>
                  <input
                    id="InvoiceBalance"
                    type="text"
                    class="form-control"
                    [value]="getInvoiceNewBalance(invoice) | currency : 'USD' : 'symbol' : '1.2-2'"
                    disabled
                  />
                </td>

                <td>
                  <button
                    type="button"
                    class="btn btn-link"
                    (click)="removeItem(i)"
                  >
                    <i class="fa fa-trash" style="color: red"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- total credit amount n remarks section -->
      <div class="form-section_2">

        <div class="form-row">
          <div class="form-group_2">
            <label for="totalCreditAmount">Total Credit Amount:</label>
            <input id="totalCreditAmount" type="text" class="form-control"
              [value]="getTotalCreditAmountEntered() | currency : 'USD' : 'symbol' : '1.2-2'" disabled />
          </div>
        </div>


        <div class="form-row">
          <div class="form-group_2">
            <label for="remarks">Remarks:</label>
            <textarea id="remarks" class="form-control" required [(ngModel)]="creditNote.remarks" name="remarks" rows="3" style="text-align: left;"></textarea>
          </div>
        </div>

        <!-- cancel n save credit note button -->
        <div class="d-flex justify-content-end mt-5 mb-4">
          <button type="button" class="btn btn-secondary me-2" style="border-radius: 12px;" (click)="onCancel()">Cancel</button>
          <button type="submit" class="btn btn-primary" style="border-radius: 12px;" (click)="onSubmit()" [disabled]="isInvalidInvoice">Save Credit Note</button>
        </div>
      </div>
    </div>
  </form>
</div>

<!-- Modal for Searching Invoices -->
<div class="modal fade" id="invoiceSearchModal" tabindex="-1" aria-labelledby="invoiceSearchModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="invoiceSearchModalLabel">Search Invoice</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form>
          <div class="mb-3">
            <label for="fromDate" class="form-label">From Date</label>
            <input type="date" class="form-control" id="fromDate" [(ngModel)]="searchCriteria.fromDate" name="fromDate" />
          </div>
          <div class="mb-3">
            <label for="toDate" class="form-label">To Date</label>
            <input type="date" class="form-control" id="toDate" [(ngModel)]="searchCriteria.toDate" name="toDate" />
          </div>
        </form>

        <!-- List of filtered invoices in table view -->
        <div *ngIf="filteredInvoices && filteredInvoices.length > 0">
          <h6>Invoices</h6>
          <table class="table table-striped">
            <thead>
              <tr>
                <th scope="col">Invoice No.</th>
                <th scope="col">Date</th>
                <th scope="col" class="text-end">Balance</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let invoice of filteredInvoices">
                <td>{{ invoice.invoiceNumber }}</td>
                <td>{{ invoice.postingDate | date : "dd-MM-yyyy" }}</td>
                <td class="text-end">{{ invoice.balanceAmount | currency }}</td>
                <td class="text-end">
                  <button class="btn btn-sm btn-primary" (click)="selectInvoice(invoice)">Select</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div *ngIf="filteredInvoices?.length === 0">
          <!--<p>No invoices found for the selected criteria.</p>-->
        </div>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" (click)="searchInvoices()">Search</button>
      </div>
    </div>
  </div>
</div>
