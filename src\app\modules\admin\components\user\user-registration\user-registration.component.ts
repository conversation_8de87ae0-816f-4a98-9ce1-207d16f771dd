import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { UserService } from '../user.service';
import { EntityService } from 'src/app/modules/entity/entity.service';
import { User, UserType } from '../user';
import { Entity } from 'src/app/modules/entity/entity';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-user-registration',
  templateUrl: './user-registration.component.html',
  styleUrls: ['./user-registration.component.css'],
})
export class UserRegistrationComponent implements OnInit {
  userForm: FormGroup;
  user: User = new User();
  entity: Entity = new Entity();
  entityId: number = 0;
  isUsernameExists: boolean = false;
  usernameExistsMessage: string = '';
  selectedUserType: UserType = new UserType();
  userTypes: UserType[] = [];
  isPasswordVisible: boolean = false;

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private router: Router,
    private entityService: EntityService
  ) {
    this.userForm = this.fb.group(
      {
        firstName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')],
        ],
        lastName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')],
        ],
        email: ['', [Validators.required, Validators.email]],
        userType: ['', Validators.required],
        password: [
          '',
          [
            Validators.required,
            Validators.pattern(
              '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()_+\\-=?.])[A-Za-z\\d!@#$%^&*()_+\\-=?.]{8,}$'
            ),
          ],
        ],
        rePassword: ['', Validators.required],
      },
      { validator: this.passwordMatchValidator }
    );
  }

  ngOnInit(): void {
    this.entityId = Number(localStorage.getItem('entityId'));
    this.loadBusinessEntity();
    this.getUserTypes();
  }

  passwordMatchValidator(form: FormGroup): { mismatch: boolean } | null {
    return form.get('password')?.value === form.get('rePassword')?.value
      ? null
      : { mismatch: true };
  }

  async onSubmitRegister(): Promise<void> {
    if (this.userForm.invalid) {
      this.userForm.markAllAsTouched();
      return;
    }

    try {
      this.user.entityIds = [this.entity.entityId];
      this.user.userTypeId = this.selectedUserType;
      await this.registerUser();
    } catch (error) {
      console.error('Error during user registration:', error);
    }
  }

  private async registerUser(): Promise<void> {
    try {
      const response = await this.userService.register(this.user).toPromise();
      this.user = response;
      this.userService.verifyUser(this.user.username).subscribe((response) => {
      });
      Swal.fire({
        title: 'Success!',
        text: 'User registered successfully.',
        icon: 'success',
        confirmButtonText: 'OK',
        confirmButtonColor: '#28a745',
        timer: 2500,
        timerProgressBar: true,
        backdrop: true,
      });

      this.router.navigate(['/dashboard']);
    } catch (error) {
      this.userService.setAuthToken(null);
      console.error('User registration failed:', error);
    }
  }

  private loadBusinessEntity(): void {
    if (this.entityId) {
      this.entityService.getBusinessEntityById(this.entityId).subscribe(
        (data) => {
          this.entity = data;
        },
        (error) => {
          console.error('Error fetching entity:', error);
        }
      );
    }
  }

  getUserTypes() {
    this.userService.getUserTypesList().subscribe(
      (response) => {
        this.userTypes = response.filter(
          (userType) =>
            userType.userType !== 'Admin' &&
            userType.userType !== 'System Admin'
        );
      },
      (error) => {
        console.error('Error fetching user types:', error);
      }
    );
  }


  mapUserTypeLabel(userType: string): string {
  switch (userType) {
    case 'Primary user':
      return 'Admin User';
    case 'General user':
      return 'Basic User';
    case 'Accountant':
      return 'Adviser – Accountant/Book keeper';
    default:
      return userType;
  }
}


  checkUser(): void {
    if (this.userForm.get('email')?.valid) {
      const email = this.userForm.get('email')?.value;
      this.userService.checkUser(email).subscribe(
        (exists) => {
          this.isUsernameExists = exists;
          this.usernameExistsMessage = exists
            ? 'This user already exists. Please try a different email.'
            : '';
        },
        (error) => {
          console.error('Error checking user existence:', error);
        }
      );
    } else {
      this.isUsernameExists = false;
      this.usernameExistsMessage = '';
    }
  }
}
