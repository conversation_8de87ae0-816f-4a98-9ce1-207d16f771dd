<app-payroll-nevigation></app-payroll-nevigation>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>
<div class="container">
  <div class="actions sub-container">
    <h2>Pay Run</h2>
  </div>

  <div>
    <div class="PayRun-Tabs">
      <div class="payroll-tabs">
        <ul class="nav nav-tabs">
          <li class="nav-item">
            <a
              class="nav-link"
              [class.active]="activeInnerTab === 'DraftPayRun'"
              (click)="setActiveInnerTab('DraftPayRun')"
              >Draft PayRuns</a
            >
          </li>
          <li class="nav-item">
            <a
              class="nav-link"
              [class.active]="activeInnerTab === 'SkippedPayRuns'"
              (click)="setActiveInnerTab('SkippedPayRuns')"
            >
              Skipped PayRuns
            </a>
          </li>
          <li class="nav-item">
            <a
              class="nav-link"
              [class.active]="activeInnerTab === 'PostPayRuns'"
              (click)="setActiveInnerTab('PostPayRuns')"
            >
              Post PayRuns
            </a>
          </li>
          <li class="nav-item">
            <a
              class="nav-link"
              [class.active]="activeInnerTab === 'Unscheduled PayRuns'"
              (click)="setActiveInnerTab('Unscheduled PayRuns')"
            >
              Unscheduled PayRuns
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Drafted pay runs -->
    <div class="Leave" *ngIf="isActiveInnerTab('DraftPayRun')">
      <!-- <button>hiran</button> -->
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Calendar</th>
              <th>Period</th>
              <th>Payment Date</th>
              <th>Earnings</th>
              <th>Deductions</th>
              <th>Reimbursements</th>
              <th>Super</th>
              <th>Tax</th>
              <th>Net Pay</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let payRunMasters of draftPayRuns
                  | paginate : { itemsPerPage: 5, currentPage: leaveTwoPage }
              "
              (click)="
                viewDetails(
                  payRunMasters.payPeriod.payCalendar.payCalendarId,
                  payRunMasters.payPeriod.payPeriodId,
                  payRunMasters.payRunId
                )
              "
            >
              <td>{{ payRunMasters.payPeriod.payCalendar.calendarName }}</td>
              <td>
                {{ payRunMasters.payPeriod.payStartDate }} :
                {{ payRunMasters.payPeriod.nextPayDate }}
              </td>
              <td>{{ payRunMasters.paymentDate }}</td>
              <td>{{ payRunMasters.earnings | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.deductions | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.reimbursements | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.superAmount | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.tax | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.netPay | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.stpFilling }}</td>
            </tr>
          </tbody>
        </table>

        <pagination-controls
          class="d-flex justify-content-end"
          (pageChange)="leaveTwoPage = $event"
        >
        </pagination-controls>

        <button
          class="btn btn-primary"
          type="button"
          data-bs-toggle="modal"
          data-bs-target="#AddPayRun"
        >
          Add Pay Run
        </button>
      </div>
    </div>

    <!-- Skipped pay runs -->
    <div class="Leave" *ngIf="isActiveInnerTab('SkippedPayRuns')">
      <!-- <button>hiran</button> -->
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Calendar</th>
              <th>Period</th>
              <th>Payment Date</th>
              <th>Earnings</th>
              <th>Deductions</th>
              <th>Reimbursements</th>
              <th>Super</th>
              <th>Tax</th>
              <th>Net Pay</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let payRunMasters of skippedPayRuns
                  | paginate : { itemsPerPage: 5, currentPage: leaveTwoPage }
              "
              (click)="
                viewDetails(
                  payRunMasters.payPeriod.payCalendar.payCalendarId,
                  payRunMasters.payPeriod.payPeriodId,
                  payRunMasters.payRunId
                )
              "
            >
              <td>{{ payRunMasters.payPeriod.payCalendar.calendarName }}</td>
              <td>
                {{ payRunMasters.payPeriod.payStartDate }} :
                {{ payRunMasters.payPeriod.nextPayDate }}
              </td>
              <td>{{ payRunMasters.paymentDate }}</td>
              <td>{{ payRunMasters.earnings | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.deductions | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.reimbursements | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.superAmount | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.tax | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.netPay | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.stpFilling }}</td>
            </tr>
          </tbody>
        </table>

        <pagination-controls
          class="d-flex justify-content-end"
          (pageChange)="leaveTwoPage = $event"
        >
        </pagination-controls>

        <button
          class="btn btn-primary"
          type="button"
          data-bs-toggle="modal"
          data-bs-target="#AddPayRun"
        >
          Add Pay Run
        </button>
      </div>
    </div>

    <!-- Post pay runs  -->
    <div class="Leave" *ngIf="isActiveInnerTab('PostPayRuns')">
      <!-- <button>hiran</button> -->
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Calendar</th>
              <th>Period</th>
              <th>Payment Date</th>
              <th>Earnings</th>
              <th>Deductions</th>
              <th>Reimbursements</th>
              <th>Super</th>
              <th>Tax</th>
              <th>Net Pay</th>
              <th>Unscheduled Pay Run</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let payRunMasters of postPayRuns
                  | paginate : { itemsPerPage: 5, currentPage: leaveTwoPage };
                let last = last
              "
              (click)="
                viewDetails(
                  payRunMasters.payPeriod.payCalendar.payCalendarId,
                  payRunMasters.payPeriod.payPeriodId,
                  payRunMasters.payRunId
                )
              "
            >
              <td>{{ payRunMasters.payPeriod.payCalendar.calendarName }}</td>
              <td>
                {{ payRunMasters.payPeriod.payStartDate }}
                {{ payRunMasters.payPeriod.nextPayDate }}
              </td>
              <td>{{ payRunMasters.paymentDate }}</td>
              <td>{{ payRunMasters.earnings | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.deductions | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.reimbursements | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.superAmount | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.tax | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.netPay | number : "1.2-4" }}</td>
              <td>
                <ng-container *ngIf="payRunMasters.payRunId === latestPostedPayRunIds[payRunMasters.payPeriod.payCalendar.payCalendarId]">
                  <button
                    (click)="
                      createUnscheduledPayRun($event, payRunMasters.payPeriod);
                      $event.stopPropagation()
                    "
                    type="button"
                    class="unscheduledpayrun-btn"
                  >
                    Create Pay Run
                  </button>
                </ng-container>
              </td>
              <!-- <td>
                <button
                  class="post"
                  (click)="
                    stpFile(
                      $event,
                      payRunMasters.payPeriod.payCalendar.payCalendarId,
                      payRunMasters.payPeriod.payPeriodId,
                      payRunMasters.payRunId
                    )
                  "
                  [disabled]="
                    filingStatus[payRunMasters.payRunId] === 'submitting' ||
                    filingStatus[payRunMasters.payRunId] === 'filed' ||
                    payRunMasters.stpFilling === 'Submitted'
                  "
                >
                  {{
                    filingStatus[payRunMasters.payRunId] === "submitting"
                      ? "Submitting..."
                      : filingStatus[payRunMasters.payRunId] === "filed" ||
                        payRunMasters.stpFilling === "Submitted"
                      ? "Submited"
                      : "Submit"
                  }}
                </button>
              </td> -->
            </tr>
          </tbody>
        </table>

        <pagination-controls
          class="d-flex justify-content-end"
          (pageChange)="leaveTwoPage = $event"
        >
        </pagination-controls>

        <button
          class="btn btn-primary"
          type="button"
          data-bs-toggle="modal"
          data-bs-target="#AddPayRun"
        >
          Add Pay Run
        </button>
      </div>
    </div>

    <!-- Unscheduled Pay run -->
    <div class="Leave" *ngIf="isActiveInnerTab('Unscheduled PayRuns')">
      <!-- <button>hiran</button> -->
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Calendar</th>
              <th>Period</th>
              <th>Payment Date</th>
              <th>Earnings</th>
              <th>Deductions</th>
              <th>Reimbursements</th>
              <th>Super</th>
              <th>Tax</th>
              <th>Net Pay</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let payRunMasters of unscheduledPayRuns
                  | paginate : { itemsPerPage: 5, currentPage: leaveTwoPage }
              "
              (click)="
                viewDetails(
                  payRunMasters.payPeriod.payCalendar.payCalendarId,
                  payRunMasters.payPeriod.payPeriodId,
                  payRunMasters.payRunId
                )
              "
            >
              <td>{{ payRunMasters.payPeriod.payCalendar.calendarName }}</td>
              <td>
                {{ payRunMasters.payPeriod.payStartDate }}
                {{ payRunMasters.payPeriod.nextPayDate }}
              </td>
              <td>{{ payRunMasters.paymentDate }}</td>
              <td>{{ payRunMasters.earnings | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.deductions | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.reimbursements | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.superAmount | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.tax | number : "1.2-4" }}</td>
              <td>{{ payRunMasters.netPay | number : "1.2-4" }}</td>
            </tr>
          </tbody>
        </table>

        <pagination-controls
          class="d-flex justify-content-end"
          (pageChange)="leaveTwoPage = $event"
        >
        </pagination-controls>

        <button
          class="btn btn-primary"
          type="button"
          data-bs-toggle="modal"
          data-bs-target="#AddPayRun"
        >
          Add Pay Run
        </button>
      </div>
    </div>
  </div>

  <div
    class="modal fade"
    id="AddPayRun"
    tabindex="-1"
    aria-labelledby="Ordinary-Time_Earnings"
    aria-hidden="true"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="ot-modal-header">
          <button
            type="button"
            class="custom-close-btn"
            data-bs-dismiss="modal"
            style="margin-left: 95%"
            (click)="navigateToPayrollPayRun()"
          >
            <i class="bi bi-x-circle"></i>
          </button>
          <div class="modal-header">
            <h5
              class="modal-title"
              id="add_pay_calendar"
            >
              Add PayRun
            </h5>
          </div>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="earnings-name" class="form-label"
                >Select Pay Period</label
              >
              <!-- Dropdown trigger -->
              <div class="dropdown w-100">
                <button
                  #dropdownButton
                  class="btn btn-secondary dropdown-toggle w-100 d-flex justify-content-between align-items-center"
                  type="button"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                  id="dropdownMenuButton"
                  (click)="toggleDropdown()"
                >
                  {{
                    selectedPayPeriodId
                      ? getPayPeriodLabel(selectedPayPeriodId)
                      : "Select a Pay Period"
                  }}
                </button>

                <!-- Dropdown list -->
                <ul
                  class="dropdown-menu w-100 custom-dropdown-scroll"
                  aria-labelledby="dropdownMenuButton"
                  [ngClass]="{ 'scroll-enabled': openPayPeriods.length > 4 }"
                >
                  <li
                    *ngFor="let period of openPayPeriods"
                    class="dropdown-item d-flex justify-content-between align-items-center"
                    (click)="selectPayPeriod(period.payPeriodId)"
                  >
                    {{
                      period.payCalendar.calendarName +
                        " : " +
                        period.payStartDate +
                        " - " +
                        period.nextPayDate
                    }}
                    <button
                      class="btn btn-xs btn-danger px-3 py-1 custom-skip-button"
                      type="button"
                      data-bs-toggle="modal"
                      data-bs-target="#AddPayRun"
                      (click)="payRunSkip(period.payPeriodId)"
                    >
                      Skip
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </form>

          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
              (click)="navigateToPayrollPayRun()"
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              [disabled]="!selectedPayPeriodId"
              (click)="onPayRunAdd()"
            >
              Add
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
