import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from '../../user.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.css'],
})
export class ForgotPasswordComponent implements OnInit {
  email: string = '';
  emailError: string | null = null; // Variable to hold the validation message

  constructor(private router: Router, private userService: UserService) {}

  ngOnInit(): void {}

  // Validates the email format
  private isEmailValid(email: string): boolean {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email);
  }

  // Checks email validity on change
  onEmailChange(): void {
    if (!this.isEmailValid(this.email)) {
      this.emailError = 'Please enter a valid email address.';
    } else {
      this.emailError = null; // Clear the error if valid
    }
  }

  // Submits the email for password reset
  onSubmit(): void {
    this.emailError = null; // Reset the error message
  
    if (!this.isEmailValid(this.email)) {
      this.emailError = 'Please enter a valid email address.';
      return;
    }
  
    this.userService.forgotPassword(this.email).subscribe(
      () => {
        // Reset password email sent successfully
        this.emailError = null; // Clear error message on success
  
        // Show SweetAlert success message
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Password reset email has been sent successfully. Please check your inbox.',
          confirmButtonText: 'OK',
        }).then(() => {
          this.navigateToUserLogin(); // Navigate to login after user closes the alert
        });
      },
      (error) => {
        console.error('Error sending password reset email:', error);
        this.emailError = 'Failed to send password reset email. Please try again.'; // Set error message on failure
      }
    );
  }

  // Navigates to the user login page
  navigateToUserLogin(): void {
    this.router.navigate(['user-login']);
  }
}
