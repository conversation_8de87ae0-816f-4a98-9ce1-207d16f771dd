<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response>

<div class="container">
    <div class="heading-section">
        <h1>Reports</h1>
    </div>

    <div class="button-header"><span class="button-arrow">&#9013;</span>Sales</div>
    <div class="button-section">
        <button type="button" class="custom-button" (click)="navigateQuotationReport()">Sales Quote <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateInvoiceReport()">Sales Invoice <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateCreditNoteReport()">Credit Notes <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigatePaymentReport()">Payments <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateInvoiceCashReport()">Sales Receivables <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateCustomerStatementReport()">Customer Statements <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateAccountsStatementReport()">Account Statements <span class="arrow">&gt;</span></button>
    </div>

    <div class="button-header mt-4"><span class="button-arrow">&#9013;</span>Finance</div>
    <div class="button-section">
        <button type="button" class="custom-button" (click)="navigateGeneralPosting()">Ledger Posting <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateGlReport()">General Ledger <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateBill()">Bill <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateCreditNoteBill()">Credit Notes <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigatePaymentBill()">Payments <span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigatePnL()">Profit & loss<span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateBalanceSheet()">Balance Sheet<span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateBAS()">Activity Statement (BAS)<span class="arrow">&gt;</span></button>
        <button type="button" class="custom-button" (click)="navigateSupplier()">Supplier<span class="arrow">&gt;</span></button>
    </div>
</div>