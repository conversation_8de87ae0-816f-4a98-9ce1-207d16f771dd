import { Component, ElementRef, ViewChild } from '@angular/core';
import { QuotationService } from '../quotation.service';
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { BusinessPartnerService } from '../../business-partner/business-partner.service';
import { BusinessPartner } from '../../business-partner/business-partner';
import { QuoteHead } from '../quotation';
import { HttpErrorResponse } from '@angular/common/http';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import * as bootstrap from 'bootstrap';
import { SwalAlertsService } from '../../swal-alerts/swal-alerts.service';

@Component({
  selector: 'app-quotation-report',
  templateUrl: './quotation-report.component.html',
  styleUrls: ['./quotation-report.component.css']
})
export class QuotationReportComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  selectedReportType: any;
  fromDate: any;
  toDate: any;
  customers: BusinessPartner[] = [];
  quotationData: QuoteHead = new QuoteHead();
  isLoading = false;
  getAllCustomers = false;  // New property
  status: string = '%';


  @ViewChild('quotationPreviewFrame') quotationPreviewFrame!: ElementRef;

  constructor(
    private quotationService: QuotationService,
    private router: Router,
    public sanitizer: DomSanitizer,
    private businessPartnerService: BusinessPartnerService,
    private swalAlerts: SwalAlertsService 
  ) { }

  ngOnInit() {
    this.quotationData.businessPartnerId = '0';
    this.loadCustomers();
  }

  previewQuotations(fromDate: string, toDate: string, status: string, businessPartnerId: any) {
  if (!fromDate || !toDate) {

    this.swalAlerts.showSwalWarning('Warning!', 'Please select Date Range', 'Missing date range for quotation report.');
    return;
  }
  this.isLoading = true;
  const entityId = +localStorage.getItem('entityId')!;
  const entityUUID = localStorage.getItem('entityUuid')!;
  const bpId = (businessPartnerId === 0 || businessPartnerId === '' || businessPartnerId == null) ? null : +businessPartnerId;

  const requestData = {
    fromDate,
    toDate,
    status,
    entityId,
    businessPartnerId: bpId,
    entityUUID
  };

  this.quotationService.getQuotationListReport(requestData).subscribe(
    data => {
      const base64String = data.response;
      if (base64String) {
        this.loadPdfIntoIframe(base64String);
      } else {
        this.isLoading = false;
        alert('No quotation data for preview.');
      }
    },
    error => {
      this.isLoading = false;
      alert('Error loading quotation preview.');
    }
  );
}

private loadPdfIntoIframe(base64String: string) {
  if (base64String && base64String.trim().length >= 50) {
    const pdfData = 'data:application/pdf;base64,' + base64String;
    const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(pdfData) as any;
    const iframe = this.quotationPreviewFrame.nativeElement;

    iframe.onload = () => {
      this.isLoading = false;
    };

    iframe.setAttribute('src', sanitizedUrl.changingThisBreaksApplicationSecurity);

    // Open modal manually using Bootstrap JS
    const modalElement = document.getElementById('simpleModal');
    const modal = new bootstrap.Modal(modalElement!);
    modal.show();
  } else {
     this.isLoading = false;
     this.swalAlerts.showSwalWarning('No Data', 'No quotation data for preview.', 'No quotation data was returned for the selected range.');

  }
}


 loadCustomers() {
  const entityId = +((localStorage.getItem('entityId')) + '');
  this.businessPartnerService.getCustomerListByEntity(entityId).subscribe(
    (customers: BusinessPartner[]) => {
      this.customers = customers;
    },
    (error: HttpErrorResponse) => {
      console.error('Error fetching customers', error);

      //  Use SwalAlertsService for error with Chimp support
      this.swalAlerts.showErrorWithChimpSupport(
        'Failed to load customers.',
        'Unable to fetch customer list for this entity. Please check if the customer service is responding.'
      );
    }
  );
}

  onCustomerChange(event: any) {
    const selectedCustomerId = event.target.value;
    const selectedCustomer = this.customers.find(cust => cust.businessPartnerId === +selectedCustomerId);

    // Set the reference field
    this.quotationData.customerName = selectedCustomer?.bpName || '';
  }

  // Method to toggle all customers' data
  toggleAllCustomers() {
    this.getAllCustomers = !this.getAllCustomers;
    if (this.getAllCustomers) {
      this.quotationData.businessPartnerId = '';  // Clear selected customer when showing all
    }
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}
