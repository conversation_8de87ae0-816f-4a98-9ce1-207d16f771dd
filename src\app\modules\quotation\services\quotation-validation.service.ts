import { Injectable } from '@angular/core';
import { QuotationConstants, QuoteStatusUtils } from '../quotation.constants';
import { ValidationResult, UserInfo } from '../quotation.interfaces';
import { QuoteHead } from '../quotation';

@Injectable({
  providedIn: 'root'
})
export class QuotationValidationService {

  constructor() { }

  validateQuotesForSending(selectedQuotes: QuoteHead[]): ValidationResult {
    if (selectedQuotes.length === 0) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.NO_QUOTES_SELECTED
      };
    }

    const validStatusQuotes = selectedQuotes.filter(q =>
      QuoteStatusUtils.isValidForSending(q.status)
    );

    if (validStatusQuotes.length === 0) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.INVALID_STATUS_FOR_SENDING
      };
    }

    return { isValid: true };
  }

  validateEmailAddress(email: string): ValidationResult {
    if (!email || email.trim() === '') {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.MISSING_EMAIL
      };
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        isValid: false,
        message: 'Please enter a valid email address.'
      };
    }

    return { isValid: true };
  }

  validateQuoteForRevision(selectedQuotes: QuoteHead[]): ValidationResult {
    if (selectedQuotes.length === 0) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.NO_SENT_QUOTE_SELECTED
      };
    }

    if (selectedQuotes.length > 1) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.MULTIPLE_QUOTES_REVISION
      };
    }

    return { isValid: true };
  }

  validateQuoteForCopy(selectedQuotes: QuoteHead[]): ValidationResult {
    if (selectedQuotes.length === 0) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.NO_QUOTE_TO_COPY
      };
    }

    if (selectedQuotes.length > 1) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.MULTIPLE_QUOTES_COPY
      };
    }

    return { isValid: true };
  }

  validateQuoteForPreview(selectedQuotes: QuoteHead[]): ValidationResult {
    if (selectedQuotes.length === 0) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.NO_QUOTE_TO_PREVIEW
      };
    }

    if (selectedQuotes.length > 1) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.MULTIPLE_QUOTES_PREVIEW
      };
    }

    return { isValid: true };
  }

  validateQuoteForInvoiceCreation(selectedQuotes: QuoteHead[]): ValidationResult {
    const validQuotes = selectedQuotes.filter(quote => 
      QuoteStatusUtils.isValidForInvoiceCreation(quote.status)
    );

    if (validQuotes.length === 0) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.NO_PENDING_QUOTE_FOR_INVOICE
      };
    }

    if (validQuotes.length > 1) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.MULTIPLE_QUOTES_INVOICE
      };
    }

    return { isValid: true };
  }

  validateSearchCriteria(searchTerm: string, startDate: string | null, endDate: string | null): ValidationResult {
    if (!searchTerm && !startDate && !endDate) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.SEARCH_CRITERIA_MISSING
      };
    }

    return { isValid: true };
  }

  validateExportData(data: any[]): ValidationResult {
    if (!data || data.length === 0) {
      return {
        isValid: false,
        message: QuotationConstants.MESSAGES.NO_DATA_TO_EXPORT
      };
    }

    return { isValid: true };
  }
}
