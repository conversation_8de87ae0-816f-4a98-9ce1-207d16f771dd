import { Component , ViewChild} from '@angular/core';
import Swal from 'sweetalert2';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { User } from '../user';
import { Router } from '@angular/router';
import { UserService } from '../user.service';

@Component({
  selector: 'app-manage-users',
  templateUrl: './manage-users.component.html',
  styleUrls: ['./manage-users.component.css']
})
export class ManageUsersComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

   user: User [] =[];
   filteredUsers: User[] = [];
   activeTab = 'all';
   constructor(private userService : UserService , private router: Router) {}
   
   ngOnInit(): void {
    this.getUsersListByEntity();
  }

  private getUsersListByEntity(): void{
   const entityId = +((localStorage.getItem('entityId')) + "");
    this.userService.getUsersListByEntity(entityId).subscribe(data => {
      this.user = data;
      this.filteredUsers = this.user;
  
    });
  }

  

  
  navigateUserInvite(): void {
    this.router.navigate(['/invite-user']);
  }

    navigateUserRegister(): void {
    this.router.navigate(['/user-registration']);
  }

   navigateRequests(): void {
    this.router.navigate(['/entity-requests']);
  }

   navigateBookkeeper(): void {
    this.router.navigate(['/bookkeeper']);
  }


  
  
    deleteUser(id: number): void {
      Swal.fire({
        title: 'Are you sure?',
        text: 'Do you really want to delete this User?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'No, keep it',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
      }).then((result) => {
        if (result.isConfirmed) {
          this.userService.deleteUser(id)
            .subscribe(
              () => {
                Swal.fire({
                  title: 'Deleted!',
                  text: 'User deleted successfully.',
                  icon: 'success',
                  confirmButtonText: 'OK',
                  confirmButtonColor: '#28a745',
                }).then(() => {
                  window.location.reload();
                });
              },
              (error) => {
                console.error('Failed to delete User!', error);
    
                Swal.fire({
                  title: 'Error!',
                  text: 'Failed to delete User!',
                  icon: 'error',
                  confirmButtonText: 'OK',
                  cancelButtonText: 'Ask Chimp',
                  confirmButtonColor: '#be0032',
                  cancelButtonColor: '#007bff',
                  showCancelButton: true, 
                }).then((result) => {
                  if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                    if (this.chatBotComponent) {
                      Swal.fire({
                        title: 'Processing...',
                        text: 'Please wait while Chimp processes your request.',
                        allowOutsideClick: false,
                        didOpen: () => {
                          Swal.showLoading();
                          this.chatBotComponent.setInputData('Failed to delete User!');
                          this.chatBotComponent.responseReceived.subscribe(response => {
                            Swal.close();
                            this.chatResponseComponent.showPopup = true;
                            this.chatResponseComponent.responseData = response;
                            this.playLoadingSound();
                            this.stopLoadingSound() 
                          });
                        },
                      });
                    } else {
                      console.error('ChatBotComponent is not available.');
                    }
                  }
                });
              }
            );
        }
      });
    }

      playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }


  
  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.filterQuotes(); // Filter quotes when tab changes
  }

  filterQuotes() {
  let filtered = this.user;

  if (this.activeTab !== 'all') {
    const normalizedTab = this.activeTab.toLowerCase().replace(/\s/g, '');
    filtered = filtered.filter(users => {
      const userType = users.userTypeId?.userType?.toLowerCase().replace(/[\s_]/g, '');
      return userType === normalizedTab;
    });
  }

  this.filteredUsers = filtered;
}

  // Reset filters
  resetFilters() {
    this.activeTab = 'all'; // Reset the active tab to 'all'
    this.filteredUsers= this.user;
  }

}
