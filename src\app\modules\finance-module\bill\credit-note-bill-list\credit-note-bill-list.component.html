<app-finance-navbar></app-finance-navbar>
<app-bot-controller></app-bot-controller>
<app-chat-response></app-chat-response> 

<div class="container">
    <div class="actions">
        <h1>Credit Notes</h1>
        <!--<div class="btn-group" [class.show]="isDropdownOpen">
            <button
              type="button"
              class="btn btn-secondary dropdown-toggle gradient-btn"
              data-bs-toggle="dropdown"
              aria-expanded="false"
              (click)="toggleDropdown()"
            >
              <i class="bi bi-three-dots-vertical"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen">
                <li>
                  <a class="dropdown-item" (click)="cancelSelectedCreditNotes()">Cancel</a>
                </li>
              </ul>              
          </div>-->
    </div>
    <div class="search-create">
        <button class="btn btn-outline-primary" (click)="addCreditNote()">
          Create Credit Note
        </button>
        <button (click)="exportToExcel()" class="export-btn">Export to Excel</button>

    </div>

    <div class="Card">
        <div class="row1">
            <div class="row1_col1">
                <label for="search-input">Credit Note No or Customer</label>
                <div class="input-container">
                    <input type="text" class="search-input" id="search-input" [(ngModel)]="searchTerm" />
                    <i class="bi bi-search"></i>
                </div>
            </div>

            <div class="row1_col3">
                <label for="StartDate">Start Date</label>
                <div style="position: relative; width: 100%;">
                <input
                    matInput
                    [matDatepicker]="startDatePicker"
                    class="date-picker"
                    id="StartDate"
                    [(ngModel)]="startDate"
                    placeholder="dd/mm/yyyy"
                />
                <mat-datepicker-toggle matSuffix [for]="startDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
                </div>
                <mat-datepicker #startDatePicker></mat-datepicker>
            </div>

            <div class="row1_col4">
                <label for="EndDate">End Date</label>
                <div style="position: relative; width: 100%;">
                <input
                    matInput
                    [matDatepicker]="endDatePicker"
                    class="date-picker"
                    id="EndDate"
                    [(ngModel)]="endDate"
                    placeholder="dd/mm/yyyy"
                />
                <mat-datepicker-toggle matSuffix [for]="endDatePicker" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></mat-datepicker-toggle>
                </div>
                <mat-datepicker #endDatePicker></mat-datepicker>
            </div>
        </div>

        <div class="row2">
            <div class="row2_col3">
                <button type="button" class="secondary-button" (click)="resetFilters()">Reset</button>
            </div>
            <div class="row2_col1">
                <button type="button" class="primary-button" (click)="filterBills()">Search</button>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table>
            <thead>
                <tr class="table-head">
                    <th scope="col" class="valueCheckbox">
                        <input type="checkbox" [checked]="isAllSelected" (change)="selectAll($event)" />
                    </th>                      
                    <th scope="col" class="valuehead">Credit Note Number</th>
                    <th scope="col" class="valuehead">Bill Number</th>
                    <th scope="col" class="valuehead">Supplier Name</th>
                    <th scope="col" class="valuehead">Document Date</th>
                    <th style="text-align: right;" scope="col" class="valuehead">Total Credit Amount</th>
                    <th scope="col" class="valuehead" style="text-align: center">Status</th>
                    <!--<th style="text-align: center;" scope="col" class="valuehead">Actions</th>-->
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let bill of filteredCreditNotes; let i = index">
                    <td class="valueCheckbox">
                        <input
                          type="checkbox"
                          [(ngModel)]="bill.selected"
                          (change)="toggleSelection(bill, $event)"
                        />
                    </td>
                    <td class="value">{{ bill.creditNoteNumberBill }}</td>
                    <td class="value">{{ bill.referenceNos }}</td>
                    <td class="value">{{ bill.supplierName }}</td>
                    <td class="value">{{ bill.documentDate | date:'dd-MM-yyyy' }}</td>
                    <td style="text-align: right;" class="value">{{ bill.totalCreditAmount | currency}}</td>
                    <td
                    style="padding-left: 15px"
                    class="value"
                      [ngClass]="{
                          'text-pending': bill.documentStatus === 'Pending',
                          'text-open': bill.documentStatus === 'Open',
                          'text-overdue': bill.documentStatus === 'Overdue',
                          'text-paid': bill.documentStatus === 'Paid',
                          'text-Closed': bill.documentStatus === 'Closed',
                          'text-canceled': bill.documentStatus === 'Canceled',
        
                          
                      }"
                  >
                    <span
                      class="lable"
                      [ngClass]="{
                                    'border-pending': bill.documentStatus === 'Pending',
                                    'border-open': bill.documentStatus === 'Open',
                                    'border-overdue': bill.documentStatus === 'Overdue',
                                    'border-paid': bill.documentStatus === 'Paid',
                                    'border-Closed': bill.documentStatus === 'Closed',
                                    'border-canceled': bill.documentStatus === 'Canceled',
                                }"
                      >{{ bill.documentStatus }}</span
                    >
                  </td>
                   <!--<td style="text-align: center;" class="value">
                        <button
                        class="btn btn-orange btn-sm"
                        style="
                          margin-right: 2px;
                          border: none;
                          background: none;
                          padding: 2px;
                          font-size: 1rem;
                        "
                        title="Edit"
                      >
                        <i class="ri-edit-box-line" style="color: #4262ff"></i>
                      </button>
                    <button class="btn btn-danger btn-sm" style="margin-right: 2px; border: none; background: none; padding: 2px; font-size: 1rem;" data-bs-toggle="tooltip" title="Delete">
                            <i class="ri-delete-bin-line" style="color: #FF0000;"></i>
                        </button>
                    </td>-->
                </tr>
            </tbody>
        </table>
    </div>
</div>