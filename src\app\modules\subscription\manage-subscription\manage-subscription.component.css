.upper-content {
  padding: 10px 20px;
  text-align: center;
  background: white;
}

.upper-content h2 {
  font-size: 2.5em;
  font-family: sans-serif;
  font-weight: 600;
  margin: 20px;
  color: #000;
}

.upper-content p {
  font-family: Inter;
}

.plans {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
  padding-bottom: 20px;
}

.plan {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: rgb(255, 255, 255);
  border-radius: 20px;
  box-shadow: 0px 4px 22px 11px rgba(0, 0, 0, 0.1);
  padding: 20px;
  width: 400px;
  text-align: left;
  margin-top: 40px;
  flex: 1 1 100%;
  /* Full width on small screens */
  max-width: 100%;
}

.plan h2 {
  font-family: Inter;
  font-size: 32px;
  font-weight: 700;
  line-height: 1.7;
  text-align: center;
  color: #000;
}

.plan p {
  font-family: "Inter", sans-serif;
  font-size: 15 px;
  font-weight: 500;
  text-align: center;
  color: #000;
}

.plan h4 {
  font-family: "Inter", sans-serif;
  font-size: 25px;
  font-weight: 800;
  line-height: 1.7;
  text-align: center;
  color: #4262ff;
}

.plan ul {
  list-style-type: none;
  padding: 0;
  margin-bottom: 20px;
  flex-grow: 1;
}

.plan ul li {
  margin-bottom: 10px;
  font-family: "Inter", sans-serif;
  font-size: 14px;
}

.plan ul li:before {
  content: "✔️";
  color: #4262ff;
  margin-right: 10px;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
s .plan button {
  width: 100%;
  padding: 10px;
  border: none;
  border-radius: 10px;
  font-size: 1em;
  font-weight: 600;
}

.change-plan {
  width: 100%;
  padding: 10px;
  border: none;
  border-radius: 10px;
  font-size: 1em;
  font-weight: 600;
  background: linear-gradient(to right, #4262ff, #512ca2);
  color: white;
}

.change-plan:hover {
  background: linear-gradient(to left, #4262ff, #512ca2);
}

.current-plan {
  background-color: gray;
  color: white;
  width: 100%;
  padding: 10px;
  border: none;
  border-radius: 10px;
  font-size: 1em;
  font-weight: 600;
}

.start-trial {
  background-color: #000;
  color: white;
  width: 100%;
  padding: 10px;
  border: none;
  border-radius: 10px;
  font-size: 1em;
  font-weight: 600;
}

.renew-plan {
  background-color: transparent;
  color: #4262ff;
  width: 100%;
  padding: 10px;
  border: 2px solid #4262ff;
  border-radius: 10px;
  font-size: 1em;
  font-weight: 600;
}

.renew-plan:hover {
  background-color: #4262ff;
  color: white;
}

.modern-subscription-header {
  text-align: center;
  margin-bottom: 32px;
}

.modern-subscription-header h2 {
  font-family: Inter, sans-serif;
  font-size: 2.2rem;
  font-weight: 700;
  color: #4262ff;
  margin-bottom: 10px;
}

.modern-subscription-header p {
  color: #555;
  font-size: 1.1rem;
  margin-bottom: 0;
}

.modern-plans {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  justify-content: center;
}

.modern-plan-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 2px 16px rgba(66, 98, 255, 0.09);
  padding: 32px 28px 28px 28px;
  min-width: 320px;
  max-width: 370px;
  flex: 1 1 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.18s, transform 0.18s;
  border: 2px solid #f4f6fa;
}

.modern-plan-card:hover {
  box-shadow: 0 6px 32px rgba(66, 98, 255, 0.16);
  transform: translateY(-4px) scale(1.03);
  border-color: #4262ff;
}

.modern-plan-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4262ff;
  margin-bottom: 12px;
  font-family: Inter, sans-serif;
}

.modern-plan-desc {
  color: #666;
  font-size: 1rem;
  margin-bottom: 18px;
  text-align: center;
}

.modern-plan-price {
  font-size: 2rem;
  font-weight: 700;
  color: #512ca2;
  margin-bottom: 24px;
}

.modern-plan-btns {
  display: flex;
  gap: 14px;
  width: 80%;
  justify-content: center;
}

.modern-btn,
.change-plan {
  background: linear-gradient(90deg, #4262ff 0%, #512ca2 100%);
  color: #fff;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
  box-shadow: 0 2px 8px rgba(66, 98, 255, 0.07);
  cursor: pointer;
}

.modern-btn:hover,
.change-plan:hover {
  background: linear-gradient(90deg, #512ca2 0%, #4262ff 100%);
  color: #fff;
}

.modern-btn:disabled,
.change-plan:disabled,
.current-plan {
  background: #e0e4f7 !important;
  color: #aaa !important;
  cursor: not-allowed;
  border: none;
}

.modern-btn-outline,
.renew-plan {
  background: #fff;
  color: #4262ff;
  border: 2px solid #4262ff;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: background 0.18s, color 0.18s, border 0.18s;
  box-shadow: none;
  cursor: pointer;
}

.modern-btn-outline:hover,
.renew-plan:hover {
  background: #4262ff;
  color: #fff;
  border: 2px solid #4262ff;
}

@media (max-width: 900px) {
  .modern-plans {
    flex-direction: column;
    align-items: center;
    gap: 24px;
  }
  .modern-plan-card {
    min-width: 90vw;
    max-width: 98vw;
    padding: 24px 10px 18px 10px;
  }
}

@media (max-width: 690px) {
  .plan {
    flex: 1 1 100%;
    /* Full width */
    max-width: 100%;
    margin-inline: 20px;
  }
}

/* Screen width 691px to 1024px: 2 columns */
@media (min-width: 691px) and (max-width: 1024px) {
  .plan {
    flex: 1 1 calc(50% - 60px);
    /* 2 columns with gap adjustment */
    max-width: calc(50% - 60px);
  }
}

/* Screen width greater than 1200px: 1 row with all items */
@media (min-width: 1024px) {
  .plan {
    flex: 1 1 calc(25% - 60px);
    /* Adjust if you want more/less items in one row */
    max-width: calc(25% - 60px);

  }
}
