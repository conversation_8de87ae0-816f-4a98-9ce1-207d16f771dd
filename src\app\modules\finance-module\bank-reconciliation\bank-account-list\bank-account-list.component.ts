import { Component } from '@angular/core';
import { BankAccount } from '../../bank/bank';
import { BankService } from '../../bank/bank.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-bank-account-list',
  templateUrl: './bank-account-list.component.html',
  styleUrls: ['./bank-account-list.component.css']
})
export class BankAccountListComponent {

filteredBankAccounts: BankAccount[] = [];
bankAccounts: BankAccount[] = [];
searchTerm: string = '';
activeTab = 'all';
minBalance: number = 0.00;
maxBalance: number = 0.00;

constructor(private bankService: BankService, private router: Router) {}

ngOnInit() {
  this.fetchBankAccounts();
}

fetchBankAccounts() {
    const entityId = parseInt(localStorage.getItem('entityId') || "0", 10);

    console.log("entity id:" , entityId);

    if (!entityId || isNaN(entityId)) {
      console.error("Invalid entityId. Cannot fetch quotations.");
      return;
  }

    this.bankService.getBankAccountsByEntityId(entityId).subscribe(
      (data: BankAccount[]) => {
        this.bankAccounts = data;
        this.filteredBankAccounts = this.bankAccounts;
      },
      (error) => {
        console.error('Error fetching quotations:', error);
      }
    );
  }

  onSearchChange() {
    this.filterBankAccounts();
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.filterBankAccounts();
  }

  filterBankAccounts() {
    const searchTermLower = this.searchTerm?.toLowerCase().trim() || '';
    console.log("search term: ", this.searchTerm);
    let filtered = this.bankAccounts;

    // Filter by active tab
    if (this.activeTab !== 'all') {
      filtered = this.bankAccounts;
    }

    // Filter by search term
    if (searchTermLower) {
      filtered = filtered.filter(account =>
            account.bankAccountId?.toString().includes(searchTermLower) ||
            account.openingBalanceId?.toString().includes(searchTermLower) ||
            account.entityId?.toString().includes(searchTermLower) ||
            account.userId?.toString().includes(searchTermLower) ||
            account.accountType?.toLowerCase().includes(searchTermLower)  ||
            account.accountName?.toLowerCase().includes(searchTermLower) ||
            account.bsbNo?.toLowerCase().includes(searchTermLower) ||
            account.balanceType?.toLowerCase().includes(searchTermLower)
      );

      console.log("searched: ", filtered);
    }

    // Filter by balance range if minBalance and maxBalance are defined
  if (this.minBalance !== undefined && this.maxBalance !== undefined) {
    filtered = filtered.filter(account =>
      account.balance >= this.minBalance && account.balance <= this.maxBalance
    );

  }
  console.log("final searched: ", filtered);
    this.filteredBankAccounts = filtered;
  }

  resetFilters() {
    this.searchTerm = '';
    this.activeTab = 'all';
    this.filteredBankAccounts = this.bankAccounts;
    this.maxBalance = 0.00;
    this.minBalance = 0.00;
  }

  openBankReconciliation(bankAccount: BankAccount | null) {
    if (bankAccount) {
      this.router.navigate(['/import-bank-reconciliation'], {
        state: { bankAccount: bankAccount }
      });
    }
  }

}
