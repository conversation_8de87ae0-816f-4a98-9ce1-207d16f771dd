import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';

@Component({
  selector: 'app-download-buttons',
  templateUrl: './download-buttons.component.html',
  styleUrls: ['./download-buttons.component.css']
})
export class DownloadButtonsComponent implements OnInit, OnDestroy {
  deferredPrompt: any = null;
  isIos: boolean = false;
  isStandalone: boolean = false;

  private beforeInstallPromptListener = (e: Event) => {
    e.preventDefault();
    this.deferredPrompt = e;
  };

  ngOnInit(): void {
    const userAgent = window.navigator.userAgent.toLowerCase();
    const isIosDevice = /iphone|ipad|ipod/.test(userAgent);
    const isInStandaloneMode = window.matchMedia('(display-mode: standalone)').matches;

    this.isIos = isIosDevice;
    this.isStandalone = isInStandaloneMode;

    window.addEventListener('beforeinstallprompt', this.beforeInstallPromptListener);
  }

  ngOnDestroy(): void {
    window.removeEventListener('beforeinstallprompt', this.beforeInstallPromptListener);
  }

  handleAndroidDownloadClick(): void {
    // Option 1: PWA Installation (current behavior)
    if (this.deferredPrompt) {
      // Show the install prompt
      this.deferredPrompt.prompt();

      // Wait for the user to respond to the prompt
      this.deferredPrompt.userChoice.then((choiceResult: any) => {
        if (choiceResult.outcome === 'accepted') {
          console.log('User accepted the PWA install prompt');
          // PWA will be installed automatically
        } else {
          console.log('User dismissed the PWA install prompt');
        }
        // Clear the deferredPrompt so it can't be used again
        this.deferredPrompt = null;
      }).catch((error: any) => {
        console.error('Error during PWA installation:', error);
        this.deferredPrompt = null;
      });
    } else {
      // Fallback for when beforeinstallprompt event hasn't fired
      this.showInstallInstructions();
    }

    // Option 2: If you want to download APK file instead, uncomment below:
    // this.downloadApkFile();
  }

  // Uncomment this method if you want actual APK download
  // private downloadApkFile(): void {
  //   const apkUrl = 'path/to/your/app.apk'; // Replace with your APK URL
  //   const link = document.createElement('a');
  //   link.href = apkUrl;
  //   link.download = 'your-app-name.apk';
  //   document.body.appendChild(link);
  //   link.click();
  //   document.body.removeChild(link);
  // }

  private showInstallInstructions(): void {
    const userAgent = navigator.userAgent.toLowerCase();
    let instructions = '';

    if (userAgent.includes('chrome')) {
      instructions = "To install this app:\n1. Tap the menu (⋮) in Chrome\n2. Select 'Add to Home screen'\n3. Tap 'Add'";
    } else if (userAgent.includes('firefox')) {
      instructions = "To install this app:\n1. Tap the menu in Firefox\n2. Select 'Install'\n3. Tap 'Add to Home screen'";
    } else if (userAgent.includes('samsung')) {
      instructions = "To install this app:\n1. Tap the menu in Samsung Internet\n2. Select 'Add page to'\n3. Choose 'Home screen'";
    } else {
      instructions = "To install this app, look for 'Install app', 'Add to Home screen', or similar option in your browser menu.";
    }

    alert(instructions);
  }

  handleIosInstructions(): void {
    alert("To install this app on your iOS device, tap 'Share' and then 'Add to Home Screen'.");
  }
}