import { Component, OnInit, ViewChild } from '@angular/core';
import { BusinessPartner, BusinessPartnerType } from '../business-partner';
import { InvoiceHead } from '../../invoice/invoice';
import { ActivatedRoute, Router } from '@angular/router';
import { BusinessPartnerService } from '../business-partner.service';
import Swal from 'sweetalert2';
import { HttpErrorResponse } from '@angular/common/http';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';

@Component({
  selector: 'app-update-business-partner',
  templateUrl: './update-business-partner.component.html',
  styleUrls: ['./update-business-partner.component.css']
})
export class UpdateBusinessPartnerComponent implements OnInit{
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;
  
  businessPartner: BusinessPartner = new BusinessPartner();
  customers: BusinessPartner[] = [];
  invoiceHead: InvoiceHead = new InvoiceHead();
  businessPartnerType: BusinessPartnerType[] =[];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private businessPartnerService :BusinessPartnerService,
  ) { }

  ngOnInit(): void {
    this.getBusinessPartnersListByEntity();
    this.loadBusinessPartnerTypes();
    const id = this.route.snapshot.paramMap.get('id'); 
    if (id) {
      this.getbusinessPartnerById(+id); 
    }
  }

  navigateToBusinessPartnerCancel() {
    this.router.navigate(['/business-partner']);
  }
  getbusinessPartnerById(id: number): void {
    this.businessPartnerService.getBusinessPartnerById(id).subscribe(
      (data: BusinessPartner) => {
        this.businessPartner = data; 
      },
      (error) => {
        console.error('Error fetching Business Partner:', error);
      }
    );
  }


  private getBusinessPartnersListByEntity(): void {
    const entityId = +((localStorage.getItem('entityId')) + "");
    this.businessPartnerService.getBusinessPartnersListByEntity(entityId).subscribe(
      (data: BusinessPartner[]) => {
        this.customers = data; // Store the list for email validation
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching business partners', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load business partners.',
          icon: 'error',
          confirmButtonText: 'OK',
        });
      }
    );
  }

  isEmailDuplicate(email: string, currentId: number): boolean {
    if (!email || !this.customers.length) return false; // Skip validation if no email or no data
    return this.customers.some(partner => 
      partner.email?.toLowerCase() === email.toLowerCase() && partner.businessPartnerId !== currentId
    );
  }
  

  onSubmitCustomerForm() {

      // Validate email before proceeding
  if (this.isEmailDuplicate(this.businessPartner.email, this.businessPartner.businessPartnerId)) {
    Swal.fire({
      title: 'Duplicate Email',
      text: 'This email is already in use. Please enter a different email.',
      icon: 'warning',
      confirmButtonText: 'OK',
    });
    return; // Prevent further execution
  }
    this.businessPartnerService.updateBusinessPartner(this.businessPartner.businessPartnerId ,this.businessPartner).subscribe(
      (response) => {
        Swal.fire({
          title: 'Success!',
          text: 'Business Partner updated successfully.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        });
        this.router.navigate(['/business-partner']);
      },
      (error) => {
        console.error('Error updating Business Partner', error);
        Swal.fire({
          title: 'Error!',
          text: 'Error updating Business Partner.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true, 
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Error updating Business Partner.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound() 
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
  }
  
  loadBusinessPartnerTypes() {
    this.businessPartnerService.getBusinessPartnerTypesList().subscribe(
      (data: BusinessPartnerType[]) => {
        this.businessPartnerType = data;
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching Business Partner Type', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load Business Partner Type.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true, 
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Failed to load Business Partner Type.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound() 
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    );
  }
  preventSubmit(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0; 
    }
  }

}
