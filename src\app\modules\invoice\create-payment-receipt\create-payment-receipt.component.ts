import { Component, ViewChild } from '@angular/core';
import { InvoiceService } from '../invoice.service';
import { EntityService } from '../../entity/entity.service';
import { Entity } from '../../entity/entity';
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { InvoiceHead, PaymentReceiptsDetails, PaymentReceiptsHead } from '../invoice';
import { BotControllerComponent } from 'src/app/chat-bot/bot-controller/bot-controller.component';
import { ChatResponseComponent } from 'src/app/chat-bot/chat-response/chat-response.component';
import { NgForm } from '@angular/forms';

@Component({
  selector: 'app-create-payment-receipt',
  templateUrl: './create-payment-receipt.component.html',
  styleUrls: ['./create-payment-receipt.component.css']
})
export class CreatePaymentReceiptComponent {
  @ViewChild(BotControllerComponent) chatBotComponent!: BotControllerComponent;
  @ViewChild(ChatResponseComponent) chatResponseComponent!: ChatResponseComponent;
  private audio!: HTMLAudioElement;

  invoiceHead: InvoiceHead = new InvoiceHead();
  paymentReceiptsHead: PaymentReceiptsHead = new PaymentReceiptsHead();
  details: PaymentReceiptsDetails = new PaymentReceiptsDetails();
  newPaymentNumber: string = '';
  businessEntityId: number = 0;
  lastPaymentNumber: string = '';
  businessEntity: Entity = new Entity();
  selectedInvoices: any[] = [];
  totalPaidAmount: number = 0;
  private debounceTimer: any = null;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private entityService: EntityService,
    private invoiceService: InvoiceService
  ) { }


  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      const invoiceIds = JSON.parse(params['invoiceIds'] || '[]');
      if (invoiceIds.length > 0) {
        this.loadInvoices(invoiceIds);
      } else {
        Swal.fire({
          title: 'No Invoices Found',
          text: 'No invoice IDs were provided.',
          icon: 'warning',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#ff7e5f',
          cancelButtonColor: '#be0032',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('No Invoices Found');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      }
    });
    this.getBusinessEntityById();

    if (!this.paymentReceiptsHead.documentDate) {
      this.paymentReceiptsHead.documentDate = this.getTodayDate();
    }
  }

  // Utility function to get today's date in 'YYYY-MM-DD' format
  getTodayDate(): string {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const dd = String(today.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }


  loadInvoices(invoiceIds: number[]): void {
    const invoiceRequests = invoiceIds.map(id => this.invoiceService.getInvoiceHeadById(id).toPromise());
    Promise.all(invoiceRequests)
      .then(invoices => {
        this.selectedInvoices = invoices;
      })
      .catch(error => {
        Swal.fire({
          title: 'Error Loading Invoices',
          text: 'There was an error fetching the invoice details.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Error Loading Invoices');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }
        });
      });
  }

  getBusinessEntityById() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '');
    this.entityService.getBusinessEntityById(this.businessEntityId).subscribe(
      (data) => {
        this.businessEntity = data;
        this.lastPaymentNumber = this.incrementPaymentNumber(
          this.businessEntity.paymentReceiptNumber
        );
        this.paymentReceiptsHead.paymentNumber = this.lastPaymentNumber;
      },
      (error) => console.error(error)
    );
  }

  incrementPaymentNumber(paymentNumber: string): string {
    if (!paymentNumber) {
      return 'P000001';
    }
    const prefix = paymentNumber.charAt(0);
    const numericPart = paymentNumber.slice(1);
    const incrementedNumber = (Number(numericPart) + 1).toString().padStart(numericPart.length, '0');
    return prefix + incrementedNumber;
  }


  getTotalPaidAmount(): number {
    return this.selectedInvoices.reduce((total, invoice) => total + (invoice.creditAmount || 0), 0);
  }


  getInvoiceNewBalance(invoice: any): number {
    const balanceAmount = invoice.balanceAmount || 0;
    const creditAmount = invoice.creditAmount || 0;
    return balanceAmount - creditAmount;
  }



  onCancel() {
    this.router.navigate(['/payment-receipt']);
  }



  preventEnter(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault(); // Prevent form submission on "Enter"
    }
  }




  onSubmit(f: NgForm) {
    // Check if credit amount is provided for all invoices
    const invalidInvoices = this.selectedInvoices.filter(invoice => !invoice.creditAmount || invoice.creditAmount <= 0);

    if (invalidInvoices.length > 0) {
      // Show validation error
      Swal.fire({
        title: 'Warning!',
        text: 'Paid Amount is required and must be greater than 0.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: 'blue'
      });
    } else {
      // Pass the invoices to the saveQuotation method
      this.paymentReceiptsHead.totalPaidAmount = this.getTotalPaidAmount();
      this.paymentReceiptsHead.details = this.selectedInvoices.map(invoice => {
        let detail = new PaymentReceiptsDetails();
        detail.invoiceHead = invoice.invoiceHeadId;
        detail.invoiceNumber = invoice.invoiceNumber;
        detail.grandTotal = invoice.grandTotal;
        detail.balanceAmount = invoice.balanceAmount;
        detail.paidAmount = invoice.creditAmount;
        detail.ledgerAccountName = "Income";
        detail.tax = invoice.grandTotal > 0 ? (invoice.totalGst / invoice.grandTotal * invoice.creditAmount) : 0;
      // detail.GlAccountIncome = invoice.grandTotal > 0 ? (invoice.creditAmount) - (invoice.totalGst / invoice.grandTotal * invoice.creditAmount) : invoice.creditAmount;

        return detail;
      });

      this.savePaymentReceipt(this.selectedInvoices);
    }
  }

  savePaymentReceipt(invoices: any[]) {

    // Ensure document date is set, default to today if not provided
    if (!this.paymentReceiptsHead.documentDate) {
      this.paymentReceiptsHead.documentDate = this.getTodayDate();
    }
    // Iterate over each invoice to validate its credit amount
    const invalidInvoices = invoices.filter(invoice => invoice.creditAmount > invoice.balanceAmount);

    if (invalidInvoices.length > 0) {
      Swal.fire({
        title: 'Paid Amount Exceeds Balance!',
        text: 'One or more invoices have Paid amounts greater than their balances. Do you want to continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, continue',
        cancelButtonText: 'Ask Chimp',
        confirmButtonColor: '#ff7e5f',
        cancelButtonColor: '#be0032',
      }).then((result) => {
        if (result.isConfirmed) {
          // If user confirms, set flag and continue
          invalidInvoices.forEach(invoice => {
            invoice.isCreditExceeded = true;
          });
          this.proceedToSave();
        } else if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          if (this.chatBotComponent) {
            Swal.fire({
              title: 'Processing...',
              text: 'Please wait while Chimp processes your request.',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
                this.chatBotComponent.setInputData('Paid Amount Exceeds Balance!');
                this.chatBotComponent.responseReceived.subscribe(response => {
                  Swal.close();
                  this.chatResponseComponent.showPopup = true;
                  this.chatResponseComponent.responseData = response;
                  this.playLoadingSound();
                  this.stopLoadingSound();
                });
              },
            });
          } else {
            console.error('ChatBotComponent is not available.');
          }
        }
      });

    } else {
      // If all credit amounts are valid, proceed to save
      this.proceedToSave();
    }
  }

  proceedToSave() {
    // Assuming the selectedInvoices array is already populated and validated
    if (this.selectedInvoices.length > 0) {
      // Set the businessPartnerId from the first invoice, as all invoices have the same customer
      this.paymentReceiptsHead.businessPartnerId = this.selectedInvoices[0].businessPartnerId;
      this.paymentReceiptsHead.customerName = this.selectedInvoices[0].reference;
      this.paymentReceiptsHead.invoiceNumbers = this.selectedInvoices.map(invoice => invoice.invoiceNumber);
    }

    // Set other credit note properties
    this.paymentReceiptsHead.documentStatus = 'Open';
    this.paymentReceiptsHead.userId = +(localStorage.getItem('userid') + "");
    this.paymentReceiptsHead.entityId = +(localStorage.getItem('entityId') + "");

    // Save the credit note
    this.invoiceService.savePaymentReceiptsHead(this.paymentReceiptsHead).subscribe(
      (response: any) => {
        this.updatePaymentReceiptNumber();

        // Update the balance amount for each selected invoice
        this.selectedInvoices.forEach(invoice => {
          // Assuming you have already calculated the credit note amount for each invoice
          const newBalanceAmount = invoice.balanceAmount - invoice.creditAmount; // Calculate new balance
          this.updateInvoiceBalance(invoice.invoiceHeadId, newBalanceAmount); // Call method to update the invoice balance
        });

        Swal.fire({
          title: 'Success!',
          text: 'The Payment Receipt has been successfully saved.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#28a745',
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/payment-receipt']);
          }
        });
      },
      (error: HttpErrorResponse) => {
        console.error('Error saving Payment Receipt', error);
        Swal.fire({
          title: 'Error!',
          text: 'Unable to save the Payment Receipt. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          cancelButtonText: 'Ask Chimp',
          confirmButtonColor: '#be0032',
          cancelButtonColor: '#007bff',
          showCancelButton: true,
        }).then((result) => {
          if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
            if (this.chatBotComponent) {
              Swal.fire({
                title: 'Processing...',
                text: 'Please wait while Chimp processes your request.',
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                  this.chatBotComponent.setInputData('Unable to save the Payment Receipt. Please try again.');
                  this.chatBotComponent.responseReceived.subscribe(response => {
                    Swal.close();
                    this.chatResponseComponent.showPopup = true;
                    this.chatResponseComponent.responseData = response;
                    this.playLoadingSound();
                    this.stopLoadingSound()
                  });
                },
              });
            } else {
              console.error('ChatBotComponent is not available.');
            }
          }

        });
      }
    );
  }

  // Method to update invoice balance in the backend
  updateInvoiceBalance(invoiceHeadId: number, newBalanceAmount: number) {
    this.invoiceService.updateInvoiceBalance(invoiceHeadId, newBalanceAmount).subscribe(
      (response: any) => {
      },
      (error: HttpErrorResponse) => {
        console.error(`Error updating balance for Invoice ${invoiceHeadId}:`, error);
      }
    );
  }

  updatePaymentReceiptNumber() {
    this.businessEntityId = +(localStorage.getItem('entityId') || '0');
    this.businessEntity.paymentReceiptNumber = this.paymentReceiptsHead.paymentNumber;
    this.entityService.updatePaymentReceiptNumber(this.businessEntity, this.businessEntityId).subscribe(
      (data) => {
      },
      (error) => {
        console.error(error);
      }
    );
  }
  playLoadingSound() {
    let audio = new Audio();
    audio.src = "../assets/google_chat.mp3";
    audio.load();
    audio.play();
  }

  stopLoadingSound(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }
}
